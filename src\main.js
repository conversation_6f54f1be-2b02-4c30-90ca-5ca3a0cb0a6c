import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css' // a modern alternative to CSS resets
import 'tailwindcss/tailwind.css'

import animated from 'animate.css'
Vue.use(animated)

import Element from 'element-ui'
import './styles/element-variables.scss'
import 'element-ui/lib/theme-chalk/index.css'

import AFTableColumn from 'af-table-column'
Vue.use(AFTableColumn)

import VueAMap from 'vue-amap'
Vue.use(VueAMap)

VueAMap.initAMapApiLoader({

  // 高德地图Key
  // key: 'aa2c54027796c2907ab50d8751c7bcea',
  key: 'e2ed3fab4c822eef0835a05afaf4f59d',
  plugin: ['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PolyEditor', 'AMap.CircleEditor', 'AMap.Geocoder'],
  v: '1.4.4'
})
import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log
import * as filters from './filters' // global filters

// 指令
import permission from './directive/permission/permission.js'// 权限判断指令
import waves from './directive/waves/index.js'// 权限判断指令
import dropDownShow from './directive/el-dropdown-show/el-show.js'// 权限判断指令
Vue.use(permission)
Vue.use(waves)
Vue.use(dropDownShow)
Vue.prototype.global = {
  cacheProvince: []
}

import globalVariable from './components/globalVariable/globalVariable'
Vue.prototype.commonsVariable = globalVariable

import Clipboard from 'clipboard'
Vue.prototype.Clipboard = Clipboard

import moment from 'moment'
Vue.prototype.$moment = moment
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
// import { mockXHR } from '../mock'
// if (process.env.NODE_ENV === 'production') {
//   mockXHR()
// }
Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})
// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
