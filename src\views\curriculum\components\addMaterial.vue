<template>
  <el-dialog
    :visible.sync="materialPop"
    :title="materialTitle"
    :close-on-click-modal="!materialPop"
    width="60%"
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="materialForm"
        :model="listQuery"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="教材版本" prop="title">
          <el-input
            v-model="listQuery.title"
            placeholder="请输入教材版本"
            maxlength="20"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="适用产品线" prop="clientCode">
          <el-select
            v-model="listQuery.clientCode"
            placeholder="请选择产品线"
            clearable
            class="filter-item"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in clientCode"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教材状态" prop="status">
          <el-select
            v-model="listQuery.status"
            placeholder="请选择教材状态"
            clearable
            class="filter-item"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in enableList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教材排序" prop="sort">
          <el-input
            v-model.number="listQuery.sort"
            placeholder="请输入排序"
            :disabled="isEdit"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(materialPop = false), cancelClass()"
        >取消</el-button
      >
      <el-button type="primary" size="mini" @click="custormClass"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { clientCode, addMaterialList, getMaterialList } from "@/api/classType";
import { enableList } from "@/utils/field-conver";
export default {
  name: "AddMaterial",
  data() {
    return {
      materialTitle: "",
      materialPop: false,
      listQuery: {
        clientCode: []
      },
      rules: {
        title: { required: true, trigger: "blur", message: "请输入教材版本" },
        clientCode: {
          required: true,
          trigger: "blur",
          message: "请选择适用产品线"
        },
        status: { required: true, trigger: "blur", message: "请选择教材状态" }
      },
      clientCode: [],
      enableList: enableList,
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false
    };
  },
  props: {
    modelType: {
      type: [String, Number],
      default: "1"
    },
    textType: {
      type: Number,
      default: 1
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode();
    });
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || [];
        this.clientCode = clientCodes.filter(item => item.level === 1);
      });
    },
    cancelClass() {
      this.listQuery = {};
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    },
    custormClass() {
      this.enableFlagsCreate = true;
      this.$refs.materialForm.validate(valid => {
        if (valid) {
          const params = Object.assign(
            {},
            this.listQuery,
            { id: this.listQuery.id ? this.listQuery.id : "" },
            { type: this.modelType },
            {
              textType: 0,
              subjectId: 0
            }
          );
          addMaterialList(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "操作成功"
                });
                this.$emit("addMaterialList");
                this.materialPop = false;
                this.listQuery = {};
                this.enableFlagsCreate = false;
                this.$refs.materialForm.clearValidate();
              }
            })
            .catch(() => {
              this.enableFlagsCreate = false;
            });
        } else {
          this.enableFlagsCreate = false;
          return false;
        }
      });
    },
    getMaterialList(ids) {
      getMaterialList(ids).then(res => {
        if (res.code === "000000") {
          this.listQuery = res.data;
        }
      });
    },
    changeInit() {
      this.listQuery = {};
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    }
  }
};
</script>

<style scoped>
.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}
</style>
