<template>
  <el-dialog :visible.sync="operaPopFlag" :title="operaTitle" :close-on-click-modal="!operaPopFlag" width="60%" @close="cancelClass">
    <el-form ref="appForm" :model="listQuery" label-width="120px">
      <el-row>
        <el-col :sm="{span:24}" :md="{span:16}" :lg="{span:16}">
          <el-form-item label="员工姓名" required>
            <el-select v-model="listQuery.realName" placeholder="请选择员工姓名" filterable remote clearable class="filter-item" :remote-method="getEnableUser" @change="getPhone">
              <el-option v-for="item in userList" :key="item.id" :label="item.realName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="listQuery.phone" placeholder="手机号码" disabled />
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:8}" :lg="{span:8}" class="wechat">
          <el-form-item>
            <div class="upload-imgs">
              <div v-if="!wechatCodeImg">
                <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="upload($event)">
                <a class="add"><i class="iconfont icon-plus" /><p>上传二维码</p></a>
              </div>
              <p class="img">
                <img v-if="wechatCodeImg" :src="wechatCodeImg">
                <a v-if="wechatCodeImg" class="close" @click="delImgB">
                  <i class="el-icon-delete" />
                </a>
              </p>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="mar15">
        <el-col :sm="{span:24}" :md="{span:10}" :lg="{span:10}">
          <el-form-item label="省份">
            <el-select v-model="listQuery.regionId" placeholder="省" filterable @change="getProvince">
              <el-option value="">全部</el-option>
              <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:10}" :lg="{span:10}">
          <el-form-item label="加盟项目">
            <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" @change="getProject">
              <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:4}" :lg="{span:4}">
          <el-button v-permission="['setting:opera:auth']" type="primary" size="mini" class="auth-btn" @click="authBtn">授权</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :sm="{span:24}" :md="{span:24}" :lg="{span:24}">
          <el-table
            v-loading="listLoading"
            :data="lists"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column type="index" />
            <el-table-column label="省份" prop="regionName" />
            <el-table-column label="项目" prop="projectName" />
            <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
              <template slot-scope="scope">
                <el-button v-permission="['setting:opera:del']" type="primary" size="mini" @click="operaDelt(scope.row,scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row>
        <el-col :sm="{span:24}" :md="{span:24}" class="center mar15">
          <el-button type="primary" size="mini" @click="addOpera">确定</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
var arr = []
var nameRule = /^\d+$|^\d+[.]?\d+$/ // 输入数字
import { uploadSuccess } from '@/api/common'
import { pageEnableUser, addOpera, operaDetail, operaUpdate } from '@/api/system-setting'
export default {
  name: 'OperaPop',
  props: {
    projectList: {
      type: Array,
      default: function() {
        return []
      }
    },
    sheng: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      operaPopFlag: false,
      operaTitle: '',
      wechatCodeImg: '',
      listQuery: {
        pageIndex: 1,
        pageSize: 1000
      },
      isEdit: false,
      userList: [],
      lists: [],
      listLoading: false,
      authObj: {
        regionName: '',
        projectName: ''
      },
      projectLists: [],
      ids: '',
      authList: [],
      subFlag: null,
      uuid: ''
    }
  },
  watch: {
    'listQuery.realName': {
      deep: true,
      handler(newVal) {
        if (!newVal) {
          this.listQuery.phone = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getEnableUser()
    })
  },
  methods: {
    getEnableUser(val) {
      let mobileVal
      let name
      if (!nameRule.test(val)) {
        name = val
      } else {
        mobileVal = val
      }
      const params = Object.assign({}, { pageIndex: this.listQuery.pageIndex, pageSize: this.listQuery.pageSize, realName: name || '', mobile: mobileVal || '' })

      pageEnableUser(params).then(res => {
        if (res.code === '000000') {
          if (res.code === '000000') {

            this.userList = res.data.records
          }
        }
      }).catch(() => {

      })
    },
    upload(e) { // 首页封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/opera/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的二维码不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/opera/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.wechatCodeImg = res.data.url
              }
            })
          }
        })
      }
    },
    delImgB() {
      this.wechatCodeImg = ''
    },
    cancelClass() {
      this.listQuery.pageIndex = 1
      this.listQuery.pageSize = 1000
      this.listQuery.realName = ''
      this.listQuery.phone = ''
      this.listQuery.regionId = ''
      this.listQuery.projectId = ''
      this.wechatCodeImg = ''
      this.lists = []
      arr = []
      this.projectLists = []
    },
    operaDelt(row, index) {
      this.$confirm('确定要删除此数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.lists.splice(index, 1)
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        arr = []

      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getPhone() {
      this.$forceUpdate()
      this.userList.forEach(item => {
        if (item.id === this.listQuery.realName) {
          this.listQuery.phone = item.mobile
        }
      })
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    },
    getProvince() {
      const obj1 = {}
      this.sheng.forEach(item => {
        if (item.id === this.listQuery.regionId) {
          obj1['regionName'] = item.name
          obj1['regionId'] = item.id
        }
      })
      this.$set(this.authObj, 'regionName', obj1.regionName)
      this.$set(this.authObj, 'regionId', obj1.regionId)
    },
    authBtn() {
      if (!this.listQuery.regionId) {
        this.$message({
          type: 'warning',
          message: '请选择省份'
        })
        return
      }
      if (!this.listQuery.projectId) {
        this.$message({
          type: 'warning',
          message: '请选择项目'
        })
        return
      }
      this.lists = [...this.lists, ...JSON.parse(JSON.stringify(this.projectLists))]
      this.projectLists = []
      this.listQuery.regionId = ''
      this.listQuery.projectId = ''
    },
    getProject() {
      const obj = {}
      this.projectList.forEach(item => {
        if (item.id === this.listQuery.projectId) {
          obj['projectName'] = item.projectName
          obj['projectId'] = item.id
        }
      })
      this.$set(this.authObj, 'projectName', obj.projectName)
      this.$set(this.authObj, 'projectId', obj.projectId)
      arr.push(this.authObj)
      if (arr.length === 2) {
        arr.splice(0, 1)
      }
      this.projectLists = arr
      this.authObj = {}
    },
    addOpera() {
      if (!this.wechatCodeImg) {
        this.$message({
          type: 'warning',
          message: '请上传二维码'
        })
        return
      }
      if (!this.listQuery.realName) {
        this.$message({
          type: 'warning',
          message: '请选择员工'
        })
        return
      }
      if (this.lists.length === 0) {
        this.$message({
          type: 'warning',
          message: '请添加运营经理权限集合'
        })
        return
      }
      if (nameRule.test(this.listQuery.realName)) {

        const params = Object.assign({}, { userId: this.listQuery.realName, wechatCodeImg: this.wechatCodeImg, operationAuths: this.lists, id: this.ids ? this.ids : '' })
        if (this.subFlag === 'create') {
          addOpera(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '新增成功'
              })
              this.operaPopFlag = false
              this.$emit('refresh')
              this.listQuery.pageIndex = 1
              this.listQuery.pageSize = 1000
              this.listQuery.realName = ''
              this.listQuery.phone = ''
              this.listQuery.regionId = ''
              this.listQuery.projectId = ''
              this.wechatCodeImg = ''
              this.lists = []
              arr = []
              this.projectLists = []
            }
          }).catch(() => {

          })
        } else if (this.subFlag === 'update' && this.ids) {
          operaUpdate(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.operaPopFlag = false
              this.$emit('refresh')
              this.listQuery.pageIndex = 1
              this.listQuery.pageSize = 1000
              this.listQuery.realName = ''
              this.listQuery.phone = ''
              this.listQuery.regionId = ''
              this.listQuery.projectId = ''
              this.wechatCodeImg = ''
              this.lists = []
              arr = []
              this.projectLists = []
            }
          }).catch(() => {

          })
        }
      } else {
        this.$message({
          type: 'warning',
          message: '该人员已经离职，请重新选择运营经理'
        })
      }
    },
    getOperaDetail(ids) { // 获取运营经理的详情
      operaDetail(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery.phone = res.data.mobile || ''
          this.getEnableUser()
          this.wechatCodeImg = res.data.wechatCodeImg || ''
          this.authList = res.data.operationAuths
          this.lists = [...res.data.operationAuths] || []
          this.ids = res.data.id
          let showFlag = false
          this.userList.forEach(item => {
            if (item.id === res.data.userId) {
              showFlag = true
            }
          })
          if (showFlag) {
            this.listQuery.realName = res.data.userId || ''
          } else {
            this.listQuery.realName = res.data.realName || ''
          }

        }
      }).catch(() => {

      })
    }
  }
}
</script>

<style scoped lang="scss">
  .upload-imgs{
    position: relative;
    width: 118px;
    height: 118px;
    font-size: 14px;
    display: inline-block;
    padding: 10px;
    margin-right: 25px;
    border: 2px dashed #ccc;
    text-align: center;
    vertical-align: middle;
  }
  .upload-imgs .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 94px;
    line-height: 94px
  }
  .upload-imgs .add .iconfont{
    padding: 10px 0;
    font-size: 40px;
  }
  .upload-imgs .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 118px;
    height: 118px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    position: relative;
    width: 94px;
    height: 94px;
    line-height: 94px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
    width: 94px;
    height: 94px;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:-10px;
    left: -10px;
    width:118px;
    height:118px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 118px;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
  .wechat{
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .auth-btn{
    margin-left: 10px;
    margin-top: 10px;
  }
  .mar15{
    margin: 15px 0;
  }
  .center{
    display: flex;
    justify-content: center;
    align-content: center;
  }
</style>
