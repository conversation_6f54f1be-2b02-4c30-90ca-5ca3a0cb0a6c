<template>
  <el-tag :type=" this.getStatus.type">{{ this.getStatus.label }}</el-tag>
</template>
<script>
import { schoolJoinStatusList } from '@/utils/field-conver'

export default {
  name: 'SchoolJoinStatusTag',
  props: {
    status:{
      type: Number,
      default: 0,
      required: true
    }
  },
  computed: {
    getStatus(){
      return schoolJoinStatusList.filter(item => item.value === this.status)[0]
    }
  }
}
</script>
<style scoped>
</style>
