<template>
  <div class="password-bg">
    <div class="common-line">
      <el-steps :active="active" class="process">
        <el-step title="验证手机号" icon="el-icon-edit" />
        <el-step title="重置密码" icon="el-icon-upload" />
        <el-step title="完成" icon="el-icon-success" />
      </el-steps>
      <div class="validate-info">
        <el-form ref="ruleForm" :model="ruleForm" class="demo-ruleForm">
          <div v-if="showFlag">
            <div class="code-line">
              <el-form-item prop="phone" class="form-info">
                <span class="svg-container">
                  <svg-icon icon-class="user" />
                </span>
                <el-input
                        v-model="ruleForm.phone"
                        placeholder="请输入手机号"
                        name="userName"
                        type="text"
                        tabindex="1"
                        autocomplete="on"
                        class="info-list"
                />
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item prop="captchaCode" style="width: 70%;margin-bottom: 10px;" class="form-info">
                <span class="svg-container">
                  <svg-icon icon-class="codeV" />
                </span>
                <el-input
                        v-model="ruleForm.captchaCode"
                        placeholder="请输入验证码"
                        name="code"
                        type="text"
                        tabindex="3"
                        autocomplete="on"
                        class="info-list"
                />
              </el-form-item>
              <ImgVerificationCode @setCaptchaId="setCaptchaId" ref="ImgVerificationCode"></ImgVerificationCode>
            </div>
            <div class="code-line">
              <el-form-item prop="code" class="form-info">
                <span class="svg-container">
                  <svg-icon icon-class="codeV" />
                </span>
                <el-input
                        v-model="ruleForm.securityCode"
                        placeholder="请输入手机验证码"
                        name="code"
                        type="text"
                        tabindex="1"
                        autocomplete="on"
                        class="info-list"
                        style="width: 77%"
                />
                <div class="childrenM">
                  <el-button :disabled="show===true?false:true" type="primary" plain style="padding: 15px;" @click.native="getCode">
                    {{ codeContent }}
                  </el-button>
                </div>
              </el-form-item>
              <el-button style="margin-top: 12px;width:100%" type="primary" @click="next">下一步</el-button>
            </div>
          </div>
          <div v-if="!showFlag">
            <div class="code-line">
              <el-form-item prop="phone" class="form-info">
                <span class="svg-container">
                  <svg-icon icon-class="passwords" />
                </span>
                <el-input
                        :key="passwordType"
                        ref="password"
                        v-model="ruleForm.newPassword"
                        placeholder="请输入新密码"
                        name="password"
                        :type="passwordType"
                        tabindex="1"
                        autocomplete="on"
                        class="info-list"
                />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>
            </div>
            <div class="code-line">
              <el-form-item prop="phone" class="form-info">
                <span class="svg-container">
                  <svg-icon icon-class="passwords" />
                </span>
                <el-input
                        :key="passwordTypeConfirm"
                        ref="passwordConfirm"
                        v-model="ruleForm.confirmNewPassword"
                        placeholder="再次确认密码"
                        name="repeatPassword"
                        :type="passwordTypeConfirm"
                        tabindex="1"
                        autocomplete="on"
                        class="info-list"
                />
                <span class="show-pwd" @click="showPwdConfirm">
                  <svg-icon :icon-class="passwordTypeConfirm === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>
              <el-button style="margin-top: 12px;width:100%" type="primary" @click="confirmSub">确认</el-button>
            </div>
            <el-button v-if="backFlag" style="margin-top: 12px;width:100%" type="primary" @click="getBack">返回上一步</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { sendCode, updatePassword } from '@/api/common'
import ImgVerificationCode from '@/views/login/components/ImgVerificationCode.vue'

export default {
  name: 'PasswordInfo',
  components: { ImgVerificationCode },
  data() {
    return {
      active: 0,
      ruleForm: {},
      codeContent: '获取验证码', // 倒计时参数
      show: true, // 倒计时参数
      count: '', // 倒计时参数
      timer: null, // 倒计时参数
      showFlag: true,
      backFlag: false,
      passwordType: 'password',
      passwordTypeConfirm: 'password'
    }
  },
  created() {
    // const hash = window.location.search.slice(1)
    // if (window.localStorage) {
    //   window.localStorage.setItem('x-admin-oauth-code', hash)
    //   window.close()
    // }
  },
  methods: {
    setCaptchaId(captchaId) {
      this.ruleForm.captchaId = captchaId
    },
    next() {
      if (this.ruleForm.securityCode && this.ruleForm.phone) {
        if (this.active++ > 2) this.active = 0
        this.showFlag = !this.showFlag
      }
      else if (!this.ruleForm.phone) {
        this.$message({
          type: 'warning',
          message: '请输入手机号'
        })
      }
      else {
        this.$message({
          type: 'warning',
          message: '请输入手机验证码'
        })
      }
    },
    // 获取短信验证码
    getCode() {
      if (this.ruleForm.phone && /^1(3|4|5|6|7|8|9)\d{9}$/.test(this.ruleForm.phone)) {

        sendCode(this.ruleForm).then(res => {
          if (res.code === '000000') {
            this.countDown()
            this.$message({
              type: 'success',
              message: '验证码发送成功'
            })
          }
          else {
            this.show = true
            clearInterval(this.timer)
          }
        }).catch(() => {

        })
      }
      else {
        this.$message({
          type: 'error',
          message: '手机号错误'
        })
      }
    },
    // 倒计时functon
    countDown() {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
            this.codeContent = this.count + 's'
          }
          else {
            this.show = true
            this.codeContent = '获取验证码'
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    confirmSub() {
      const reg = /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{6,16}$))/
      if (this.ruleForm.newPassword && this.ruleForm.confirmNewPassword) {
        if (!reg.test(this.ruleForm.newPassword)) {
          this.$message({
            type: 'warning',
            message: '请输入6~16位新密码包含数字，符号，字母且区分大小写'
          })
        }
        else if (this.ruleForm.newPassword !== this.ruleForm.confirmNewPassword) {
          this.$message({
            type: 'warning',
            message: '2次输入的密码不一致'
          })
        }
        else {
          const data = Object.assign({}, this.ruleForm, {mobile: this.ruleForm.phone})
          updatePassword(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '密码修改成功'
              })
              setTimeout(() => {
                this.$router.push({
                  name: 'Navigation'
                })
              }, 1000)
            }
          }).catch(() => {
            this.backFlag = true
          })
        }
      }
      else if (!this.ruleForm.newPassword) {
        this.$message({
          type: 'warning',
          message: '请输入新的密码'
        })
      }
      else {
        this.$message({
          type: 'warning',
          message: '请输入确认的密码'
        })
      }
    },
    getBack() {
      this.showFlag = true
      this.ruleForm.securityCode = ''
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      }
      else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    showPwdConfirm() {
      if (this.passwordTypeConfirm === 'password') {
        this.passwordTypeConfirm = ''
      }
      else {
        this.passwordTypeConfirm = 'password'
      }
      this.$nextTick(() => {
        this.$refs.passwordConfirm.focus()
      })
    }
  },
  render: function (h) {
    return h() // avoid warning message
  }
}
</script>
<style scoped lang="scss">
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #333;
$white: #ffffff;
.password-bg {
  background: #eaeaea;
  width: 100%;
  height: 100vh;
}

.process {
  width: 80%;
  padding: 20px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: #fff;
  margin: 0 auto;
}

.validate-info {
  width: 30%;
  margin: 50px auto;
  padding: 40px 20px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: #fff;

  .form-info {
    position: relative;
    position: relative;
    max-width: 100%;

    .svg-container {
      position: absolute;
      top: 0px;
      left: 0;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
      z-index: 9999;
    }

    .show-pwd {
      position: absolute;
      top: 0px;
      right: 10px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
      z-index: 9999;
    }

    .childrenM {
      position: absolute;
      right: 0;
      top: 0;
      background: none !important;
      border: none !important;
    }
  }
}

.svg-container {
  padding: 6px 5px 6px 15px;
  color: $dark_gray;
  vertical-align: middle;
  width: 30px;
  display: inline-block;
}

.info-list {
  background-color: #eaeaea;
  overflow: hidden;
  border-radius: 6px;
  width: 100%;
  padding: 5px 0;
}
</style>
<style lang="scss">
$bg: #283443;
.el-input--medium .el-input__inner {
  height: 36px;
  line-height: 36px;
  border: none;
  background: none;
  padding-left: 10%;

  &:-webkit-autofill {
    box-shadow: 0 0 0px 1000px #E5E5E5 inset !important;
    -webkit-text-fill-color: $bg !important;
  }
}
</style>
