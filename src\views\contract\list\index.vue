<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.contractCode" placeholder="合同编号" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.orderCode" placeholder="订单编号" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.clueCode" placeholder="客户编号" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.searchField" placeholder="合伙人/手机号/校区名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" class="filter-item" style="width: 120px;" clearable filterable>
        <el-option
          v-for="item in projectList"
          :key="item.id"
          :label="item.projectName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="合同状态" class="filter-item" style="width: 120px;" clearable filterable>
        <el-option
          v-for="item in contractStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <area-picker :area-list="areaList" :level="'3'" area-style="width: 300px" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="contractTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        unlink-panels
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="合同编号" prop="contractCode" width="100">
        <template slot-scope="scope">
          <span class="link-type" @click="handleView(scope.row, false,0)">{{ scope.row.contractCode }}</span>
        </template>
      </af-table-column>

      <af-table-column label="订单编号" width="80">
        <template slot-scope="scope">
          <span class="link-type" @click="toDetailHandover(scope.row)">{{ scope.row.orderCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="客户编号" width="80">
        <template slot-scope="{ row }">
          <router-link :to="'/customer/detail/'+row.clueId" class="link-type">
            <span>{{ row.clueCode }}</span>
          </router-link>
        </template>
      </af-table-column>
      <el-table-column label="客户名称" prop="customer" width="90" show-overflow-tooltip />
      <af-table-column label="合同名称" prop="contractName" width="220" show-overflow-tooltip/>
      <af-table-column label="校区地址" prop="provinceName" width="260" show-overflow-tooltip>
        <template slot-scope="{row}" show-overflow-tooltip>
          {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}{{ row.address }}
        </template>
      </af-table-column>
      <af-table-column label="加盟项目" prop="projectName" />
      <el-table-column label="合同状态" prop="status" :formatter="getContractStatus" width="80" />
      <el-table-column label="合同类型" prop="signVersion"  width="95" >
        <template slot-scope="{row}">
          <el-tag v-if="row.signVersion === 3" type="success">本地合同</el-tag>
          <el-tag v-else type="primary">线上合同</el-tag>
        </template>
      </el-table-column>
      <af-table-column label="合同开始日期" prop="startTime" :formatter="getStartTime" width="110" />
      <af-table-column label="合同结束日期" prop="endTime" :formatter="getEndTime" width="110" />
      <el-table-column label="操作" width="260" fixed="right" class-name="small-padding fixed-width action-warp">
        <template slot-scope="{row}">
          <span v-show="row.status == 1">
            <el-button v-permission="['contract:list:generateContract']" type="primary" plain size="mini" @click="confirmQualification(row)">
              确认资质
            </el-button>
          </span>
          <span v-show="row.status == 0 || row.status == 1 || row.status == 4">
            <el-button v-permission="['contract:list:update']" type="primary" plain size="mini" @click="handleView(row, 'true',0)">
              确认合同条款
            </el-button>
          </span>
          <span v-show="row.status == 1">

<!--        <el-popover
        v-permission="['contract:list:generateContract']"
        placement="top"
        width="350"
        trigger="click">
            <div>
              <div class="el-row&#45;&#45;flex is-justify-space-around">
                <el-button
                        type="primary"
                        size="small"
                        @click="generateContract(row)"
                >
                  生成电子合同
                </el-button>
                <el-button
                        type="success"
                        size="small"
                        @click="showUpload(row)"
                >
                  上传已签署的本地合同
                </el-button>
              </div>
            </div>
            <el-button type="primary"
                       size="mini"
                       slot="reference">生成合同
            </el-button>
          </el-popover>-->
            <el-button v-show="row.confirmTerms === 1" v-permission="['contract:list:generateContract']" type="primary" size="mini" @click="generateContract(row)">
              生成电子合同
            </el-button>
                     <el-button
                             v-permission="['contract:list:generateContract']"
                             plain
                             type="info"
                             size="mini"
                             @click="showUpload(row)"
                     >
                  上传本地合同
                </el-button>
          </span>
          <!--          合同2待签署、3已签署-->
          <span>
            <el-button v-show="row.status == 2||row.status == 8" v-permission="['contract:list:cancel']" type="warning" plain size="mini" @click="handleCancel(row)">
              撤回
            </el-button>
          </span>
          <!--          合同已签署-->
          <span>
            <el-button v-show="row.status == 3" v-permission="['contract:list:effectvie']" type="primary" size="mini" @click="okContract(row)">
              生效
            </el-button>
          </span>
          <!--          合同状态为 作废&重签-->
          <!--<span v-show="row.status == 8">-->
          <!--<el-button v-permission="['contract:list:toSign']" type="primary" size="mini" @click="handleSign(row)">-->
          <!--发送签署-->
          <!--</el-button>-->
          <!--</span>-->
          <!--          变更合同无错签 , contractClass 1:普通合同 2：解约合同-->
          <span v-show="(row.versionType!=10) && (row.status == 5 && row.contractClass == 1) &&(row.signVersion!==3)">
            <el-button v-permission="['contract:list:rescission']" type="danger" plain size="mini" @click="handleRescission(row)">
              错签
            </el-button>
          </span>
          <!--5;生效，8-预览,3已签署-->
          <span v-show="row.status == 3 || row.status == 5">
            <el-button v-permission="['contract:list:contractPdf']" type="primary" plain size="mini" @click="handleViewContractPdf(row)">
              查看
            </el-button>
          </span>
          <span v-show="row.status == 8">
            <el-button v-permission="['contract:list:contractPdf']" type="primary" size="mini" @click="handleViewContractPdf(row)">
              发送签署
            </el-button>
          </span>
          <span v-show="(row.status == 3 || row.status == 5)&&(row.signVersion!==3)">
            <el-button v-permission="['contract:list:downContractPdf']" type="primary" plain size="mini" @click="handleDownloadContractPdf(row)">
              下载
            </el-button>
          </span>
          <span v-show="row.status == 0 || row.status == 1 || row.status == 4">
            <el-button v-permission="['contract:list:updateContractType']" type="info" plain size="mini" @click="handleChangeType(row)">
              类型变更
            </el-button>
          </span>
           <!-- 开通账号 直接开通账号 -->
           <span v-if="row.signVersion === 3">
            <span>
              <el-button v-show="row.agencyFlag == 1&&(row.joinStatus==2||row.joinStatus==1)" v-permission="['customer:schoolProject:openAccount']" size="mini" @click="openAccount(row)">
                开通账号
              </el-button>
            </span>
            <span>
              <el-button v-show="row.agencyFlag == 1&&(row.joinStatus==2||row.joinStatus==1)" v-permission="['customer:schoolProject:openAccountForAdmin']"  size="mini" @click="openAdminAccount(row)">
                直接开通账号
              </el-button>
            </span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <contract-pdf ref="contractPdf" />
    <!--查看合同弹框-->
    <el-dialog top="2vh" width="70%" title="查看合同" :visible.sync="checkContract" :close-on-click-modal="!checkContract">
      <iframe ref="iframe" :src="srcContract" frameborder="0" width="99%" height="800px" />
      <div class="contract-title">
        <el-button v-if="signStatus===8" v-permission="['contract:list:toSign']" type="primary" @click="handleSign()">
          发送签署
        </el-button>
      </div>
    </el-dialog>
    <!--查看合同弹框-->
    <el-dialog
            title="本地已签约合同"
            :visible.sync="dialogVisible"
            width="70%"
            :before-close="closeUpload">
     <div>
       合同文件：
       <ObsUploader business-path="santao_stip/crm/contract/" :single="false" :limit="20"
        :accept="'.jpg,.jpeg,.png,.gif,.pdf'"
        :on-success="setContract"
        :on-remove="removeContract"
                    :file-list.sync="fileList"
                    :disabled="viewContractFile"
       >
       </ObsUploader>
     </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUpload">取 消</el-button>
        <el-button type="primary" v-if="!viewContractFile" @click="submitLocalContract">确定并创建校区信息</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import { getContractList } from '@/api/system-setting'
import { getAllProject } from '@/api/common'
import {
  sendRescission,
  toDealContract,
  doContractCancel,
  completionContract,
  downloadContractCancel,
  ViewContract,
  generateContract,
  updateContractType,
  viewLocalContract
} from '@/api/contract'
import {
  openAccount,
  openAdminAccount
} from '@/api/school-project'
import { getContractStatus, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import ContractPdf from './components/contractPdf'
import { getFileUrlArrayByObj, parseTime } from '@/utils'
import ObsUploader from '@/components/upload/ObsUploader.vue'
import { uploadLocalContract } from '@/api/qualifications'

export default {
  name: 'Contract',
  components: { ObsUploader, Pagination, AreaPicker, ContractPdf },
  directives: {},
  data() {
    return {
      listLoading: false,
      dialogVisible:false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        orderCode: '',
        contractCode: ''
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      total: 0,
      contractStatus: getContractStatus,
      contractTime: '',
      projectList: [],
      checkContract: false,
      srcContract: '',
      ids: null,
      signStatus: null,
      viewContractFile:false,//
      contractList:[],//合同列表
      fileList:[],// 展示的图片集合
      current:{}, //当前选项
    }
  },
  created() {
    this.listLoading = false
    this.listQuery.contractCode = this.$route.query.orderCode || ''
    this.listQuery.orderCode = this.$route.query.contractCode || ''
    this.getList()
    this.getProject()
  },
  methods: {
    // 确认资质
    confirmQualification (row) {
      this.$router.push({
        path: `/handover/qualifications/${ row.orderId }`,
        query: {
          type: 'uploadQualification',
          clueCode: `${ row.clueCode }`
        }
      })
    },
    showUpload(row){
      this.viewContractFile=false;
      this.current=row;
      this.dialogVisible=true;
      this.fileList=[];
    },
    viewContractFiles(row){
       viewLocalContract(row.id).then(res=>{
         this.viewContractFile=true;
         this.dialogVisible=true;
         this.fileList=getFileUrlArrayByObj(res.data)
      })
    },
    closeUpload(){
      this.current={};
      this.fileList=[];
      this.dialogVisible=false;
    },
    setContract(data, file, list){
      this.contractList.push({imagePath:data.url,name:file.name});
    },
    openAccount(row) { // 开通账号
      this.$confirm('是否需要开通机构账号', '提示', {
        confirmButtonText: '需要',
        cancelButtonText: '不需要',
        type: 'warning'
      }).then(() => {
        openAccount(row.institutionId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '开通成功!'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },
    openAdminAccount(row) { // 直接开通账号
      this.$confirm('是否需要直接开通机构账号', '提示', {
        confirmButtonText: '需要',
        cancelButtonText: '不需要',
        type: 'warning'
      }).then(() => {
        openAdminAccount(row.institutionId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '开通成功!'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },
    removeContract(data,file, list){
      this.contractList.forEach((item,index)=>{
        if(item.imagePath===data.url){
          this.contractList.splice(index,1);
        }
      })
      this.fileList=getFileUrlArrayByObj(this.contractList,'name','imagePath')
    },
    submitLocalContract(){
      if(this.contractList.length===0){
        this.$message({
          type: 'warning',
          message: '请上传至少一张合同！'
        })
        return;
      }
      // 确认框
      this.$confirm('确认上传合同并创建校区信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const param={id:this.current.id,orderId:this.current.orderId,list:this.contractList}
        uploadLocalContract(param).then(res=>{
          this.$message({
            type: 'success',
            message: '上传成功，并已经创建好校区相关账号信息！'
          })
          this.getList();
          this.closeUpload();
        }).catch(e=> {

        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })



    },
    /**
       * 项目列表
       * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    handleChangeType(row){
      let that=this;
      // 确认变更？
      this.$confirm('确认变更合同类型？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateContractType(row.id).then(res => {
          that.$message({
            type: 'success',
            message: '变更合同类型完成，请到交接单重新上传资质！!'
          })
          that.getList()
        })
      }).catch(() => {
      })
    },
    /**
       * 查询列表
       * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      if (this.contractTime) {
        this.listQuery.startTime = this.contractTime[0]
        this.listQuery.endTime = this.contractTime[1]
      } else {
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
      this.getList()
    },
    /**
       * 重置
       * */
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.contractTime = []
      this.getList()
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },

    getList() {
      const that = this
      const params = Object.assign(that.listQuery, that.areaList)
      getContractList(params).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    handleView(row, isEdit, flags) {
      if (row.contractClass === 1) {
        if (row.projectId === 1 && row.versionType === 3) { // 抢分合同
          const titlePoints = '抢分'
          this.$router.push({
            path: '/contract/points',
            query: {
              id: row.id,
              isEdit: isEdit,
              title: titlePoints,
              flags: flags
            }
          })
        } else if ((row.projectId === 1 || row.projectId === 3) && row.versionType === 9) { // 特色班型合同
          this.$router.push({
            path: '/contract/features',
            query: {
              id: row.id,
              isEdit: isEdit,
              flags: flags,
              projectId: row.projectId
            }
          })
        } else if (row.projectId === 3 && row.versionType === 8) { // 招生服务合同合同
          this.$router.push({
            path: '/contract/service',
            query: {
              id: row.id,
              isEdit: isEdit,
              flags: flags
            }
          })
        } else if ((row.projectId === 1 || row.projectId === 2 || row.projectId === 3) && (row.versionType === 1 || row.versionType === 2)) {
          const title = row.projectId === 1 ? '三陶普高' : (row.projectId === 2 ? '芝麻艺考' :'烨晨中学')
          this.$router.push({ path: '/contract/common', query: { id: row.id, isEdit: isEdit, title: title, flags: flags }})
        } else if (row.projectId === 3 && (row.versionType === 5 || row.versionType === 6)) { // 5 烨晨市代/6 烨晨渠道
          const title = row.projectId === 3 && row.versionType === 5 ? '烨晨市代' : '烨晨渠道'
          this.$router.push({ path: '/contract/ycNew', query: { id: row.id, isEdit: isEdit, title: title, flags: flags, versionType: row.versionType }})
        } else if (row.projectId === 4 && row.versionType !== 3) {
          this.$router.push({ path: '/contract/txt', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.projectId === 3 && row.versionType === 4) {
          this.$router.push({ path: '/contract/yc', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.projectId === 5 && row.versionType !== 3) {
          this.$router.push({ path: '/contract/ai', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.projectId === 6 && row.versionType !== 3) {
          this.$router.push({ path: '/contract/jt', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.versionType === 7) { // 高考绝招合同
          this.$router.push({ path: '/contract/entrance', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.projectId === 8) { // 抖音云连锁
          this.$router.push({ path: '/contract/douyin', query: { id: row.id, isEdit: isEdit, flags: flags }})
        } else if (row.versionType === 10) { // 变更合同
          this.$router.push({
            path: '/contract/modification',
            query: {
              id: row.id,
              isEdit: isEdit,
              flags: flags
            }
          })
        }
      } else if (row.contractClass === 4) { // 调转到延期合同
        this.$router.push({ path: '/contract/delay', query: { id: row.id, isEdit: isEdit, type: row.contractClass }}) // 解约合同
      } else {
        // type: 2： 普通解约合同 3：校区项目解约合同
        this.$router.push({ path: '/contract/rescission', query: { id: row.id, isEdit: isEdit, type: row.contractClass }}) // 解约合同
      }
    },
    handleSign() {
      this.$confirm('确定发送合同签署？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.ids) {
          const params = this.ids
          toDealContract(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '发送成功',
                type: 'success'
              })
              this.checkContract = false
              this.getList()
            }
          }).catch(res => {

          })
        }
      }).catch(action => {
      })
    },
    // 生效和合同
    okContract(row) {
      const params = row.id
      this.$confirm('是否确认生效？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        completionContract(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '合同已生效',
              type: 'success'
            })
            this.getList()
          }
        }).catch(res => {

        })
      }).catch(action => {
      })
    },
    // 作废
    handleCancel(row) {
      const params = row.id
      this.$confirm('确定撤销此合同？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        doContractCancel(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '合同已撤销',
              type: 'success'
            })
            this.getList()
          }
        }).catch(res => {

        })
      }).catch(action => {
      })
    },
    handleDownloadContractPdf(row) { // 下载
      downloadContractCancel(row.id).then(res => {

        if (res.code === '000000') { //  https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/sign_contract/prod/2790.pdf
          window.location.href = res.data.downloadUrl
        }
      }).catch(() => {

      })
    },
    // 解约（错签）
    handleRescission(row) {
      this.$confirm('确定要解约合同?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sendRescission(row.id).then(res => {
          this.$message({
            type: 'success',
            message: '合同解约成功!'
          })
          this.getList()
        }).catch(res => {

        })
      }).catch(() => {
      })
    },
    toDetailHandover(row) {
      this.$router.push({
        name: 'HandoverDetail',
        query: {
          name: row.customer,
          orderId: row.orderId
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    /**
       * 查看电子合同
       */
    handleViewContractPdf(row) {
      if(row.signVersion===3){
        this.viewContractFiles(row)
        return;
      }
      // this.$refs.contractPdf.getDetail(row)
      this.ids = row.id
      this.signStatus = row.status
      ViewContract(row.id).then(res => {
        if (res.code === '000000') {

          this.checkContract = true
          this.srcContract = res.data.viewUrl
        }
      }).catch(() => {

      })
    },
    generateContract(row) { // 生成电子合同
      this.$confirm('确定要生成电子合同?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        generateContract(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getContractStatus(row) {
      return converseEnToCn(getContractStatus, row.status)
    },
    getStartTime(row) {
      return parseTime(row.startTime, '{y}-{m}-{d}')
    },
    getEndTime(row) {
      return parseTime(row.endTime, '{y}-{m}-{d}')
    }
  }
}
</script>

<style scoped>
  .contract-title{
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top:20px;
    border-top: 1px #eaeaea solid;
  }
</style>
