<template>
  <el-dialog v-el-drag-dialog title="上传产品" :visible.sync="productDialog" :close-on-click-modal="!productDialog" width="40%">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="130px"
      :rules="baseInfoRules"
      :disabled="isEdit"
    >
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="产品名称：" prop="productName"><el-input v-model="detail.productName" placeholder="产品名称" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12">
          <el-form-item label="项目类型：" prop="projectId">
            <el-select v-model="detail.projectId" placeholder="项目类型" filterable clearable style="width: 100%" @change="getProjects">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="产品类型：" prop="productType">
            <el-select v-model="detail.productType" placeholder="产品类型" filterable clearable style="width: 100%" @change="getPros">
              <el-option
                v-for="item in productType"
                :key="item.itemValue"
                :label="item.itemName"
                :value="item.itemValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12">
          <el-form-item label="是否共有：" prop="common">
            <el-select v-model="detail.common" placeholder="是否共有" filterable clearable style="width: 100%">
              <el-option
                v-for="item in isCommon"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="单位：" prop="productUnit">
            <el-select v-model="detail.productUnit" placeholder="单位" filterable clearable style="width: 100%">
              <el-option
                v-for="item in productUnit"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12">
          <el-form-item label="是否需要物流：" prop="needDelivery">
            <el-select v-model="detail.needDelivery" placeholder="是否需要物流" filterable clearable style="width: 100%">
              <el-option
                v-for="item in needDelivery"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="是否可用：" prop="valid">
            <el-select v-model="detail.valid" placeholder="是否可用" filterable clearable style="width: 100%">
              <el-option
                v-for="item in menuValidList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-if="(detail.projectId==1||detail.projectId==2||detail.projectId==3)&&(detail.productType==4)" :xs="24" :sm="24">
          <el-form-item label="播课产品：" prop="podcastProductId">
            <el-select ref="selectCh" v-model="detail.podcastProductId" placeholder="播课产品" filterable clearable style="width: 100%" @change="getBoke">
              <el-option
                v-for="item in bokes"
                :key="item.podcastProductId"
                :label="item.podcastProductName"
                :value="item.podcastProductId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="detail.podcastProductName" style="display:none;" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="!isEdit" type="primary" @click="confirmExpressDetail">确 定</el-button>
      <el-button @click="closeExpressDetail">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addProduct, getProductDetail } from '@/api/system-setting'
import { getAllProject, getbo, getProductType } from '@/api/common'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { menuValidList, isCommon, needDelivery, productUnit } from '@/utils/field-conver'

export default {
  name: 'ProductDialog',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      productDialog: false,
      detail: {},
      baseInfoRules: {
        productName: { required: true, message: '产品名称必填', trigger: 'blur' },
        common: { required: true, message: '是否共有必选', trigger: 'blur' },
        needDelivery: { required: true, message: '是否需要物流必选', trigger: 'blur' },
        productType: { required: true, message: '产品类别必选', trigger: 'blur' },
        productUnit: { required: true, message: '单位必选', trigger: 'blur' },
        valid: { required: true, message: '是否可用必选', trigger: 'blur' },
        projectId: { required: true, message: '所属项目必选', trigger: 'blur' },
        productTypeName: { required: true, message: '产品类型名称必填', trigger: 'blur' }
      },
      isCommon: isCommon,
      needDelivery: needDelivery,
      productType: [],
      menuValidList: menuValidList,
      productUnit: productUnit,
      projectList: [],
      bokes: [],
      projectFlag: 0,
      proFlag: 0,
      projectIds: 0
    }
  },
  created() {
    this.getAllProject()
    this.getProductType()
  },
  methods: {
    getAllProject() {
      const that = this
      getAllProject().then(res => {
        if (res.code === '000000') {
          that.projectList = res.data
        }
      })
    },
    getProductType() {
      const that = this
      getProductType('PRODUCT_TYPE').then(res => {
        if (res.code === '000000') {
          that.productType = res.data
        }
      })
    },
    getDetail(id) {
      const that = this
      that.detail = {}
      getProductDetail(id).then(res => {
        that.detail = res.data
        that.productDialog = true
        getbo(res.data.projectId).then(res => {
          if (res.code === '000000') {
            this.bokes = res.data
          }
        })
        // that.getProjects(res.data.projectId)
      })
    },
    editDetail(data) {
      const that = this
      that.detail = data
      that.productDialog = true
    },
    /**
     * 确认修改信息
     */
    confirmExpressDetail() {
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!that.detail.id) {
            addProduct(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '新增成功！',
                  type: 'success'
                })
              }
              that.productDialog = false
              that.$emit('refresh')
              that.detail.projectId = 0
              that.detail.productType = 0
            }).catch(res => {
              that.productDialog = false

            })
          }
        } else {

          return false
        }
      })
    },
    closeExpressDetail() {
      this.detail.projectId = 0
      this.detail.productType = 0
      this.productDialog = false
      if (this.$refs.detailForm) {
        this.$refs.detailForm.clearValidate()
      }
      this.$emit('refresh')
    },
    getProjects(val) {
      const that = this
      that.detail.projectId = val
      if (val) {
        getbo(val).then(res => {
          if (res.code === '000000') {
            that.bokes = res.data
          }
        })
      }
    },
    getPros(val) {
      this.detail.productType = val
    },
    getBoke(val) {
      this.$nextTick(() => {
        this.detail.podcastProductName = this.$refs.selectCh.selectedLabel
      })
    },
    handleAdd() {

      this.$refs.detailForm.clearValidate()
    }
  }
}
</script>

<style scoped>
</style>
