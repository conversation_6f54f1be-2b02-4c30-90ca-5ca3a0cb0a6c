<template>
  <el-select v-model="tmpId" multiple  clearable placeholder="请选择试卷类型" :disabled="disabled" filterable>
    <el-option
            v-for="item in optionList"
            :key="item.id"
            filterable
            :label="item.name"
            :value="Number(item.id)">
    </el-option>
  </el-select>
</template>
<script>
import { getPaperTypeFromXK } from '@/api/exercises'

export default {
  name: 'PaperTypeFromXKSelect',
  data: function () {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 200, //一个年级 50个班应该够了
        status: 0,
        deleteFlag: 0
      },
      optionList: []
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number,Array],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    productCode:{
      type: Number,
      required: true,
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? this.targetId  : []
      },
      set(val) {
        this.handleChange(val)
      }
    },
    stageId:{
      get() {
        // 100=高中  200=初中  //  4高中.  3初中
        return this.productCode === 100 ? 4 : 3
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      // const selectedOption = this.optionList.find(option => option.id == value)
      // const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value)
    },
    getList() {
      this.loading = true
      this.queryParams.parentId = this.cityId
      getPaperTypeFromXK().then(res => {
        if (res.code === '000000') {
          const data = res.data
          // // 从字符串转换为json
          const dataList = JSON.parse(data)
          dataList.forEach(item => {
            if (item.stage_id === this.stageId) {
              this.optionList.push(item)
            }
          })
          this.loading = false
        }
      })
    },
    changeParent(val) {
      this.optionList = []
      this.total = 0
      this.tmpId = ''
      this.getList()
    }
  }
}
</script>
<style scoped>
</style>
