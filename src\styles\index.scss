@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-weight: 400;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"]{
  -moz-appearance: textfield;
}
.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}
em,i{
  font-style: normal;
}
ul,li{
  list-style: none;
}
a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

a.link {
  text-decoration: underline;
  color: #46a6ff;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.mt10 {
  margin-top: 12px;
}
.ml-10 {
  margin-left: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.width-100 {
  width: 100%;
}

.block {
  display: block;
}

.flex {
  display: flex;
}
.flex-warp{
  display: flex;
  flex-wrap: wrap;
}
.flex-j-center {
  justify-content: center;
}
.flex-between{
  display: flex;
  justify-content: space-between;
}

.flex-align-item-center,.flex-item-center {
  align-items: center;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 10px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin:20px 0;
  padding: 0 !important;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: #409EFF;
    text-decoration: underline;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-top: 9px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
.app-container.bgGrey {
  position: relative;
}
.bgGreys {
  background-color: #EAEAEA;
  position: relative;
  padding: 10px;
}


@media screen and (max-width: 768px)  {
  .el-dialog {
    width: 100%!important;
  }
  /* 手机分辨率下去掉table固定 */
  .el-table th.is-hidden>*, .el-table td.is-hidden>*{
    visibility: visible!important;
  }
  .el-table__fixed, .el-table__fixed-right{
    display: none;
  }
  .el-table__body-wrapper::-webkit-scrollbar {width:5px;height: 5px;}
  // 滚动条的滑块
  .el-table__body-wrapper::-webkit-scrollbar-thumb {background-color: #ddd;border-radius:8px;}
}
//.el-dropdown {
//  vertical-align: top;
//}
//.el-dropdown + .el-dropdown {
//  margin-left: 10px;
//}
//.el-icon-arrow-down {
//  font-size: 12px;
//}
//.el-dropdown .el-button {
//  margin-left: 10px;
//}
.dialog-footer {
  text-align: center;
}
.el-table th{
  background: #f2f2f2;
}

.el-table__body-wrapper::-webkit-scrollbar {width:12px;height: 12px;}
// 滚动条的滑块
.el-table__body-wrapper::-webkit-scrollbar-thumb {background-color: #ddd;border-radius:8px;}
.delay-form{
  max-height:650px;
  overflow-y: auto
}
.delay-form::-webkit-scrollbar {width:12px;height: 12px;}
.delay-form::-webkit-scrollbar-thumb {background-color: #ddd;border-radius:8px;}
.el-button+.el-dropdown {
  margin-left: 10px;
}
.app-container.bgGrey {
  background-color: #f3f3f3;
}

//cms
.assign-operas{
  display: flex;
  justify-content: center;
  align-content: center;
}
.el-select{
  width: 100%;
}
.cover-img{
  width: 80px;
  height: 80px;
}
//上传图片
.uploadFile{
  width: 80px;
  height: 80px;
  display: inline-block;
  line-height: 80px;
  text-align: center;
  border: 1px solid #ECECEC;
  text-decoration: none;
  color: #666;
  vertical-align: middle;
  margin-left:-4px;;
  cursor: pointer;
  position: relative;
  span{
    color: #1890ff;
    font-size: 30px;
    cursor: pointer;
  }
  .uFile{
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 80px;
    height: 80px;
    cursor: pointer;
  }

}
.charge-flow{
  display: flex;
}
.package-list{
  padding-bottom:20px;
}
.package-title{
  font-weight: bold;
  color: red;
  font-size: 14px;
  padding-bottom: 15px;
  padding-right: 15px;
}
.subjects{
  display: block;
  margin-bottom: 10px;
}

//暑假招生--试卷试题
h2,h3,h4,ul,li{
  margin: 0;
  padding: 0;
}
.questions{
  padding: 15px;
}
.questions-head{
  padding-bottom: 15px;
  border-bottom: 1px #e6edf3 solid;
  margin-bottom: 15px;
}
.questions-title{
  font-size: 18px;
  color: #626d77;
}
.questions-list{
  margin-bottom:25px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  .questions-list-title{
    background:#fff;
    padding: 10px;
    display:flex;
    justify-content: space-between;
    .questions-title-left{
      position: relative;
      width: 30%;
      font-size: 16px;
      color: #1387ec;
      margin-right:10px;
      &:after{
        position: absolute;
        content: '';
        left: 0;
        bottom: 0;
        height: 1px;
        width: 50px;
        background: #1387ec;
      }
    }
  }
  .questions-knowledge{
    background: #f0f4f8;
    padding: 10px;
    h2{
      font-weight: normal;
      color: #4a5258;
      padding-bottom: 10px;
      font-size: 16px;
    }
    p{
      font-size: 14px;
      line-height: 24px;
      color: #586067;
    }
    .questions-knowledge-list{
      display: flex;
      justify-content: flex-end;
      span{
        margin-right: 8px;
      }
    }
  }
}
//试题管理
.course-detail{
  h2{
    font-weight: normal;
    color: #4a5258;
    padding-bottom: 10px;
    font-size: 16px;
  }
  .basis-info{
    padding: 20px;
  }
  .questions-info{
    background: #f7fafd;
    padding: 20px;
  }
}
.preview-info-title{
  display: flex;
  justify-content: space-between;
  padding: 15px;
  font-weight: normal;
  font-size: 16px;
  em{
    color: #4c565f;
  }
  i{
    color: #67727c;
  }
}
.preview-infos{
  background: #f7fafd;
  border:1px #d4e1ee solid;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  padding:15px;
  h3{
    font-weight: normal;
    color: #414952;
    padding-bottom: 10px;
  }
  p{
    font-size: 14px;
    color: #58626c;
  }
}
.answer-list{
  padding: 15px 0;
  .answer-in{
    margin-bottom: 15px;
    display: flex;
    em{
      padding-right: 10px;
      padding-top: 5px;
    }
    p{
      padding: 5px;
      // height: 85px;
      overflow-y:auto;
      overflow-x: hidden;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px;
      width: 92%;
      position: relative;
      img{
        vertical-align: middle;
      }
      .checked{
        position: absolute;
        bottom: 2px;
        right: 0px;
        color: #18c845;
        font-size:26px;
      }
      &.actived{
        border:1px #18c845 solid;
      }
    }
  }
}
.answer-in p::-webkit-scrollbar {
  width: 4px;
}
.answer-in p::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
.answer-in p::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: rgba(0,0,0,0.1);

}
.line{
  border:1px #d8dee4 solid;
}
.parse{
  padding-bottom: 30px;
}
.no-data{
  text-align: center;
  font-size: 16px;
  color: #59636c;
  padding: 20px 0;
}
.small-img{
  width:60px;
  margin-right: 10px;
}
.feedack-info{
  padding: 20px;
  h2{
    font-weight: normal;
    padding-bottom: 15px;
    color: #354049;
    em{
      padding-right: 15px;
    }
  }
}
.feedback-sub{
  padding-bottom: 15px;
  border-bottom: 1px #bac1c8 solid;
  h2{
    font-weight: normal;
    color: #4a5762;
    display: flex;
    justify-content: space-between;
  }
  p{
    font-size: 14px;
    color: #505d6b;
    line-height: 24px;
    padding-left:20px;
    padding-bottom: 15px;
  }
  div{
    padding-left:10px;
  }
}
.big-img{
  width: 100%;
  height: auto;
}
.reply-top{
  padding-top: 15px;
}
.reply-opera{
  padding-top: 15px;
}
.help-block{
  font-size: 12px;
  color: red;
}
.upload-imgs{
  display: flex;
}
.upload-imgs li img{
  width: 95px;
  height: 95px;
}
.purchase-top{
  margin-top: 20px;
}
.purchase-btns{
  display: flex;
  justify-content: center;
  padding: 20px 0;
  div{
    width: 350px;
    text-align: center;
    height: 35px;
    line-height: 35px;
    font-size: 16px;
    color: #fff;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    background: #409EFF;
  }
}

//试题的图片
.title-big{
  width: 100%;
  height: auto;
}
.title-img{
  width: 94%;
  height: 35px;
}
.tips{
  color: red;
  font-size: 12px;
}
.questions-lists{
  margin-bottom: 15px;
}
.edit-one-bg{
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,.6);
  z-index: 111;
  .edit-one-title{
    position: absolute;
    top: 6%;
    left: 15%;
    width: 70%;
    padding:30px;
    background: #fff;
    z-index: 222;
    overflow-y: auto;
    overflow-x: hidden;
    .assing-info{
      h3{
        color: #666;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        span{
          font-size: 16px;
        }
        em{
          font-size: 26px;
          cursor: pointer;
        }
      }
    }
  }
}
.title-tips{
  color: #409EFF;
  &:hover{
    color: #409EFF;
  }
}
.feedack-info-type{
  padding: 8px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  border:1px #a2d0ff solid;
  background: #d2e8ff;
  font-size: 12px;
  color: #3799ff;
}
.replay-btns{
  display: flex;
  justify-content: flex-end;
}
.btns-operas{
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
  background: #1890ff;
  color: #fff;
  font-size: 12px;
  display: inline-block;
  margin-right: 8px;
  padding: 0px 20px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
}
.rates-info{
  width: 100%;
  margin: 20px 0;
}
.rate-info{
  display: flex;
  .rate-tips{
    font-size: 16px;
    color: #666;
    padding-right: 10px;
    padding-top: 7px;
    padding-left: 0 !important;
  }
}
.reason{
  padding-top: 25px;
  font-size: 16px;
  color: #666;
  i{
    padding-left: 10px;
  }
}
.rate{
  width: 100%;
  margin: 20px 0;
  text-align: center;
  font-size: 16px;
  color: #666;
}
.dom-nodes{
  height: 70%;
  overflow-y: auto;
  overflow-x: hidden;
}
.dom-nodes::-webkit-scrollbar {
  width: 4px;
}
.dom-nodes::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
.dom-nodes::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: rgba(0,0,0,0.1);

}
.dom-list{
  height: 85%;
  overflow-y: auto;
  overflow-x: hidden;
}
.dom-list::-webkit-scrollbar {
  width: 4px;
}
.dom-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
.dom-list::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: rgba(0,0,0,0.1);

}
@media screen and (min-width:1368px) and (max-width:1601px) {
  .edit-one-title{
    max-height:700px;
  }
}
@media screen and (max-width: 1441px) {
  .dom-nodes{
    height: 65%;
  }
  .dom-list{
    height: 82%;
  }
}
@media screen and (max-width: 1367px) {
  .dom-nodes{
    height: 50%;
  }
  .dom-list{
    height: 82%;
  }
}
@media screen and (max-width: 1366px) {
  .edit-one-title{
    max-height:600px;
    top: 3%;
  }
}
.mobile-card{
  margin-top:5px;

   .el-card__header{
    padding: 10px 5px 5px 10px !important;
  }
   .el-card__body{
    padding:5px 10px 10px !important;
  }
}

.wwLogin_quick_footer{
  display: none !important;
}
