<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="searchField" placeholder="学生姓名/手机号" class="filter-item" style="width: 160px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="grade" placeholder="类型" filterable clearable class="filter-item" style="width: 120px;" @change="getselect">
        <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="isAllotted" placeholder="分配状态" filterable clearable class="filter-item" style="width: 120px;">
        <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="预约开始日期"
        end-placeholder="预约结束日期"
        style="width:270px"
      />
      <el-button class="filter-item" size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button class="filter-item" size="mini" type="primary" @click="handleReset">重置</el-button>
      <el-button v-permission="['website:appointment:export']" class="filter-item" size="mini" type="primary" @click="handleExport">导出</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" />
      <af-table-column label="学生姓名" show-overflow-tooltip prop="name" />
      <af-table-column label="手机号" show-overflow-tooltip prop="phone" />
      <af-table-column label="年级" show-overflow-tooltip width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.grade==1">高一</span>
          <span v-if="scope.row.grade==2">高二</span>
          <span v-if="scope.row.grade==3">高三</span>
        </template>
      </af-table-column>
      <el-table-column label="区域">
        <template slot-scope="scope">
          <span>
            <em v-if="scope.row.provinceName">{{ scope.row.provinceName }}</em>
            <em v-if="scope.row.cityName">{{ scope.row.cityName }}</em>
            <em v-if="scope.row.areaName">{{ scope.row.areaName }}</em>
          </span>
        </template>
      </el-table-column>
      <af-table-column label="分配状态" prop="isAllotted" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.isAllotted===1">已分配</span>
          <span v-if="scope.row.isAllotted===0">未分配</span>
        </template>
      </af-table-column>
      <af-table-column label="分配合伙人" prop="partnerName" show-overflow-tooltip />
      <af-table-column label="预约时间" prop="createTime" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" align="center" width="180" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['website:appointment:distribution']" type="primary" size="mini" @click="distributionShow=true,distributionPlan(row)">分配</el-button>
          <el-button type="primary" size="mini" @click="copyMessShow=true,copyPlan(row)">复制</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageIndex"
      :limit.sync="pageSize"
      @pagination="getList"
    />
    <!--分配人员-->
    <el-dialog :visible.sync="distributionShow" :title="distributionTitle" :close-on-click-modal="!distributionShow" width="40%">
      <el-form>
        <el-form-item>
          <span style="padding-right:10px">
            <i class="red">*</i>
            <em>分配合伙人</em>
          </span>
          <el-select v-model="partnerId" filterable placeholder="请选择分配合伙人" clearable class="filter-item" style="width:70%">
            <el-option v-for="item in partnerLists" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item class="opera-btns">
          <el-button type="primary" size="mini" @click="confirmFollow">确定</el-button>
          <el-button type="default" size="mini" @click="cancelFollow">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--分配人员-->
    <!--复制信息-->
    <el-dialog :visible.sync="copyMessShow" :title="copyMessTitle" :close-on-click-modal="!copyMessShow" width="40%">
      <div class="copy-mess">
        {{ copyMess }}
      </div>
      <div class="opera-btns">
        <el-button type="primary" size="mini" class="copy" :data-clipboard-text="copyMess" @click="confirmCopy(copyMess,$event)">复制</el-button>
        <el-button type="default" size="mini" @click="copyMessShow=false,cancelCopy()">取消</el-button>
      </div>
    </el-dialog>
    <!--复制信息-->
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import { listListen, partnerLists, allottPartner } from '@/api/website'
export default {
  name: 'NewsList',
  components: { AreaPicker, Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      grades: [{ id: '1', name: '高一' }, { id: '2', name: '高二' }, { id: '3', name: '高三' }],
      pageIndex: 1,
      pageSize: 10,
      grade: '',
      searchField: '',
      areaList: {
        province: '',
        city: '',
        area: ''
      },
      distributionShow: false,
      distributionTitle: '分配合伙人',
      person: null,
      hotShowFlagList: [],
      followDate: [],
      isAllotted: null,
      typeList: [
        {
          id: 1,
          title: '已分配'
        }, {
          id: 0,
          title: '未分配'
        }
      ],
      partnerId: null,
      auditionId: null,
      partnerLists: [],
      copyMessShow: false,
      copyMessTitle: '复制信息',
      copy: {},
      copyMess: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getPartner()
    })
  },
  methods: {
    async getList() {
      const params = Object.assign({}, {
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        area: this.areaList.area,
        city: this.areaList.city,
        province: this.areaList.province,
        grade: this.grade,
        isAllotted: this.isAllotted,
        searchField: this.searchField,
        beginTime: this.followDate.length > 0 && this.followDate[0] ? this.followDate[0] : '',
        endTime: this.followDate.length > 0 && this.followDate[1] ? this.followDate[1] : ''
      })
      listListen(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {
        this.$message({ message: '查询失败', type: 'error' })
      })
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.province = data.provinceId
      this.areaList.city = data.cityId
      this.areaList.area = data.areaId
    },
    // 搜索预约试听
    handleSearch() {
      this.getList()
    },
    // 重置预约试听
    handleReset() {
      this.areaList = {}
      this.pageIndex = 1
      this.pageSize = 10
      this.grade = ''
      this.followDate = []
      this.searchField = ''
      this.isAllotted = null
      this.getList()
    },
    handleFilter() {
      this.pageIndex = 1
      this.getList()
    },
    getselect() {
      this.getList()
    },
    distributionPlan(row) {
      this.auditionId = row.id
      this.partnerId = row.partnerId
      this.getPartner()
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'website/audition/export?pageIndex=' + that.pageIndex + '&pageSize=' + that.pageSize
      url = url + '&grade=' + `${that.grade ? that.grade : ''}` + '&isAllotted=' + `${that.isAllotted ? that.isAllotted : ''}` + '&searchField=' + `${that.searchField ? that.searchField : ''}` + '&beginTime=' + `${that.followDate.length > 0 && that.followDate[0] ? that.followDate[0] : ''}` + '&endTime=' + `${that.followDate.length > 0 && that.followDate[1] ? that.followDate[1] : ''}` + '&province=' + `${that.areaList.province ? that.areaList.province : ''}` + '&city=' + `${that.areaList.city ? that.areaList.city : ''}` + '&area=' + `${that.areaList.area ? that.areaList.area : ''}`
      that.$confirm('确定导出数据?', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        that.listLoading = true
        const params = Object.assign({
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          area: this.areaList.area,
          city: this.areaList.city,
          province: this.areaList.province,
          grade: this.grade,
          isAllotted: this.isAllotted,
          searchField: this.searchField,
          beginTime: this.followDate.length > 0 && this.followDate[0] ? this.followDate[0] : '',
          endTime: this.followDate.length > 0 && this.followDate[1] ? this.followDate[1] : ''
        })
        listListen(params).then(res => {
          that.list = res.data.records || []
          that.total = res.data.total || 0
          that.listLoading = false
          if (url && that.list.length > 0) {
            a.href = url
            a.target = '_blank'
            document.body.appendChild(a)
            a.click()
          } else {
            setTimeout(() => {
              that.$message({
                type: 'warning',
                message: '没有可以导出的数据'
              })
            }, 500)
          }
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '没有可以导出的数据'
          })
        })
      }).catch(() => {
        that.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getPartner() {
      partnerLists().then(res => {
        const arrs = []
        if (res.code === '000000') {
          res.data.length > 0 ? res.data.forEach(item => {
            const objs = {
              id: item.id,
              title: `${item.customer}(${item.mobile})`
            }
            arrs.push(objs)
          }) : []
          this.partnerLists = arrs
        }
      }).catch(error => {

      })
    },
    confirmFollow() {
      if (!this.partnerId) {
        this.$message({
          message: '请选择分配的合伙人',
          type: 'error'
        })
        return false
      }
      allottPartner(this.auditionId, this.partnerId).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '分配成功',
            type: 'success'
          })
          this.partnerId = null
          this.auditionId = null
          this.distributionShow = false
          this.getList()
        }
      }).catch(error => {

      })
    },
    cancelFollow() {
      this.partnerId = null
      this.auditionId = null
      this.distributionShow = false
    },
    copyPlan(row) {
      this.copy = row
      let grade = ''
      let copyMessInfo = ''
      if (row.grade === 1) {
        grade = '高一'
      } else if (row.grade === 2) {
        grade = '高二'
      } else if (row.grade === 3) {
        grade = '高三'
      }
      copyMessInfo = `${row.name} ${row.phone}  ${grade}  ${row.provinceName ? row.provinceName : ''}  ${row.cityName ? row.cityName : ''}  ${row.areaName ? row.areaName : ''}`
      this.copyMess = copyMessInfo
    },
    confirmCopy(url, e) {

      var clipboard = new this.Clipboard('.copy')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制

        // 释放内存
        clipboard.destroy()
      })
    },
    cancelCopy() {
      var clipboard = new this.Clipboard('.copy')
      clipboard.destroy()
      this.copyMess = ''
    }
  }
}
</script>

<style scoped lang="scss">
.red{
  color: red;
}
.opera-btns{
  display: flex;
  justify-content: center;
}
.copy-mess{
  text-align: center;
  padding-bottom: 15px;
  font-size: 18px;
}
</style>
