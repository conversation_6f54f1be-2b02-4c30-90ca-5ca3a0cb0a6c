<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="学校名称"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:320px'" class="filter-item" @getAreaList="getAreaList" />
      <el-select v-model="listQuery.status" placeholder="学校状态" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:school:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="学校编号" show-overflow-tooltip prop="id" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </el-table-column>
      <af-table-column label="省份" prop="provinceName" show-overflow-tooltip />
      <af-table-column label="城市" prop="cityName" show-overflow-tooltip />
      <af-table-column label="区县" prop="areaName" show-overflow-tooltip />
      <af-table-column label="乡镇" prop="townName" />
      <af-table-column label="学校名称" prop="name" />
      <af-table-column label="学校状态" prop="status" :formatter="getStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:school:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['setting:school:opera']" type="primary" size="mini" @click="handleSet(row)">{{ row.status===1?'停用':'启用' }}</el-button>
          <!--<el-button v-if="row.status===99" type="primary" size="mini" v-permission="['setting:school:edit']" @click="handleSet(row)">启用</el-button>-->
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <add-school ref="schools" @updateSchool="getList" />
  </div>
</template>
<script>
import AreaPicker from '@/components/area-picker'
import Pagination from '@/components/Pagination'
import AddSchool from './components/addSchool'
import { clientCode } from '@/api/classType'
import { enableList, converseEnToCn } from '@/utils/field-conver'
import { getSchoolList, enableSchool } from '@/api/system-setting'
export default {
  name: 'School',
  components: {
    Pagination,
    AreaPicker,
    AddSchool
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      enableList: enableList
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 2) || []
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign({}, {
        pageIndex: that.listQuery.pageIndex,
        pageSize: that.listQuery.pageSize
      }, this.listQuery, this.areaList)
      await getSchoolList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.getList()
    },
    handleCreate() {
      this.$refs.schools.schoolPop = true
      this.$refs.schools.schoolTitle = '新增学校信息'
      this.$refs.schools.getSchoolDetail()
      this.$refs.schools.isEdit = false
      this.$refs.schools.flags = 1
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    handleUpdate(row) {
      this.$refs.schools.schoolPop = true
      this.$refs.schools.schoolTitle = '修改学校信息'
      this.$refs.schools.getSchoolDetail(row.id)
      this.$refs.schools.isEdit = false
      this.$refs.schools.flags = 0
    },
    getStatus(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    handleSet(row) {
      const title = row.status === 1 ? '停用' : '启用'
      const params = {
        id: row.id
      }
      this.$confirm(`确定要${title}操作?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableSchool(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    getDetail(row) { // 获取列表详情
      this.$refs.schools.schoolPop = true
      this.$refs.schools.flags = 0;
      this.$refs.schools.schoolTitle = `${row.name}详情`
      this.$refs.schools.getSchoolDetail(row.id)
      this.$refs.schools.isEdit = true
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
