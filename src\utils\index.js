/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (!time) {
    return null
  }
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  )
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 获取图片的uuid
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function uuid() {
  var s = []
  var hexDigits = '0123456789abcdef'
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'
  var uuid = s.join('')
  return uuid
}

// eslint-disable-next-line no-undef
export const obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})

/**
 * 获取图片路径
 * @param fileObjOrArray
 * @param fileNameProp
 * @param fileUrlProp
 * @returns {*[]|[{name, url: string}]}
 */
export const getFileUrlArrayByObj = function(fileObjOrArray, fileNameProp, fileUrlProp) {
  if (fileObjOrArray) {
    if (fileObjOrArray instanceof Array && fileObjOrArray.length > 0) {
      const result = []
      if (fileNameProp && fileUrlProp) {
        fileObjOrArray.forEach((item) => {
          result.push({ name: item[fileNameProp], url: getFileUrl(item[fileUrlProp]) })
        })
        return result
      } else {
        fileObjOrArray.forEach((item) => {
          result.push({ name: getFileUrl(item), url: getFileUrl(item) })
        })
        return result
      }
    } else if (fileObjOrArray instanceof Object && fileObjOrArray[fileNameProp] && fileObjOrArray[fileUrlProp]) {
      return getFileUrlArrayByValues(fileObjOrArray[fileNameProp], fileObjOrArray[fileUrlProp])
    }
  }
  return []
}

export const getFileUrlArrayByValues = function(fileName, fileUrl) {
  if (fileName && fileUrl) {
    return [{ name: fileName, url: getFileUrl(fileUrl) }]
  }
  return []
}

export const getFileUrl = function(fileUrl) {
  return (fileUrl && (fileUrl.indexOf('http') === 0 ? fileUrl : fileUrl)) || ''
}

export const getVideoFileUrl = function(fileUrl) {
  return (fileUrl && (fileUrl.indexOf('http') === 0 ? fileUrl : fileUrl)) || ''
}
// 通过逗号间隔的字符串，获取文件类型数组
export const getFileTypeArrayByAcceptStr = function(fileTypeStr) {
  const result = []
  if (fileTypeStr) {
    const typeArray = fileTypeStr.split(',')
    for (let i = 0; i < typeArray.length; i++) {
      result.push(getFileTypeBySuffix(typeArray[i]))
    }
  }
  return result
}

// 通过后缀名字符串 ， 如.jepg,.jpg,.avi 获取文件类型
export const getFileTypeBySuffix = function(suffix) {
  if (suffix) {
    const suffixStr = suffix.toLowerCase()
    if (suffixStr === '.jpg' || suffixStr === '.jpeg' || suffixStr === '.png' || suffixStr === '.gif') {
      return 'image'
    } else if (
      suffixStr === '.avi' ||
      suffixStr === '.mp4' ||
      suffixStr === '.rmvb' ||
      suffixStr === '.rm' ||
      suffixStr === '.flv' ||
      suffixStr === '.3gp' ||
      suffixStr === '.mkv' ||
      suffixStr === '.mov'
    ) {
      return 'video'
    } else if (
      suffixStr === '.mp3' ||
      suffixStr === '.wav' ||
      suffixStr === '.wma' ||
      suffixStr === '.ogg' ||
      suffixStr === '.ape' ||
      suffixStr === '.flac'
    ) {
      return 'audio'
    } else if (suffixStr === '.pdf') {
      return 'pdf'
    } else {
      return 'other'
    }
  }
  return 'other'
}

export const getFileTypeFn = function(file) {
  if (!file.raw && file.url) {
    // 通过url获取文件类型
    const type = file.url.split('.').pop()
    if (
      type === 'mp4' ||
      type === 'avi' ||
      type === 'rmvb' ||
      type === 'rm' ||
      type === 'flv' ||
      type === '3gp' ||
      type === 'mkv' ||
      type === 'mov'
    ) {
      return 'video'
    } else if (type === 'mp3' || type === 'wav' || type === 'wma' || type === 'ogg' || type === 'ape' || type === 'flac') {
      return 'audio'
    }
    if (type === 'pdf') {
      return 'pdf'
    } else if (type === 'jpg' || type === 'jpeg' || type === 'png' || type === 'gif') {
      return 'image'
    } else {
      return 'other'
    }
  } else {
    const type = (file.raw && file.raw.type.split('/')[0]) || ''
    if (type === 'image') {
      return 'image'
    } else if (type === 'video') {
      return 'video'
    } else if (type === 'audio') {
      return 'audio'
    } else if (type === 'pdf') {
      return 'pdf'
    } else {
      return 'file'
    }
  }
}

/**
 * 判断当前浏览器是否为企业微信
 * @returns {boolean} 如果是企业微信返回 true，否则返回 false
 */
export const isWeComBrowser = function() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('wxwork')
}
