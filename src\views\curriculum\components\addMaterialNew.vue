<template>
  <el-dialog
    :visible.sync="materialPop"
    :title="materialTitle"
    :close-on-click-modal="!materialPop"
    width="60%"
    destroy-on-close
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="materialForm"
        :model="listQuery"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="适用产品线" prop="clientCode">
          <el-select
            v-model="listQuery.clientCode"
            placeholder="请选择产品线"
            clearable
            class="filter-item"
            :disabled="isEdit"
            @change="getChangeSubjectList"
          >
            <el-option
              v-for="item in clientCode"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="科目" prop="subjectId">
          <el-select
            filterable
            v-model="listQuery.subjectId"
            @change="getChangeSubjectList"
          >
            <el-option
              v-for="item in subjectsList"
              :key="item.index"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="textType">
          <el-radio-group v-model="listQuery.textType">
            <el-radio :label="1">菁优网</el-radio>
            <el-radio :label="0">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="教材版本"
          :prop="listQuery.textType ? 'editionId' : 'title'"
        >
          <el-select
            v-show="!!listQuery.textType"
            filterable
            placeholder="请选择教材版本"
            v-model="listQuery.editionId"
          >
            <el-option
              v-for="item in bookList"
              :key="item.index"
              :label="item.editionName"
              :value="item.editionId"
            ></el-option>
          </el-select>
          <el-input
            v-show="!listQuery.textType"
            v-model="listQuery.title"
            placeholder="请输入教材版本"
            maxlength="20"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="教材状态" prop="status">
          <el-select
            v-model="listQuery.status"
            placeholder="请选择教材状态"
            clearable
            class="filter-item"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in enableList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教材排序" prop="sort">
          <el-input
            v-model.number="listQuery.sort"
            placeholder="请输入排序"
            :disabled="isEdit"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(materialPop = false), cancelClass()"
        >取消</el-button
      >
      <el-button type="primary" size="mini" @click="custormClass"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  clientCode,
  addMaterialListNew,
  getMaterialDetailsNew,
  getv3BookList
} from "@/api/classType";
import { enableList } from "@/utils/field-conver";
export default {
  name: "AddMaterialNew",
  data() {
    return {
      materialTitle: "",
      materialPop: false,
      listQuery: {
        clientCode: "",
        textType: 1,
        title: "",
        subjectId: "",
        editionId: "",
        status: 1
      },
      rules: {
        title: { required: true, trigger: "blur", message: "请输入教材版本" },
        clientCode: {
          required: true,
          trigger: "change",
          message: "请选择适用产品线"
        },
        textType: { required: true, trigger: "blur", message: "请选择类型" },
        status: { required: true, trigger: "blur", message: "请选择教材状态" },
        subjectId: { required: true, trigger: "change", message: "请选择学科" },
        editionId: { required: true, trigger: "change", message: "请选择教材" }
      },
      clientCode: [],
      enableList,
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false,
      bookList: [] //v3教材列表
    };
  },
  props: {
    modelType: {
      type: [String, Number],
      default: "1"
    },
    subjectsList: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode();
    });
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || [];
        this.clientCode = clientCodes.filter(item => item.level === 1);
      });
    },

    // 获取教材列表
    async getChangeSubjectList() {
      if (!this.listQuery.subjectId || !this.listQuery.clientCode) return;
      await this.getBookList();
      this.listQuery.editionId = this.bookList.length
        ? this.bookList[0].editionId
        : "";
      this.$nextTick(() => {
        this.$refs.materialForm.clearValidate(["editionId", "title"]);
      });
    },

    async getBookList() {
      if (!this.listQuery.subjectId || !this.listQuery.clientCode) return;
      const { data } = await getv3BookList({
        subjectId: this.listQuery.subjectId,
        code: this.listQuery.clientCode
      });
      this.bookList = data;
    },


    cancelClass() {
      this.listQuery = {};
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    },
    custormClass() {
      this.enableFlagsCreate = true;
      this.$refs.materialForm.validate(valid => {
        if (valid) {
          const params = Object.assign(
            {},
            this.listQuery,
            { id: this.listQuery.id ? this.listQuery.id : "" },
            { type: this.modelType }
          );
          if (this.listQuery.textType) {
            params.title = this.bookList.find(
              item => item.editionId === params.editionId
            ).editionName;
          }
          addMaterialListNew(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "操作成功"
                });
                this.$emit("addMaterialList");
                this.materialPop = false;
                this.listQuery = {};
                this.enableFlagsCreate = false;
                this.$refs.materialForm.clearValidate();
              }
            })
            .catch(() => {
              this.enableFlagsCreate = false;
            });
        } else {
          this.enableFlagsCreate = false;
          return false;
        }
      });
    },
    getMaterialList(ids) {
      getMaterialDetailsNew(ids).then(res => {
        if (res.code === "000000" && res.data) {
          this.listQuery = res.data;
          this.getBookList()
        }
      });
    },
    changeInit() {
      this.resetListQuery();
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    },

    resetListQuery() {
      this.listQuery = {
        clientCode: "",
        textType: 1,
        title: "",
        subjectId: "",
        editionId: "",
        status: 1
      };
    }
  }
};
</script>

<style scoped>
.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}

::v-deep .el-radio__original {
  display: none !important; /* 隐藏原生 radio 输入，但仍然允许交互 */
}
</style>
