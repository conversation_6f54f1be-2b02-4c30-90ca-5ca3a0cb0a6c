<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form st-relative" autocomplete="on" label-position="left">
      <svg-icon @click="loginType = 'wx'" v-show="loginType === 'password'" icon-class="qrcode" class-name=" st-cursor-pointer st-text-gray-400 !st-w-[30px] !st-h-[30px] st-absolute st-right-[10px] st-top-[10px]" />
      <svg-icon @click="loginType = 'password'" v-show="loginType === 'wx'" icon-class="switchHorizontal" class-name=" st-cursor-pointer st-text-gray-400 !st-w-[30px] !st-h-[30px] st-absolute st-right-[10px] st-top-[10px]" />
      <div class="title-container">
        <h3 class="title">三陶教育CRM系统</h3>
      </div>
      <span v-show="loginType === 'password'">
        <el-form-item prop="userName">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
          <el-input
            ref="userName"
            v-model="loginForm.userName"
            placeholder="请输入手机号/用户名"
            oninput="value=value.replace(/\s/g,'')"
            name="userName"
            type="text"
            tabindex="1"
            autocomplete="on"
          />
        </el-form-item>
        <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
          <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
            <el-input
              :key="passwordType"
              ref="password"
              oninput="value=value.replace(/\s/g,'')"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="请输入密码"
              name="password"
              tabindex="2"
              autocomplete="on"
              @keyup.native="checkCapslock"
              @blur="capsTooltip = false"
              @keyup.enter.native="handleLogin"
            />
            <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
          </el-form-item>
        </el-tooltip>
        <div class="code-line">
          <el-form-item prop="captchaCode" style="width: 70%;margin-bottom: 10px;">
          <span class="svg-container">
            <svg-icon icon-class="codeV" />
          </span>
            <el-input
              v-model="loginForm.captchaCode"
              oninput="value=value.replace(/\s/g,'')"
              placeholder="请输入验证码"
              name="code"
              type="text"
              tabindex="3"
              autocomplete="on"
            />
          </el-form-item>
          <div>
            <!--          <identify :identify-code="identifyCode" />-->
            <ImgVerificationCode  @setCaptchaId="setCaptchaId" ref="ImgVerificationCode"></ImgVerificationCode>
          </div>
        </div>
        <a class="back-password" @click.prevent="getPassword">忘记密码</a>
        <el-button :loading="loading" type="danger" class="st-w-full !st-mb-[20px]" @click.native.prevent="handleLogin">登 录</el-button>
      </span>
      <div  v-show="loginType === 'wx'" id="ww_login" class="st-bg-white st-p-[8px] st-border st-border-[rgba(6,15,26,.1)] st-border-solid st-flex st-justify-center st-m-[0_auto] st-rounded-[8px]"></div>
    </el-form>
    <div id="loginDiv" tabindex="1" style="outline:0;">
      <input v-focus type="text" style="opacity:0;position:absolute;">
    </div>
    <div id="ww_login"></div>
  </div>
</template>

<script>

import identify from '../../components/sidentify/sidentify.vue'
import ImgVerificationCode from '@/views/login/components/ImgVerificationCode.vue'
import qiwx from '@/assets/img/qiwx.png'
import * as ww from "@wecom/jssdk";
import {Loading} from "element-ui";

export default {
  name: 'Login',
  components: { ImgVerificationCode, identify },
  directives: {
    focus: {
      // 指令的定义
      inserted: function(el) {
        el.focus()
      }
    }
  },
  data() {
    return {
      qiwx:qiwx,
      loginType: 'password',
      loginForm: {
        userName: '',
        password: '',
        captchaCode:'',
        captchaId:'',
      },
      loginRules: {
        userName: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        password: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
        captchaCode: [{ required: true, trigger: 'blur', message: '验证码不能为空' }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},
      // 验证码初始值
      identifyCode: '1234',
      // 验证码的随机取值范围
      identifyCodes: '1234567890'
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    },
    loginType(val) {
      if (val === 'wx') {
        document.getElementById('ww_login').innerHTML = ''
        // 初始化登录组件
        const _this = this;
        const wwLogin = ww.createWWLoginPanel({
          el: '#ww_login',
          params: {
            login_type: 'CorpApp',
            appid: process.env.VUE_APP_WX_CORPID,
            agentid: process.env.VUE_APP_WX_AGENTID,
            redirect_uri: process.env.VUE_APP_WX_NOT_SUPPORTED, //跳转到企业微信内建的回调页面
            state: 'loginState',
            redirect_type: 'callback',
          },
          onCheckWeComLogin(event) {
            console.log(event);
            //获取企业微信桌面端登录状态回调
            // isWeComLogin: true 已登录，false 未登录
          },
          onLoginSuccess({ code }) {
            //企业微信登录成功回调
            const loadingInstance  = Loading.service({
              lock: true,
              text: '登陆中...',
              background: 'rgba(0, 0, 0, 0.3)'
            })
            _this.$store.dispatch('user/wxLogin', {code:code}).then(res => {
              loadingInstance.close();
              _this.$router.replace({ path: _this.redirect || '/', query: _this.otherQuery })
            }).catch(err => {
              if(err.code === '1001'){
                _this.loginType = 'password'
              }
              loadingInstance.close();
            })
          },
          onLoginFail(err) {
            console.log("登录失败")
            console.log(err)
          },
        })
      }
    }
  },
  created() {
    const that = this
    document.onkeypress = function(e) {
      var keycode = document.all ? event.keyCode : e.which
      if (keycode === 13) {
        that.handleLogin()// 登录方法名
        return false
      }
    }
  },
  mounted() {
    // 刷新页面就生成随机验证码
    this.identifyCode = ''
    this.makeCode(this.identifyCodes, 4)
  },
  destroyed() {
  },
  methods: {
    wxLogin(){
      // this.loginType = 'wx'
      this.$router.replace({ path:  '/wxLogin'})
      // window.location.href = `https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=${process.env.VUE_APP_WX_CORPID}&agentid=${process.env.VUE_APP_WX_AGENTID}&redirect_uri=${process.env.VUE_APP_WX_REDIRECT_URI}&state=crm`;
    },
    setCaptchaId(captchaId){
      this.loginForm.captchaId=captchaId
    },
    checkCapslock({ shiftKey, key } = {}) {
      if (key && key.length === 1) {
        if (shiftKey && (key >= 'a' && key <= 'z') || !shiftKey && (key >= 'A' && key <= 'Z')) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === 'CapsLock' && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {

      this.$refs.loginForm.validate(valid => {
        if (valid && this.loginForm.captchaId && this.loginForm.captchaCode) {
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm)
            .then(() => {
              this.loading = false
              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              // this.$router.push({ path: '/', query: this.otherQuery })
            })
            .catch(() => {
              this.loading = false
              this.changeCode()
              this.loginForm.captchaCode = ''
              this.loginForm.password = ''
              this.$refs.ImgVerificationCode.initCode()
            })
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    // 点击验证码刷新验证码
    changeCode() {
      this.identifyCode = ''
      this.makeCode(this.identifyCodes, 4)
    },
    // 生成一个随机整数  randomNum(0, 10) 0 到 10 的随机整数， 包含 0 和 10
    randomNum(min, max) {
      max = max + 1
      return Math.floor(Math.random() * (max - min) + min)
    },
    // 随机生成验证码字符串
    makeCode(data, len) {
      for (let i = 0; i < len; i++) {
        this.identifyCode += data[this.randomNum(0, data.length - 1)]
      }
    },
    getPassword() {
      this.$router.push({ path: '/passwordInfo' })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#333;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 81%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: #333;
      height: 47px;
      caret-color: #484848;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #F1F3F4 inset !important;
        background-color: rgba(0,0,0,0.1);
        -webkit-text-fill-color: #333 !important;
      }
    }
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: #F1F3F4;
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#333;
$white:#ffffff;

.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  background: url('~@/assets/img/loginNewCrm.png') no-repeat center center;
  background-size: 100% 100%;
  .login-form {
    position: relative;
    width: 460px;
    max-width: 100%;
    padding: 30px 40px;
    margin: 200px auto 0 57%;
    background-color:#fff;
    overflow: hidden;
    border-radius: 10px;
  }

  .tips {
    font-size: 14px;
    color: $light_gray;
    margin-bottom: 10px;
    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: #fff;
      margin: 0px auto 30px auto;
      text-align: center;
      color: #333;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
  .code-line{
    display: flex;
    justify-content: space-between;
  }
  .back-password{
    display: block;
    width: 100%;
    text-align: right;
    color: #ff6d6d;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 10px;
  }
  @media screen and (max-width:1400px){
    .login-container .login-form{
      margin:100px auto 0 auto;
      width:418px;
    }
  }

.el-divider__text{
  padding: 0 5px;
  border-radius: 6px;
  background-color:  #3E4042 !important;
  color: white;
}
</style>
