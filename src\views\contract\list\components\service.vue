<template>
  <div class="app-container bgGrey">
    <el-form
      v-if="detail.contractId"
      ref="detailForm"
      :model="detail"
      :rules="comRules"
      label-width="130px"
      :disabled="!isEdit"
    >
      <el-row :gutter="10">
        <el-col :lg="{ span: 12 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} |
                      {{ detail.contractClue.cityName }} |
                      {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{ span: 12 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="校区地址：">
                    <span>{{ detail.clueSchool.provinceName }} |
                      {{ detail.clueSchool.cityName }} |
                      {{ detail.clueSchool.areaName }} |
                      {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" />
      <el-row :gutter="10">
        <el-col :lg="{ span: 24 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="签约期限：" label-width="110px">
                    <span v-if="detail.contractOrder.signStartTime">{{
                      detail.contractOrder.signStartTime
                    }}</span>
                    <span
                      v-if="detail.contractOrder.signEndTime"
                    >-{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 9 }">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :lg="{ span: 12 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{ span: 12 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                  <el-form-item label="企业地址：">
                    <span>{{
                      detail.contractEnterprise.enterpriseAddress
                    }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="22">
        <el-col :lg="{ span: 24 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 11 }">
                  <el-form-item label="合同编号:" label-width="130px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item label="合同名称:" label-width="130px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 11 }">
                  <el-form-item label="加盟项目:" label-width="130px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item label="区域类型:" label-width="130px" required>
                    <el-radio-group v-model="detail.cooperationType" disabled>
                      <el-radio :label="1">区县独家</el-radio>
                      <el-radio :label="0">区县单点</el-radio>
                      <el-radio :label="2">乡镇独家</el-radio>
                      <el-radio :label="3">乡镇单点</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 11 }">
                  <el-form-item
                    label="合同类型:"
                    prop="contractType"
                    label-width="130px"
                  >
                    <el-radio-group
                      v-model="detail.contractType"
                      class="radios"
                      :disabled="detail.orderStatus !== 21"
                    >
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item label="合同版本:">
                    <div>{{ versionTypeStatus }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col v-if="!detail.yeChenAdmissionContract.minimumEnrollment && detail.yeChenAdmissionContract.minimumEnrollment != 0" :xs="{ span: 24 }" :sm="{ span: 11 }">
                  <el-form-item
                    label="招生服务类型:"
                    prop="yeChenAdmissionContract.serviceType"
                    label-width="130px"
                  >
                    <el-radio-group
                      v-model="detail.yeChenAdmissionContract.serviceType"
                      class="radios"
                      @change="changeAreaSingle"
                    >
                      <el-radio :label="0">普通招生服务</el-radio>
                      <el-radio :label="1">保底招生服务</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col v-if="!detail.yeChenAdmissionContract.minimumEnrollment && detail.yeChenAdmissionContract.minimumEnrollment != 0" :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item label="合同期限:" required>
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{ span: 24 }" :sm="{ span: 13 }" :offset="11">
                  <el-form-item label="合同期限:" required>
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 16px">
                <el-col
                  v-if="
                    detail.yeChenAdmissionContract.serviceType === 0 && !detail.yeChenAdmissionContract.minimumEnrollment"
                  :xs="{ span: 24 }"
                  :sm="{ span: 11 }"
                >
                  <el-form-item
                    label="普通招生服务费/元:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.serviceCharge"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.serviceCharge"
                      :placeholder="comRules.yeChenAdmissionContract.serviceCharge.message"
                      type="number"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment"
                  :xs="{ span: 24 }"
                  :sm="{ span: 11 }"
                >
                  <el-form-item
                    label="保底招生服务费/元:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.minServiceCharge"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.minServiceCharge"
                      :placeholder="
                        comRules.yeChenAdmissionContract.minServiceCharge
                          .message
                      "
                      type="number"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="招生服务费/元:" prop="yeChenAdmissionContract.serviceCharge">
                    <el-input v-model="detail.yeChenAdmissionContract.serviceCharge" :placeholder="comRules.yeChenAdmissionContract.serviceCharge.message" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item
                    label="第三方服务机构:"
                    label-width="130px"
                    prop="yeChenAdmissionContract.theThirdOrg"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.theThirdOrg"
                      :placeholder="
                        comRules.yeChenAdmissionContract.theThirdOrg.message
                      "
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 15px">
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 0 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 11 }">
                  <el-form-item
                    label="第三方提成比例:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.commissionRatio"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.commissionRatio"
                      :placeholder="
                        comRules.yeChenAdmissionContract.commissionRatio.message
                      "
                    />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" :xs="{ span: 24 }" :sm="{ span: 11 }">
                  <el-form-item
                    label="保底营业额/元:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.miniTurnover"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.miniTurnover"
                      :placeholder="
                        comRules.yeChenAdmissionContract.miniTurnover
                          .message
                      "
                      type="number"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="保底招生数/人:" label-width="130px" prop="yeChenAdmissionContract.minimumEnrollment">
                    <el-input v-model="detail.yeChenAdmissionContract.minimumEnrollment" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item
                    label="银行户名:"
                    label-width="130px"
                    prop="yeChenAdmissionContract.bankAccName"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.bankAccName"
                      :placeholder="
                        comRules.yeChenAdmissionContract.bankAccName.message
                      "
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 15px">
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 0 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 9 }">
                  <el-form-item label="第三方食宿费用不包含在招生总营收" label-width="250px" prop="yeChenAdmissionContract.totalRevenueRatio">
                    <el-input v-model="detail.yeChenAdmissionContract.totalRevenueRatio" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 0 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 2 }">
                  <span style="float:left;color: #606266;font-size:14px;display:block;padding:7px 0 0 4px;"> 的范畴内 </span>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 11 }">
                  <el-form-item label="返还比例:" label-width="150px" prop="yeChenAdmissionContract.returnRatio">
                    <el-input v-model="detail.yeChenAdmissionContract.returnRatio" :placeholder="comRules.yeChenAdmissionContract.returnRatio.message" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="提成比例:" label-width="130px" prop="yeChenAdmissionContract.commissionRatio">
                    <el-input v-model="detail.yeChenAdmissionContract.commissionRatio" :placeholder="comRules.yeChenAdmissionContract.commissionRatio.message" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item
                    label="银行账号:"
                    label-width="130px"
                    prop="yeChenAdmissionContract.bankAcc"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.bankAcc"
                      :placeholder="
                        comRules.yeChenAdmissionContract.bankAcc.message
                      "
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" style="margin-top: 15px">
                <el-col :sm="{ span: 5 }">
                  <el-form-item label="(即招生营业额每少" prop="yeChenAdmissionContract.lowMiniTurnover">
                    <el-input v-model="detail.yeChenAdmissionContract.lowMiniTurnover" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :sm="{ span: 4 }">
                  <el-form-item label="元返还" label-width="60px" prop="yeChenAdmissionContract.returnAmount">
                    <el-input v-model="detail.yeChenAdmissionContract.returnAmount" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :sm="{ span: 2 }">
                  <span style="float:left;color: #606266;font-size:14px;display:block;padding:7px 0 0 4px;">
                    元)
                  </span>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1" :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item
                    label="开户行:"
                    label-width="130px"
                    prop="yeChenAdmissionContract.openingBank"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.openingBank"
                      :placeholder="comRules.yeChenAdmissionContract.openingBank.message"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 15px">
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 0 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 11 }">
                  <el-form-item label="赠送转化服务/月:" label-width="150px" prop="yeChenAdmissionContract.serviceMonth">
                    <el-input v-model="detail.yeChenAdmissionContract.serviceMonth" :placeholder="comRules.yeChenAdmissionContract.serviceMonth.message" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 11 }">
                  <el-form-item
                    label="第三方提成比例:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.commissionRatio"
                  >
                    <el-input v-model="detail.yeChenAdmissionContract.commissionRatio" :placeholder=" comRules.yeChenAdmissionContract.commissionRatio.message" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 11 }">
                  <el-form-item
                    label="第三方食宿费用不包含在招生总营收"
                    label-width="250px"
                    prop="yeChenAdmissionContract.totalRevenueRatio"
                  >
                    <el-input v-model="detail.yeChenAdmissionContract.totalRevenueRatio" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment" :sm="{ span: 2 }">
                  <span style="float:left;color: #606266;font-size:14px;display:block;padding:7px 0 0 4px;">
                    的范畴内
                  </span>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="退费标准(元/人):" label-width="130px" prop="yeChenAdmissionContract.refundAmount">
                    <el-input v-model="detail.yeChenAdmissionContract.refundAmount" type="number" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.yeChenAdmissionContract.serviceType === 0 || detail.yeChenAdmissionContract.minimumEnrollment || detail.yeChenAdmissionContract.minimumEnrollment === 0" :xs="{ span: 24 }" :sm="{ span: 13 }">
                  <el-form-item
                    label="开户行:"
                    label-width="130px"
                    prop="yeChenAdmissionContract.openingBank"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.openingBank"
                      :placeholder="comRules.yeChenAdmissionContract.openingBank.message"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 15px">
                <el-col
                  v-if="detail.yeChenAdmissionContract.serviceType === 1 && !detail.yeChenAdmissionContract.minimumEnrollment"
                  :xs="{ span: 24 }"
                  :sm="{ span: 11 }"
                >
                  <el-form-item
                    label="赠送转化服务/月:"
                    label-width="150px"
                    prop="yeChenAdmissionContract.serviceMonth"
                  >
                    <el-input
                      v-model="detail.yeChenAdmissionContract.serviceMonth"
                      :placeholder="
                        comRules.yeChenAdmissionContract.serviceMonth.message
                      "
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="!detail.yeChenAdmissionContract.minimumEnrollment && detail.yeChenAdmissionContract.minimumEnrollment != 0" :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="补充条款：" label-width="150px">
                  <el-input
                    v-model="detail.yeChenAdmissionContract.supplementInfo"
                    type="textarea"
                    placeholder="请输入补充条款内容"
                    maxlength="300"
                    show-word-limit
                  />
                </el-form-item>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        @click="confirmEdit(2)"
      >提交</el-button>
    </div>
  </div>
</template>

<script>
import {
  getContractDetail,
  modifyContractDetail,
  getCreatContract
} from '@/api/contract'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  orderStatusList,
  contractClass,
  getContractStatus,
  versionTypes
} from '@/utils/field-conver'

export default {
  name: 'AdmissionsService',
  data() {
    return {
      detail: {
        yeChenAdmissionContract: {}
      },
      comRules: {
        // 合同类型
        contractType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        // 区域类型
        cooperationType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        // 招生服务合同 其他必填信息
        yeChenAdmissionContract: {
          serviceCharge: {
            required: true,
            message: '请输入普通招生服务费',
            trigger: 'blur'
          },
          minServiceCharge: {
            required: true,
            message: '请输入保底招生服务费',
            trigger: 'blur'
          },
          theThirdOrg: {
            required: true,
            message: '请输入第三方服务校区名称',
            trigger: 'blur'
          },
          bankAccName: {
            required: true,
            message: '请输入第三方服务机构银行户名',
            trigger: 'blur'
          },
          totalRevenueRatio: {
            required: false,
            message: '',
            trigger: 'blur'
          },
          miniTurnover: {
            required: true,
            message: '请输入保底营业额/元',
            trigger: 'blur'
          },
          commissionRatio: {
            required: true,
            message: '提成比例',
            trigger: 'blur'
          },
          bankAcc: {
            required: true,
            message: '请输入第三方服务机构银行账号',
            trigger: 'blur'
          },
          openingBank: {
            required: true,
            message: '请输入第三方服务机构开户行',
            trigger: 'blur'
          },
          serviceMonth: {
            required: true,
            message: '请输入赠送转化服务/月',
            trigger: 'blur'
          },
          returnRatio: {
            required: true,
            message: '请输入返还比例',
            trigger: 'blur'
          },
          lowMiniTurnover: {
            required: false,
            message: '请输入招生营业额',
            trigger: 'blur'
          },
          returnAmount: {
            required: false,
            message: '请输入返还金额',
            trigger: 'blur'
          },
          supplementInfo: '' // 补充条款字段
        }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      timeArr: [],
      versionTypeStatus: null
    }
  },
  watch: {
    $route(to, from) {
      this.$router.go(0)
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) {
      // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit
      ? `招生服务合同-编辑合同`
      : `招生服务合同-合同详情`
    this.setTagsViewTitle(tagsName)
  },
  mounted() {},
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr =
          that.detail.startTime !== null && that.detail.endTime !== null
            ? [that.detail.startTime, that.detail.endTime]
            : []
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.yeChenAdmissionContract !== null) {
          this.detail.yeChenAdmissionContract =
            res.data.yeChenAdmissionContract
        } else {
          this.detail.yeChenAdmissionContract = {
            serviceType: 0,
            serviceCharge: 10000,
            minServiceCharge: 30000,
            miniTurnover: 60000,
            theThirdOrg: '',
            bankAccName: '',
            commissionRatio: '40%',
            totalRevenueRatio: '40%',
            bankAcc: '',
            openingBank: '',
            serviceMonth: '2',
            returnRatio: '2:1',
            lowMiniTurnover: 10000,
            returnAmount: 5000,
            supplementInfo: '' // 补充条款字段
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = that.id
        that.areaSingle = converseEnToCn(
          getAreaSingle,
          that.detail.contractOrder.areaSingle
        )
        that.businessType = converseEnToCn(
          businessTypeList,
          that.detail.contractOrder.businessType
        )
        that.channel = converseEnToCn(
          channelList,
          that.detail.contractOrder.channel
        )
        that.orderStatusList = converseEnToCn(
          orderStatusList,
          that.detail.contractOrder.status
        )
        that.contractClass = converseEnToCn(
          contractClass,
          that.detail.contractClass
        )
        that.getContractStatus = converseEnToCn(
          getContractStatus,
          that.detail.status
        )
        that.versionTypeStatus = that.detail.versionType
          ? converseEnToCn(versionTypes, that.detail.versionType)
          : null
      })
    },
    getCreatContract() {
      // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr =
          that.detail.startTime !== null && that.detail.endTime !== null
            ? [that.detail.startTime, that.detail.endTime]
            : []
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.yeChenAdmissionContract !== null) {
          this.detail.yeChenAdmissionContract =
            res.data.yeChenAdmissionContract
        } else {
          this.detail.yeChenAdmissionContract = {
            serviceType: 0,
            serviceCharge: 10000,
            minServiceCharge: 30000,
            miniTurnover: 60000,
            theThirdOrg: '',
            bankAccName: '',
            commissionRatio: '40%',
            totalRevenueRatio: '40%',
            bankAcc: '',
            openingBank: '',
            serviceMonth: '2',
            returnRatio: '2:1',
            lowMiniTurnover: 10000,
            returnAmount: 5000,
            supplementInfo: '' // 补充条款字段
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = res.data.contractId
        that.areaSingle = converseEnToCn(
          getAreaSingle,
          that.detail.contractOrder.areaSingle
        )
        that.businessType = converseEnToCn(
          businessTypeList,
          that.detail.contractOrder.businessType
        )
        that.channel = converseEnToCn(
          channelList,
          that.detail.contractOrder.channel
        )
        that.orderStatusList = converseEnToCn(
          orderStatusList,
          that.detail.contractOrder.status
        )
        that.contractClass = converseEnToCn(
          contractClass,
          that.detail.contractClass
        )
        that.getContractStatus = converseEnToCn(
          getContractStatus,
          that.detail.status
        )
        that.versionTypeStatus = that.detail.versionType
          ? converseEnToCn(versionTypes, that.detail.versionType)
          : null
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
     * 确认修改信息
     */
    confirmEdit(num) {
      const that = this
      if (!that.detail.contractType) {
        that.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!that.timeArr || (that.timeArr && that.timeArr.length < 2)) {
        that.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.serviceCharge && that.detail.yeChenAdmissionContract.serviceType === 0) {
        that.$message({
          type: 'warning',
          message: '普通招生服务费不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.minServiceCharge && that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '保底招生服务费不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.theThirdOrg) {
        that.$message({
          type: 'warning',
          message: '第三方服务校区名称不能为空!'
        })
        return
      }
      if (
        !that.detail.yeChenAdmissionContract.miniTurnover &&
        that.detail.yeChenAdmissionContract.serviceType === 1
      ) {
        that.$message({
          type: 'warning',
          message: '保底营业额不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.commissionRatio &&
        that.detail.yeChenAdmissionContract.serviceType === 0) {
        that.$message({
          type: 'warning',
          message: '请输入第三方团队的提成比列!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.totalRevenueRatio &&
        that.detail.yeChenAdmissionContract.serviceType === 0) {
        that.$message({
          type: 'warning',
          message: '请输入总营收范畴比列!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.bankAccName) {
        that.$message({
          type: 'warning',
          message: '第三方服务机构银行户名不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.returnRatio &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '返还比例不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.lowMiniTurnover &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '招生营业额!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.returnAmount &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '返还金额不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.serviceMonth &&
        that.detail.yeChenAdmissionContract.serviceType === 0) {
        that.$message({
          type: 'warning',
          message: '赠送转化服务/月不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.bankAcc) {
        that.$message({
          type: 'warning',
          message: '第三方服务机构银行账号不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.commissionRatio &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '请输入第三方团队的提成比列!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.totalRevenueRatio &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '请输入总营收范畴比列!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.openingBank) {
        that.$message({
          type: 'warning',
          message: '第三方服务机构开户行不能为空!'
        })
        return
      }
      if (!that.detail.yeChenAdmissionContract.serviceMonth &&
        that.detail.yeChenAdmissionContract.serviceType === 1) {
        that.$message({
          type: 'warning',
          message: '赠送转化服务/月不能为空!'
        })
        return
      }
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          const data = Object.assign(that.detail, {
            operateType: num,
            startTime: that.timeArr[0],
            endTime: that.timeArr[1],
            yeChenAdmissionContractDTO: that.detail.yeChenAdmissionContract
          })
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '编辑成功!'
              })
              this.$store
                .dispatch('tagsView/delView', this.$route)
                .then(res => {
                  this.$router.go(-1)
                })
            }
          })
        }
      })
    },
    changeAreaSingle(val) {
      if (val === 1) {
        this.detail.yeChenAdmissionContract = {
          serviceType: val,
          serviceCharge: 10000,
          minServiceCharge: 30000,
          miniTurnover: 60000,
          theThirdOrg: '',
          bankAccName: '',
          commissionRatio: '40%',
          totalRevenueRatio: '40%',
          bankAcc: '',
          openingBank: '',
          serviceMonth: '2',
          returnRatio: '2:1',
          lowMiniTurnover: 10000,
          returnAmount: 5000,
          supplementInfo: '' // 补充条款字段
        }
      } else {
        this.detail.yeChenAdmissionContract = {
          serviceType: val,
          serviceCharge: 10000,
          minServiceCharge: 30000,
          theThirdOrg: '',
          bankAccName: '',
          commissionRatio: '40%',
          totalRevenueRatio: '40%',
          bankAcc: '',
          openingBank: '',
          serviceMonth: '2',
          supplementInfo: '' // 补充条款字段
        }
      }
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 10px;
}

/deep/ .el-card .el-card__header {
  position: relative;
}
/deep/ .el-input__inner{
  padding: 0 8px !important;
 }

.el-card__header .el-button {
  position: absolute;
  right: 20px;
  top: 10px;
}
</style>
