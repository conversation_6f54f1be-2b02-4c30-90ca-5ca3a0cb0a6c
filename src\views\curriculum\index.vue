<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        placeholder="班型名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
<!--      <el-select v-model="listQuery.clientCode" placeholder="客户端" clearable class="filter-item" style="width: 140px;">-->
<!--        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
<!--      </el-select>-->
      <ProductLineSelect v-model="listQuery.clientCode"></ProductLineSelect>
      <SeriesByClientSelect v-model="listQuery.seriesId" :client-code="listQuery.clientCode"></SeriesByClientSelect>
      <el-select v-model="listQuery.status" placeholder="班型状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in classType" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.checkAddress" placeholder="是否跨区校验" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in checkAddress" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:index:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      class="table"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="班型编号" show-overflow-tooltip prop="id" width="120px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
<!--      <af-table-column label="首页封面" show-overflow-tooltip prop="imageUrl" width="120px">-->
<!--        <template slot-scope="scope">-->
<!--          <img v-if="scope.row.imageUrl" :src="scope.row.imageUrl" class="cover-img">-->
<!--        </template>-->
<!--      </af-table-column>-->
      <el-table-column label="班型名称" prop="title" show-overflow-tooltip />
      <el-table-column label="班型简介" prop="introduction" show-overflow-tooltip width="300px" />
      <af-table-column label="适用客户端" prop="clientNames" show-overflow-tooltip />
      <af-table-column label="班型状态" prop="status" :formatter="getJoinStatusCN" />
      <af-table-column label="跨区校验" prop="checkAddress" :formatter="getCheckAddress" />
      <af-table-column label="班型排序" prop="sort" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:index:edit']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['curriculum:index:area']" type="primary" size="mini" @click="handleArea(row)">
            区域屏蔽
          </el-button>
          <!--<el-button type="primary" size="mini" @click="handleDelete(row)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改班型弹框-->
    <add-class ref="addClass" @addClassList="getList" />
    <!--    区域屏蔽-->
    <area-shield ref="area" :class-type-id="classTypeId" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AreaShield from './components/areaShield'
import AddClass from './components/addClassPop'
import { getPayList, clientCode, classTypeDelete } from '@/api/classType'
import {
  classType,
  checkAddress,
  converseEnToCn
} from '@/utils/field-conver'
import ProductLineSelect from '@/components/Select/ProductLineSelect.vue'
import SeriesByClientSelect from '@/components/Select/SeriesByClientSelect.vue'
export default {
  name: 'ClassType',
  components: {
    SeriesByClientSelect,
    ProductLineSelect,
    Pagination,
    AddClass,
    AreaShield
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      classType: classType,
      checkAddress: checkAddress,
      classTypeId: 0
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {

        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 2)

      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)

      await getPayList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleCreate() {
      this.$refs.addClass.classPop = true
      this.$refs.addClass.classTitle = '添加班型'
      this.$refs.addClass.isEdit = false
      this.$refs.addClass.flags = 1
      this.$refs.addClass.getSysClients()
    },
    handleUpdate(row) {
      this.$refs.addClass.classPop = true
      this.$refs.addClass.classTitle = '修改班型'
      this.$refs.addClass.isEdit = false
      this.$refs.addClass.flags = 0
      this.$refs.addClass.getClassDetail(row.id)
      this.$refs.addClass.getSysClients()
    },
    handleArea(row) {
      this.$refs.area.areaPop = true
      this.$refs.area.areaList = {}
      this.$refs.area.areaTitle = `<em style="color: red;font-size: 16px;font-weight: bold">${row.title}</em>`
      this.$refs.area.getList(row.id)
      this.$refs.area.orderId = row.id
      this.classTypeId = row.id
    },
    /**
     * 转换班型状态
     */
    getJoinStatusCN(row) {
      return converseEnToCn(this.classType, row.status)
    },
    getCheckAddress(row) {
      return converseEnToCn(this.checkAddress, row.checkAddress)
    },
    handleDelete(row) {
      this.$confirm('删除此项可能造成引用此项的数据显示异常，确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        classTypeDelete(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getDetail(row) { // 获取列表详情
      this.$refs.addClass.classPop = true
      this.$refs.addClass.classTitle = '班型详情'
      this.$refs.addClass.isEdit = true
      this.$refs.addClass.getClassDetail(row.id)
    }
  }
}
</script>

<style scoped>
.codes{
  font-weight: bold;
  color: #0a76a4;
}
/deep/.table {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%
}
/deep/.table >>> .el-table__header-wrapper {
  height: 90px;
}
/deep/.table >>> .el-table__body-wrapper {
  height: calc(100% - 90px) !important;
}
</style>
