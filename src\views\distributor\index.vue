<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.contactName"
        placeholder="经销商联系人"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.contactPhone"
        placeholder="经销商联系号码"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.distributorName"
        placeholder="经销商名称"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.parentDistributorName"
        placeholder="上级经销商"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.parentContactPhone"
        placeholder="上级经销商手机号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="applyDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="申请开始日期"
        end-placeholder="申请结束日期"
        style="width:260px"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="经销商联系人" prop="contactName" />
      <af-table-column label="经销商联系号码" prop="contactPhone" />
      <el-table-column label="经销商名称" prop="distributorName" width="200" />
      <af-table-column label="上级经销商联系人" prop="parentContactName" show-overflow-tooltip />
      <af-table-column label="上级经销商手机号" prop="parentContactPhone" show-overflow-tooltip />
      <af-table-column label="状态" prop="status">
        <template slot-scope="scope">
          <span v-show="scope.row.status===0">未审批</span>
          <span v-show="scope.row.status===1">审批通过</span>
          <span v-show="scope.row.status===2">审批驳回</span>
        </template>
      </af-table-column>
      <af-table-column label="申请时间" prop="applyTime" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-show="row.status===0" type="primary" size="mini" @click="approval(row)">审批</el-button>
          <el-button v-show="row.status===1||row.status===2" class="ml-10" type="primary" size="mini" @click="toDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { distributorList } from '@/api/customer'
import Pagination from '@/components/Pagination'
export default {
  name: 'Distributor',
  components: {
    Pagination
  },
  data() {
    return {
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      listLoading: false,
      list: [],
      statusList: [// 0: 未审批/审批中,1:审批通过,2:审批驳回
        {
          id: 0,
          name: '未审批'
        },
        {
          id: 1,
          name: '审批通过'
        },
        {
          id: 2,
          name: '审批驳回'
        }
      ],
      applyDate: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const params = Object.assign({}, this.listQuery, {
        startDate: this.applyDate.length > 0 ? this.applyDate[0] : '',
        endDate: this.applyDate.length > 0 ? this.applyDate[1] : ''
      })
      this.listLoading = true
      distributorList(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(err => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.applyDate = []
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    approval(row) {

      this.$store.commit('user/DISTRIBUTORID', row.id)
      this.$router.push({
        name: 'DistributorDetail'
      })
    },
    toDetail(row) {
      this.$store.commit('user/DISTRIBUTORID', row.id)
      this.$router.push({
        name: 'DistributorDetail'
      })
    }
  }
}
</script>

<style>

</style>
