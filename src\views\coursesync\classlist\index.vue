<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.classTypeName" placeholder="班型名称" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.clientCode" filterable placeholder="所属产品线" class="filter-item" style="width: 200px;" @change="getClientCodes">
        <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.classSeriesId" placeholder="班型系列" clearable class="filter-item" style="width: 200px" filterable>
        <el-option v-for="item in seriesList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="updateDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="更新开始日期"
        end-placeholder="更新结束日期"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">查询</el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">重置</el-button>
    </div>

    <el-table ref="assignTab" v-loading="listLoading" :data="list" border fit stripe highlight-current-row style="width: 100%;" class="table">
      <af-table-column label="#" type="index" width="40" align="center" />
      <el-table-column label="班型名称（草稿）" prop="mgrClassTypeName" width="200px" show-overflow-tooltip />
      <el-table-column label="所含科目" prop="mgrClassTypeSubjects" show-overflow-tooltip width="300px" />
      <af-table-column label="产品线" prop="clientCodeName" align="center" show-overflow-tooltip />
      <el-table-column label="班型系列" prop="classSeriesName" align="center" width="200px" />
      <af-table-column label="已关联班型数" prop="relationNums" align="center" />
      <!--   lastSyncTime < lastUpdateTime 显示小红点  -->
      <af-table-column label="草稿最后更新时间" prop="lastUpdateTime" align="center" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <div class="sync-opera" @click="syncClassType(scope.row)">
            <em>同步班型</em>
            <i v-show="scope.row.isShow" class="circle" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getClassTypePage" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { clientCode, classSystem, getSysClients } from '@/api/classType'
import { pageClassType } from '@/api/courseSyncApi'

export default {
  name: 'ClassTypeMgr',
  inject: ['reload'],
  components: {
    Pagination
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      classTypeId: 0,
      seriesList: [],
      productCodeList: [],
      updateDate: []
    }
  },
  watch: {
    'listQuery.productCode': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (oldVal && oldVal !== newVal) {
          this.listQuery.classSeriesId = ''
        }
      }
    }
  },
  created() {
  },
  mounted() {
    this.getClassTypePage()
    this.getClientCode()
    this.getSysClients()
  },
  methods: {
    getClientCode() {
      clientCode().then(res => {

        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 2)
        //
      })
    },
    async getClassTypePage() { // updateDate
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, {
        beginTime: that.updateDate && that.updateDate.length > 0 ? that.updateDate[0] : '',
        endTime: that.updateDate && that.updateDate.length > 0 ? that.updateDate[1] : ''
      })
      //
      await pageClassType(params).then(response => {
        const arr = []
        response.data.records.forEach(item => {

          let objs = {}
          objs = Object.assign({}, item, {
            isShow: !!((item.lastUpdateTime && item.lastSyncTime) && ((new Date(Date.parse(item.lastUpdateTime.replace('-', '/'))) > (new Date(Date.parse(item.lastSyncTime.replace('-', '/')))))))
          })
          arr.push(objs)
        })
        that.list = arr
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getClassTypePage()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.updateDate = []
      this.getClassTypePage()
    },
    getSysClients() {
      const params = {
        level: 1
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.productCodeList = res.data || []
        }
      }).catch((error) => {

      })
    },
    getClientCodes(val) {
      this.getClassSystem(val)
    },
    getClassSystem(code) {
      classSystem(code).then(res => {
        if (res.code === '000000') {
          this.seriesList = res.data || []
        }
      }).catch((error) => {

      })
    },
    syncClassType(row) {
      const params = {
        seriesName: row.classSeriesName,
        clientCodeName: row.clientCodeName,
        mgrClassTypeName: row.mgrClassTypeName,
        mgrClassTypeId: row.mgrClassTypeId,
        lastUpdateTime: row.lastUpdateTime
      }
      localStorage.setItem('syncObject', JSON.stringify(params))
      this.$router.push({
        name: 'ClassTypeRelation',
        params: {
          seriesName: row.classSeriesName,
          clientCodeName: row.clientCodeName,
          mgrClassTypeName: row.mgrClassTypeName,
          mgrClassTypeId: row.mgrClassTypeId,
          lastUpdateTime: row.lastUpdateTime
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sync-opera{
    position: relative;
    color: #fff;
    background-color: #1890ff;
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    .circle{
      position: absolute;
      top: 0px;
      right: 1px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: red;
    }
}
.codes{
  font-weight: bold;
  color: #0a76a4;
}
/deep/.table {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%
}
/deep/.table >>> .el-table__header-wrapper {
  height: 90px;
}
/deep/.table >>> .el-table__body-wrapper {
  height: calc(100% - 90px) !important;
}
</style>
