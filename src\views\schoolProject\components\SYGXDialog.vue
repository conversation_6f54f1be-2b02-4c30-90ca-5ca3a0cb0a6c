<template>
  <!--  dialog弹窗编辑或新增页面-->
  <el-dialog
    title="设置生涯规划数量"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :modal="true"
    :modal-append-to-body="true"
  >
    <el-row :gutter="10">
      <el-col :span="22">
        <el-form :model="form" ref="formRef"
                 size="mini"
                 label-width="160px"
                 label-position="right">
          <el-form-item label="高考志愿一卡通数量:" prop="gkzyNum">
            <el-input v-model="form.gkzyNum" placeholder="高考志愿卡" type="number" />
          </el-form-item>
          <el-form-item label="高中升学一卡通数量:" prop="gzsxNum">
            <el-input v-model="form.gzsxNum" placeholder="高中升学卡" type="number"/>
          </el-form-item>
          <el-form-item label="生涯规划卡数量:" prop="syghNum">
            <el-input v-model="form.syghNum" placeholder="生涯规划卡" type="number"/>
          </el-form-item>
          <el-form-item label="志愿填报卡数量:" prop="zytbNum">
            <el-input v-model="form.zytbNum" placeholder="志愿填报卡" type="number" />
          </el-form-item>
          <el-form-item label="备注:" prop="remark">
            <el-input v-model="form.remark" maxlength="200" :autosize="{ minRows: 10, maxRows: 14}" show-word-limit type="textarea"
                      placeholder="请输入本次操作备注" />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" round>取 消</el-button>
      <el-button type="primary" @click="handleSubmit" round>确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getSXGHData, updateSXGHData } from '@/api/schoolCampus'

export default {
  name: 'SYGXDialog',
  data() {
    return {
      dialogVisible: true,
      form: {
        schoolId: this.schoolId,
        syghNum: 0, //生涯规划卡数量
        zytbNum: 0, //志愿填报卡数量
        gkzyNum: 0,//高考志愿一卡通数量
        gzsxNum: 0,//高中升学一卡通数量
      },
    }
  },
  props: {
    schoolId: {
      type: [String, Number],
      required: true
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getSXGHData(this.schoolId).then(res => {
        if (res.code === SUCCESS) {
          this.form = res.data
        }
      })
    },
    handleSubmit() {
      const data = Object.assign({}, this.form, { schoolId: this.schoolId })
      updateSXGHData(data).then(res => {
        if (res.code === SUCCESS) {
          this.$message({
            message: '设置成功',
            type: 'success'
          })
          this.handleClose()
        }
      })
    },
    handleClose(done) {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">
.el-tag {
  margin-right: 10px;
}

.button-new-tag {
  height: 26px;
  line-height: 26px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 120px;
  vertical-align: bottom;
}
</style>
