<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="套餐管理"
    :close-on-click-modal="false"
    width="60%"
    @close="handleClose"
  >
    <div class="package-management">
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="addPackageRow">
          新增套餐
        </el-button>
      </div>

      <div class="table-container">
        <el-table
          :data="packageList"
          border
          style="width: 100%"
          :loading="loading"
        >
          <el-table-column label="数量(小时)">
            <template slot-scope="scope">
              <el-input
                :disabled="scope.row.id"
                v-model.number="scope.row.number"
                placeholder="请输入小时数"
                type="number"
                min="0"
                @input="validateNumber(scope.row, 'number')"
              >
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="价格(元)">
            <template slot-scope="scope">
              <el-input
                :disabled="scope.row.id"
                v-model.number="scope.row.price"
                placeholder="请输入价格"
                type="number"
                min="0"
              >
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  saveProductPackagesForV2,
  getProductPackagesForV2,
  deleteProductPackage,
} from "@/api/schoolCampus";

export default {
  name: "PackageManagement",
  props: {
    productId: {
      type: [String, Number],
      default: null,
    },
    schoolId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      dialogVisible: false,
      packageList: [],
      currentRow: null,
      loading: false,
    };
  },
  methods: {
    async open() {
      this.dialogVisible = true;
      await this.$nextTick();
      this.resetForm();
      this.getProductPackages();
    },

    async getProductPackages() {
      this.loading = true;
      try {
        const res = await getProductPackagesForV2({
          productId: this.productId,
          schoolId: this.schoolId,
        });
        if (res.code === "000000") {
          this.packageList = res.data;
        }
      } catch (error) {
        this.$message.error("获取套餐列表失败");
      } finally {
        this.loading = false;
      }
    },

    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    resetForm() {
      this.packageList = [{ number: null, price: null }];
    },
    addPackageRow() {
      this.packageList.push({
        number: null,
        price: null,
      });
    },
    async handleDelete(index) {
      // 如果没有ID，说明是新增的行，可以直接删除
      if (!this.packageList[index].id) {
        this.packageList.splice(index, 1);
        return;
      }

      await this.deletePackage(this.packageList[index].id);
    },

    async deletePackage(id) {
      const res = await deleteProductPackage(id);
      if (res.code === "000000") {
        this.$message.success("套餐删除成功");
        this.getProductPackages();
      }
    },

    validateNumber(row, field) {
      if (row[field] !== null && row[field] !== undefined) {
        // Ensure it's a number and not negative
        row[field] = Math.max(0, parseInt(row[field]) || 0);
      }
    },
    validateForm() {
      let valid = true;
      let errorMsg = "";

      if (!this.productId) {
        valid = false;
        errorMsg = "未选择流量包";
      } else {
        // Check if any row has empty fields
        for (let i = 0; i < this.packageList.length; i++) {
          const row = this.packageList[i];
          if (
            row.number === null ||
            row.number === undefined ||
            row.number === "" ||
            row.price === null ||
            row.price === undefined ||
            row.price === ""
          ) {
            valid = false;
            errorMsg = "请填写所有套餐的数量和价格";
            break;
          }

          // Validate that hours and price are positive numbers
          if (row.number <= 0 || row.price <= 0) {
            valid = false;
            errorMsg = "数量和价格必须大于0";
            break;
          }
        }
      }

      if (!valid) {
        this.$message.error(errorMsg);
      }

      return valid;
    },
    submitForm() {
      if (!this.validateForm()) {
        return;
      }

      // Prepare data for submission
      const packages = this.packageList.filter((item) => !item.id);

      if (packages.length === 0) {
        this.dialogVisible = false
        return;
      }

      // Submit data to API
      saveProductPackagesForV2({
        schoolId: this.schoolId,
        productId: this.productId,
        packages,
      }).then((res) => {
        if (res.code === "000000") {
          this.$message.success("套餐添加成功");
          this.dialogVisible = false;
          this.$emit("refresh");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.package-management {
  padding: 0 20px;
}

.table-container {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
}
</style> 