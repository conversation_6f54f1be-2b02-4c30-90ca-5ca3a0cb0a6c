<template>
  <div style="padding: 15px;">
    <div class="filter-container">
      <el-input
        v-model="listQuery.userId"
        placeholder="学生ID"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userSearchField"
        placeholder="学生姓名/学生账号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.partner"
        placeholder="合伙人/校区名称/手机号"
        class="filter-item"
        style="width: 180px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.gradeName"
        placeholder="年级"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.classType"
        placeholder="开通班型"
        clearable
        class="filter-item"
        style="width: 150px;"
      >
        <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select
        v-model="listQuery.clientCode"
        placeholder="产品线"
        clearable
        class="filter-item"
        style="width: 150px;"
      >
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="学生状态"
        clearable
        class="filter-item"
        style="width: 150px;"
      >
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="studentlDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建开始日期"
        end-placeholder="创建结束日期"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      :span-method="objectSpanMethod"
    >
      <af-table-column label="学生ID" show-overflow-tooltip prop="userId" />
      <af-table-column label="学生姓名(账号)" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.realName">{{ scope.row.realName }}</span>
          <span v-if="scope.row.userAccount">({{ scope.row.userAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="合伙人/机构账号">
        <template slot-scope="scope">
          <span v-if="scope.row.userPartner">{{ scope.row.userPartner }}</span>
          <span v-if="scope.row.partnerAccount">({{ scope.row.partnerAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="mainSchoolName" />
      <af-table-column label="产品线" prop="clientCodeName" />
      <af-table-column label="年级(所属班级)" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>
            <em v-if="scope.row.gradeName">{{ scope.row.gradeName }}</em>
            <em v-if="scope.row.className">({{ scope.row.className }})</em>
          </span>
        </template>
      </af-table-column>
      <af-table-column label="所属分校" prop="branchSchoolName" />
      <af-table-column label="开通班型" prop="classTypeName" />
      <af-table-column label="充值总数" prop="totalIncrease">
        <template slot-scope="scope">
          <span>{{ parseInt((scope.row.totalIncrease?scope.row.totalIncrease:0)/60) }}小时</span>
          <span>{{ parseInt((scope.row.totalIncrease?scope.row.totalIncrease:0)%60) }}分</span>
        </template>
      </af-table-column>
      <af-table-column label="扣减总数" prop="totalDecrease">
        <template slot-scope="scope">
          <span>{{ parseInt((scope.row.totalDecrease?scope.row.totalDecrease:0)/60) }}小时</span>
          <span>{{ parseInt((scope.row.totalDecrease?scope.row.totalDecrease:0)%60) }}分</span>
          <span>{{ Math.round(Number((
            (scope.row.totalDecrease?scope.row.totalDecrease:0)%60 -
            (parseInt((scope.row.totalDecrease?scope.row.totalDecrease:0)%60)))
            .toFixed(2)) * 60) }}秒</span>
        </template>
      </af-table-column>
      <af-table-column label="剩余总数" prop="balance">
        <template slot-scope="scope">
          <span>{{ parseInt((scope.row.balance?scope.row.balance:0)/60) }}小时</span>
          <span>{{ parseInt((scope.row.balance?scope.row.balance:0)%60) }}分</span>
          <span>{{ Math.round(Number((
            (scope.row.balance?scope.row.balance:0)%60 -
            (parseInt((scope.row.balance?scope.row.balance:0)%60)))
            .toFixed(2)) * 60) }}秒</span>
        </template>
      </af-table-column>
      <af-table-column label="状态" prop="status" :formatter="getStudentStatus" />
      <af-table-column label="账号到期时间" prop="expireTime" />
      <af-table-column label="最近登录时间" prop="loginTime" />
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp auto-fixed"
        min-width="230"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button v-permission="['resources:openClassDetail']" type="primary" size="mini" @click="classDetailPop=true,handleUpdate(row)">班型开通详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :visible.sync="classDetailPop"
      :title="classDetailTitle"
      :close-on-click-modal="!classDetailPop"
      width="90%"
    >
      <div v-if="haveClassTypes&&haveClassTypes.length>0">
        <el-row :gutter="20">
          <el-col v-for="(item,index) in haveClassTypes" :key="index" :sm="{span:24}" :lg="{span:8}" :md="{span:8}">
            <el-card class="box-card types">
              <div slot="header" class="clearfix student-type-title">
                <span><em class="el-icon-reading" /><em>{{ item.classTypeName }}</em></span>
              </div>
              <div v-if="item.haveSubjects&&item.haveSubjects.length>0" class="item">
                <p>
                  <span v-for="(itemClass,i) in item.haveSubjects" :key="i" class="subject-item">{{ itemClass.subjectName }}</span>
                </p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <p v-else>暂无相关数据</p>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
const cityOptions = ['上海', '北京', '广州', '深圳']
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
import { clientCode, getAllClassType } from '@/api/classType'
import { schoolLists, classDetail } from '@/api/charge'
export default {
  name: 'StudentList',
  components: {
    Pagination
  },
  data() {
    return {
      studentsPop: false,
      studentsTitle: '学生列表',
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      assignSatuts: [],
      classDetailPop: false,
      classDetailTitle: '',
      haveClassTypes: [],
      cities: cityOptions,
      enableList: enableList,
      mainSchoolId: '',
      schoolLists: [],
      clientCode: [],
      allClassType: [],
      arrStudent: [],
      spanArr: [],
      pos: 0,
      branchSchools: [],
      studentlDate: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
      this.getList()
      this.getCode()
      this.getAllClassType()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign({}, that.listQuery, {
        startDate: that.studentlDate !== null && that.studentlDate[0] ? that.studentlDate[0] : '',
        endDate: that.studentlDate !== null && that.studentlDate[1] ? that.studentlDate[1] : ''
      })
      await schoolLists(params).then(response => {
        that.total = response.data.total
        that.listLoading = false
        const arrStudent = []
        const studentNum = response.data.records
        studentNum.forEach((item, i) => {
          const singleObj = {
            id: item.id,
            userId: item.userId,
            userAccount: item.userAccount,
            realName: item.realName,
            gradeName: item.gradeName,
            mainSchoolName: item.mainSchoolName,
            className: item.className,
            branchSchoolName: item.branchSchoolName,
            status: item.status,
            expireTime: item.expireTime,
            loginTime: item.loginTime,
            createTime: item.createTime,
            partnerAccount: item.partnerAccount,
            userPartner: item.userPartner,
            schoolName: item.schoolName,
            clientCodeName: item.clientCodeName
          }
          if (item.classBalances && item.classBalances.length > 0) {
            item.classBalances.forEach((ele, j) => {
              arrStudent.push({
                ...singleObj,
                ...ele
              })
            })
          } else {
            arrStudent.push({
              ...singleObj
            })
          }
        })
        that.list = arrStudent

        that.getSpanArr(that.list)
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.studentlDate = []
      this.getList()
    },
    getStudentStatus(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    handleUpdate(row) {
      this.classDetailTitle = `班型开通详情-${row.realName}`
      this.getStudent(row.id)
    },
    getStudent(ids) {
      classDetail(ids).then(res => {
        if (res.code === '000000') {
          this.haveClassTypes = res.data.haveClassTypes || []
        }
      })
    },
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 1)
      })
    },
    getAllClassType() { // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data
      })
    },
    getSpanArr(data) {
      // spanArr和pos需要定义
      this.spanArr = []
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1)
          this.pos = 0
        } else {
          if (data[i].userId === data[i - 1].userId) { this.spanArr[this.pos] += 1; this.spanArr.push(0) } else { this.spanArr.push(1); this.pos = i }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断合并列 也就是上面表格的类型
      if (columnIndex < 16 && columnIndex > -1 && columnIndex !== 7 && columnIndex !== 8 && columnIndex !== 9 && columnIndex !== 10) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
  h2,h3,h4,h5,h6{
    padding: 0;
    margin: 0;
  }
  .subject-item{
    padding-right: 8px;
    font-size: 16px;
  }
  .student-title{
    display: flex;
    margin-bottom: 15px;
    span{
      font-size: 16px;
      padding-right: 10px;
      em{
        font-size: 24px;
        font-weight: bold;
        padding-left: 5px;
      }
    }
  }
  .student-list{
    ul{
      display: flex;
      padding:0;
      margin: 0;
      li{
        border-radius: 5px;
        color: #fff;
        margin-right:25px;
        &.bg1{
          background: #409EFF;
        }
        &.bg2{
          background: #67C23A;
        }
        &.bg3{
          background: #F56C6C;
        }
        .list-title{
          padding:5px 15px;
          h3{
            padding-bottom: 10px;
          }
          p{
            span{
              font-size: 20px;
            }
            em{
              padding-left: 8px;
            }
          }
        }
        .tips{
          border-top: 1px rgba(255,255,255,.6) solid;
          padding-top:15px;
          padding-bottom: 15px;
          span{
            font-size: 14px;
            padding:8px 0 8px 15px;
            &:last-child{
              padding-right: 15px;
              position: relative;
              &:before{
                position: absolute;
                left:5px;
                top:8px;
                width: 1px;
                height: 15px;
                content: '';
                background:rgba(255,255,255,.6);
              }
            }
          }
        }
      }
    }
  }
  .types{
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    border:1px #1890ff solid;
    .student-type-title{
      span{
        em{
          font-size: 18px;
          padding-right: 8px;
        }
      }
    }
  }

  @media screen and (max-width: 768px){
    .student-title{
      display: block;
      span{
        display: block;
        width: 100%;
      }
    }
    .student-list{
      ul{
        display: block;
        li{
          display: block;
          width: 100%;
          margin-bottom: 15px;
        }
      }
    }
  }
</style>
<style>
  @media screen and (max-width: 768px){
    .el-dialog__body{
      padding:0;
    }
  }
</style>
