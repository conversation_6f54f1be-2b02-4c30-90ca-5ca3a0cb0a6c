<template>
  <div class="app-container bgGrey">
    <el-row :gutter="10">
      <el-col :lg="{span:10}">
        <el-form
          ref="form"
          size="small"
          :model="customerInfo"
          label-width="100px"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>客户信息</span>
            </div>

            <div class="item">
              <div class="item-header">
                基础信息
              </div>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <div>{{ customerInfo.orderCode }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <div>{{ customerInfo.customer }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="手机号码：">
                    <div>{{ customerInfo.mobile }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <div>{{ customerInfo.institution }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="所在区域：">
                    <div>{{ customerInfo.provinceName }} | {{ customerInfo.cityName }} | {{ customerInfo.areaName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：">
                    <div>{{ customerInfo.address }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="item">
              <div class="item-header">
                其他信息
              </div>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="信息来源：">
                    <div class="information-sources">
                      {{customerInfo.originName}}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="意向度：">
                    <div>{{ getIntention(customerInfo.clueType) }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="微信：">
                    <div>{{ customerInfo.weixin }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="QQ：">
                    <div>{{ customerInfo.qq }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="邮箱：">
                    <div>{{ customerInfo.mail }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <div>{{ customerInfo.remark }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'PayDetail',
  components: { },
  directives: {},
  data() {
    return {
      schoolOption: '',
      relativeTitle: '',
      createSchoolTitle: '',
      currentCustomerId: '', // 当前客户的id
      customerInfo: {},
      relationList: [],
      schoolList: [],
      projectList: [],
      relationListLoading: false,
      schoolListLoading: false,
      projectListLoading: false
    }
  },
  computed: {
  },
  created() {
    const id = this.$route.params && this.$route.params.id
    this.currentCustomerId = id
  },
  methods: {
    /**
     * 查看线索/客户详情
     * @param row
     */
    getCustomerInfo(id) {
      // getCustomerDetail(id).then(res => {
      //   this.customerInfo = res.data
      // })
    }
  }
}
</script>

<style scoped lang="scss">
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .information-sources{
    display: flex;
    span{
      padding-right: 5px;
    }
  }
</style>

