<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.levelOneId"
        placeholder="一级标题编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.levelOneTitle"
        placeholder="一级标题"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['faq:title:add']" class="filter-item" size="mini" type="primary" @click="titlePop=true,handleCreate()">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="一级标题编号" show-overflow-tooltip prop="id" width="150px" />
      <af-table-column label="一级标题" show-overflow-tooltip prop="title" />
      <af-table-column label="排序" prop="sort" />
      <el-table-column label="二级标题管理" prop="subCount" width="300px">
        <template slot-scope="scope">
          <a v-if="scope.row.subCount>0" class="title-tips" @click="getParent(scope.row)">二级标题({{ scope.row.subCount }})</a>
          <a v-else @click="getParent(scope.row)">二级标题(0)</a>
        </template>
      </el-table-column>
      <af-table-column label="提交时间" prop="updateTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['faq:title:edit']" type="primary" size="mini" @click="titlePop=true,handleUpdate(row)">修改</el-button>
          <el-button v-permission="['faq:title:del']" type="primary" size="mini" @click="handleDelet(row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <parent-title ref="parentTitle" @refreshTitle="getList" />
    <!--一级标题的新增/修改弹框-->
    <el-dialog :visible.sync="titlePop" :title="oneTitle" :close-on-click-modal="!titlePop" width="60%" @close="changeInit">
      <el-form ref="parentFormUps" :model="parentFormUp" :rules="rules" label-width="150px">
        <el-form-item label="一级标题" prop="title">
          <el-input v-model="parentFormUp.title" placeholder="一级标题" maxlength="20" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="parentFormUp.sort" placeholder="排序" maxlength="1000" type="number" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="handelCreated">确定</el-button>
          <el-button type="infor" size="mini" @click="changeInit">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ParentTitle from './components/parentTitle'
import { levelOneList, delTitle, addTitle, titleIn, editTitle } from '@/api/faq'
export default {
  name: 'Title',
  components: {
    ParentTitle
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {},
      enableList: [],
      titlePop: false,
      oneTitle: '',
      parentFormUp: {},
      editFlag: '',
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入一级标题' },
        sort: { required: true, trigger: 'blur', message: '请输入排序' }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    async getList() {
      const data = Object.assign({}, this.listQuery)
      levelOneList(data).then(res => {
        if (res.code === '000000') {
          this.listLoading = false
          this.list = res.data
          this.total = res.data.length
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleCreate() {
      this.oneTitle = '新增一级标题'
      this.editFlag = 'create'
    },
    handleUpdate(row) {
      this.editFlag = 'update'
      this.oneTitle = `修改${row.title}`
      this.titleIn(row.id)
    },
    handleDelet(row) {
      if (row.subCount > 0) {
        this.$message({
          type: 'warning',
          message: '请先删除下级标题'
        })
      } else {
        this.$confirm('是否确认删除该标题?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTitle(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.getList()
            }
          }).catch(() => {

          })
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      }
    },
    getParent(row) {
      this.$refs.parentTitle.faqTitlePop = true
      this.$refs.parentTitle.faqParentTitle = `${row.title}`
      this.$refs.parentTitle.getList(row.id)
      this.$refs.parentTitle.parentId = row.id
    },
    changeInit() {
      this.titlePop = false
      this.parentFormUp = {}
      if (this.$refs.parentFormUps) {
        this.$refs.parentFormUps.clearValidate()
      }
    },
    handelCreated() {
      this.$refs.parentFormUps.validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.parentFormUp, { parent: 0, level: 1, id: this.parentFormUp.id ? this.parentFormUp.id : '' })
          if (this.editFlag === 'create') {
            addTitle(data).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '一级标题新增成功'
                })
                this.titlePop = false
                this.parentFormUp = {}
                this.getList()
              }
            }).catch(() => {

            })
          } else {
            editTitle(data).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '一级标题修改成功'
                })
                this.titlePop = false
                this.parentFormUp = {}
                this.getList()
              }
            })
          }
        } else {

          return false
        }
      })
    },
    titleIn(ids) { // 标题详情
      titleIn(ids).then(res => {
        if (res.code === '000000') {
          this.parentFormUp = res.data
        }
      }).catch(() => {

      })
    }
  }
}
</script>

<style scoped>

</style>
