<template>
  <el-select v-model="tmpId" style="width: 220px;" filterable clearable placeholder="教材版本（新）" no-data-text="请先选择科目">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="`${item.title}`"
      :value="`${item.id}`">
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import {getCourseMaterialByParam} from '@/api/classType'

/**
 * 教材版本（新）
 */
export default {
  name: 'MaterialByClassTypeSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    subjectId: {
      type: [String, Number],
      required: false
    },
    clientCode: {
      type: [String, Number],
      required: false
    },
    classTypeId: {
      type: [String, Number],
      required: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  watch: {
    subjectId(val) {
      this.dataList = []
      this.getList()
    },
    clientCode(val) {
      this.dataList = []
      this.getList()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.title : ''
      return this.$emit('change', value, selectedName)
    },
    setDefaults(){
      const selectedOption = this.dataList.find(option => option.id == this.id)
      if(!selectedOption || !selectedOption.id){
        this.handleChange(null)
      }
    },
    getList() {
      if (!this.subjectId || !this.clientCode) {
        return
      }
      this.loading = true
      getCourseMaterialByParam({
        clientCode:this.clientCode,
        subjectId:this.subjectId,
      }).then(res => {
        if (res.code === SUCCESS) {
          this.loading = false
          this.dataList= res.data
          this.setDefaults()
        }
        else {
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
</style>
