// 习题答案附件
<template>
  <div class="app-container">
    <div class="filter-container"> <el-button  v-waves class="filter-item" type="primary"   @click="openForm">上传答案</el-button></div>
    <el-table
      v-loading="listLoading"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      :data="pdfList"
    >
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1" class="ml-2" type="success">试题打卡</el-tag>
          <el-tag v-else class="ml-2">试题答案</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件" align="center">
        <template slot-scope="scope">
          <el-link type="primary" target="_blank" :href="scope.row.fileName.indexOf('http')>0? scope.row.fileName:host+scope.row.fileName">打开文件</el-link>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp"
        fixed="right"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span style="color: #409EFF" title="编辑" @click="editForm(scope.row)">编辑</span>
          <span style="color: #409EFF" title="删除" @click="deletePdf(scope.row)">删除</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 习题答案对话框 -->
    <el-dialog :title="submitForm.id ? '编辑习题答案': '添加习题答案'" :visible.sync="submitFormDialog" width="500px" custom-class="student-dialog" center append-to-body>
      <el-form
        ref="formRef"
        :model="submitForm"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="答案类型"  prop="type">
          <el-radio-group v-model="submitForm.type">
            <el-radio :label="1">试题打卡</el-radio>
            <el-radio :label="4">试题答案</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="习题附件">
          <el-upload
            :action="host"
            :data="aliData"
            :http-request="resourcesUpload"
            :on-exceed="exceed"
            :on-remove="resourcesRemove"
            :file-list="submitForm.fileList"
            :limit="1"
            accept=".pdf"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传pdf文件，且不超过5M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormAction('formRef')">确 定</el-button>
        <el-button @click="submitFormDialog = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { queryPdfList, sumbitPdf, updatePdf, deletePdf } from '@/api/exercises'
import { uploadSuccess } from '@/api/common'
import { uuid } from '@/utils'
import { getOSSClient } from '@/components/upload/getOSSTst'
export default {
  props: {
    courseInfo: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      host: 'https://santaohw.oss-cn-beijing.aliyuncs.com',
      aliData: {
        name: '',
        key: 'exercises/pdf/' + '${filename}',
        success_action_status: '200'
      },
      client:null,
      listLoading: false,
      submitFormDialog: false,
      pdfList: [],
      formRules: {
        fileUrl: { required: true, message: '请上次习题附件', trigger: ['blur', 'change'] }
      },

      submitForm: {
        courseId: '',
        fileId: '',
        fileList: [],
        fileName: '',
        homeworkId: '',
        id: '',
        type: 1
      }
    }
  },

  async mounted() {
    this.client= await getOSSClient('santaohw')
  },
  created() {
    this.getQueryPdfList()
  },

  methods: {
    getQueryPdfList() {
      this.listLoading = true
      queryPdfList(this.courseInfo.id,this.courseInfo.testType).then(res => {
        this.listLoading = false
        if (res.code === '000000') {
          this.pdfList = res.data
        }
      })
    },

    openForm(row) {
      Object.keys(this.submitForm).forEach((item, index) => {
        this.submitForm[item] = ''
      })
      this.submitForm.courseId = this.courseInfo.courseId
      this.submitForm.homeworkId = this.courseInfo.id
      this.submitForm.type = 1
      this.submitForm.fileList = []
      this.submitFormDialog = true
    },
    editForm(row) {
      this.submitForm = Object.assign({}, row)
      this.submitForm.fileList = []
      this.submitForm.fileList.push({ name: row.fileName, url: row.fileName })
      this.submitFormDialog = true
    },

    deletePdf(row) {
      this.$confirm('确认删除？', {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        deletePdf(row).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '确认删除成功'
            })
            this.getQueryPdfList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    submitFormAction() {
      if (!this.submitForm.fileId) {
        this.$message({
          type: 'warning',
          message: '请上传习题附件！'
        })
        return
      }
      this.submitFormDialog = false
      if (this.submitForm.id) {
        updatePdf(this.submitForm).then(res => {
          if (res.code === '000000') {
            this.getQueryPdfList()
          }
        })
      } else {
        sumbitPdf(this.submitForm).then(res => {
          if (res.code === '000000') {
            this.getQueryPdfList()
          }
        })
      }
    },
    exceed() {
      this.$message({
        type: 'warning',
        message: '超出文件上传个数'
      })
    },

    resourcesUpload(params) {
      const that = this
      var file = params.file
      const tempName = file.name.split('.')
      const fileName = '/exercises/pdf/' + this.courseInfo.id + '/' + uuid() + '.' + tempName[tempName.length - 1]
      this.client.put(fileName, file).then(({ res, _url, name }) => {
        if (res && res.status === 200) {
          const paramsUpload = Object.assign({}, {
            imageUrl: fileName,
            resourceType: 'image'
          })

          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              that.submitForm.fileId = res.data.id
              that.submitForm.fileName = res.data.url
              that.submitForm.fileList.push({ name: tempName, url: _url })
            }
          })
        }
      }).catch((err) => {

      })
    },
    resourcesRemove(file) {
      this.submitForm.fileId = ''
      this.submitForm.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.exercises-pdf {
  position: relative;
  background: #fff;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  padding: 15px;
  //margin-top: 10px;
}
</style>
