<template>
  <el-select
    v-model="tmpId"
    style="width: 220px;"
    filterable
    :clearable="!isTest"
    placeholder="产品线"
  >
    <el-option
      v-for="item in dataList"
      :key="item.code"
      :label="item.name"
      :value="`${item.code}`"
    >
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from "@/utils/http-status-code";
import { clientCode } from "@/api/classType";

/**
 * 产品线选择框（数据来自远端接口）
 */
export default {
  name: "ProductLineSelect",
  data: function() {
    return {
      dataList: []
    };
  },
  model: {
    prop: "id",
    event: "change"
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    // 是否闪测  true 闪测  false 正常
    isTest: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + "" : "";
      },
      set(val) {
        this.handleChange(val);
      }
    },
    schoolId: {
      get() {
        return this.$store.state.user.schoolID;
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.code == value);
      const selectedName = selectedOption ? selectedOption.name : "";
      return this.$emit("change", value, selectedName);
    },
    getList() {
      this.loading = true;
      clientCode().then(res => {
        if (res.code === SUCCESS) {
          this.loading = false;
          const clientCodes = res.data || [];
          this.dataList = clientCodes.filter(item => item.level === 1) || [];
          // 闪测过滤芝麻艺考 有值赋值  没有值默认第一个
          if (this.isTest) {
            this.dataList = this.dataList.filter(
              item => item.code === 100 || item.code === 200
            );
            this.tmpId = this.id || this.dataList[0].code;
            // 闪测列表传递出去 供闪测同步提示语使用
            this.$emit("dataList", this.dataList);
          }
        } else {
          this.loading = false;
        }
      });
    }
  }
};
</script>
<style scoped lang="scss"></style>
