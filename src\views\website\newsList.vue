<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="title"
        placeholder="标题"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="type" placeholder="新闻类型" filterable clearable class="filter-item" style="width: 120px;" @change="getselect">
        <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="hotShowFlag" placeholder="热门栏目状态" filterable clearable class="filter-item" style="width: 160px;" @change="getselect">
        <el-option v-for="item in hotShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="newShowFlag" placeholder="最新栏目状态" filterable clearable class="filter-item" style="width: 160px;" @change="getselect">
        <el-option v-for="item in newShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="selfShowFlag" placeholder="本栏目状态" filterable clearable class="filter-item" style="width: 160px;" @change="getselect">
        <el-option v-for="item in selfShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-button class="filter-item" size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button class="filter-item" size="mini" type="primary" @click="handleReset">重置</el-button>
      <el-button v-permission="['website:news:add']" class="filter-item" size="mini" type="primary" @click="addNews">新增资讯</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" />
      <af-table-column label="新闻标题" prop="title" width="300px" />
      <af-table-column label="副标题" prop="subtitle" width="300px" />
      <af-table-column label="标题图片链接" prop="titleImage" width="500" />
      <af-table-column label="内容链接" prop="contentUrl" width="400" />
      <af-table-column label="新闻板块">
        <template slot-scope="scope">
          <span v-if="scope.row.type==1">三陶资讯</span>
          <span v-if="scope.row.type==2">家长须知</span>
          <span v-if="scope.row.type==3">学员干货</span>
        </template>
      </af-table-column>
      <af-table-column label="热门栏目状态">
        <template slot-scope="scope">
          <span v-if="scope.row.hotShowFlag==1">显示</span>
          <span v-if="scope.row.hotShowFlag==0">隐藏</span>
        </template>
      </af-table-column>
      <af-table-column label="最新栏目状态">
        <template slot-scope="scope">
          <span v-if="scope.row.newShowFlag==1">显示</span>
          <span v-if="scope.row.newShowFlag==0">隐藏</span>
        </template>
      </af-table-column>
      <af-table-column label="本栏目状态">
        <template slot-scope="scope">
          <span v-if="scope.row.selfShowFlag==1">显示</span>
          <span v-if="scope.row.selfShowFlag==0">隐藏</span>
        </template>
      </af-table-column>
      <af-table-column label="排序" prop="sort" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" align="center" width="180" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['website:news:edit']" type="primary" size="mini" @click="editNews(row)">修改</el-button>
          <el-button v-permission="['website:news:del']" type="primary" size="mini" @click="handleDelet(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageIndex"
      :limit.sync="pageSize"
      @pagination="getList"
    />
    <news-popup ref="newsDialog" @refreshList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import newsPopup from './component/newsPopup'
import { listPage, removeNews } from '@/api/website'
export default {
  name: 'NewsList',
  components: {
    Pagination,
    newsPopup
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      hotShowFlag: '',
      hotShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }],
      newShowFlag: '',
      newShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }],
      pageIndex: 1,
      pageSize: 10,
      selfShowFlag: '',
      selfShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }],
      title: '',
      type: '',
      typeList: [{ title: '三陶资讯', id: 1 }, { title: '家长须知', id: 2 }, { title: '学员干货', id: 3 }]

    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    async getList() {
      listPage(this.hotShowFlag, this.newShowFlag, this.pageIndex, this.pageSize, this.selfShowFlag, this.title, this.type).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {
        this.$message({ message: '查询失败', type: 'error' })
      })
    },
    // 搜索新闻
    handleSearch() {
      this.getList()
    },
    // 重置新闻
    handleReset() {
      this.hotShowFlag = ''
      this.newShowFlag = ''
      this.pageIndex = 1
      this.pageSize = 10
      this.selfShowFlag = ''
      this.title = ''
      this.type = ''
      this.getList()
    },
    handleFilter() {
      this.pageIndex = 1
      this.getList()
    },
    getselect() {
      this.getList()
    },
    // 添加资讯
    addNews() {
      this.$nextTick(() => {
        this.$refs.newsDialog.newsTitle = '新增新闻资讯'
        this.$refs.newsDialog.newsPop = true
        this.$refs.newsDialog.newsFlag = 'create'
        this.$refs.newsDialog.addNews()
      })
    },
    // 编辑资讯
    editNews(row) {
      this.$nextTick(() => {
        this.$refs.newsDialog.newsPop = true
        this.$refs.newsDialog.newsTitle = '编辑新闻资讯'
        this.$refs.newsDialog.getNews(row.id)
        this.$refs.newsDialog.editFlag = 'update'
      })
    },
    handleDelet(row) {
      this.$confirm('确定要删除此条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeNews(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
