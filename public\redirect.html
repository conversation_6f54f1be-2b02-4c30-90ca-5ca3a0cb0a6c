<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>企业微信登录</title>
  <style>
    body {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
    }
    .successImg{
      width: 80%;
    }
    .successText{
      font-size: 20px;
      color: #333;
    }
    .hidden{
      display: none;
    }
    #banding{
      margin-top: 100px;
    }
  </style>
</head>
<body>
<div id="banding" style="text-align: center">
  <img src="./Loading.svg" class="successImg" alt="success">
  <p class="successText">绑定中，请稍等</p>
</div>
<div id="success" class="hidden" style="text-align: center">
  <img src="./success.png" class="successImg" alt="success">
  <p class="successText">恭喜！绑定成功</p>
</div>
<div id="failer" class="hidden" style="text-align: center">
  <img src="./failer.png" class="successImg" alt="success">
  <p class="successText">抱歉！绑定失败，请重试</p>
</div>
<!-- 通过 CDN 引入 vconsole.js -->
<!--<script src="https://cdn.jsdelivr.net/npm/vconsole@3.15.0/dist/vconsole.min.js"></script>-->
<script>
  // 初始化 vConsole
  // const vConsole = new VConsole();
  // 获取 url 中的参数
  function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(window.location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
  }
  const code = getUrlParameter('code');
  const state = getUrlParameter('state');

  //发起请求
  const baseUrl = 'https://sapi.52santao.com/crm';
  fetch(baseUrl+"/wxLogin/bind?code="+code+"&state="+state)
    .then(response => response.json())
    .then(data => {
      document.getElementById('banding').classList.add('hidden');
      if(data.code !== '000000'){
        document.getElementById('failer').classList.remove('hidden');
        document.getElementById('failer').getElementsByTagName('p')[0].innerHTML = data.msg;
      }else{
        document.getElementById('success').classList.remove('hidden');
      }
    })
    .catch(error => {
      console.error('Error:', error);
    });
</script>
</body>
</html>
