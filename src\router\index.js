import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   //当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               if set true, will always show the root menu
 * //当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 //只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 //若你想不管路由下面的 children 声明的个数都显示你的根路由
 //你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    roles: ['admin','editor']    设置该路由进入的权限，支持多个权限叠加
    title: 'title'               设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             设置该路由的图标
    noCache: true                如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/example/list'  如果设置路径，侧边栏将突出显示您设置的路径
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
//   {
//   path: '/redirect',
//   component: Layout,
//   hidden: true,
//   children: [{
//     path: '/redirect/:path*',
//     component: () => import('@/views/redirect/index')
//   }]
// },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/wxLogin',
    component: () => import('@/views/wxLogin/index'),
    hidden: true
  },
  {
    path: '/codeLogin',
    component: () => import('@/views/wxLogin/codeLogin'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/passwordInfo',
    component: () => import('@/views/login/passwordInfo'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/navigation',
    children: [{
      path: 'navigation',
      component: () => import('@/views/navigation/index'),
      name: 'Navigation',
      meta: {
        title: '首页',
        icon: 'home',
        noCache: false
      }
    }]
  }
  // {
  //   path: '/passwordInfo',
  //   component: Layout,
  //   redirect: '/passwordInfo/index',
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/passwordInfo/index'),
  //     name: 'PasswordInfo',
  //     meta: {
  //       title: '找回密码',
  //       icon: '',
  //       noCache: false
  //     },
  //     hidden: true
  //   }]
  // }

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/handover'
  // }

]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  // 客户管理
  {
    path: '/customer',
    component: Layout,
    alwaysShow: true,
    name: 'Customer',
    redirect: '/customer/list',
    meta: {
      title: '客户管理',
      icon: 'peoples',
      code: 'customer'
    },
    children: [{
      path: 'list',
      component: () => import('@/views/customer/list'),
      name: 'CustomerList',
      meta: {
        title: '客户列表',
        code: 'customer:list',
        noCache: false
      }
    }, {
      path: 'followList',
      component: () => import('@/views/customer/followList'),
      name: 'followList',
      meta: {
        title: '跟进统计数据',
        code: 'customer:follow:list',
        noCache: false
      }
    }, {
      path: 'emptyList',
      component: () => import('@/views/customer/empty'),
      name: 'emptyList',
      meta: {
        title: '区域空白列表',
        code: 'customer:empty',
        noCache: false
      }
    },
    {
      path: 'schoolProject',
      component: () => import('@/views/schoolProject/index'),
      name: 'SchoolProject',
      meta: {
        title: '校区列表',
        code: 'customer:schoolProject',
        noCache: false
      }
    },
    {
      path: 'startupQuery',
      component: () => import('@/views/startup/index'),
      name: 'startupQuery',
      meta: {
        title: '启动期列表',
        code: 'customer:startupQuery',
        noCache: false
      }
    },
    {
      path: 'campus',
      component: () => import('@/views/schoolProject/campus'),
      name: 'Campusmanagement',
      meta: {
        title: '校区管理',
        noCache: true
      },
      hidden: true
    },
    // {
    //   path: 'boxLocation',
    //   component: () => import('@/views/schoolProject/components/boxLocation'),
    //   name: 'BoxLocation',
    //   meta: {
    //     title: '智能终端定位',
    //     noCache: true
    //   },
    //   hidden: true
    // },
    {
      path: 'boxLocation/:mainSchoolId',
      component: () => import('@/views/resources/index'),
      name: 'BoxLocation',
      meta: {
        title: '校区终端定位',
        noCache: true
      },
      props: route => {
        const id = Number(route.params.mainSchoolId)
        const isValidId = !isNaN(id)
        return { mainSchoolId: isValidId ? id : null }
      },
      hidden: true
    },
    {
      path: 'pay',
      component: () => import('@/views/pay/index'),
      name: 'PayList',
      meta: {
        title: '交易记录',
        code: 'customer:payRecord',
        noCache: false
      }
    },
    {
      path: 'create',
      component: () => import('@/views/customer/create'),
      name: 'CustomerCreate',
      meta: {
        title: '新增客户',
        noCache: false
      },
      hidden: true
    },
    {
      path: 'detail/:id(\\d+)',
      component: () => import('@/views/customer/detail'),
      name: 'CustomerDetail',
      meta: {
        title: '客户详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'purchase',
      component: () => import('@/views/customer/purchase'),
      name: 'CustomerPurchase',
      meta: {
        title: '复购交接单',
        noCache: false
      },
      hidden: true
    },
    {
      path: 'distributor',
      component: () => import('@/views/distributor/index'),
      name: 'Distributor',
      meta: {
        title: '经销商申请列表',
        code: 'customer:distributor',
        noCache: false
      }
    },
    {
      path: 'distributorDetail',
      component: () => import('@/views/distributor/detail'),
      name: 'DistributorDetail',
      meta: {
        title: '审批页面',
        code: '',
        noCache: false
      },
      hidden: true
    }
    ]
  },
  // 交接单
  {
    path: '/handover',
    component: Layout,
    alwaysShow: true,
    redirect: '/handover/index',
    meta: {
      title: '交接单管理',
      icon: 'order',
      code: 'order'
    },
    children: [{
      path: 'index',
      component: () => import('@/views/handover/list/index'),
      name: 'Handover',
      meta: {
        title: '交接单列表',
        code: 'order:list',
        noCache: false
      }
    },
    {
      path: 'detail/:clueId(\\d+)',
      component: () => import('@/views/handover/list/components/detail'),
      name: 'HandoverDetail',
      meta: {
        title: '交接单详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'create/:clueId(\\d+)',
      component: () => import('@/views/handover/list/components/create'),
      name: 'HandoverCreate',
      meta: {
        title: '新建交接单'
      },
      hidden: true
    },
    {
      path: 'edit/:clueId(\\d+)',
      component: () => import('@/views/handover/list/components/create'),
      name: 'HandoverEdit',
      meta: {
        title: '交接单修改'
      },
      hidden: true
    },
    {
      path: 'renew/:clueId(\\d+)',
      component: () => import('@/views/handover/list/components/create'),
      name: 'HandoverRenew',
      meta: {
        title: '交接单续约'
      },
      hidden: true
    },
    {
      path: 'upgrade/:clueId(\\d+)',
      component: () => import('@/views/handover/list/components/create'),
      name: 'HandoverUpgrade',
      meta: {
        title: '加盟升级'
      },
      hidden: true
    },
    {
      path: 'paymentRecord',
      component: () => import('@/views/handover/paymentRecord/index'),
      name: 'PaymentRecord',
      meta: {
        title: '打款记录',
        noCache: false,
        code: 'order:paymentRecord'
      }
    },
    {
      path: 'logistics',
      component: () => import('@/views/handover/logistics/index'),
      name: 'Logistics',
      meta: {
        title: '发货单',
        noCache: false,
        code: 'order:logistic'
      }
    },
    {
      path: 'LogisticCreate',
      component: () => import('@/views/handover/logistics/update'),
      name: 'LogisticCreate',
      meta: {
        title: '发货单修改',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'qualifications/:orderId(\\d+)',
      component: () => import('@/views/handover/list/components/qualifications'),
      name: 'Qualifications',
      meta: {
        title: '资质审核'
      },
      hidden: true
    },
    {
      path: 'invoice',
      component: () => import('@/views/handover/list/components/deliver'),
      name: 'Invoice',
      meta: {
        title: '发货单'
      },
      hidden: true
    },
    {
      path: 'refund',
      component: () => import('@/views/handover/list/components/refund'),
      name: 'Refund',
      meta: {
        title: '退款',
        noCache: false
      },
      hidden: true
    }
    ]
  },
  // 合同
  {
    path: '/contract',
    component: Layout,
    alwaysShow: true,
    redirect: '/contract/index',
    meta: {
      title: '合同管理',
      icon: 'contract',
      code: 'contract'
    },
    children: [{
      path: 'index',
      component: () => import('@/views/contract/list/index'),
      name: 'Contract',
      meta: {
        title: '合同列表',
        code: 'contract:list',
        noCache: false
      }
    },
    {
      path: 'common',
      component: () => import('@/views/contract/list/components/common'),
      name: 'CommonContract',
      meta: {
        title: '合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'txt',
      component: () => import('@/views/contract/list/components/txt'),
      name: 'TxtContract',
      meta: {
        title: '陶小桃合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'ai',
      component: () => import('@/views/contract/list/components/ai'),
      name: 'AiContract',
      meta: {
        title: 'AI合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'jt',
      component: () => import('@/views/contract/list/components/jt'),
      name: 'JiatuiContract',
      meta: {
        title: '加推合同详情',
        noCache: true
      },
      hidden: true
    }, {
      path: 'douyin',
      component: () => import('@/views/contract/list/components/douyin'),
      name: 'douyinContract',
      meta: {
        title: '抖音云连锁合同',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'rescission',
      component: () => import('@/views/contract/list/components/rescission'),
      name: 'RescissionContract',
      meta: {
        title: '解约合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'delay',
      component: () => import('@/views/contract/list/components/delay'),
      name: 'DelayContract',
      meta: {
        title: '延期合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'points',
      component: () => import('@/views/contract/list/components/points'),
      name: 'PointsContract',
      meta: {
        title: '抢分合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'yc',
      component: () => import('@/views/contract/list/components/yc'),
      name: 'YC',
      meta: {
        title: '特色班型合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'ycNew',
      component: () => import('@/views/contract/list/components/ycNew'),
      name: 'YCNew',
      meta: {
        title: '烨晨渠道加盟合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'entrance',
      component: () => import('@/views/contract/list/components/entrance'),
      name: 'Entrance',
      meta: {
        title: '高考绝招合同合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'service',
      component: () => import('@/views/contract/list/components/service'),
      name: 'AdmissionsService',
      meta: {
        title: '招生服务合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'features',
      component: () => import('@/views/contract/list/components/features'),
      name: 'Features',
      meta: {
        title: '普高/烨晨 特色班型合同详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'modification',
      component: () => import('@/views/contract/list/components/modification'),
      name: 'Modification',
      meta: {
        title: '变更/补充合同合同详情',
        noCache: true
      },
      hidden: true
    }
    ]
  },
  // 客户资源指派
  {
    path: '/mvp',
    component: Layout,
    alwaysShow: true,
    name: 'Mvp',
    redirect: '/mvp/list',
    meta: {
      title: 'MVP',
      icon: 'assigned',
      code: 'mvp'
    },
    children: [{
      path: 'list',
      component: () => import('@/views/mvp/list'),
      name: 'CustomerAssigned',
      meta: {
        title: '家长线索指派',
        code: 'mvp:list',
        noCache: false
      }
    },
    {
      path: 'onlinePay',
      component: () => import('@/views/mvp/onlinePay'),
      name: 'OnlinePayList',
      meta: {
        title: '线上交易记录',
        code: 'mvp:onlinePay',
        noCache: false
      }
    },
    {
      path: 'logisticsRecord',
      component: () => import('@/views/mvp/logisticsRecord'),
      name: 'LogisticsRecord',
      meta: {
        title: '宝盒物流记录',
        code: 'mvp:logisticsRecord',
        noCache: false
      }
    }]
  },
  {
    path: '/order',
    component: Layout,
    alwaysShow: true,
    redirect: '/order/index',
    meta: {
      title: '订单中心',
      icon: 'orders',
      code: 'orders'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/order/index'),
        name: 'OrderList',
        meta: {
          title: '机构充值统计',
          noCache: false,
          code: 'orders:index'
        }
      }
    ]
  },
  {
    path: '/resources',
    component: Layout,
    alwaysShow: true,
    redirect: '/resources/index',
    meta: {
      title: '资源中心',
      icon: 'resources',
      code: 'resources'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/resources/index'),
        name: 'ResourcesList',
        meta: {
          title: '盒子总览',
          noCache: false,
          code: 'resources:list'
        }
      },
      {
        path: 'student',
        component: () => import('@/views/resources/student'),
        name: 'StudentList',
        meta: {
          title: '学生总览',
          noCache: false,
          code: 'resources:studentList'
        }
      },
      {
        path: 'flatSystem',
        component: () => import('@/views/resources/flatSystem'),
        name: 'FlatSystem',
        meta: {
          title: 'OMO平板管理',
          noCache: false,
          code: ''
        }
      }
    ]
  },
  // cms的课程中心
  {
    path: '/curriculum',
    component: Layout,
    alwaysShow: true,
    name: 'Curriculum',
    redirect: '/curriculum/index',
    meta: {
      title: '课程中心',
      icon: 'class',
      code: 'curriculum'
    },
    children: [
      {
        path: 'series',
        component: () => import('@/views/curriculum/series.vue'),
        name: 'Series',
        meta: {
          title: '系列管理',
          code: 'curriculum:series',
          noCache: false
        }
      },
      {
        path: 'grade',
        component: () => import('@/views/curriculum/grade'),
        name: 'Grade',
        meta: {
          title: '年级管理',
          code: 'curriculum:grade',
          noCache: false
        }
      }, {
        path: 'index',
        component: () => import('@/views/curriculum/index'),
        name: 'ClassType',
        meta: {
          title: '班型管理',
          code: 'curriculum:index',
          noCache: false
        }
      },
      // {
      //   path: 'classmgr',
      //   component: () => import('@/views/coursesync/classlist/index'),
      //   name: 'ClassTypeMgr',
      //   meta: {
      //     title: '草稿箱',
      //     code: '',
      //     noCache: false
      //   }
      // },
      {
        path: 'courseSyncPage',
        component: () => import('@/views/curriculum/courseSyncPage'),
        name: 'CourseSyncPage',
        meta: {
          title: '课程同步',
          code: '',
          noCache: false
        },
        hidden: true
      },
      {
        path: 'classrelation',
        component: () => import('@/views/coursesync/classrelation/index'),
        name: 'ClassTypeRelation',
        meta: {
          title: '同步班型',
          code: '',
          noCache: false
        },
        hidden: true
      },
      {
        path: 'syncPage',
        component: () => import('@/views/curriculum/components/syncPage'),
        name: 'SyncPage',
        meta: {
          title: '同步',
          noCache: true
        },
        hidden: true
      },
      {
        path: 'subjects',
        component: () => import('@/views/curriculum/subjects'),
        name: 'Subjects',
        meta: {
          title: '科目管理',
          code: 'curriculum:subjects',
          noCache: false
        }
      }, {
        path: 'teacher',
        component: () => import('@/views/curriculum/teacher'),
        name: 'Teacher',
        meta: {
          title: '教师管理',
          code: 'curriculum:teacher',
          noCache: false
        }
      },
      {
        path: 'material',
        component: () => import('@/views/curriculum/material'),
        name: 'Material',
        meta: {
          title: '教材管理',
          code: 'curriculum:material',
          noCache: false
        }
      },
      {
        path: 'outline',
        component: () => import('@/views/curriculum/outline'),
        name: 'Outline',
        meta: {
          title: '大纲管理',
          code: 'curriculum:outline',
          noCache: false
        }
      },
      {
        path: 'materialV3',
        component: () => import('@/views/curriculum/materialNew.vue'),
        name: 'MaterialV3',
        meta: {
          title: '教材版本3.0',
          code: 'curriculum:materialV3',
          noCache: false
        }
      },
      {
        path: 'styleModel',
        component: () => import('@/views/curriculum/styleModel'),
        name: 'styleModel',
        meta: {
          title: '科目样式管理',
          code: 'curriculum:style',
          noCache: false
        }
      }, {
        path: 'knowledgeModel',
        component: () => import('@/views/curriculum/knowledgeModel'),
        name: 'knowledgeModel',
        meta: {
          title: '知识模块',
          code: 'curriculum:knowledge',
          noCache: false
        }
      },
      {
        path: 'course',
        component: () => import('@/views/curriculum/course'),
        name: 'Course',
        meta: {
          title: '课程管理',
          code: 'curriculum:course',
          noCache: false
        }
      },
      {
        path: 'knowledgeFlashTest',
        component: () => import('@/views/curriculum/knowledgeFlashTest'),
        name: 'KnowledgeFlashTest',
        meta: {
          title: '闪测知识点',
          code: 'curriculum:knowledgeFlashTest',
          noCache: false
        }
      },
      {
        path: 'knowledgeConfiguration',
        component: () => import('@/views/curriculum/knowledgeConfiguration'),
        name: 'KnowledgeConfiguration',
        meta: {
          title: '知识点配置',
          code: 'curriculum:knowledgeConfiguration',
          noCache: false
        }
      },
      {
        path: 'exercises/:id(\\d+)',
        component: () => import('@/views/curriculum/exercises'),
        name: 'Exercises',
        meta: {
          title: '课程习题',
          code: 'curriculum:course',
          noCache: false
        },
        hidden: true
      }]
  },

  // 新版课程中心
  // {
  //   path: '/curriculum',
  //   component: Layout,
  //   alwaysShow: true,
  //   name: 'Curriculum',
  //   redirect: '/curriculum/index',
  //   meta: {
  //     title: '课程中心3.0',
  //     icon: 'class',
  //     code: 'curriculum'
  //   },
  //   children: [{
  //     path: 'grade',
  //     component: () => import('@/views/curriculum/grade'),
  //     name: 'Grade',
  //     meta: {
  //       title: '年级管理',
  //       code: 'curriculum:grade',
  //       noCache: false
  //     }
  //   }, {
  //     path: 'index',
  //     component: () => import('@/views/curriculum/index'),
  //     name: 'ClassType',
  //     meta: {
  //       title: '班型管理',
  //       code: 'curriculum:index',
  //       noCache: false
  //     }
  //   },
  //     {
  //       path: 'classmgr',
  //       component: () => import('@/views/coursesync/classlist/index'),
  //       name: 'ClassTypeMgr',
  //       meta: {
  //         title: '草稿箱',
  //         code: '',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'courseSyncPage',
  //       component: () => import('@/views/curriculum/courseSyncPage'),
  //       name: 'CourseSyncPage',
  //       meta: {
  //         title: '课程同步',
  //         code: '',
  //         noCache: false
  //       },
  //       hidden: true
  //     },
  //     {
  //       path: 'classrelation',
  //       component: () => import('@/views/coursesync/classrelation/index'),
  //       name: 'ClassTypeRelation',
  //       meta: {
  //         title: '同步班型',
  //         code: '',
  //         noCache: false
  //       },
  //       hidden: true
  //     },
  //     {
  //       path: 'syncPage',
  //       component: () => import('@/views/curriculum/components/syncPage'),
  //       name: 'SyncPage',
  //       meta: {
  //         title: '同步',
  //         noCache: true
  //       },
  //       hidden: true
  //     },
  //     {
  //       path: 'subjects',
  //       component: () => import('@/views/curriculum/subjects'),
  //       name: 'Subjects',
  //       meta: {
  //         title: '科目管理',
  //         code: 'curriculum:subjects',
  //         noCache: false
  //       }
  //     }, {
  //       path: 'teacher',
  //       component: () => import('@/views/curriculum/teacher'),
  //       name: 'Teacher',
  //       meta: {
  //         title: '教师管理',
  //         code: 'curriculum:teacher',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'material',
  //       component: () => import('@/views/curriculum/material'),
  //       name: 'Material',
  //       meta: {
  //         title: '教材管理',
  //         code: 'curriculum:material',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'outline',
  //       component: () => import('@/views/curriculum/outline'),
  //       name: 'Outline',
  //       meta: {
  //         title: '大纲管理',
  //         code: 'curriculum:outline',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'course',
  //       component: () => import('@/views/curriculum/course'),
  //       name: 'Course',
  //       meta: {
  //         title: '课程管理',
  //         code: 'curriculum:course',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'exercises/:id(\\d+)',
  //       component: () => import('@/views/curriculum/exercises'),
  //       name: 'Exercises',
  //       meta: {
  //         title: '课程习题',
  //         code: 'curriculum:course',
  //         noCache: false
  //       },
  //       hidden: true
  //     }]
  // },
  // 商品中心
  {
    path: '/goods',
    component: Layout,
    alwaysShow: true,
    redirect: '/goods/index',
    meta: {
      title: '商品中心',
      icon: 'goods',
      code: 'goods'
    },
    children: [{
      path: 'index',
      component: () => import('@/views/goods/index'),
      name: 'Goods',
      meta: {
        title: '商品管理',
        code: 'goods',
        noCache: false
      }
    }]
  },

  // 暑期招生
  // {
  //   path: '/admissions',
  //   component: Layout,
  //   alwaysShow: true,
  //   redirect: '/admissions/index',
  //   meta: {
  //     title: '试题管理',
  //     icon: 'student',
  //     code: 'admissions'
  //   },
  //   children: []
  // },
  {
    path: '/figure',
    component: Layout,
    alwaysShow: true,
    redirect: '/figure/index',
    meta: {
      title: '报表统计',
      icon: 'figure',
      code: 'figure'
    },
    children: [{
      path: 'index',
      component: () => import('@/views/figure/index'),
      name: 'PerformanceStatistics',
      meta: {
        title: '财务业绩报表',
        code: 'figure:PerformanceStatistics',
        noCache: false,
        showFlags: 'Performance'
      }
    },
    {
      path: 'cwmxFigure',
      component: () => import('@/views/figure/index'),
      name: 'cwmxFigure',
      meta: {
        title: '财务业绩明细报表',
        code: 'figure:cwmxFigure',
        noCache: false,
        showFlags: 'cwmx'
      }
    },
    {
      path: 'handoverFigure',
      component: () => import('@/views/figure/index'),
      name: 'HandoverFigure',
      meta: {
        title: '交接单月度报表',
        code: 'figure:HandoverFigure',
        noCache: false,
        showFlags: 'Handover'
      }
    },
    {
      path: 'areaFigure',
      component: () => import('@/views/figure/index'),
      name: 'AreaFigure',
      meta: {
        title: '区域校区统计报表',
        code: 'figure:areaFigure',
        noCache: false,
        showFlags: 'Area'
      }
    },
    {
      path: 'studentFigure',
      component: () => import('@/views/figure/index'),
      name: 'StudentFigure',
      meta: {
        title: '学生人数统计报表',
        code: 'figure:StudentFigure',
        noCache: false,
        showFlags: 'Student'
      }
    },
    {
      path: 'smartFigure',
      component: () => import('@/views/figure/index'),
      name: 'SmartFigure',
      meta: {
        title: '智能终端统计报表',
        code: 'figure:smartFigure',
        noCache: false,
        showFlags: 'Smart'
      }
    },
    {
      path: 'classListFigure',
      component: () => import('@/views/figure/index'),
      name: 'ClassListFigure',
      meta: {
        title: '班型消耗报表',
        code: 'figure:classListFigure',
        noCache: false,
        showFlags: 'ClassList'
      }
    },
    {
      path: 'subjectFigure',
      component: () => import('@/views/figure/index'),
      name: 'SubjectFigure',
      meta: {
        title: '班型科目老师课耗报表',
        code: 'figure:subjectFigure',
        noCache: false,
        showFlags: 'Subject'
      }
    }, {
      path: 'ycsubjectFigure',
      component: () => import('@/views/figure/index'),
      name: 'ycSubjectFigure',
      meta: {
        title: '烨晨班型科目老师课耗报表',
        code: 'figure:ycsubjectFigure',
        noCache: false,
        showFlags: 'ycSubject'
      }
    },
    {
      path: 'provinceFigure',
      component: () => import('@/views/figure/index'),
      name: 'ProvinceFigure',
      meta: {
        title: '省份充值课耗统计报表',
        code: 'figure:provinceFigure',
        noCache: false,
        showFlags: 'Province'
      }
    },
    {
      path: 'czmxbbFigure',
      component: () => import('@/views/figure/index'),
      name: 'czmxbbFigure',
      meta: {
        title: '充值明细报表',
        code: 'figure:czmxbbFigure',
        noCache: false,
        showFlags: 'czmxbb'
      }
    },
    {
      path: 'pgyysjtjFigure',
      component: () => import('@/views/figure/index'),
      name: 'pgyysjtjFigure',
      meta: {
        title: '普高每日运营报表',
        code: 'figure:pgyysjtjFigure',
        noCache: false,
        showFlags: 'pgyysjtj'
      }
    }, {
      path: 'ykyysjtjFigure',
      component: () => import('@/views/figure/index'),
      name: 'ykyysjtjFigure',
      meta: {
        title: '艺考每日运营报表',
        code: 'figure:ykyysjtjFigure',
        noCache: false,
        showFlags: 'ykyysjtj'
      }
    },
    {
      path: 'ycyysjtjFigure',
      component: () => import('@/views/figure/index'),
      name: 'ycyysjtjFigure',
      meta: {
        title: '烨晨每日运营报表',
        code: 'figure:ycyysjtjFigure',
        noCache: false,
        showFlags: 'ycyysjtj'
      }
    },
    {
      path: 'sanTaoFigure',
      component: () => import('@/views/figure/index'),
      name: 'SanTaoFigure',
      meta: {
        title: '普高运营数据看板',
        code: 'figure:sanTaoFigure',
        noCache: false,
        showFlags: 'SanTao'
      }
    },
    {
      path: 'ykkbFigure',
      component: () => import('@/views/figure/index'),
      name: 'ykkbFigure',
      meta: {
        title: '艺考运营数据看板',
        code: 'figure:ykkbFigure',
        noCache: false,
        showFlags: 'ykkb'
      }
    },
    {
      path: 'yckbFigure',
      component: () => import('@/views/figure/index'),
      name: 'yckbFigure',
      meta: {
        title: '烨晨运营数据看板',
        code: 'figure:yckbFigure',
        noCache: false,
        showFlags: 'yckb'
      }
    },
    {
      path: 'xqbxFigure',
      component: () => import('@/views/figure/index'),
      name: 'xqbxFigure',
      meta: {
        title: '校区班型课耗报表',
        code: 'figure:xqbxFigure',
        noCache: false,
        showFlags: 'xqbx'
      }
    },
    {
      path: 'zxhzFigure',
      component: () => import('@/views/figure/index'),
      name: 'zxhzFigure',
      meta: {
        title: '在线盒子版本统计',
        code: 'figure:zxhzFigure',
        noCache: false,
        showFlags: 'zxhz'
      }
    }]
  },
  // 官网管理
  {
    path: '/website',
    component: Layout,
    alwaysShow: true,
    redirect: '/website/banner',
    meta: {
      title: '官网管理',
      icon: 'eye',
      code: 'website'
    },
    children: [{
      path: 'banner',
      component: () => import('@/views/website/banner'),
      name: 'banner',
      meta: {
        title: 'banner图',
        code: 'website:banner',
        noCache: false
      }
    },
    {
      path: 'newsList',
      component: () => import('@/views/website/newsList'),
      name: 'newsList',
      meta: {
        title: '新闻列表',
        code: 'website:news',
        noCache: false
      }
    },
    {
      path: 'listenList',
      component: () => import('@/views/website/listenList'),
      name: 'listenList',
      meta: {
        title: '预约试听列表',
        code: 'website:appointment',
        noCache: false
      }
    },
    {
      path: 'agentList',
      component: () => import('@/views/website/agentList'),
      name: 'agentList',
      meta: {
        title: '申请代理列表',
        code: 'website:agent',
        noCache: false
      }
    },
    {
      path: 'cooperationPlan',
      component: () => import('@/views/website/cooperationPlan'),
      name: 'CooperationPlan',
      meta: {
        title: '合作方案列表',
        code: 'website:cooperation',
        noCache: false
      }
    }
    ]
  },
  {
    path: '/posters',
    component: Layout,
    alwaysShow: true,
    redirect: '/posters/index',
    meta: {
      title: '产品线设置',
      icon: 'poster',
      code: 'posters'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/posters/index'),
        name: 'Posters',
        meta: {
          title: '海报管理',
          code: 'posters:index',
          noCache: false
        }
      },
      {
        path: 'terminalSet',
        component: () => import('@/views/posters/terminalSet'),
        name: 'TerminalSet',
        meta: {
          title: '终端设置',
          code: 'poster:terminalSet',
          noCache: false
        }
      },
      {
        path: 'questionsDetail',
        component: () => import('@/views/admissions/components/questionsDetail'),
        name: 'QuestionsDetail',
        meta: {
          title: '试卷试题',
          noCache: true
        },
        hidden: true
      },
      {
        path: 'courseDetail',
        component: () => import('@/views/admissions/components/courseDetail'),
        name: 'CourseDetail',
        meta: {
          title: '试题编辑',
          noCache: true
        },
        hidden: true
      }, {
        path: 'courseUpload',
        component: () => import('@/views/admissions/components/courseUpload'),
        name: 'CourseUpload',
        meta: {
          title: '试题编辑',
          noCache: true
        },
        hidden: true
      },
      {
        path: 'story',
        component: () => import('@/views/admissions/story'),
        name: 'Story',
        meta: {
          title: '烨晨故事列表',
          noCache: true,
          code: 'admissions:storys'
        }
      },
      {
        path: 'addStory',
        component: () => import('@/views/admissions/components/addStory'),
        name: 'AddStory',
        meta: {
          title: '新增故事',
          noCache: true,
          code: ''
        },
        hidden: true
      },
      {
        path: '/mini/config',
        component: () => import('@/views/download/index'),
        name: 'miniConfig',
        // 传入参数
        props: {
          menuType: 'miniConfig'
        },
        meta: {
          title: '合伙人小程序设置',
          code: 'system:mini:config',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/download',
    component: Layout,
    alwaysShow: true,
    redirect: '/posters/index',
    meta: {
      title: '下载中心管理',
      icon: 'download',
      code: 'download'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/download/index'),
        name: 'Download',
        meta: {
          title: '下载中心',
          code: 'download:center',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/train',
    component: Layout,
    alwaysShow: true,
    redirect: '/posters/index',
    meta: {
      title: '客户培训中心',
      icon: 'download',
      code: 'train'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/download/index'),
        name: 'Download',
        // 传入参数
        props: {
          menuType: 'train'
        },
        meta: {
          title: '培训资料管理',
          code: 'train:upload',
          noCache: false
        }
      },
      {
        path: 'study',
        component: () => import('@/views/customer/studyRecordList.vue'),
        // 传入参数
        props: {
          clientCode: 9
        },
        name: 'studyRecordList',
        meta: {
          title: '客户学习记录',
          code: 'train:studyRecordList',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/train_employ',
    component: Layout,
    alwaysShow: true,
    redirect: '/posters/index',
    meta: {
      title: '铁军培训中心',
      icon: 'download',
      code: 'train:employ'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/download/index'),
        name: 'Download',
        // 传入参数
        props: {
          menuType: 'train_employ'
        },
        meta: {
          title: '铁军培训资料管理',
          code: 'train:employ:upload',
          noCache: false
        }
      },
      {
        path: 'study',
        component: () => import('@/views/customer/studyRecordList.vue'),
        name: 'studyRecordList',
        // 传入参数
        props: {
          clientCode: 10
        },
        meta: {
          title: '铁军学习记录',
          code: 'train:employ:studyRecordList',
          noCache: false
        }
      }
    ]
  },

  // 消息中心
  {
    path: '/message',
    component: Layout,
    alwaysShow: true,
    redirect: '/message/index',
    meta: {
      title: '消息中心',
      icon: 'message',
      code: 'message'
    },
    children: [{
      path: 'index',
      component: () => import('@/views/message/index'),
      name: 'MessageList',
      meta: {
        title: '业务消息',
        code: 'message',
        noCache: false
      }
    }, {
      path: 'renewal',
      component: () => import('@/views/message/renewal'),
      name: 'RenewalReminder',
      meta: {
        title: '续约消息',
        code: 'message:renewal',
        noCache: false
      }
    }, {
      path: 'carousel',
      component: () => import('@/views/message/carousel'),
      name: 'Carousel',
      meta: {
        title: '轮播图消息',
        code: 'message:carousel',
        noCache: false
      }
    }]
  },
  // 意见反馈
  {
    path: '/feedback',
    component: Layout,
    alwaysShow: true,
    redirect: '/feedback/institutions',
    meta: {
      title: '意见反馈',
      icon: 'feedback',
      code: 'feedback'
    },
    children: [
      {
        path: 'requirements',
        component: () => import('@/views/feedback/requirements'),
        name: 'Requirements',
        meta: {
          title: '需求处理',
          code: 'feedback:requirements',
          noCache: false
        }
      },
      {
        path: 'requirementsInfo',
        component: () => import('@/views/feedback/components/requirementsInfo'),
        name: 'RequirementsInfo',
        meta: {
          title: '需求处理详情',
          code: '',
          noCache: false
        },
        hidden: true
      },
      {
        path: 'student',
        component: () => import('@/views/feedback/student'),
        name: 'StudentFeedback',
        meta: {
          title: '学生反馈',
          code: 'feedback:student',
          noCache: false
        }
      },
      {
        path: 'system',
        component: () => import('@/views/feedback/system'),
        name: 'SystemFeedback',
        meta: {
          title: '机构意见反馈',
          code: 'feedback:institutions',
          noCache: false
        }
      }, {
        path: 'feedbackInfo',
        component: () => import('@/views/feedback/components/feedbackInfo'),
        name: 'FeedbackInfo',
        meta: {
          title: '系统反馈详情',
          code: '',
          noCache: false
        },
        hidden: true
      }, {
        path: 'feedbackRanking',
        component: () => import('@/views/feedback/feedbackRanking'),
        name: 'FeedbackRanking',
        meta: {
          title: '系统服务排名',
          code: 'feedback:feedbackRanking',
          noCache: false
        }
      },
      // 常见问题
      {
        path: 'index',
        component: () => import('@/views/faq/index'),
        name: 'FAQ',
        meta: {
          title: '常见问题列表',
          code: 'faq',
          noCache: false
        }
      }, {
        path: 'title',
        component: () => import('@/views/faq/title'),
        name: 'Title',
        meta: {
          title: '标题管理',
          code: '',
          noCache: false
        },
        hidden: true
      }]
  },
  {
    path: '/setting',
    component: Layout,
    alwaysShow: true,
    redirect: '/setting/menu',
    meta: {
      title: '系统设置',
      icon: 'set',
      code: 'setting'
    },
    children: [{
      path: 'menu',
      component: () => import('@/views/setting/menu/index'),
      name: 'Menu',
      meta: {
        title: '菜单管理',
        noCache: false,
        code: 'setting:menu'
      }
    },
    {
      path: 'institutions',
      component: () => import('@/views/setting/menu/institutions'),
      name: 'Institutions',
      meta: {
        title: '机构后台管理',
        code: 'setting:institutions',
        noCache: false
      }
    },
    {
      path: 'urlConfig',
      hidden: true,
      component: () => import('@/views/setting/menu/urlConfig'),
      name: 'urlConfig',
      meta: {
        title: 'url配置',
        noCache: false,
        activeMenu: '/setting/institutions'
      }
    },
    {
      path: 'role',
      component: () => import('@/views/setting/role/index'),
      name: 'Role',
      meta: {
        title: '角色管理',
        noCache: false,
        code: 'setting:role'
      }
    },
    {
      path: 'employee',
      component: () => import('@/views/setting/employee/index'),
      name: 'Employee',
      meta: {
        title: '员工管理',
        noCache: false,
        code: 'setting:employee'
      }
    },
    {
      path: 'employeeDetail',
      component: () => import('@/views/setting/employee/detail'),
      name: 'EmployeeDetail',
      meta: {
        title: '员工信息详情',
        noCache: true
      },
      hidden: true
    },
    {
      path: 'department',
      component: () => import('@/views/setting/department/index'),
      name: 'Department',
      meta: {
        title: '部门管理',
        noCache: false,
        code: 'setting:department'
      }
    },
    {
      path: 'field',
      component: () => import('@/views/setting/field/index'),
      name: 'field',
      meta: {
        title: '字段管理',
        noCache: false,
        code: 'setting:field'
      }
    },
    {
      path: 'dictionary',
      component: () => import('@/views/setting/dictionary/index'),
      name: 'Dictionary',
      meta: {
        title: '数据字典',
        noCache: false,
        code: 'setting:dictionary'
      }
    },
    {
      path: 'policy',
      component: () => import('@/views/setting/policy/index'),
      name: 'Dictionary',
      meta: {
        title: '套餐维护',
        noCache: false,
        code: 'setting:policy'
      }
    }, {
      path: 'address',
      component: () => import('@/views/setting/address/index'),
      name: 'Address',
      meta: {
        title: '地址库',
        noCache: false,
        code: 'setting:address'
      }
    },
    {
      path: 'school',
      component: () => import('@/views/setting/school/index'),
      name: 'School',
      meta: {
        title: '学校管理',
        noCache: false,
        code: 'setting:school'
      }
    },
    {
      path: 'appUpdate',
      component: () => import('@/views/setting/appUpdate/index'),
      name: 'AppUpdate',
      meta: {
        title: 'APP升级管理',
        noCache: false,
        code: 'setting:appUpdate'
      }
    },
    {
      path: 'operating',
      component: () => import('@/views/setting/operating/index'),
      name: 'Operating',
      meta: {
        title: '运营经理设置',
        noCache: false,
        code: 'setting:opera'
      }
    }
    ]
  },
  // 404 page must be placed at the end !!!
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
export default router
