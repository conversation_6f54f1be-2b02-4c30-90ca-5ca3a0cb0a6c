{"name": "vue-element-admin", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:prodAndOpen": "vue-cli-service build && start . ", "build:stage": "vue-cli-service build --mode staging", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode pre", "build:testAndOpen": "vue-cli-service build --mode pre  && start . ", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "deploy:test": "npm run build:test && node deploy.js"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@wecom/jssdk": "^2.3.1", "af-table-column": "^1.0.3", "ali-oss": "^6.9.1", "animate.css": "^4.1.0", "axios": "0.18.1", "clipboard": "^2.0.4", "cluster": "^0.7.7", "codemirror": "5.45.0", "dns": "^0.2.2", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^4.2.1", "element-ui": "^2.13.0", "esdk-obs-nodejs": "^3.20.7", "file-saver": "2.0.1", "fuse.js": "3.4.4", "http-proxy-middleware": "^1.0.6", "install": "^0.13.0", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcode": "^1.5.4", "repl": "^0.1.3", "screenfull": "4.2.0", "showdown": "1.9.0", "sortablejs": "^1.8.4", "streamsaver": "^2.0.5", "uuid": "^8.2.0", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-count-to": "1.0.13", "vue-drag-it-dude": "^1.3.0", "vue-drag-resize": "^1.4.2", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "^2.20.0", "vuex": "3.1.0", "vuex-persistedstate": "^2.7.0", "wangeditor": "^3.1.1", "xlsx": "0.14.1", "zip-stream": "^4.0.2"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "copy-webpack-plugin": "^6.3.2", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "plop": "2.3.0", "postcss": "^8.0.0", "postcss-loader": "^4.0.0", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "scp2": "^0.5.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "tailwindcss": "^3.4.17", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "volta": {"node": "14.0.0"}}