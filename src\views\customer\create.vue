<template>
  <el-dialog
    v-if="dialogCreateCustomer"
    v-el-drag-dialog
    :close-on-click-modal="!dialogCreateCustomer"
    width="40%"
    top="0"
    @close="dialogCreateCustomer = false;isDisabledMobile = false"
    :title="title"
    :visible.sync="dialogCreateCustomer"
  >
    <el-form ref="detailForm" :model="customer" :rules="customerRules" class="demo-form-inline" label-width="100px">
      <el-row>
         <el-col :xs="{span:24}" :sm="{span:14}">
          <el-form-item label="手机号码" prop="mobile">
            <el-input :disabled="isDisabledMobile" v-model="customer.mobile" maxlength="11" />
          </el-form-item>
        </el-col>
         <el-col :xs="{span:24}" :sm="{span:10}" class="data-btn">
          <el-form-item label="">
            <el-button v-if="!isEdit" :disabled="!customer.mobile || (customer.mobile && customer.mobile.length !== 11)" type="primary" @click="getData">获取尘锋CRM数据</el-button>
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:14}">
          <el-form-item label="客户名称" prop="customer">
            <el-input v-model="customer.customer" minlength="2" maxlength="15" />
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:14}" v-if="isEdit">
          <el-form-item label="客户类型" prop="customerType">
            <el-select disabled v-model="customer.customerType" placeholder="客户类型" clearable class="filter-item" style="width: 100%;" filterable>
              <el-option v-for="item in customerTypeList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:14}">
          <el-form-item label="校区名称" prop="institution">
            <el-input v-model="customer.institution" maxlength="40" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所在区域">
            <area-picker
              :area-list="areaList"
              :level="'3'"
              :area-style="'width:100%'"
              class="filter-item"
              @getAreaList="getAreaList"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="机构地址" prop="address">
            <el-input v-model="customer.address" maxlength="50" />
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:14}">
          <el-form-item label="信息来源" prop="originName">
            <el-input v-model="customer.originName"  maxlength="20"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="customer.remark" type="textarea" maxlength="255" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCreateCustomer = false;isDisabledMobile = false">取 消</el-button>
      <el-button v-if="!isEdit" type="primary" @click="confirmCreate" :disabled="!isDisabledMobile">确 定</el-button>
      <el-button v-if="isEdit" type="primary" @click="confirmUpdate">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCustomerDetail, createCustomer, updateCustomer, getCfCrmData } from '@/api/customer'
import { intentionList, customerTypeList } from '@/utils/field-conver'
import AreaPicker from '@/components/area-picker'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { validPhone, customer, weixin, mail } from '@/utils/validate.js'
export default {
  name: 'CustomerCreate',
  directives: { elDragDialog },
  components: { AreaPicker },
  props: {
    title: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogCreateCustomer: false,
      customer: {},
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      isDisabledMobile: false,
      intentionList: intentionList,
      customerTypeList: customerTypeList,
      customerRules: {
        customer: {
          required: true,
          trigger: 'blur',
          validator: customer
        },
        mobile: { required: true, validator: validPhone, trigger: 'blur' },
        institution: {
          required: true,
          message: '请输入校区名称 ',
          trigger: 'blur'
        },
        address: {
          required: true,
          message: '请输入机构地址 ',
          trigger: 'blur'
        },
        originName: {
          required: true,
          message: '请填写信息来源',
          trigger: 'blur'
        },
        customerType: {
          required: true,
          message: '请选择客户级类型',
          trigger: 'blur'
        },
        weixin: {
          required: false,
          validator: weixin,
          trigger: 'blur'
        },
        qq: {
          required: false,
          message: '请输入正确的QQ号',
          trigger: 'blur'
        },
        mail: {
          required: false,
          trigger: 'blur',
          validator: mail
        }
      },
      CreateSchoolFlags: false,
      UpdateSchoolFlags: false
    }
  },
  mounted() {
    this.isDisabledMobile = false;
  },
  methods: {
    openDialog() {
      this.customer = {
      }
      this.areaList = {
        provinceId: '',
        cityId: '',
        areaId: ''
      }
      this.dialogCreateCustomer = true
      this.$set(this.customer, 'customerType', 1)
    },
    updateInfo(params) {
      getCustomerDetail(params).then(res => {
        this.customer = res.data
        this.areaList = {
          provinceId: this.customer.provinceId,
          cityId: this.customer.cityId,
          areaId: this.customer.areaId
        }
        this.dialogCreateCustomer = true
      })
    },
    getData() {
      const mobile = this.customer.mobile
      getCfCrmData({mobile: this.customer.mobile}).then(res => {
        this.customer = res.data
        this.areaList = {
          provinceId: this.customer.provinceId,
          cityId: this.customer.cityId,
          areaId: this.customer.areaId
        }
        if (res.data.mobile === mobile) {
          this.isDisabledMobile = true
          this.customer.mobile = mobile
        }
        this.dialogCreateCustomer = true
      })
    },
    confirmCreate() {
      this.CreateSchoolFlags = true
      const params = Object.assign(this.customer, this.areaList)
      if (!this.areaList.provinceId) {
        this.$message({
          message: '请选择省份',
          type: 'warning'
        })
        this.CreateSchoolFlags = false
        return
      } else if (!this.areaList.cityId) {
        this.$message({
          message: '请选择城市',
          type: 'warning'
        })
        this.CreateSchoolFlags = false
        return
      } else if (!this.areaList.areaId) {
        this.$message({
          message: '请选择区域',
          type: 'warning'
        })
        this.CreateSchoolFlags = false
        return
      }

      this.$refs['detailForm'].validate(valid => {
        if (valid) {
          createCustomer(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增客户成功',
                type: 'success'
              })
              this.dialogCreateCustomer = false
              this.isDisabledMobile = false
              this.$emit('updateList')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    confirmUpdate() {
      this.UpdateSchoolFlags = true
      const params = Object.assign(this.customer, this.areaList)
      if (!this.areaList.provinceId || !this.areaList.cityId || !this.areaList.areaId) {
        this.$message({
          message: '所在区域填写有误',
          type: 'warning'
        })
        return
      }
      this.$refs['detailForm'].validate(valid => {
        if (valid) {
          updateCustomer(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '客户信息修改成功',
                type: 'success'
              })
              this.dialogCreateCustomer = false
              this.UpdateSchoolFlags = false
              this.isDisabledMobile = false
              this.$emit('updateList')
            }
          }).catch(() => {
            this.UpdateSchoolFlags = false

          })
        } else {

          return false
        }
      })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    }
  }
}
</script>

<style scoped lang="scss">
  .data-btn  {
    /deep/ .el-form-item__content {
      margin-left: 20px !important;
    }
  }
</style>
