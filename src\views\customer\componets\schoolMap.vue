<template>
  <el-dialog
    v-if="innerVisible"
    v-el-drag-dialog
    width="900px"
    title="获取经纬度"
    :close-on-click-modal="!innerVisible"
    :visible.sync="innerVisible"
  >
    <el-form label-width="100px">
      <el-row :gutter="22">
        <el-col>
          <el-form-item label="签约区域：">
            <area-picker
              :area-list="areaList"
              :level="'4'"
              area-style="width:100%"
              class="filter-item"
              @getAreaList="getAreaList"
              @getAreaName="getAreaName"
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="详细地址：">
            <el-input v-model="addressDetail" maxlength="50">
              <el-button slot="append" @click="getMap">点击查看</el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="lng&&lat&&center.length>0" class="map">
        <el-amap ref="map" vid="amapDemo" :center="center" :zoom="zoom" :events="events" class="amap-demo">
          <el-amap-marker :position="center" :icon="icon" :events="events" />
          <el-amap-circle
            :center="center"
            :radius="defaulRadius"
            :fill-opacity="0.3"
            :stroke-weight="0"
            :fill-color="'#409EFF'"
            :stroke-color="'#409EFF'"
            :events="events"
          />
        </el-amap>
      </div>
      <div v-else class="no-data">未获取到经纬度，请从新选择地址</div>
    </el-form>
    <div v-if="lng&&lat&&center.length>0" slot="footer" class="dialog-footer text-center">
      <el-button @click="innerVisible = false">取 消</el-button>
      <el-button type="primary" :disabled="!hadGetLngLat" @click="confirmLngLat">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import AreaPicker from '@/components/area-picker'
import axios from 'axios'
import Icon from '@/assets/img/marker.png'
export default {
  name: 'CreateNewSchool',
  components: {
    AreaPicker
  },
  directives: {
    elDragDialog
  },
  data() {
    const self = this
    return {
      hadGetLngLat: false,
      zoom: 13,
      center: [121.59996, 31.197646],
      defaulRadius: 3000, // 默认招生范围半径1000米
      icon: Icon,
      events: {
        init: (o) => {},
        'moveend': () => {},
        'zoomchange': () => {},
        'click': (e) => {
          self.viewIp(e)
        }
      },
      innerVisible: false,
      areaNameStr: '',
      addressDetail: '',
      lng: '',
      lat: '',
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      }
    }
  },
  created() {
    // this.init()
  },
  methods: {
    /**
       * 点击获取地址
       * @returns {Promise<any>}
       */
    viewIp(e) {
      const {
        lng,
        lat
      } = e.lnglat
      this.lng = lng
      this.lat = lat
      // 这里通过高德 SDK 完成。
      const geocoder = new AMap.Geocoder({
        radius: 2500,
        extensions: 'all'
      })
      const that = this
      geocoder.getAddress([lng, lat], function(status, result) {
        if (status === 'complete' && result.info === 'OK') {
          if (result && result.regeocode) {
            // self.address = result.regeocode.formattedAddress;

            that.addressDetail = result.regeocode.formattedAddress ? result.regeocode.formattedAddress : ''
            that.center = [lng, lat]

          }
        }
      })
    },
    /**
       * 通过地址获取坐标
       * @returns {Promise<any>}
       */
    getMap() {
      const that = this
      const url = 'https://restapi.amap.com/v3/geocode/geo'





      const address = this.areaNameStr + this.addressDetail
      const params = {
        key: '8c0a773c6b49528f726dce0d937a2e73',
        address: address
      }
      if (!that.areaList.provinceId || !that.areaList.cityId || !that.areaList.areaId) {
        that.$message({
          type: 'warning',
          message: '请选择签约区域'
        })
      } else if (!that.addressDetail) {
        that.$message({
          type: 'warning',
          message: '请输入详细地址'
        })
      } else {
        axios.get(url, {
          params: params
        }).then(res => {

          if (res.data.status === '1') {
            const geocodes = res.data.geocodes

            if (geocodes.length > 0) {
              const fGeocode = geocodes.length > 0 ? geocodes[0] : undefined
              that.center = fGeocode ? fGeocode.location.split(',') : []
              that.lng = that.center.length > 0 ? that.center[0] : ''
              that.lat = that.center.length > 0 ? that.center[1] : ''
              that.hadGetLngLat = true
            } else {
              that.$message({
                type: 'warning',
                message: '未获取到经纬度'
              })
              that.center = []
              that.lng = ''
              that.lat = ''
            }
          } else {
            that.$message({
              type: 'warning',
              message: '地址获取失败'
            })
            that.center = []
            that.lng = ''
            that.lat = ''
          }
        })
      }
    },
    /**
       * 获取经纬度
       */

    confirmLngLat() {
      this.$emit('refresh', {
        addressDetail: this.addressDetail,
        lng: this.lng,
        lat: this.lat
      })
      this.innerVisible = false
    },
    getLonAndLat(area, address) {

      this.innerVisible = true
      this.areaList = area
      this.addressDetail = address
    },
    getMapData(areas, addressDetail) { // 从修改校区带地址，根据地址获取经纬度


      const that = this
      const urls = 'https://restapi.amap.com/v3/geocode/geo'
      const addressMap = areas + addressDetail
      const datas = {
        key: '8c0a773c6b49528f726dce0d937a2e73',
        address: addressMap
      }
      axios.get(urls, {
        params: datas
      }).then(res => {
        if (res.data.status === '1') {
          const geocodes = res.data.geocodes
          if (geocodes.length > 0) {
            const fGeocode = geocodes.length > 0 ? geocodes[0] : undefined
            that.center = fGeocode ? fGeocode.location.split(',') : []
            that.lng = that.center && that.center.length > 0 ? that.center[0] : ''
            that.lat = that.center && that.center.length > 0 ? that.center[1] : ''
            that.hadGetLngLat = true
          } else {
            that.$message({
              type: 'warning',
              message: '未获取到经纬度'
            })
            that.center = []
            that.lng = ''
            that.lat = ''
          }
        } else {
          that.$message({
            type: 'warning',
            message: '地址获取失败'
          })
          that.center = []
          that.lng = ''
          that.lat = ''
        }
      })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
      this.areaList.countyId = data.countyId
    },
    getAreaName(name) {
      this.areaNameStr = ''
      this.areaNameStr = (name.cityName || '') + (name.areaName || '') + (name.countyName || '') + (name.zhenName ||
          '')
    }
  }
}
</script>

<style scoped>
  .amap-demo {
    height: 500px;
    width: 100%;
  }
  .no-data{
    text-align: center;
    font-size: 18px;
    color: #666;
    padding: 20px 0;
  }
</style>
