<template>
  <el-dialog :visible.sync="examinationPop" :title="examinationTitle" :close-on-click-modal="!examinationPop" width="60%">
    <div class="assing-info">
      <el-form :model="listQuery" :rules="rules" label-width="100px">
        <el-form-item label="大纲名称" prop="status">
          <el-select v-model="listQuery.assignSatuts" placeholder="请选择大纲名称" filterable clearable class="filter-item">
            <el-option v-for="item in assignSatuts" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="考点名称" prop="name">
          <el-input v-model="listQuery.name" placeholder="请输入考点名称" maxlength="20" />
        </el-form-item>
        <el-form-item label="考点状态" prop="status">
          <el-select v-model="listQuery.assignSatuts" placeholder="请选择考点状态" filterable clearable class="filter-item">
            <el-option v-for="item in assignSatuts" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="assign-operas">
      <el-button type="infor" size="mini" @click="examinationPop=false,cancelClass()">取消</el-button>
      <el-button type="primary" size="mini" @click="custormClass">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddClassPop',
  data() {
    return {
      examinationTitle: '',
      examinationPop: false,
      listQuery: {
        checkList: []
      },
      rules: {
        name: { required: true, trigger: 'blur', message: '请输入大纲名称' },
        status: { required: true, trigger: 'blur', message: '请选择教材状态' }
      },
      assignSatuts: []

    }
  },
  methods: {
    cancelClass() {

    },
    custormClass() {

    }
  }
}
</script>

<style scoped>
  .assign-operas{
    display: flex;
    justify-content: center;
    align-content: center;
  }
</style>
