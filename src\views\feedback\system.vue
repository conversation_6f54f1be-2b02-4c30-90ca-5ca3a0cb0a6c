<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.feedbackId"
        placeholder="反馈编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.searchField"
        placeholder="合伙人/手机号/校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.content"
        placeholder="反馈内容"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.feedbackType" placeholder="反馈类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in feedbackList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.category" placeholder="问题类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in questionCategory" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="反馈状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in feedbackStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="满意度" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in feedbackStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="反馈开始时间"
        end-placeholder="反馈结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column type="index" />
      <el-table-column label="反馈编号" show-overflow-tooltip prop="feedbackId" />
      <af-table-column label="机构ID" prop="agencyId" show-overflow-tooltip />
      <af-table-column label="合伙人" prop="partnerName" show-overflow-tooltip />
      <af-table-column label="校区名称" prop="schoolName" />
      <af-table-column label="签约区域">
        <template slot-scope="scope">
          <span v-if="scope.row.provinceName">{{ scope.row.provinceName }}|</span>
          <span v-if="scope.row.cityName">{{ scope.row.cityName }}|</span>
          <span v-if="scope.row.areaName">{{ scope.row.areaName }}</span>
        </template>
      </af-table-column>
      <el-table-column label="反馈内容" prop="content" width="400px" />
      <af-table-column label="反馈类型" prop="feedbackType" :formatter="getFeedbackType" />
      <af-table-column label="问题类型" prop="category" :formatter="getCategory" />
      <af-table-column label="反馈状态" prop="status" :formatter="getStatus" />
      <af-table-column label="满意度" prop="assessStar">
        <template slot-scope="scope">
          <span v-if="scope.row.assessStar===1">不满意</span>
          <span v-if="scope.row.assessStar===2||scope.row.assessStar===3">一般</span>
          <span v-if="scope.row.assessStar===4||scope.row.assessStar===5">满意</span>
        </template>
      </af-table-column>
      <af-table-column label="反馈时间" prop="feedbackTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="['feedback:system:detail']" type="primary" size="mini" @click="getDetail(scope.row)">详情</el-button>
          <el-button v-permission="['feedback:system:transfer']" type="primary" size="mini" @click="problemsType(scope.row)">问题转交</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <feedback-type ref="feedbackType" @refreshList="getList" />
  </div>
</template>

<script>
import AreaPicker from '@/components/area-picker'
import FeedbackType from './components/feedbackType'
import Pagination from '@/components/Pagination'
import { systemFeedback, questionCategories } from '@/api/feedback'
import { feedbackList, feedbackStatus, converseEnToCn } from '@/utils/field-conver'
export default {
  name: 'SystemFeedback',
  components: {
    AreaPicker,
    FeedbackType,
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      feedbackList: feedbackList,
      questionCategory: [],
      feedbackStatus: feedbackStatus,
      followDate: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCategories()
    })
  },
  methods: {
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    problemsType(row) {
      this.$refs.feedbackType.feedbackPop = true
      this.$refs.feedbackType.feedbackId = row.feedbackId
    },
    getCategories() {
      questionCategories().then(res => {
        if (res.code === '000000') {
          const questionList = res.data && res.data.length > 0 ? res.data : []
          const arr = []
          questionList.forEach(item => {
            const questionObj = {}
            questionObj['label'] = item.name
            questionObj['value'] = item.id
            arr.push(questionObj)
          })
          this.questionCategory = arr
        }
      }).catch(() => {

      })
    },
    async getList() {
      const that = this
      const params = Object.assign({}, that.listQuery, { beginTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })
      systemFeedback(params).then(res => {
        if (res.code === '000000') {
          that.list = res.data.records
          that.listLoading = false
          that.total = res.data.total
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    getDetail(row) {
      localStorage.setItem('feedbackIn', row.feedbackId)
      this.$router.push({
        name: 'FeedbackInfo',
        params: {
          type: row.feedbackType,
          id: row.feedbackId
        }
      })
    },

    getFeedbackType(row) {
      return converseEnToCn(this.feedbackList, row.feedbackType)
    },
    getCategory(row) {
      return converseEnToCn(this.questionCategory, row.category)
    },
    getStatus(row) {
      return converseEnToCn(this.feedbackStatus, row.status)
    }
  }
}
</script>

<style scoped>

</style>
