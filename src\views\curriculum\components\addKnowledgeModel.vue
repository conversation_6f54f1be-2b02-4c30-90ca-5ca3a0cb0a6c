<template>
  <el-dialog :visible.sync="teacherPop" :title="teacherTitle" :close-on-click-modal="!teacherPop" width="60%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="teacherForm" :model="listQuery" :rules="rules" label-width="120px">
        <el-form-item label="模块名称" prop="title">
          <el-input v-model="listQuery.title" placeholder="请输入名称" style="width: 220px;"></el-input>
        </el-form-item>
        <el-form-item label="适用产品线">
          <ProductLineSelect v-model="listQuery.clientCode"></ProductLineSelect>
        </el-form-item>
        <el-form-item label="适用班型" prop="classTypeId">
          <ClassTypeByClientSelect v-model="listQuery.classTypeId" :client-code="listQuery.clientCode"></ClassTypeByClientSelect>
        </el-form-item>
        <el-form-item label="适用科目" prop="subjectId">
          <SubjectSelect v-model="listQuery.subjectId"></SubjectSelect>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <StatusSelect v-model="listQuery.status"></StatusSelect>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="listQuery.sort" style="width: 140px;" type="number" placeholder="请输入排序" maxlength="17" :disabled="isEdit" />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="teacherPop=false,cancelClass()">取消</el-button>
      <!--      新增的确定-->
      <el-button v-if="flags===1" type="primary" size="mini" @click="submit">确定</el-button>
      <!--      修改的确定-->
      <el-button v-if="flags===0" type="primary" size="mini" @click="editTeacher">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {  addSubjectCatalog, getSubjectCatalogDetail } from '@/api/classType'
import {
  enableList
} from '@/utils/field-conver'
import { uploadSuccess } from '@/api/common'
import SubjectSelect from '@/components/Select/SubjectSelect.vue'
import StatusSelect from '@/components/Select/StatusSelect.vue'
import StyleSelect from '@/components/Select/StyleSelect.vue'
import ProductLineSelect from '@/components/Select/ProductLineSelect.vue'
import ClassTypeByClientSelect from '@/components/Select/ClassTypeByClientSelect.vue'

export default {
  name: 'AddKnowledgeModel',
  components: { ClassTypeByClientSelect, ProductLineSelect, StyleSelect, StatusSelect, SubjectSelect },
  data() {
    return {
      teacherTitle: '',
      teacherPop: false,
      listQuery: {
        clientCode: null,
        style: 1,
        status: 1
      },
      clients: [],
      customerUpdate: true,
      rules: {
        classTypeId: { required: true, trigger: 'blur', message: '请选择班型' },
        subjectId: { required: true, trigger: 'blur', message: '请选择科目' },
        status: { required: true, trigger: 'blur', message: '请选择状态' },
        title: { required: true, trigger: 'blur', message: '请输入模块名称' },
      },
      enableList: enableList,
      subjectsAll: [],
      isEdit: false,
      flags: -1,
      certImgResource: '', // 教师资格证图片
      certImgResourceId: '', // 教师资格证图片id
      imageResource: '', // 教师头像图片
      imageResourceId: '', // 教师头像图片id
      bigImageResource: '', // 教师图片
      bigImageResourceId: '', // 教师图片id
      enableFlagsCreate: false,
      enableFlagsUpdate: false,
      uuid: ''
    }
  },
  mounted() {
  },
  methods: {
    cancelClass() {
      if (this.$refs.teacherForm) {
        this.$refs.teacherForm.clearValidate()
      }
      this.listQuery = {}
    },
    submit() {
      this.enableFlagsCreate = true
      this.$refs.teacherForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery)
          addSubjectCatalog(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$emit('addTeacherList')
              this.teacherPop = false
              this.listQuery = {}
              this.certImgResource = ''
              this.certImgResourceId = ''
              this.imageResource = ''
              this.imageResourceId = ''
              this.bigImageResourceId = ''
              this.bigImageResource = ''
              this.enableFlagsCreate = false
              this.$refs.classForms.clearValidate()
            }
          }).catch(() => {
            this.enableFlagsCreate = false

          })
        }
        else {
          this.enableFlagsCreate = false
          return false
        }
      })
    },
    getTeacherDetail(ids) { // 获取教师详情
      getSubjectCatalogDetail(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
        }
      })
    },
    changeInit() {
      this.listQuery = {}
      if (this.$refs.teacherForm) {
        this.$refs.teacherForm.clearValidate()
      }
    },
    editTeacher() {
      this.enableFlagsUpdate = true
      this.$refs.teacherForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery)
          addSubjectCatalog(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.$emit('addTeacherList')
              this.teacherPop = false
              this.listQuery = {}
              this.enableFlagsUpdate = false
              this.$refs.classForms.clearValidate()
            }
          }).catch(() => {
            this.enableFlagsUpdate = false

          })
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
em {
  font-style: normal;
}

.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}

.course-duration {
  display: flex;
  justify-content: space-around;

  em {
    padding-left: 8px;
    padding-right: 8px;
  }
}

.upload-imgs {
  position: relative;
  width: 118px;
  height: 118px;
  font-size: 14px;
  display: inline-block;
  padding: 10px;
  margin-right: 25px;
  border: 2px dashed #ccc;
  text-align: center;
  vertical-align: middle;
}

.upload-imgs .add {
  display: block;
  background-color: #ccc;
  color: #ffffff;
  height: 94px;
  line-height: 94px
}

.upload-imgs .add .iconfont {
  padding: 10px 0;
  font-size: 40px;
}

.upload-imgs .upload {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 118px;
  height: 118px;
  opacity: 0;
  cursor: pointer;
}

.upload-imgs .img {
  position: relative;
  width: 94px;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .img img {
  vertical-align: middle;
  width: 94px;
  height: 94px;
}

.upload-imgs .img .close {
  display: none;
}

.upload-imgs:hover .img .close {
  display: block;
  position: absolute;
  top: -10px;
  left: -10px;
  width: 118px;
  height: 118px;
  background: rgba(0, 0, 0, .5);
  text-align: center;
  line-height: 118px;
  font-size: 24px;
  color: #fff;
}

.img-upload {
  padding-right: 8px;
}
</style>
