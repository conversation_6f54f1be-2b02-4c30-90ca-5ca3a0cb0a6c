<template>
  <el-dialog :visible.sync="show" @close="handleClose" title="设置业绩部门">
    <div>
      <el-form
        ref="form"
        size="small"
        :model="recommendInfo"
        label-width="100px"
      >
        <el-form-item
          label="业绩部门："
          :rules="{
            required: true,
            message: '请选择业绩部门',
            trigger: 'change',
          }"
          prop="salesDept"
        >
          <PerformanceDepartment :value.sync="recommendInfo.salesDept" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleClose">关 闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import PerformanceDepartment from "@/components/PerformanceDepartment/PerformanceDepartment.vue";
import { updateSalesDept } from "@/api/handover";
export default {
  components: {
    PerformanceDepartment,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: 0,
    },
    salesDept: {
       type: [Number, String],
      default: "",
    },
  },
  data() {
    return {
      salesDeptPop: false,
      recommendInfo: {
        salesDept: "",
      },
    };
  },

  watch: {
    salesDept: {
      handler(newVal) {
        this.recommendInfo.salesDept = newVal;
      },
    },
  },

  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const res = await updateSalesDept({
            id: this.id,
            salesDept: this.recommendInfo.salesDept,
          });
          if (res.code === "000000") {
            this.$message.success("设置业绩部门成功");
            this.emitSuccess();
            this.handleClose();
          }
        }
      });
    },

    handleClose() {
      this.recommendInfo.salesDept = "";
      this.show = false;
    },

    emitSuccess() {
      this.$emit("success");
    },
  },

};
</script>






<style>
</style>