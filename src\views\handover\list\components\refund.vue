<template>
  <div class="app-container bgGrey">
    <el-form ref="detailForm" label-width="130px">
      <!--    01普高，02艺考，03烨晨 -->
      <el-row :gutter="10">
        <el-col :lg="{span:24}" :sm="{span:24}" :md="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="客户编号：">
                    <span>{{ customerInfo.clueCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="客户名称：">
                    <span>{{ customerInfo.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="手机号码：">
                    <span>{{ customerInfo.mobile }}</span>
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="机构区域：">
                    <span>{{ customerInfo.provinceName }} | {{ customerInfo.cityName }} | {{ customerInfo.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="机构地址：">
                    <span>{{ customerInfo.address }}</span>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top:20px;">
        <el-col :lg="{span:24}" :sm="{span:24}" :md="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="订单编号：">
                    <span>{{ orderBaseInfo.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="原订单金额：">
                    <span>{{ orderBaseInfo.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col v-if="isEdit" :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="已退款金额/元：">
                    <span>{{ orderBaseInfo.refundAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="加盟项目：">
                    <span>{{ joinProject.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="支付类目：">
                    <span>退款</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="退款金额/元：" required>
                    <el-input v-model.number="refundAmount" type="number" :disabled="!isEdit" @input="oninput" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="校区编码：">
                    <span>{{ schoolInfo.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="校区名称：">
                    <span>{{ schoolInfo.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="签约区域：">
                    <div>{{ schoolInfo.provinceName }} | {{ schoolInfo.cityName }} | {{ schoolInfo.areaName }}
                      {{ schoolInfo.countyName ? (' | ' + schoolInfo.countyName) : '' }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="退款时间：" required>
                    <el-date-picker
                      v-model="orderBaseInfo.refundTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="退款时间"
                      style="width:300px"
                      :disabled="!isEdit"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="退款方式" required>
                    <el-select v-model="orderBaseInfo.payMethod" :disabled="!isEdit" placeholder="退款方式" filterable clearable @change="getAccountType3('pay_type')">
                      <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="冲减业绩" required>
                    <el-radio v-model="orderBaseInfo.writeDowns" label="0" :disabled="!isEdit">否</el-radio>
                    <el-radio v-model="orderBaseInfo.writeDowns" label="1" :disabled="!isEdit">是</el-radio>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：" required>
                    <el-input v-model="orderBaseInfo.remark" type="textarea" show-word-limit maxlength="255" :disabled="!isEdit" />
                  </el-form-item>
                </el-col>
                <el-col v-if="isEdit" :sm="{span:24}" :md="{span:24}" style="margin:20px 0;text-align: center;">
                  <el-button type="primary" size="mini" style="width:300px;" @click="refundConfirm">确认</el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { getOrderDetail, refundHandover } from '@/api/handover'
import { getPayType } from '@/api/common'
export default {
  name: 'Refund',
  inject: ['reload'],
  data() {
    return {
      detail: {},
      customerInfo: {},
      schoolInfo: {},
      joinProject: {},
      orderId: null,
      oldOrderId: null,
      orderBaseInfo: {},
      refundAmount: null,
      isEdit: false,
      payTypes: []
    }
  },
  watch: {
    $route: {
      handler(val, oldVal) {
        if (val.params.id) {
          this.getOrderDetailInfo(val.params.id)
        }
      },
      // 深度观察监听
      deep: true
    }
  },
  created() {

    this.orderId = this.$route.params.id
    this.isEdit = this.$route.params.isEdit
    this.getOrderDetailInfo(this.orderId)
    this.getAccountType3('pay_type')
  },
  mounted() {

  },
  methods: {
    getOrderDetailInfo(id) {
      getOrderDetail(id).then(res => {
        if (res.code === '000000') {
          this.orderDetailInfo = JSON.parse(JSON.stringify(res.data))
          this.oldOrderId = this.orderDetailInfo.id
          this.customerInfo = this.orderDetailInfo.clueInfo // 合伙人信息
          this.schoolInfo = this.orderDetailInfo.joinSchool // 项目所属校区资料
          this.joinProject = this.orderDetailInfo.joinProject // 加盟项目的信息
          this.refundAmount = this.isEdit ? null : this.orderDetailInfo.refundAmount
          this.orderBaseInfo = { // 交接单基本信息
            orderCode: this.isEdit ? this.orderDetailInfo.orderCode : this.orderDetailInfo.oldOrderCode,
            realAmount: this.orderDetailInfo.realAmount,
            refundTime: this.orderDetailInfo.refundTime,
            refundAmount: this.orderDetailInfo.refundAmount,
            remark: this.isEdit ? null : this.orderDetailInfo.remark,
            writeDowns: typeof this.orderDetailInfo.writeDowns === 'string' ? this.orderDetailInfo.writeDowns : JSON.stringify(this.orderDetailInfo.writeDowns),
            payMethod: this.orderDetailInfo.payMethod
          }

        }
      }).catch(() => {

      })
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 100)
      })
    },
    refundConfirm() {
      if (this.detail.refundAmount > this.orderBaseInfo.realAmount) {
        this.$message({
          type: 'error',
          message: '退款金额不可大于原订单金额'
        })
        return false
      }
      if (Number(this.refundAmount) <= 0) {
        this.$message({
          type: 'error',
          message: '退款金额必须大于0'
        })
        return false
      }
      if (!this.refundAmount) {
        this.$message({
          type: 'error',
          message: '退款金额必填'
        })
        return false
      }
      if (!this.orderBaseInfo.refundTime) {
        this.$message({
          type: 'error',
          message: '退款时间必填'
        })
        return false
      }
      if (!this.orderBaseInfo.payMethod) {
        this.$message({
          type: 'error',
          message: '退款方式必选'
        })
        return false
      }
      if (!this.orderBaseInfo.writeDowns) {
        this.$message({
          type: 'error',
          message: '冲减业绩必选'
        })
        return false
      }
      if (!this.orderBaseInfo.remark) {
        this.$message({
          type: 'error',
          message: '备注必填'
        })
        return false
      }
      const params = Object.assign({}, {
        oldOrderId: this.oldOrderId,
        payItem: 22,
        refundAmount: this.refundAmount,
        refundTime: this.orderBaseInfo.refundTime,
        remark: this.orderBaseInfo.remark,
        payMethod: this.orderBaseInfo.payMethod,
        writeDowns: this.orderBaseInfo.writeDowns
      })
      refundHandover(params).then(res => {
        if (res.code === '000000') {
          this.refundAmount = ''
          this.orderBaseInfo.refundTime = ''
          this.orderBaseInfo.remark = ''
          this.orderBaseInfo.payMethod = ''
          this.orderBaseInfo.writeDowns = ''
          this.closeIt()
        }
      })
    },
    oninput(e) {

      var that = this
      // 通过正则过滤小数点后两位
      e = (e.match(/^\d*(\.?\d{0,2})/g)[0]) || null
      that.refundAmount = e
    },
    getAccountType3(str) {
      const that = this
      getPayType(str).then(res => {
        that.payTypes = res.data
      })
    }
  }
}
</script>
<style scoped>

</style>
