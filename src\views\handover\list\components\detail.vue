<template>
  <div class="app-container bgGrey">
    <el-row>
      <el-col :span="24" class="time-line-col">
        <div class="el-row--flex flex-align-item-center">
          <div style="width: 70px;">
            审批进度：
          </div>
          <el-timeline :reverse="reverse">
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :icon="activity.icon"
              :type="activity.type"
            >
              {{ activity.label }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <!--      合伙人信息-->
      <el-col :lg="{span:24}">
        <el-form
          ref="form"
          size="small"
          label-width="100px"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:4}">
                  <el-form-item label="客户编号：">
                    <div>{{ customerInfo.clueCode }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:4}">
                  <el-form-item label="客户名称：">
                    <div>{{ customerInfo.customer }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:4}">
                  <el-form-item label="手机号：">
                    <div>{{ customerInfo.mobile }}</div>
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="机构区域：">
                    <div>{{ customerInfo.provinceName }} | {{ customerInfo.cityName }} | {{ customerInfo.areaName }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="机构地址：">
                    <div>{{ customerInfo.address }}</div>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </div>
          </el-card>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <!--      交接单信息-->
      <el-col :lg="{span:13}">
        <el-form
          ref="form"
          size="small"
          label-width="140px"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="订单编号：">
                    <div>{{ orderBaseInfo.orderCode }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：">
                    <div>{{ joinProject.projectName }}</div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="业务类型：">
                    <span>{{ getBusinessTypeList(orderBaseInfo.businessType) }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" v-if="!isthreeThousandPlanets">
                  <el-form-item label="区域类型：">
                    <span>{{ isSingle(orderBaseInfo.areaSingle) }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}" v-if="!isthreeThousandPlanets">
                  <el-form-item label="版本类型：">
                    <span v-if="orderBaseInfo.versionType === 1">正式</span>
                    <span v-if="orderBaseInfo.versionType === 2">预签</span>
                    <span v-if="orderBaseInfo.versionType === 3">抢分</span>
                    <span v-if="orderBaseInfo.versionType === 4">特色班型</span>
                    <span v-if="orderBaseInfo.versionType === 5">烨晨市代</span>
                    <span v-if="orderBaseInfo.versionType === 6">烨晨渠道</span>
                    <span v-if="orderBaseInfo.versionType === 7">高考绝招班</span>
                    <span v-if="orderBaseInfo.versionType === 8">招生服务合同</span>
                    <span v-if="orderBaseInfo.versionType === 9">特色班型合同</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" v-if="isShowContract">
                    <el-form-item  label="初高中合同：" class="no-label-item">
                        <el-checkbox  disabled v-model="orderBaseInfo.businessVersion"></el-checkbox>
                    </el-form-item>
                  </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" v-if="orderBaseInfo.payItem && !isthreeThousandPlanets">
                  <el-form-item label="支付类目：">
                    <span>{{ category }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="实付金额：">
                    <span>{{ orderBaseInfo.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="应付金额：">
                    <span>{{ orderBaseInfo.payAmount }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="套餐名称：">
                    <span>{{ policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="套餐价格：">
                    <span>{{ policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="!isthreeThousandPlanets">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区编号：">
                    <div>{{ schoolInfo.schoolCode }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <div>{{ schoolInfo.schoolName }}</div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="!isthreeThousandPlanets">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约区域：">
                    <div>{{ schoolInfo.provinceName }} | {{ schoolInfo.cityName }} | {{ schoolInfo.areaName }}
                      {{ schoolInfo.countyName ? (' | ' + schoolInfo.countyName) : '' }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="!isthreeThousandPlanets">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约详细地址：">
                    <div>{{ schoolInfo.address }}</div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="!isthreeThousandPlanets">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="签约主体：">
                  {{ orderDetailInfo.signPartyName }}
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="签约周期：">
                    {{ orderBaseInfo.signStartTime | parseTime('{y}-{m}-{d}') }}
                    至 {{ orderBaseInfo.signEndTime |parseTime('{y}-{m}-{d}') }}
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="完款时间：">
                    <div>{{ orderBaseInfo.completionTime | parseTime('{y}-{m}-{d}') }}</div>
                  </el-form-item>
                </el-col> -->
              </el-row>

              <el-row v-if="joinProject.id < 4 && orderBaseInfo.versionType=== 1&&orderBaseInfo.areaSingle!==1">
                <el-col  :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="公办校：">
                    <div>{{ orderBaseInfo.publicSchool }}</div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <div>{{ orderBaseInfo.remark }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card class="box-card" shadow="hover" v-if="!isthreeThousandPlanets">
              <div slot="header" class="clearfix">
                <span>签约合同类型</span>
              </div>
              <div class="item">
                <el-row style="margin-bottom: 0">
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="签约合同类型：">
                      <div>{{ getContractType(orderBaseInfo.contractType) }}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-bottom: 0">
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="补充内容：">
                     <div>{{orderBaseInfo.reinforCement}}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
        </el-form>

      </el-col>
      <!--      产品信息列表-->
      <el-col :lg="{span:11}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>产品信息</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="projectInfo"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%;"
                >
                  <af-table-column type="index" label="#" align="center" />
                  <af-table-column label="产品名称" prop="productName" />
                  <!--                  <af-table-column label="产品类别" prop="productType" :formatter="getProductType" />-->
                  <af-table-column label="产品数量/课时" prop="productNum" />
                  <af-table-column label="产品价格" prop="productPrice" />
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <!--      业绩所属人-->
      <el-col :lg="{span:13}">
        <el-form
          ref="form"
          size="small"
          label-width="100px"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>业绩所属人</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="推荐渠道：">
                    <div>{{ getChannelType(recommendInfo.channel) }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="姓名：">
                    <div>{{ recommendInfo.recName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="手机号：">
                    <div>{{ referrerDeliveryAddress.mobile }}</div>
                  </el-form-item>
                </el-col>
                <el-col v-if="recommendInfo.channel===2" :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区：">
                    <div>{{ recommendInfo.recInstitutionName }}</div>
                  </el-form-item>
                </el-col>
                <el-col v-if="recommendInfo.channel == 2" :span="24">
                  <el-form-item label="收货地址：">
                    <div>{{ referrerDeliveryAddress.provinceName }} | {{ referrerDeliveryAddress.cityName }} | {{ referrerDeliveryAddress.areaName }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col v-if="recommendInfo.channel == 2" :span="24">
                  <el-form-item label="详细地址：">
                    <div>{{ referrerDeliveryAddress.address }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </el-col>
      <!--      推荐人产品-->
      <el-col v-if="recommendInfo.channel == '2'" :lg="{span:11}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>推荐人产品</span>
            <!--              <el-button size="mini" type="primary">选择校区</el-button>-->
          </div>
          <div class="item">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="referrerProductList"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%;"
                >
                  <el-table-column type="index" label="#" align="center" width="40" />
                  <af-table-column label="产品名称" prop="productName" />
                  <af-table-column label="产品类别" prop="productType" :formatter="getProductType" />
                  <af-table-column label="产品数量" prop="productNum" />
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>业绩部门</span>
          </div>
          <div class="text">
            <ElForm>
              <el-form-item label="业绩部门：">
               <PerformanceDepartment :value.sync="recommendInfo.salesDept" :disabled="true" :isFormItem="false"/>
              </el-form-item>
            </ElForm>
          </div>
        </el-card>
      </el-col>

    </el-row>
    <el-row>
      <!--      打款记录-->
      <el-col :lg="{span:24}">

        <el-form
          ref="form"
          size="small"
          label-width="100px"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>打款记录</span>
            </div>
            <div class="text">
              <el-row>
                <el-col :span="24">
                  <el-table
                    :data="paymentRecordList"
                    border
                    fit
                    stripe
                    highlight-current-row
                    style="width: 100%;"
                  >
                    <el-table-column type="index" label="#" align="center" width="40" />
                    <af-table-column label="打款金额" prop="payAmount" />
                    <af-table-column label="打款方式" prop="payTypeName" />
                    <af-table-column label="打款时间" width="150" prop="payTime" />
                    <af-table-column label="审核状态" prop="auditStatus" :formatter="getAuditStatus" />
                    <af-table-column label="交易流水号" prop="transactionNo" />
                    <af-table-column label="备注" prop="remark" show-overflow-tooltip width="200" />
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </el-col>
    </el-row>
    <!--    发货记录-->
    <el-row>
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>发货记录</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="deliveryList"
                  border
                  fit
                  stripe
                  highlight-current-row
                >
                  <el-table-column type="index" label="#" align="center" width="40" />
                  <af-table-column label="客户名称" prop="customer" />
                  <af-table-column label="校区名称" prop="institution" />
                  <af-table-column label="客户手机号" prop="mobile" />
                  <af-table-column label="加盟项目" prop="projectName" />
                  <af-table-column label="签约区域" show-overflow-tooltip>
                    <template slot-scope="{row}">
                      {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}{{ row.adress }}
                    </template>
                  </af-table-column>
                  <af-table-column label="物流公司" prop="shipName" />
                  <af-table-column label="快递单号" prop="shipNo" show-overflow-tooltip />
                  <af-table-column label="发货类型" prop="shipType" :formatter="setShipType" />
                  <af-table-column label="发货单状态" prop="status" :formatter="setShipStatus" />

                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!--    操作与审核-->
    <el-row>
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>操作记录</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="optionsLogsList"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%;"
                >
                  <el-table-column type="index" label="#" align="center" width="40" />
                  <af-table-column label="操作记录" prop="auditTypeName" />
                  <af-table-column label="操作备注" show-overflow-tooltip prop="remark" width="800"/>
                  <af-table-column label="操作人" prop="createBy" />
                  <af-table-column label="操作时间" width="200">
                    <template slot-scope="{row}">
                      <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                  </af-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getOrderPaymentRecordList } from '@/api/payment'
import { getOrderDetail, getOptionLogs, getOrderDeliveryRecord } from '@/api/handover'
import { getAllProject, getRecommentList, getPayType } from '@/api/common'
import {
  originList, joinStatusList, intentionList, converseEnToCn, businessTypeList, getAreaSingle,
  productTypeList, channelList, auditStatus, payMethod, orderStatusList, orderStatusActiveList, getShipStatus, getShipType
} from '@/utils/field-conver'
import PerformanceDepartment from '@/components/PerformanceDepartment/PerformanceDepartment.vue'

export default {
  name: 'HandoverDetail',
  components: {
    PerformanceDepartment
  },

  directives: {},
  data() {
    return {
      isthreeThousandPlanets: false, // 是否是三千星球
      isShowContract: false, // 是否显示初高中合同
      reverse: false,
      activities: [],
      tempRoute: {},
      customerAreaList: { //
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      joinProject: {}, // 加盟的项目信息
      comProjectList: [], // 项目列表
      orderDetailInfo: {}, // 交接单详情数据
      currentCustomerId: '', // 当前客户的id
      customerInfo: {}, // 合伙人信息
      schoolInfo: {}, // 校区信息
      orderBaseInfo: {}, // 交接单基础信息
      projectInfo: [], // 产品信息
      recommendInfo: {}, // 推荐人基本信息
      referrerProductList: [], // 推荐人产品列表
      referrerDeliveryAddress: {}, // 推荐人地址信息
      paymentRecordList: [], // 打款记录列表
      deliveryList: [],
      optionsLogsList: [], // 操作记录
      policyName: '',
      policyId: '',
      policyPrice: '',
      payCategoryList: []
    }
  },
  computed: {
    category() {
      const category = this.payCategoryList.find(item => item.id === this.orderDetailInfo.payItem)
      return category ? category.itemName : ''
    }
  },
  created() {
    const name = this.$route.query.name || ''
    const orderId = this.$route.query.orderId || ''
    this.currentCustomerId = this.$route.params && this.$route.params.clueId
    this.getOrderDetailInfo(orderId)
    this.getPayRecord(orderId)
    this.getProject()
    this.getHandoverLogs(orderId)
    this.setTagsViewTitle(name)
    this.getDeliveryList(orderId)
    this.getPayCategoryList()
  },
  methods: {
    setTagsViewTitle(name) {
      const cRoute = Object.assign({}, this.$route)
      const title = '交接单详情'
      const route = Object.assign({}, cRoute, { title: `${title}-${name}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    /**
     * 查看线索/客户详情
     * @param row
     */
    getOrderDetailInfo(id) {
      getOrderDetail(id).then(res => {
        this.orderDetailInfo = JSON.parse(JSON.stringify(res.data))
        this.policyName = this.orderDetailInfo.policyName || ''

        this.policyPrice = this.orderDetailInfo.policyPrice

        this.policyId = this.orderBaseInfo.policyId || ''
        this.customerInfo = this.orderDetailInfo.clueInfo // 合伙人信息
        this.schoolInfo = this.orderDetailInfo.joinSchool // 项目所属校区资料
        this.orderBaseInfo = { // 交接单基本信息
          id: this.orderDetailInfo.id,
          businessVersion: this.orderDetailInfo.businessVersion === 1,
          reinforCement: this.orderDetailInfo.reinforCement,
          contractType: this.orderDetailInfo.contractType,
          status: this.orderDetailInfo.status,
          versionType: this.orderDetailInfo.versionType,
          businessType: this.orderDetailInfo.businessType,
          payAmount: this.orderDetailInfo.payAmount,
          completionTime: this.orderDetailInfo.completionTime,
          remark: this.orderDetailInfo.remark,
          areaSingle: this.orderDetailInfo.areaSingle,
          signStartTime: this.orderDetailInfo.signStartTime,
          signEndTime: this.orderDetailInfo.signEndTime,
          publicSchool: this.orderDetailInfo.publicSchool,
          orderQueryNo: this.orderDetailInfo.orderQueryNo,
          orderCode: this.orderDetailInfo.orderCode,
          realAmount: this.orderDetailInfo.realAmount,
          payItem: this.orderDetailInfo.payItem
        }
        this.projectInfo = this.orderDetailInfo.clueProductList // 产品列表信息
        this.recommendInfo = { // 业绩所属人
          recClueId: this.orderDetailInfo.recClueId,
          recName: this.orderDetailInfo.recName,
          channel: this.orderDetailInfo.channel,
          recInstitutionName: this.orderDetailInfo.recInstitutionName || '',
          salesDept: this.orderDetailInfo.salesDept || ''
        }
        if (this.orderDetailInfo.referrerDeliveryAddress != null) {
          this.referrerDeliveryAddress = this.orderDetailInfo.referrerDeliveryAddress // 推荐人地址信息
        }
        this.referrerProductList = this.orderDetailInfo.referrerProductList // 推荐人产品列表
        this.joinProject = this.orderDetailInfo.joinProject // 加盟项目的信息
        this.isthreeThousandPlanets = this.joinProject.id === 9;
        this.setCurrentOrderStatus(this.orderDetailInfo.status) // 设置当前进度条被激活的状态
        this.getOriginUserList(this.orderDetailInfo.recName ? this.orderDetailInfo.recName : '') // 根据推荐人渠道
        // 是否显示初高中合同
        this.isShowContractBtn();
      })
    },
    isShowContractBtn() {
      // 三陶教育 且正式
      if (this.joinProject.id === 1 && this.orderBaseInfo.versionType === 1) {
        this.isShowContract = true;
      } else {
        this.isShowContract = false;
        this.orderBaseInfo.businessVersion = ''
      }
    },
     /**
     * 获取推荐人列表
     * */
    getOriginUserList() {
      console.log('this.recommendInfo.recName', this.recommendInfo.recName, this.recommendInfo.channel)
      if (this.recommendInfo.channel) {
        getRecommentList({ search: this.recommendInfo.recName, channel: this.recommendInfo.channel }).then(res => {
          this.recommendUserList = res.data
          const tmpRow = res.data.filter(item => {
            return item.id === this.recommendInfo.recClueId
          })
          console.log('tmpRow', tmpRow)
          this.$set(this.referrerDeliveryAddress, 'mobile', tmpRow[0] ? tmpRow[0].mobile : '');
        })
      }
    },

    // 获取支付类目
    getPayCategoryList() {
      getPayType('pay_item').then(res => {
        this.payCategoryList = res.data
      })
    },

    /**
     * 获取打款记录
     * */
    getPayRecord(id) {
      const params = { orderId: id }
      getOrderPaymentRecordList(params).then(res => {
        this.paymentRecordList = res.data
      })
    },
    /**
     * 获取操作记录
     * */
    getHandoverLogs(id) {
      getOptionLogs(id).then(res => {
        this.optionsLogsList = res.data
      })
    },
    /**
     * 获取订单的发货列表
     * */
    getDeliveryList(id) {
      getOrderDeliveryRecord(id).then(res => {
        if (res.code === '000000') {
          this.deliveryList = res.data
        }
      })
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.comProjectList = res.data
      })
      // orderBaseInfo
    },
    /**
     * 设置当前进度条进度
     *   { value: 1, label: '待专员打款' },
     { value: 2, label: '财务审核' },
     { value: 3, label: '财务审核不通过' },
     { value: 4, label: '市场审核' },
     { value: 5, label: '市场审核不通过' },
     { value: 6, label: '合伙人提交资质' },
     { value: 7, label: '运营审批资质' },
     { value: 8, label: '运营创建合同' },
     { value: 9, label: '运营审批资质不通过' },
     { value: 10, label: '待签署' },
     { value: 11, label: '已签署' },
     { value: 12, label: '已作废' },
     { value: 13, label: '已生效' }
     * */
    setCurrentOrderStatus(status) {

      this.activities = []
      status = status === 21 ? 4 : status


      orderStatusActiveList.forEach((item, index, arr) => {

        if (status > (item.value || item.flag)) {
          item.type = 'primary'
          item.icon = 'el-icon-check'
        } else {
          if (index === 0) {
            item.icon = 'el-icon-more'
            item.type = 'primary'
          } else {
            const beCurrent = arr[index - 1] && (arr[index - 1]['value'] || arr[index - 1]['flag'])
            const beNext = arr[index + 1] && (arr[index + 1]['value'] || arr[index + 1]['flag'])
            if (status > beCurrent && (beNext ? (status < beNext) : true)) {
              item.icon = 'el-icon-check'
              item.type = 'primary'
            }
          }
        }
        if ((status === 19 && item.value === 19) || (status === 11 && item.value === 11)) {
          item.icon = 'el-icon-check'
        }
        item.icon && this.activities.push(item)

      })
    },
    getInfoOrigin(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(originList, isNaN(data) ? data.origin : data)
    },
    /**
     * 转换加盟状态
     */
    getJoinStatusCN(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(joinStatusList, isNaN(data) ? data.status : data)
    },
    /**
     * 转换意向度key与value
     */
    getIntention(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(intentionList, isNaN(data) ? data.clueType : data)
    },
    getBusinessTypeList(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(businessTypeList, isNaN(data) ? data.businessType : data)
    },
    getCustomerAreaList(data) {
      this.customerAreaList.provinceId = data.provinceId
      this.customerAreaList.cityId = data.cityId
      this.customerAreaList.areaId = data.areaId
    },
    /**
     * 区域类型
     */
    isSingle(data) {
      return converseEnToCn(getAreaSingle, data)
    },
    /**
     * 产品类别
     */
    getProductType(row) {
      return converseEnToCn(productTypeList, row.productType)
    },
    // 渠道
    getChannelType(data) {
      return converseEnToCn(channelList, data)
    },
    // 合同类型
    getContractType(data) {
      return data === 1 ? '个人合同' : '企业合同'
    },
    getAuditStatus(data) {
      return converseEnToCn(auditStatus, data.auditStatus)
    },
    getPayMethod(row) {
      return converseEnToCn(payMethod, row.payMethod)
    },
    setOrderStatus(data) {
      return converseEnToCn(orderStatusList, data)
    },
    setShipType(data) {
      return converseEnToCn(getShipType, data.shipType)
    },
    setShipStatus(data) {
      return converseEnToCn(getShipStatus, data.status)
    }
  }
}
</script>

<style scoped>
 .project-img img {
   position: absolute;
   right: 10px;
   top: 10px;
   width: 220px;
 }
  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }

  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .time-line-col {
    background-color: #ffffff;
    padding: 12px 30px;
    border-radius: 30px;
  }

  /deep/ .el-timeline-item__tail {
    position: absolute;
    top: 8px;
    width: 100%;
    border-top: 2px solid #dfe4ed;
    left: 0;
    height: 0;
    border-left: 0;
  }

  /deep/ .el-timeline-item {
    position: relative;
    display: inline-block;
    padding-right: 10px;
    padding-bottom: 0;
    min-width: 94px;
  }

  /deep/ .el-timeline-item__wrapper {
    position: relative;
    padding: 10px 0;
    left: -3px;
    top: 16px;
  }

  /deep/ .el-timeline-item__node--normal {
    width: 17px;
    height: 17px;
  }

  /deep/ .el-timeline-item__content {
    font-size: 12px;
  }
</style>
