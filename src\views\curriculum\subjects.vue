<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        placeholder="科目名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="科目状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:subjects:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="科目编号" show-overflow-tooltip prop="id" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
      <af-table-column label="科目名称" show-overflow-tooltip prop="title" />
      <af-table-column label="科目状态" prop="status" show-overflow-tooltip :formatter="getJoinStatusCN" />
      <af-table-column label="科目排序" prop="sort" show-overflow-tooltip />
      <af-table-column label="更新时间" prop="updateTime" />
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:subjects:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改科目弹框-->
    <add-subject ref="subjects" @addClassList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AddSubject from './components/addSubject'
import { getSubjects } from '@/api/classType'
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
export default {
  name: 'Subjects',
  components: {
    Pagination,
    AddSubject
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      enableList: enableList,
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })

      await getSubjects(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.subjects.subjectsPop = true
      this.$refs.subjects.subjectsTitle = '添加科目'
      this.$refs.subjects.isEdit = false
      this.$refs.subjects.flags = 1
    },
    handleUpdate(row) {
      this.$refs.subjects.subjectsPop = true
      this.$refs.subjects.subjectsTitle = '修改科目'
      this.$refs.subjects.isEdit = false
      this.$refs.subjects.flags = 0
      this.$refs.subjects.getSubjectsDetail(row.id)
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    getDetail(row) {
      this.$refs.subjects.subjectsPop = true
      this.$refs.subjects.subjectsTitle = '班型详情'
      this.$refs.subjects.isEdit = true
      this.$refs.subjects.getSubjectsDetail(row.id)
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
