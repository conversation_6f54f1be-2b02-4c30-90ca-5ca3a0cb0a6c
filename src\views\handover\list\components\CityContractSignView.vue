<template>
  <table border="1px" cellspacing="0" width="100%">
    <tr>
      <td colspan="3" style="font-size: 20px;font-weight: bolder;text-align: left">
        <div class="box">
          <span>省份、城市对应签约主体对照表</span>
          <span>
            <el-input size="mini" clearable placeholder="省、市名快速搜索" v-model="city" @input="filterList">
               <el-button type="primary" slot="append" @click="clearCity">显示全部</el-button>
            </el-input>
          </span>
        </div>
      </td>
    </tr>
    <tr style="background: #3B91FF;color: #fff;font-weight: bolder;">
      <th>主体公司名称</th>
      <th>省份</th>
      <th>城市</th>
    </tr>
    <tr v-for="item in cityContractDataTmp">
      <td>{{ item.company }}</td>
      <td>{{ item.province }}</td>
      <td>{{ item.city }}</td>
    </tr>
  </table>
</template>
<script>
import { debounce } from 'lodash'

export default {
  name: 'CityContractSignView',
  data() {
    return {
      city: '',
      showTip: true,
      cityContractDataTmp: [],
      cityContract: [
        {
          'company': '吉林省三陶宁科技有限公司',
          'province': '辽宁省',
          'city': '鞍山，本溪，朝阳，大连，丹东，阜新，营口'
        },
        {
          'company': '吉林省三陶辽科技有限公司',
          'province': '辽宁省',
          'city': '葫芦岛，锦州，辽阳，沈阳，铁岭，盘锦，抚顺'
        },
        {
          'company': '吉林省三陶龙科技有限公司',
          'province': '黑龙江省、内蒙古',
          'city': ''
        },
        {
          'company': '四川三陶陕科技有限公司',
          'province': '陕西，甘肃，宁夏，云南',
          'city': ''
        },
        {
          'company': '石家庄三陶冀电子科技有限公司',
          'province': '河北',
          'city': '保定，沧州，承德，秦皇岛'
        },
        {
          'company': '石家庄三陶燕电子科技有限公司',
          'province': '河北',
          'city': '邯郸，衡水，唐山，张家口'
        },
        {
          'company': '上海三陶福科技有限公司',
          'province': '西藏，新疆，天津，上海，浙江，福建',
          'city': ''
        },
        {
          'company': '石家庄三陶赵电子科技有限公司',
          'province': '河北',
          'city': '廊坊，石家庄，邢台'
        },
        {
          'company': '郑州三陶原文化科技有限公司',
          'province': '河南',
          'city': '安阳，鹤壁，焦作，洛阳 ，漯河，南阳，平顶山，濮阳'
        },
        {
          'company': '郑州三陶豫文化科技有限公司',
          'province': '河南',
          'city': '商丘，新乡，信阳，许昌，郑州，周口，驻马店，开封'
        },
        {
          'company': '泰安市三陶齐教育科技有限公司',
          'province': '山东省',
          'city': '临沂，潍坊，烟台，枣庄，淄博'
        },
        {
          'company': '泰安市三陶升教育科技有限公司',
          'province': '山东省',
          'city': '济宁，聊城，威海，青岛，日照，泰安'
        },
        {
          'company': '广州三陶粤科技有限公司',
          'province': '广东省,海南省',
          'city': ''
        },
        {
          'company': '泰安市三陶儒教育科技有限公司',
          'province': '山东省',
          'city': '滨州，德州，东营，菏泽，济南'
        },
        {
          'company': '上海三陶苏科技有限公司',
          'province': '江苏省',
          'city': ''
        },
        {
          'company': '郑州三陶晋文化科技有限公司',
          'province': '山西省',
          'city': ''
        },
        {
          'company': '合肥三陶皖科技有限公司',
          'province': '安徽省，贵州',
          'city': ''
        },
        {
          'company': '上海三陶湘科技有限公司',
          'province': '湖南省',
          'city': '常德，郴州，衡阳，邵阳，湘潭，怀化，湘西，株洲'
        },
        {
          'company': '上海三陶楚科技有限公司',
          'province': '湖南省',
          'city': '长沙，张家界，岳阳，永州，益阳，娄底'
        },

        {
          'company': '四川三陶蜀科技有限公司',
          'province': '四川，湖北，重庆,广西',
          'city': ''
        },
        {
          'company': '上海三陶赣科技有限公司',
          'province': '江西省',
          'city': ''
        },
        {
          'company': '上海烨伴教育科技有限公司',
          'province': '吉林，青海省',
          'city': ''
        },
        {
          'company': '上海恒提文化传播有限公司',
          'province': '全国',
          'city': '抖音云连锁专用'
        },
      ]
    }
  },
  mounted() {
    this.cityContractDataTmp = this.cityContract
  },
  props: {
    cityName: {
      type: String,
      require: false,
      default: ''
    },
    province: {
      type: String,
      require: false,
      default: ''
    }
  },
  watch: {
    province: function (newVal, oldVal) {
      this.city = newVal
      this.filterList()
    }
  },
  created() {
    // let result = this.cityContract.filter(item => {
    //   return item.province.includes(this.cityName) || item.city.includes(this.cityName)
    // })
    // if(result.length===0){
    //   this.city=this.province;
    // }else{
    //   this.city=this.cityName;
    // }
    this.city = this.province
    this.filterList()
  },
  methods: {
    clearCity: function () {
      this.city = ''
      this.filterList()
    },
    filterList: debounce(function () {
      if (this.city === null || this.city === '') {
        this.cityContractDataTmp = this.cityContract
      }
      //根据省份或城市，过滤当前cityContract中数据
      else {
        this.cityContractDataTmp = this.cityContract.filter(item => {
          return item.province.includes(this.city) || item.city.includes(this.city)
        })
      }
    }, 500)
  }
}
</script>
<style lang="scss" scoped>
/deep/ .productInfo td {
  padding: 0;
}

.box {
  display: flex;
  justify-content: space-between;
}

/deep/ .box .el-input input {
  background: #f0f8ff !important;
  width: 150px;
  margin-left: 20px;
}
</style>
