import { getBtns, getMenuList } from '@/utils/auth'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
function checkBtnPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = getBtns()
    const permissionRoles = value

    const hasPermission = roles.some(btn => {
      return permissionRoles.includes(btn.code)
    })

    return hasPermission
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`)
    return false
  }
}

function checkMenuPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = getMenuList()
    const permissionRoles = value

    const hasPermission = roles.some(btn => {
      return permissionRoles.includes(btn.code)
    })

    return hasPermission
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`)
    return false
  }
}

export { checkBtnPermission, checkMenuPermission }
