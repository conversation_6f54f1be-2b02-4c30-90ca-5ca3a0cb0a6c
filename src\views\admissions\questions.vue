<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.questionCode"
        placeholder="题号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select filterable v-model="listQuery.subjectId" placeholder="选择科目" clearable class="filter-item" style="width: 140px;" @change="getOutLine">
        <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select filterable v-model="listQuery.outlineId" placeholder="大纲" clearable class="filter-item" style="width: 200px;" @change="getQuestionKeynote">
        <el-option v-for="item in outline" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select filterable v-model="listQuery.keynoteId" placeholder="考点" clearable class="filter-item" style="width: 200px;" @change="refresh">
        <el-option v-for="item in keynote" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select filterable v-model="listQuery.clientCode" placeholder="产品线" clearable class="filter-item" style="width: 200px;" @change="refresh">
        <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['admissions:questions:creat']" class="filter-item" type="primary" size="mini" @click="handleImport">
        批量导入
      </el-button>
    </div>
    <div v-for="(item,index) in questionList" id="question-import" :key="index" class="questions-list">
      <div class="questions-list-title">
        <div>
          <span class="questions-title-left">
            <em>题号:{{ item.questionCode }}</em>
          </span>
          <el-tag type="primary">{{ item.subjectName }}</el-tag>
          <el-tag type="warning">选择题-{{ item.viewType===1?'批量导入':'普通新增' }}</el-tag>
          <el-tag type="danger">{{ item.clientName }}</el-tag>
        </div>
        <div class="questions-btns">
          <el-button v-show="item.viewType===0" v-permission="['admissions:questions:edit']" type="primary" size="mini" @click="editQuestion(item)">编辑</el-button>
          <el-button v-permission="['admissions:questions:del']" type="primary" size="mini" @click="delQuestion(item)">删除</el-button>
          <el-button v-permission="['admissions:questions:check']" type="primary" size="mini" @click="previewQuestion(item,index)">试题预览</el-button>
        </div>
      </div>
      <div class="questions-knowledge">
        <h2 v-if="item.question&&item.viewType!==1">{{ item.question }}</h2>
        <h2 v-if="item.viewType===1&&item.question" v-html="item.question" />
        <img v-if="item.questionImage&&item.questionImage!==null" :src="item.questionImage">
        <div class="questions-knowledge-list"><i>知识点:</i><el-tag v-for="(itemKnowledge,i) in item.keynoteList" :key="i" type="info" size="mini">{{ itemKnowledge }}</el-tag></div>
      </div>
    </div>
    <p v-show="questionList.length===0" class="no-data">暂无数据</p>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <question-preview ref="previewQuestions" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import QuestionPreview from './components/questionPreview'
import { getQuestionList, getQuestionOutline, getQuestionKeynote, getSubjects, removeQuestionItem } from '@/api/admissions'
import { getSysClients } from '@/api/classType'
import { converseEnToCode } from '@/utils/field-conver'
export default {
  name: 'Questions',
  components: {
    Pagination,
    QuestionPreview
  },
  data() {
    return {
      questionIshow: false,
      addQuestionTitle: '添加习题',
      enableList: [],
      total: 0,
      listLoading: true,
      outline: [],
      keynote: [],
      subjectsList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      questionList: [],
      productCodeList: []
    }
  },
  watch: {
    outline: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.outlineId = ''
          this.listQuery.keynoteId = ''
        }
      },
      deep: true
    },
    keynote: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.keynoteId = ''
        }
      },
      deep: true
    },
    'listQuery.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.outlineId = ''
          this.keynote = []
        }
      }
    },
    'listQuery.outlineId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.keynoteId = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getSubjects()
      this.getList()
      this.getSysClientCode()
    })
  },
  methods: {
    async getList() {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery)
      getQuestionList(params).then(res => {
        if (res.code === '000000') {
          const lists = []
          res.data.records.length > 0 ? res.data.records.forEach(item => {
            const objs = {
              id: item.id,
              include: item.include,
              keynoteList: item.keynoteList,
              outlineName: item.outlineName,
              question: item.question,
              questionCode: item.questionCode,
              questionImage: item.questionImage,
              subjectName: item.subjectName,
              type: item.type,
              viewType: item.viewType,
              clientName: this.getClientCode(item.clientCode),
              clientCode: item.clientCode
            }
            lists.push(objs)
          }) : []
          this.questionList = lists || []
          this.total = res.data.total
          this.listLoading = false
          this.$nextTick(() => {
            if (this.commonsVariable.isMathjaxConfig) {
              this.commonsVariable.initMathjaxConfig()
            }
            this.commonsVariable.MathQueue('question-import')
          })
        }
      }).catch(() => {

      })
    },
    getClientCode(code) {
      return converseEnToCode(this.productCodeList, code)
    },
    changeInit() {
      this.questionIshow = false
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getOutLine(val) {

      if (val) {
        getQuestionOutline(val).then(res => {
          if (res.code === '000000') {
            this.outline = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    getQuestionKeynote(val) { // 考点
      if (val) {
        getQuestionKeynote(val).then(res => {
          if (res.code === '000000') {
            this.keynote = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择大纲'
        })
      }
    },
    handleImport() {
      localStorage.setItem('questionName', '新增')
      localStorage.setItem('showFlag', 'upload')
      this.$router.push({
        name: 'CourseUpload',
        params: {
          isEdit: false,
          productCodeList: this.productCodeList
        }
      })
    },
    handleCreat() {
      localStorage.setItem('questionName', '新增')
      localStorage.setItem('showFlag', 'upload')
      this.$router.push({
        name: 'CourseDetail',
        params: {
          isEdit: false
        }
      })
    },
    editQuestion(row) {
      localStorage.setItem('questionName', row.questionCode)
      localStorage.setItem('showFlag', 'edit')
      this.$router.push({
        name: 'CourseDetail',
        params: {
          isEdit: false,
          id: row.id
        }
      })
    },
    previewQuestion(row, index) {
      this.$refs.previewQuestions.questionPreviewPop = true
      this.$refs.previewQuestions.previewObj.indexs = `${index + 1}`
      this.$refs.previewQuestions.questionInfo(row.id)
    },
    delQuestion(row) {
      this.$confirm('确定要删除此题?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeQuestionItem(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    refresh() {
      this.$forceUpdate()
    },
    getSysClientCode() {
      const params = {
        level: 1
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.productCodeList = res.data || []
        }
      }).catch((error) => {

      })
    }
  }
}
</script>

<style scoped>

</style>
