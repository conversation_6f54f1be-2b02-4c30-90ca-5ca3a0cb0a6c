<template>
  <el-select v-model="tmpId" filterable clearable placeholder="请选择课题分类" :disabled="disabled">
    <el-option
            v-for="item in optionList"
            :key="item.id"
            filterable
            :label="item.name"
            :value="Number(item.id)">
    </el-option>
  </el-select>
</template>
<script>
import { getSubjectFromXK } from '@/api/exercises'

export default {
  name: 'SubjectFromXKSelect',
  data: function () {
    return {
      optionList: []
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [Number,String],
      required: false
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    productCode:{
      type: Number,
      required: true,
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId)  : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
    stageId:{
      get() {
        // 100=高中  200=初中  //  4高总.  3初中
        return this.productCode === 100 ? 4 : 3
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedOption)
    },
    getList() {
      this.loading = true
      getSubjectFromXK({ 'stageId': this.stageId }).then(response => {
        if (response.code === '000000') {
          const data = response.data;
          // // 从字符串转换为json
          this.optionList = JSON.parse(data)
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped>
</style>
