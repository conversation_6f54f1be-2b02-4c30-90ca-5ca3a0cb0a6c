import request from '@/utils/request'

/**
 * 获取省市区的接口
 * @param data
 */
export function getArea(parentId) {
  return request({
    url: `stip/regions/subs/${parentId}`,
    method: 'get'
  })
}
/**
 * 获取所有的项目信息
 * @param data
 */
export function getAllProject() {
  return request({
    url: 'common/findProject',
    method: 'get'
  })
}
/**
 * 获取产品类型
 * @param data
 */
export function getProductType(dictCode) {
  return request({
    url: 'dicts/items/' + dictCode,
    method: 'get'
  })
}
/**
 * 查询项目的产品信息
 * @param data
 */
export function getProjectProduct(data) {
  return request({
    url: 'common/findProduct',
    method: 'get',
    params: data
  })
}
/**
 * 查询推荐人列表
 * @param data
 */
export function getRecommentList(data) {
  return request({
    url: 'common/findReferrer',
    method: 'get',
    params: data
  })
}
/**
 * 查询推荐人产品列表
 * @param data
 */
export function getRecommentProductList(data) {
  return request({
    url: 'common/findProductList',
    method: 'get'
  })
}

/**
 * 查询支付记录类型
 * @param data
 */
export function getPayType(type) {
  return request({
    url: 'dicts/items/' + type,
    method: 'get'
  })
}

/**
 * 获取播客产品类型
 * @param data
 */
export function getbo(projectId) {
  return request({
    url: 'product/podcastProducts/' + projectId,
    method: 'get'
  })
}
/**
 * 华为云上传图片/文件成功后调用的接口
 * @param data
 */
export function uploadSuccess(data) {
  return request({
    url: 'stip/resources',
    method: 'POST',
    data: data
  })
}
/**
 * 华为云上传图片/文件成功后调用的接口  (官网banner)
 * @param data
 */
export function uploadBanner(data) {
  return request({
    url: 'websiteBanner/add',
    method: 'POST',
    data: data
  })
}
/**
 * 发送验证码接口--忘记密码
 * @param params
 */
export function sendCode(params) {
  return request({
    url: `messagecenter/sms/send/verificationCode`,
    method: 'POST',
    params
  })
}
/**
 * 找回密码
 * @param data
 */
export function updatePassword(data) {
  return request({
    url: 'user/resetPassword',
    method: 'PUT',
    data: data
  })
}
/**
 * stip 数据字典
 * @param data
 */
export function getDataCode(typeCode) {
  return request({
    url: `dict/selectDictByDictTypeCode?dictTypeCode=${typeCode}`,
    method: 'GET'
  })
}

/**
 * 获取签约主体列表
 * @param typeCode
 * @returns {*}
 */
export function getListSignParty(typeCode) {
  return request({
    url: `/sign/templates/listSignPartyByVersion`,
    method: 'GET'
  })
}

/**
 * crm 数据字典
 * @param data
 */
export function getDataCodeCrm(dictCode) {
  return request({
    url: `/dicts/items/${dictCode}`,
    method: 'GET'
  })
}
/**
 * 根据手机号或用户名查询用户
 * @param data
 */
export function getOperaList(search) {
  return request({
    url: `/user/findUser?search=${search}`,
    method: 'GET'
  })
}
