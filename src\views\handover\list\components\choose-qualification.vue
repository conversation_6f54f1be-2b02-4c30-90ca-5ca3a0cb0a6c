<template>
  <el-dialog v-el-drag-dialog width="80%"   :title="chooseQualificationParam.title" :visible.sync="dialogQualification" :close-on-click-modal="!dialogQualification">
    <el-row>
      <div>
        <el-col :span="24" class="search-wrap">
          <el-col :span="19" class="search-wrap-inner">
            <el-input
              v-model="listQuery.clueCode"
              prefix-icon="el-icon-search"
              placeholder="客户编号"
              class="filter-item"
              clearable
              style="width: 100%;"
              @input="resetKey"
            />
          </el-col>
          <el-col :span="5" class="custorm-serch">
            <el-button type="primary" size="small" round @click="getLists">搜索</el-button>
          </el-col>
        </el-col>
      </div>
      <el-col :span="24">
        <el-table v-loading="qualificationListLoading" :data="qualificationList" border fit stripe highlight-current-row height="700" style="width: 100%;">
          <el-table-column label="#" type="index" width="60" align="center" />
          <af-table-column label="订单编号" prop="orderCode" />
          <af-table-column label="客户编号" width="80" prop="clueCode" />
          <af-table-column label="客户名称" prop="customer" />
          <af-table-column label="校区项目编号" prop="institutionCode" />
          <af-table-column label="校区编号" prop="schoolCode" />
          <af-table-column label="校区名称" prop="schoolName" width="300" show-overflow-tooltip/>
          <af-table-column label="签约区域" show-overflow-tooltip>
            <template slot-scope="{row}">
              {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}
            </template>
          </af-table-column>
          <af-table-column label="加盟项目" prop="projectName" />

          <af-table-column label="操作" show-overflow-tooltip width="100">
            <template slot-scope="{row}">
              <el-button  type="primary" size="mini" @click="lookShowQualification(row)">
                查看
              </el-button>
            </template>
          </af-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-dialog title="查看资质" :visible.sync="innerDialogVisible" append-to-body width="90%">
      <el-row :gutter="10" class="mb10">
        <!--      个人资质-->
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>客户签约资质</span>
            </div>
            <div class="item">
              <el-form
                ref="customerForm"
                size="small"
                label-width="110px"
                :model="customer"
                :disabled="dialogShowQualificationDisable">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:8}">
                    <el-form-item label="客户名称：" prop="userName">
                      <el-input v-model="customer.userName" maxlength="8" minlength="2" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:8}">
                    <el-form-item label="手机号：" prop="phone">
                      <el-input v-model="customer.phone" maxlength="11" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="邮箱：" prop="email">
                      <el-input v-model="customer.email" maxlength="35" />
                    </el-form-item>
                  </el-col> -->
                  <el-col  :xs="{span:24}" :sm="{span:8}">
                    <div v-if="signType===2">
                      <el-form-item label="人员类型：" prop="principalType">
                        <el-select v-model="customer.principalType" placeholder="请选择" filterable>
                          <el-option
                            v-for="item in principalTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div>
                    <div v-else style="visibility: hidden;">
                    <el-form-item label="人员类型：">
                      <!-- 占位但不显示内容 -->
                      <el-input disabled placeholder=" "></el-input>
                    </el-form-item>
                  </div>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:8}">
                    <el-form-item label="证件类型：" prop="certType">
                      <el-select v-model="customer.certType" placeholder="请选择" filterable>
                        <el-option
                          v-for="item in certTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :xs="{span:24}" :sm="{span:8}">
                    <el-form-item label="证件号码：" prop="idCard">
                      <el-input v-model="customer.idCard" maxlength="18" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="银行卡：" prop="bankNumber">
                      <el-input v-model="customer.bankNumber" maxlength="19" />
                    </el-form-item>
                  </el-col> -->
                  <!-- <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="联系地址：" prop="contactAddress">
                      <el-input v-model="customer.contactAddress" maxlength="50" />
                    </el-form-item>
                  </el-col> -->
                  <el-col :xs="{span:24}" :sm="{span:10}">
                    <el-form-item label="收货地址：" prop="areaValid">
                      <area-picker :area-list="areaList" :level="'3'" area-style="width: 100%" class="filter-item" @getAreaList="getAreaList" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:14}">
                    <el-form-item label="详细地址：">
                      <el-input v-model="customer.address" maxlength="50" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                    <el-form-item label="银行卡照片：" class="img-item">
                      <em class="items">*</em>
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="customer.bankCard"
                        :limit="1"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}">
                      </el-upload>
                    </el-form-item>
                  </el-col> -->
                  <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                    <el-form-item label="身份证人像面：" class="img-item">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="customer.idCardFront"
                        :limit="1"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}">
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                    <el-form-item label="身份证国徽面：" class="img-item">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="customer.idCardBack"
                        :limit="1"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}"
                      >

                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}" class="img-item required-items">
                    <el-form-item label="手持身份证：" class="img-item">
                      <em class="items">*</em>
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="customer.idCardHold"
                        :limit="1"
                        :disabled="dialogShowQualificationDisable"
                        :class="{ disabled: true}"
                      >
                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col> -->
                </el-row>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="mb10" :gutter="10">
        <!--      校区资质-->
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>校区签约资质</span>
            </div>
            <div class="item">
              <el-form
                ref="schoolForm"
                size="small"
                label-width="120px"
                :model="school"
                :disabled="dialogShowQualificationDisable"
              >
                <el-row>
                  <el-col v-if="school.status === 2 && school.remark!==''" :span="24">
                    <el-form-item label="驳回理由：">
                      <el-tag type="danger">{{ school.remark }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="校区名称：" prop="schoolName">
                      <el-input v-model="school.schoolName" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="办学年限：" prop="leaseDate">
                      <el-input v-model="school.leaseDate" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="在职老师数：" prop="teacherNumber">
                      <el-input v-model="school.teacherNumber" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="在校学生数：" prop="studentNumber">
                      <el-input v-model="school.studentNumber" />
                    </el-form-item>
                  </el-col> -->
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="签约地址：" prop="address">
                      <div style="display: flex;">
                        <span>{{ school.provinceName }}{{ school.cityName }}{{ school.areaName }}{{ school.countyName }}</span>
                        <el-input v-model="school.address" style="width: 300px; padding-left: 20px" /></div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="img-item required-items">
                    <el-form-item label="校区照片：" class="img-item">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="school.schoolImgs"
                        :limit="3"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}"
                      >
                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="img-item required-items">
                    <el-form-item label="租赁合同：" class="img-item">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="school.leaseContract"
                        :limit="3"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}"
                      >
                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="img-item required-items">
                    <el-form-item label="办学许可证：" class="img-item">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="school.schoolLicense"
                        :limit="3"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}"
                      >
                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="signType!==1" class="mb10" :gutter="10">
        <!--      企业资质-->
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业签约资质</span>
            </div>
            <div class="item">
              <el-form
                ref="enterpriseForm"
                size="small"
                label-width="120px"
                :disabled="dialogShowQualificationDisable"
                :model="enterprise"
              >
                <el-row>
                  <el-col v-if="enterprise.status === 2 && enterprise.remark!==''" :span="24">
                    <el-form-item label="驳回理由：">
                      <el-tag type="danger">{{ enterprise.remark }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="企业名称：" prop="enterpriseName">
                      <el-input v-model="enterprise.enterpriseName" maxlength="40" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="企业法人：" prop="enterpriseLegal">
                      <el-input v-model="enterprise.enterpriseLegal" minlength="2" maxlength="8" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="社会统一码：" prop="creditCode">
                      <el-input v-model="enterprise.creditCode" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="联系电话：" prop="contactPhone">
                      <el-input v-model="enterprise.contactPhone" maxlength="20" />
                    </el-form-item>
                  </el-col> -->
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="注册地址：" prop="enterpriseAddress">
                      <el-input v-model="enterprise.enterpriseAddress" maxlength="50" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="enterprise required-items">
                    <el-form-item label="营业执照：">
                      <el-upload
                        :action="host"
                        list-type="picture-card"
                        :file-list="enterprise.businessLicense"
                        :limit="4"
                        :disabled="dialogShowQualificationDisable"
                        :class="{hide: dialogShowQualificationDisable}"
                      >
                        <i slot="default" class="el-icon-plus" />
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="relate-foot">
          <el-button @click="cancelShowQualification">取 消</el-button>
          <el-button type="primary" @click="confirmCreateShowQualification">使 用</el-button>
      </el-row>
    </el-dialog>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import AreaPicker from '@/components/area-picker'

import { getQualficationDetail, orderListQualification } from '@/api/qualifications'
import { certType, principalType } from '@/utils/field-conver'

export default {
  name: 'ChooseQualification',
  directives: { elDragDialog },
  components: { AreaPicker },
  props: {
    chooseQualificationParam: {
      type: Object
    }
  },
  data() {
    return {
      dialogQualification: false,
      qualificationListLoading: false,
      qualificationList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        clueCode: this.chooseQualificationParam.clueCode,
        contractType: null
      },
      innerDialogVisible: false,
      // 为查看资质处理
      dialogShowQualificationDisable: true,
      signType: null,
      customerQualificationType: '',
      schoolQualificationType: '',
      enterpriseQualificationType: '',
      school: {}, // 校区
      enterprise: {}, // 企业
      customer: {
        certType: '0' // 客户资质
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },

      certTypeList: certType,
      principalTypeList: principalType,
      host: 'https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/',
      //  请求的资质详情
      detailQualfication: {school: {}, customer: {}, enterprise: {}, areaList: {}}
    }
  },
  methods: {
    /**
       * 获取列表
       * */
    getLists() {
      const that = this
      that.listQuery.contractType = that.chooseQualificationParam.contractTypeSign
      const params = this.listQuery

      orderListQualification(params).then(res => {
        that.qualificationList = res.data
        that.dialogQualification = true
      })
    },
    resetKey() {
      this.getLists()
    },

    lookShowQualification(row) {
      this.innerDialogVisible = true
      this.doGetDetailQualfication(row.id)
    },

    cancelShowQualification() {
      this.innerDialogVisible = false
    },

    doGetDetailQualfication(orderId) { // 获取签约资质的详情
      getQualficationDetail(orderId).then(res => {
        if (res.code === '000000') {

          this.signType = res.data.signType || null
          // 个人信息
          this.customer = res.data.signatory || {}

          const deliveryAddress = res.data.deliveryAddress || { areaId: '', cityId: '', provinceId: '' }
          this.areaList = deliveryAddress
          this.detailQualfication.areaList = this.areaList

          if (res.data.signatory !== null) {
            this.customer.bankCard = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 5
            })
            this.customer.idCardFront = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 6
            })
            this.customer.idCardBack = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 7
            })
            this.customer.idCardHold = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 8
            })
            this.customerQualificationType = this.getQualifications(res.data.signatory.status)
          } else {
            this.customer.bankCard = []
            this.customer.idCardFront = []
            this.customer.idCardBack = []
            this.customer.idCardHold = []
          }

          this.customer.address = res.data.deliveryAddress !== null && res.data.deliveryAddress.address ? res.data.deliveryAddress.address : ''

          this.detailQualfication.customer = this.customer
          // 学校信息
          this.school = res.data.school || {}
          if (res.data.school !== null) {
            this.school.leaseContract = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 2
            })
            this.school.schoolLicense = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 3
            })
            this.school.schoolImgs = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 4
            })
            this.schoolQualificationType = this.getQualifications(res.data.school.status)
          }
          this.detailQualfication.school = this.school
          // 企业信息
          this.enterprise = res.data.enterprise || {}
          this.enterprise.businessLicense = (res.data.enterprise && res.data.enterprise.images.length > 0) ? res.data.enterprise.images.filter(item => {
            item.url = item.imagePath
            return item.type === 1
          }) : []
          res.data.enterprise && (this.enterpriseQualificationType = this.getQualifications(res.data.enterprise.status))
          this.chooseQualificationParam.contractType = (this.signType !== 1) ? 2 : 1
          this.detailQualfication.enterprise = this.enterprise
        }
      }).catch(res => {

      })
    },

    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },

    confirmCreateShowQualification() {
      this.innerDialogVisible = false
      this.dialogQualification = false
      this.$emit('chooseQualificationOk', this.detailQualfication)
    },

    getQualifications(type) {
      let str = ''
      switch (type) {
        case 0:
          str = 'warning'
          break
        case 1:
          str = 'success'
          break
        case 2:
          str = 'danger'
          break
      }
      return str
    }

  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
  }

  /deep/ .search-wrap-inner .el-input__inner {
    border-radius: 18px;
  }

  .search-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }



  .custorm-serch {
    display: flex;
    justify-content: flex-end;
  }
  .relate-foot{
    display: flex;
    justify-content: center;
  }

  /deep/ .el-upload--picture-card {
    display: none;
  }
</style>
