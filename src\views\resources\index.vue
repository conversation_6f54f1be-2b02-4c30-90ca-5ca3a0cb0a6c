<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.serialNumber"
        placeholder="盒子序列号(可识别冒号)"
        class="filter-item"
        style="width: 200px;"
        @blur="changeSNCode"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.queryInfo"
        placeholder="合伙人/手机号/校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userName"
        placeholder="学生姓名"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userAccount"
        placeholder="学生账号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.clientId" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="智能终端状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in statusLists" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.isOpen" placeholder="智能终端跨区状态" filterable clearable class="filter-item" style="width: 160px;">
        <el-option v-for="item in regionalStatusLists" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="schoolDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="最近定位开始时间"
        end-placeholder="最近定位结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-tag
              type="success"
              v-if="mainSchoolId"
              style="margin-left: 10px;"
      >
        当前校区ID：{{ mainSchoolId }}
      </el-tag>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
<!--      <el-table-column label="终端编号" show-overflow-tooltip prop="id" width="80px">-->
<!--        <template slot-scope="scope">-->
<!--          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <af-table-column label="盒子序列号" prop="serialNumber" show-overflow-tooltip />
      <af-table-column label="合伙人（机构账号）" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.partnerName&&scope.row.partnerAccount">{{ scope.row.partnerName }}({{ scope.row.partnerAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="schoolName" show-overflow-tooltip >
        <template slot-scope="{row}">
          <a class="codes" @click="toCampusList(row)">{{ row.schoolName }}</a>
        </template>
      </af-table-column>
      <af-table-column label="产品线" prop="clientName" width="80" />
      <el-table-column label="允许跨区" prop="isOpen">
        <template slot-scope="{row}">
          <el-tag v-if="row.isOpen===1" type="success">允许</el-tag>
          <el-tag v-if="row.isOpen===0" type="info">禁止</el-tag>
        </template>
      </el-table-column>
      <af-table-column label="最近定位时间" prop="gpsTime"   width="155"/>
      <af-table-column label="校区地址" prop="schoolAddress" />
      <af-table-column label="定位地址" prop="gpsAddress" />
      <af-table-column label="最近登录学生（学生账号）">
        <template slot-scope="scope">
          <span v-if="scope.row.userName">{{ scope.row.userName }}</span>
          <span v-if="scope.row.userAccount">{{ scope.row.userAccount }}</span>
        </template>
      </af-table-column>
      <af-table-column label="盒子状态" prop="status" :formatter="getStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="300" fixed="right">
        <template slot-scope="{row}">
          <!--          isOpen	是否开放跨区[0:不开放，1:开放]-->
          <el-button v-permission="['resources:list:InterregionalCheck']" type="text" size="mini" @click="interregionalSet(row)">查看位置</el-button>
          <!--          <el-button v-permission="['resources:list:interregional']" type="primary" size="mini" @click="interregionalOpen(row)">{{ row.isOpen===1?'关闭跨区':'开启跨区' }}</el-button>-->
          <el-button v-permission="['resources:list:interregional']" type="text" size="mini" @click="regionalSettingPop=true,regionalSetting(row)">跨区设置</el-button>
          <el-button v-permission="['resources:list:interregionalOpera']" type="text" size="mini" @click="operation(row)">{{ row.status===2?'启用':'禁用' }}</el-button>
          <el-button v-permission="['resources:list:operaRecords']" type="text" size="mini" @click="recordsPop=true,operaRecords(row)">操作记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <interregional-pop ref="interregional" />
    <el-dialog :title="recordsTitle" :visible.sync="recordsPop" :close-on-click-modal="!recordsPop">
      <el-table
        ref="assignTab"
        v-loading="recordsLoading"
        :data="listRecords"
        border
        fit
        stripe
        highlight-current-row
        style="width: 100%;"
      >
        <af-table-column label="操作人" prop="operationUser" show-overflow-tooltip />
        <af-table-column label="操作类型" prop="operationTypeName" show-overflow-tooltip />
        <af-table-column label="变更前" prop="changeBefore" show-overflow-tooltip />
        <af-table-column label="变更后" prop="changeAfter" show-overflow-tooltip />
        <af-table-column label="操作时间" prop="operationTime" show-overflow-tooltip />
      </el-table>
    </el-dialog>
    <!--跨区设置弹框-->
    <el-dialog title="跨区设置" :visible.sync="regionalSettingPop" :close-on-click-modal="!regionalSettingPop" width="70%" @close="regionalSettingCancel">
      <el-form :model="regionalForm" label-width="120px">
        <el-form-item label="跨区状态：">
          <el-radio-group v-model="regionalForm.isOpen">
            <el-radio :label="1">允许跨区</el-radio>
            <el-radio :label="0">禁止跨区</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="regionalForm.isOpen === 1">
          <el-form-item label="跨区有效期：">
            <el-checkbox v-model="openDateChecked">开启</el-checkbox>
            <el-date-picker
              v-if="openDateChecked"
              v-model="openDate"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              style="margin-left: 20px"
            />
          </el-form-item>
          <el-form-item label="地区范围限制：">
            <el-checkbox v-model="openLocationChecked">开启</el-checkbox>
            <el-button v-if="openLocationChecked" type="primary" size="mini" style="margin-left: 20px" @click="getLatitude">设置区域</el-button>
          </el-form-item>
          <el-form-item v-if="openLocationChecked && regionalForm.address" label="当前允许区域：">
            <span>{{ regionalForm.address }} 附近{{ regionalForm.locationRadius }}米</span>
          </el-form-item>
          <el-form-item label="允许观看班型：" required>
            <el-checkbox v-if="regionalForm.openClass.length>0" v-model="checkAll" @change="handleCheckAllChange(checkAll)">全选</el-checkbox>
            <el-checkbox-group v-model="checkedOpenClass" @change="handleCheckedClassChange(checkedOpenClass)">
              <el-checkbox v-for="openClass in regionalForm.openClass" :key="openClass.classTypeId" :label="openClass.classTypeId" style="width: 18%">{{ openClass.classTypeName }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="regionalSettingCancel">取消</el-button>
          <el-button type="primary" size="mini" @click="regionalSettingConfirm">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <school-map ref="schoolMap" @refresh="getMapData" />


<!--    <el-dialog-->
<!--            title="校区信息"-->
<!--            :visible.sync="schoolDialog"-->
<!--            width="90%"-->
<!--            center-->
<!--            :append-to-body="true"-->
<!--            >-->
<!--      <SchoolProject :agency-id="this.agencyId"></SchoolProject>-->
<!--    </el-dialog>-->
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { clientCode } from '@/api/classType'
import { getResourceList, interregionalOpen, isStatus, resourceDeviceLogs, getOpenSetting, saveOpenSetting } from '@/api/charge'
import { statusLists, regionalStatusLists, converseEnToCn } from '@/utils/field-conver'
import InterregionalPop from './components/interregionalPop'
import SchoolMap from '@/views/customer/componets/schoolMapNew'
import { checkMenuPermission } from '@/utils/permission'
import SchoolProject from '@/views/schoolProject/index.vue'
export default {
  name: 'ResourcesList',
  components: {
    SchoolProject,
    Pagination,
    InterregionalPop,
    SchoolMap
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      statusLists: statusLists,
      regionalStatusLists: regionalStatusLists,
      recordsPop: false,
      recordsTitle: '',
      listRecords: [],
      recordsLoading: true,
      schoolDate: [],
      regionalSettingPop: false,
      regionalForm: {
        isOpen: 0,
        locationRadius: 300, // 开启地区范围限制-定位半径(米)
        lockLocation: 0,
        openClass: []
      },
      openDateChecked: false,
      openLocationChecked: false,
      openDate: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      },
      openClassInfo: {
        checked: 0,
        classTypeId: -1,
        classTypeName: ''
      },
      agencyId:'',
      schoolDialog:false,
      checkAll: false,
      // 跨区时，该智能终端可以观看的班型id列表
      checkedOpenClass: []
    }
  },
  props:{
    mainSchoolId:{
      type: Number,
      default: null,
      required: false,
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
    })
  },
  methods: {
    changeSNCode(){
      this.listQuery.serialNumber = this.listQuery.serialNumber.replace(/:/g, '')
    },
    getCode() {
      clientCode().then(res => { // 1产品线 2客户端
        this.clientCode = res.data.filter(item => item.level === 1) || []
      })
    },
    toCampusList(row) { // 调到校区管理页面
      if(checkMenuPermission(['customer:schoolProject'])){
        this.$router.push({
          name: 'SchoolProject',
          query: {
            agencyId: row.mainSchoolId
          }
        })

      }
    },
    async getList(row) {
      // if(row){
      //   const { column, prop, order } = row
      //   //排序
      //   this.listQuery.sortField = prop
      //   this.listQuery.sort = order==='ascending'?'asc':'desc'
      // }
      const that = this
      that.listLoading = true
      if(this.mainSchoolId){
        that.listQuery.mainSchoolId = this.mainSchoolId
      }
      const params = Object.assign(that.listQuery, { beginTime: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[0] : '', endTime: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[1] : '' })


      await getResourceList(params).then(response => {
        that.total = response.data.total
        that.listLoading = false
        const lists = response.data.records
        const arr = []
        for (let m = 0; m < lists.length; m++) {
          const obj = {}
          obj['mainSchoolId']=lists[m].mainSchoolId
          obj['id'] = lists[m].id
          obj['serialNumber'] = lists[m].serialNumber
          obj['partnerName'] = lists[m].partnerName
          obj['partnerAccount'] = lists[m].partnerAccount
          obj['schoolName'] = lists[m].schoolName
          obj['clientName'] = lists[m].clientName
          obj['gpsAddress'] = lists[m].gpsAddress
          obj['userAccount'] = lists[m].userAccount
          obj['userName'] = lists[m].userName
          obj['status'] = lists[m].status
          obj['gpsTime'] = lists[m].gpsTime
          obj['isOpen'] = lists[m].isOpen
          if (lists[m].provinceName !== null && lists[m].cityName !== null && lists[m].areaName !== null) {
            if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = ''
              obj['city'] = ''
              obj['area'] = ''
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = ''
              obj['city'] = lists[m].cityName
              obj['area'] = lists[m].areaName
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = ''
              obj['city'] = lists[m].cityName
              obj['area'] = ''
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = ''
              obj['city'] = ''
              obj['area'] = lists[m].areaName
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = lists[m].provinceName
              obj['city'] = ''
              obj['area'] = ''
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = lists[m].provinceName
              obj['city'] = lists[m].cityName
              obj['area'] = ''
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = lists[m].provinceName
              obj['city'] = lists[m].cityName
              obj['area'] = lists[m].areaName
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = lists[m].provinceName
              obj['city'] = ''
              obj['area'] = lists[m].areaName
            }
          } else {
            obj['province'] = ''
            obj['city'] = ''
            obj['area'] = ''
          }
          if (lists[m].areaName !== null) {
            if (lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['area'] = ''
            } else {
              obj['area'] = lists[m].areaName
            }
          } else {
            obj['area'] = ''
          }
          obj['schoolAddress'] = lists[m].schoolAddress !== null ? `${obj['province']}${obj['city']}${obj['area']}${lists[m].schoolAddress}` : ''
          arr.push(obj)
        }

        that.list = arr
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.schoolDate = []
      this.getList()
    },
    getStatus(row) {
      return converseEnToCn(this.statusLists, row.status)
    },
    getIsOpen(row) {
      return converseEnToCn(this.regionalStatusLists, row.isOpen)
    },
    getDetail(row) { // 获取列表详情
    },
    interregionalSet(row) {
      this.$refs.interregional.addressDetail = row.schoolAddress
      this.$refs.interregional.gpsAddress = row.gpsAddress
      this.$refs.interregional.gpsTime = row.gpsTime
      this.$refs.interregional.getDeviceCheckRegion(row.id)
      this.$refs.interregional.interregionalPop = true
    },
    operation(row) {
      const title = row.status === 2 ? '确认进行恢复正常操作' : '确认禁用跨区'
      const operaTitle = row.status === 2 ? '智能终端恢复正常!' : '智能终端禁用跨区成功!'
      const params = {
        id: row.id,
        status: row.status
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        isStatus(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${operaTitle}`
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    interregionalOpen(row) {
      const title = row.isOpen === 0 ? '是否开启跨区' : '是否关闭跨区'
      const params = {
        id: row.id
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        interregionalOpen(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    /**
     * 打开地图获取经纬度
     */
    getLatitude() {

      this.$refs.schoolMap.getLonAndLat(this.areaList, this.regionalForm.address, this.regionalForm.locationRadius)
      this.$refs.schoolMap.getMapData('', this.regionalForm.address)
    },
    /**
     * 获取 地图选择的经纬度
     */
    getMapData(val) {
      this.$set(this.regionalForm, 'address', val.addressDetail)
      this.$set(this.regionalForm, 'longitude', val.lng)
      this.$set(this.regionalForm, 'latitude', val.lat)
      this.$set(this.regionalForm, 'locationRadius', val.defaulRadius)
      this.$set(this.areaList, 'provinceId', val.areaList.provinceId)
      this.$set(this.areaList, 'cityId', val.areaList.cityId)
      this.$set(this.areaList, 'areaId', val.areaList.areaId)
      this.$set(this.areaList, 'countyId', val.areaList.countyId)
    },
    // 全选操作
    handleCheckAllChange(val) {
      if (val) {
        this.regionalForm.openClass.forEach(item => {
          this.checkedOpenClass.push(item.classTypeId)
        })
      } else {
        this.checkedOpenClass = []
      }
      this.$forceUpdate()
    },
    // 单个班型选择监听
    handleCheckedClassChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.regionalForm.openClass.length
    },
    // 获取跨区域设置
    regionalSetting(row) {
      const that = this
      getOpenSetting(row.id).then(res => {
        if (res.code === '000000') {
          that.regionalForm = JSON.parse(JSON.stringify(res.data))
          // 跨区有效期
          that.openDate = that.regionalForm.openStartDate && that.regionalForm.openEndDate ? [that.regionalForm.openStartDate, that.regionalForm.openEndDate] : []
          that.openDateChecked = that.openDate.length !== 0
          that.openLocationChecked = that.regionalForm.lockLocation === 1
          // 跨区地区范围限制
          that.areaList = {
            provinceId: that.regionalForm.provinceId,
            cityId: that.regionalForm.cityId,
            areaId: that.regionalForm.areaId,
            countyId: that.regionalForm.countyId
          }
          // 允许观看班型
          that.regionalForm.openClass.forEach(item => {
            if (item.checked === 1) that.checkedOpenClass.push(item.classTypeId)
          })
          that.checkAll = that.checkedOpenClass.length === that.regionalForm.openClass.length
        }
      })
    },
    regionalSettingCancel() {
      this.regionalSettingPop = false
      this.openDateChecked = false
      this.openLocationChecked = false
      this.regionalForm = {}
      this.checkedOpenClass = []
      this.checkAll = false
    },
    // 保存终端跨区设置
    regionalSettingConfirm() {
      let data = {}
      if (this.regionalForm.isOpen === 1) {
        // 允许跨区
        if (this.openDateChecked) {
          if (!this.openDate || this.openDate.length === 0) {
            this.$message({
              type: 'warning',
              message: '跨区有效期不能为空！'
            })
            return
          }
          if (this.openDate[0] === this.openDate[1]) {
            this.$message({
              type: 'warning',
              message: '跨区有效期起始日期不能相同！'
            })
            return
          }
        } else {
          // 跨区有效期关闭  清空时间
          this.openDate = ['', ''];
        }
        if (this.openLocationChecked && !this.regionalForm.address) {
          this.$message({
            type: 'warning',
            message: '地区范围限制不能为空！'
          })
          return
        }
        if (!this.checkedOpenClass || this.checkedOpenClass.length === 0) {
          this.$message({
            type: 'warning',
            message: '允许观看班型不能为空！'
          })
          return
        }
        data = Object.assign(this.regionalForm, this.areaList, {
          openStartDate: this.openDate[0],
          openEndDate: this.openDate[1],
          openClasses: this.checkedOpenClass,
          lockLocation: this.openLocationChecked ? 1 : 0
        })
      } else {
        // 禁止跨区
        data = {
          id: this.regionalForm.id,
          isOpen: 0
        }
      }

      saveOpenSetting(data).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.regionalSettingCancel()
          this.getList()
        }
      }).catch(() => {

      })
    },
    operaRecords(row) {
      this.recordsTitle = `${row.serialNumber}操作记录`
      resourceDeviceLogs(row.serialNumber).then(res => {
        if (res.code === '000000') {

          this.listRecords = res.data || []
          this.recordsLoading = false
        }
      }).catch(() => {

      })
    }
  }
}
</script>

<style scoped>
  .codes{
    color: #46a6ff;
    text-decoration: underline;
  }
  /deep/ .el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered {
      margin-left: 0px;
  }
</style>
