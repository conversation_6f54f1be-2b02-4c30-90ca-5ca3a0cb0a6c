<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="智能终端ID"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.serialNumber"
        placeholder="智能终端序列号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userName"
        placeholder="学生姓名"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userAccount"
        placeholder="学生账号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="智能终端状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in statusLists" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.beginTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="创建开始日期"
      />
      <el-date-picker
        v-model="listQuery.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="创建结束日期"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="智能终端ID" show-overflow-tooltip prop="id" />
      <af-table-column label="智能终端序列号" show-overflow-tooltip prop="serialNumber" />
      <af-table-column label="校区地址" prop="schoolAddress" show-overflow-tooltip />
      <af-table-column label="定位地址" prop="gpsAddress" show-overflow-tooltip />
      <af-table-column label="所属学生(学生账号)">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
          <span v-if="scope.row.userAccount">({{ scope.row.userAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="智能终端状态" prop="status" :formatter="getStatus" />
      <af-table-column label="最近登录时间" prop="loginTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="280" fixed="right">
        <template slot-scope="{row}">
          <!--          isOpen	是否开放跨区[0:不开放，1:开放]-->
          <el-button v-permission="['customer:schoolProject:InterregionalCheck']" type="primary" size="mini" @click="interregionalSet(row)">跨区校验</el-button>
          <el-button v-permission="['customer:schoolProject:interregional']" type="primary" size="mini" @click="interregionalOpen(row)">{{ row.isOpen===1?'关闭跨区':'开启跨区' }}</el-button>
          <!--<el-button v-if="row.isOpen===0" type="primary" size="mini" @click="interregionalOpen(row)">开启跨区</el-button>-->
          <el-button v-permission="['customer:schoolProject:interregionalOpera']" type="primary" size="mini" @click="operation(row)">{{ row.status===2?'恢复正常':'跨区禁用' }}</el-button>
          <!--<el-button v-if="row.status===1" type="primary" size="mini" @click="operation(row)">跨区禁用</el-button>-->
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <interregional-pop ref="interregional" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import InterregionalPop from '../../resources/components/interregionalPop'
import { statusLists, converseEnToCn } from '@/utils/field-conver'
import { interregionalOpen, isStatus } from '@/api/charge'
import { terminalGPSList } from '@/api/schoolCampus'
export default {
  name: 'BoxLocation',
  components: {
    Pagination,
    InterregionalPop
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      assignSatuts: [],
      statusLists: statusLists,
      mainSchoolId: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.mainSchoolId = this.$route.query.id

      this.getList()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { mainSchoolId: this.mainSchoolId })
      await terminalGPSList(params).then(response => {
        that.total = response.data.total
        that.listLoading = false
        const lists = response.data.records
        const arr = []
        for (let m = 0; m < lists.length; m++) {
          const obj = {}
          obj['id'] = lists[m].id
          obj['serialNumber'] = lists[m].serialNumber
          obj['partnerName'] = lists[m].partnerName
          obj['partnerAccount'] = lists[m].partnerAccount
          obj['schoolName'] = lists[m].schoolName
          obj['clientName'] = lists[m].clientName
          obj['gpsAddress'] = lists[m].gpsAddress
          obj['userAccount'] = lists[m].userAccount
          obj['userName'] = lists[m].userName
          obj['status'] = lists[m].status
          obj['loginTime'] = lists[m].loginTime
          obj['isOpen'] = lists[m].isOpen
          if (lists[m].provinceName !== null && lists[m].cityName !== null && lists[m].areaName !== null) {
            if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = ''
              obj['city'] = ''
              obj['area'] = ''
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = ''
              obj['city'] = lists[m].cityName
              obj['area'] = lists[m].areaName
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = ''
              obj['city'] = lists[m].cityName
              obj['area'] = ''
            } else if (lists[m].schoolAddress.includes(`${lists[m].provinceName}`) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = ''
              obj['city'] = ''
              obj['area'] = lists[m].areaName
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = lists[m].provinceName
              obj['city'] = ''
              obj['area'] = ''
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['province'] = lists[m].provinceName
              obj['city'] = lists[m].cityName
              obj['area'] = ''
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].cityName}`)) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = lists[m].provinceName
              obj['city'] = lists[m].cityName
              obj['area'] = lists[m].areaName
            } else if (!(lists[m].schoolAddress.includes(`${lists[m].provinceName}`)) && lists[m].schoolAddress.includes(`${lists[m].cityName}`) && !(lists[m].schoolAddress.includes(`${lists[m].areaName}`))) {
              obj['province'] = lists[m].provinceName
              obj['city'] = ''
              obj['area'] = lists[m].areaName
            }
          } else {
            obj['province'] = ''
            obj['city'] = ''
            obj['area'] = ''
          }
          if (lists[m].areaName !== null) {
            if (lists[m].schoolAddress.includes(`${lists[m].areaName}`)) {
              obj['area'] = ''
            } else {
              obj['area'] = lists[m].areaName
            }
          } else {
            obj['area'] = ''
          }
          obj['schoolAddress'] = lists[m].schoolAddress !== null ? `${obj['province']}${obj['city']}${obj['area']}${lists[m].schoolAddress}` : ''
          arr.push(obj)
        }

        that.list = arr
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    interregionalSet(row) {
      this.$refs.interregional.addressDetail = row.schoolAddress
      this.$refs.interregional.getDeviceCheckRegion(row.id)
      this.$refs.interregional.interregionalPop = true
    },
    operation(row) {
      const title = row.status === 2 ? '确认进行恢复正常操作' : '确认禁用跨区'
      const params = {
        id: row.id,
        status: row.status
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        isStatus(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    interregionalOpen(row) {
      const title = row.isOpen === 0 ? '是否开启跨区' : '是否关闭跨区'
      const params = {
        id: row.id
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        interregionalOpen(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getStatus(row) {
      return converseEnToCn(this.statusLists, row.status)
    }
  }
}
</script>

<style scoped>

</style>
