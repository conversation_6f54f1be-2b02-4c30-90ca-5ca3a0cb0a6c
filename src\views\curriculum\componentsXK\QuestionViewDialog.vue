<template>
  <el-dialog :title="`共找到 ${getQuestionList.length} 道题目，点击题目进行选择`"
             :visible="showTree"
             @close="handleClose"
             :append-to-body="true"
             top="0"
             v-loading="loading"
             width="80%">
    <div style="height:85vh;overflow-y: auto;overflow-x: hidden;" >
      <QuestionView title="点击进行选择" v-for="(question,index) in getQuestionList" :question="question"
                    :selected="hasSelected(question)" :key="index"
                    @tap="toggleSelect"></QuestionView>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-tooltip class="item" effect="dark" content="没有合适的？尝试换一换" placement="top">
          <el-button type="primary" plain class="fl" @click="reload" v-if="queryParam">没找到合适的？点击换一换</el-button>
        </el-tooltip>

        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedQuestions.length===0">保存 ({{
            selectedQuestions.length
          }}道题)</el-button>
      </span>
  </el-dialog>
</template>
<script>
import QuestionView from '@/views/curriculum/componentsXK/QuestionView.vue'
import { getQuestionFromXK } from '@/api/exercises'

export default {
  name: 'QuestionViewDialog',
  components: { QuestionView },
  props: {
    questions: {
      type: Array,
      required: true
    },
    queryParam: {
      type: Object,
      required: false,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      loading: false,
      showTree: true,
      localQuestionList: [],
      selectedQuestions: []
    }
  },
  created() {
    // this.questions.forEach(question => {
    //     this.selectedQuestions.push(question)
    // })
  },
  computed: {
    getQuestionList() {
      return this.questions.concat(...this.localQuestionList)
    }
  },
  methods: {
    handleClose() {
      this.showTree = false
      this.$emit('close')
    },
    handleConfirm() {
      if (this.selectedQuestions.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择试题！'
        })
        return
      }
      this.$emit('ok', this.selectedQuestions)
      this.handleClose()
    },
    reload() {
      if (this.queryParam) {
        this.loading = true
        getQuestionFromXK(this.queryParam).then((res) => {
          if (res.code === '000000') {
            if (res.data.length === 0) {
              this.$message({ type: 'warning', message: '未获取到相关试题！' })
            }else{
              this.localQuestionList =this.localQuestionList.concat(...res.data)
              this.$notify.success(`追加了${res.data.length}道题`)
            }
            this.loading = false
          }
        })
      }
    },
    toggleSelect(item) {
      if (this.selectedQuestions.indexOf(item) > -1) {
        this.selectedQuestions.splice(this.selectedQuestions.indexOf(item), 1)
      }
      else {
        this.selectedQuestions.push(item)
      }
    },
    hasSelected(question) {
      return this.selectedQuestions.indexOf(question) > -1
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog__body {
  padding: 0 20px;
}
</style>
