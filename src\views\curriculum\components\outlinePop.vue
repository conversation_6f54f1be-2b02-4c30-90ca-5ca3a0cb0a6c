<template>
  <el-dialog :visible.sync="outlinePop" :title="outlineTitle" :close-on-click-modal="!outlinePop" width="60%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="outlineForm" :model="listQuery" :rules="rules" label-width="100px">
        <el-form-item label="班型名称" prop="classTypeId">
          <el-select v-model="listQuery.classTypeId" placeholder="请选择班型名称" clearable class="filter-item" :disabled="isEdit" @change="getMaterial" filterable>
            <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="适用科目" prop="subjectId">
          <el-select v-model="listQuery.subjectId" placeholder="请选择适用科目" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="教材版本" prop="materialId">
          <el-select v-model="listQuery.materialId" placeholder="请选择教材版本" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in materials" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="大纲状态" prop="status">
          <el-select v-model="listQuery.status" placeholder="请选择大纲状态" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="大纲名称" prop="title">
          <el-input v-model="listQuery.title" placeholder="请输入大纲名称" maxlength="20" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="大纲排序" prop="sort">
          <el-input v-model="listQuery.sort" placeholder="请输入大纲排序" maxlength="20" :disabled="isEdit" />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="outlinePop=false,cancelClass()">取消</el-button>
      <el-button type="primary" size="mini" @click="custormClass">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getOutLine, getAllClassType, getAllSubjects, getMaterials, addOutLine } from '@/api/classType'
import { enableList } from '@/utils/field-conver'
export default {
  name: 'AddClassPop',
  data() {
    return {
      outlineTitle: '',
      outlinePop: false,
      listQuery: {
      },
      rules: {
        classTypeId: { required: true, trigger: 'blur', message: '请选择班型' },
        subjectId: { required: true, trigger: 'blur', message: '请选择科目' },
        materialId: { required: true, trigger: 'blur', message: '请选择教材版本' },
        status: { required: true, trigger: 'blur', message: '请输入大纲名称' },
        title: { required: true, trigger: 'blur', message: '请输入大纲名称' }
      },
      enableList: enableList,
      isEdit: false,
      allClassType: [],
      subjectsAll: [],
      materials: [],
      enableFlagsCreate: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getAllClassType()
      this.getAllSubjects()
    })
  },
  methods: {
    getOutLine(ids) {
      getOutLine(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
          this.getAllClassType()
          this.getAllSubjects()
          this.getMaterial(this.listQuery.classTypeId)
        }
      })
    },
    getAllClassType() { // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data
      })
    },
    getAllSubjects() { // 有效科目
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data
        }
      })
    },
    getMaterial(val) { // 根据班型获取教材
      getMaterials(val).then(res => {
        if (res.code === '000000') {
          this.materials = res.data
        }
      })
    },
    cancelClass() {
      this.listQuery = {}
      if (this.$refs.outlineForm) {
        this.$refs.outlineForm.clearValidate()
      }
    },
    custormClass() {
      this.enableFlagsCreate = true
      this.$refs.outlineForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery, { id: this.listQuery.id ? this.listQuery.id : '' })
          addOutLine(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$emit('addOutLineList')
              this.outlinePop = false
              this.listQuery = {}
              this.enableFlagsCreate = false
              this.$refs.outlineForm.clearValidate()
            }
          }).catch(() => {
            this.enableFlagsCreate = false

          })
        } else {
          this.enableFlagsCreate = false
          return false
        }
      })
    },
    changeInit() {
      this.listQuery = {}
      if (this.$refs.outlineForm) {
        this.$refs.outlineForm.clearValidate()
      }
    }
  }
}
</script>

<style scoped>
  .assign-operas{
    display: flex;
    justify-content: center;
    align-content: center;
  }
</style>
