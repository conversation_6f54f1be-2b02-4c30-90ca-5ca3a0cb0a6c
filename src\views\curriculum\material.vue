<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        placeholder="教材版本"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.clientCode" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="版本状态" clearable class="filter-item" style="width: 140px;" filterable>
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建开始日期"
        end-placeholder="创建结束日期"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:material:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="教材编号" show-overflow-tooltip prop="id" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
      <af-table-column label="教材版本" show-overflow-tooltip prop="title" />
      <af-table-column label="适用产品线" prop="clientName" show-overflow-tooltip />
      <af-table-column label="状态" prop="status" show-overflow-tooltip>
        <template slot-scope="scope">
          <EnableStatusTag :status="scope.row.status"></EnableStatusTag>
        </template>
      </af-table-column>
      <af-table-column label="排序" prop="sort" />
      <af-table-column label="更新时间" prop="updateTime" />
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="260">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:material:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['curriculum:material:opera']" type="primary" size="mini" @click="enable(row)">{{ row.status===1?'禁用':'启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改教材弹框-->
    <AddMaterial ref="materials" :model-type="modelType" @addMaterialList="getList" />
    <SubMaterialPop v-if="materialId"  :material-id="materialId" @close="handleClose"></SubMaterialPop>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AddMaterial from './components/addMaterial.vue'
import { materialList, clientCode, enable } from '@/api/classType'
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
import EnableStatusTag from '@/components/StatusTag/EnableStatusTag.vue'
import SubMaterialPop from '@/views/curriculum/components/subMaterialPop.vue'
export default {
  name: 'Material',
  components: {
    SubMaterialPop,
    EnableStatusTag,
    Pagination,
    AddMaterial
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        clientCode:100
      },
      clientCode: [],
      enableList: enableList,
      followDate: [],
      materialId: ''
    }
  },
  props:{
    modelType: {
      type: [String,Number],
      default: 1
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 1)
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' },{type:this.modelType})
      await materialList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        clientCode:100
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.materials.materialPop = true
      this.$refs.materials.materialTitle = '新增教材'
      this.$refs.materials.isEdit = false
      this.$refs.materials.flags = 1
      this.$refs.materials.listQuery.status = 1
    },
    handleUpdate(row) {
      this.$refs.materials.materialPop = true
      this.$refs.materials.materialTitle = '修改教材'
      this.$refs.materials.getMaterialList(row.id)
      this.$refs.materials.isEdit = false
      this.$refs.materials.flags = 0
    },
    handleSub(row){
      this.materialId=row.id
    },
    handleClose(){
      this.materialId=''
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    getDetail(row) {
      this.$refs.materials.materialPop = true
      this.$refs.materials.materialTitle = `教材详情`
      this.$refs.materials.getMaterialList(row.id)
      this.$refs.materials.isEdit = true
    },
    enable(row) { // 启用/禁用
      const title = row.status === 1 ? '禁用此项会造成引用此项的数据显示异常，确认删除?' : '确定要启用吗?'
      const tips = row.status === 1 ? '禁用成功' : '启用成功'
      this.$confirm(`${title}`, {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        enable(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${tips}`
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
