import request from '@/utils/request'
/**
 * 新增校区
 */
export function createNewSchool(data) {
  return request({
    url: 'clueSchool/add',
    method: 'POST',
    data: data
  })
}
/**
 * 获取校区详情
 */
export function getSchoolDetail(data) {
  return request({
    url: 'clueSchool/getClueSchool',
    method: 'get',
    params: data
  })
}
/**
 * 修改校区
 */
export function updateSchool(data) {
  return request({
    url: 'clueSchool/modify',
    method: 'PUT',
    data: data
  })
}
/**
 * 修改校区
 */
export function deleteSchool(data) {
  return request({
    url: 'clueSchool/remove',
    method: 'DELETE',
    params: data
  })
}
/**
 * 增加校区
 * @param data
 */
export function addSchoolProjectDetail(data) {
  return request({
    url: 'institutions',
    method: 'POST',
    data: data
  })
}
