<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.itemName" placeholder="字典内容名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.valid" placeholder="是否启用" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in menuValidList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      height="500px"
    >
      <el-table-column label="字典内容名称" align="center" prop="itemName" width="120" />
      <el-table-column label="字典值" align="center" prop="itemValue" width="100" />
      <el-table-column label="排序值" align="center" prop="sortOrder" width="100" />
      <el-table-column label="描述" align="center" prop="description" width="200" />
      <el-table-column label="是否可用" prop="valid" width="80" align="center" :formatter="getMenuValidList" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <drawer-dialog ref="drawerDialog" style="z-index: 3000" :is-edit="isEdit" :dict-id="dictId" @refresh="getList" />
  </div>
</template>

<script>
import { getDictionaryDetailList } from '@/api/system-setting'
import { menuValidList, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import drawerDialog from './drawerDialog'
export default {
  name: 'DictDrawer',
  components: { Pagination, drawerDialog },
  directives: {},
  props: {
    dictId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        dictId: 0
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      menuValidList: menuValidList,
      drawer: false
    }
  },
  created() {
    this.listLoading = false
    this.list = []
    this.getList()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增字典内容
     * */
    handleAdd() {
      this.$refs.drawerDialog.getDetail()
      this.isEdit = false
    },

    getList() {
      const that = this
      that.listQuery.dictId = that.dictId
      getDictionaryDetailList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    /**
     * 修改字典内容
     * */
    handleUpdate(row) {
      this.isEdit = true
      const form = JSON.parse(JSON.stringify(row))
      this.$refs.drawerDialog.editDetail(form)
    },
    getMenuValidList(row) {
      return converseEnToCn(menuValidList, row.valid)
    }
  }
}
</script>

<style scoped>

</style>
