<template>
  <el-select v-model="tmpId" filterable clearable placeholder="请选择学制" :disabled="disabled">
    <el-option
      v-for="item in optionList"
      :key="item.id"
      filterable
      :label="item.name"
      :value="item.id">
    </el-option>
  </el-select>
</template>
<script>
export default {
  name: 'EduSystemFromXKSelect',
  data: function () {
    return {
      optionList: [
        { name: '五四学制', id: 'G54' },
        { name: '六三学制', id: 'G63' }
      ]
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [Number, String],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    productCode: {
      type: Number,
      required: true,
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? this.targetId : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
    stageId: {
      get() {
        // 100=高中  200=初中  //  4高中.  3初中
        return this.productCode === 100 ? 4 : 3
      }
    }
  },
  created() {
  },
  methods: {
    handleChange(value) {
      return this.$emit('change', value)
    },
  }
}
</script>
