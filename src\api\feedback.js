import request from '@/utils/request'
/**
 * 获取机构反馈列表
 * @param data
 */
export function getnstitutionsList(data) {
  return request({
    url: 'feedback/partnerList',
    method: 'POST',
    data: data
  })
}
/**
 * 获取学生反馈列表
 * @param data
 */
export function getStudents(data) {
  return request({
    url: 'feedback/studentList',
    method: 'POST',
    data: data
  })
}

/**
 * 系统反馈列表
 * @param data
 */
export function systemFeedback(data) {
  return request({
    url: 'feedback/partnerList',
    method: 'GET',
    params: data
  })
}
/**
 * 问题分类列表
 * @param data
 */
export function questionCategories() {
  return request({
    url: 'feedback/categories',
    method: 'GET'
  })
}
/**
 * 问题分类修改
 * @param data
 */
export function editQuestionCategories(data) {
  return request({
    url: 'feedback/changeCategory',
    method: 'POST',
    data: data
  })
}
/**
 * 机构反馈详情
 * @param data
 */
export function feedbackIn(id) {
  return request({
    url: `feedback/detail/${id}`,
    method: 'GET'
  })
}
/**
 * 意见反馈客服回复
 * @param data
 */
export function replayInfo(data) {
  return request({
    url: `feedback/replay`,
    method: 'POST',
    data: data
  })
}
/**
 * 意见反馈客服回复
 * @param data
 */
export function replayRefused(data) {
  return request({
    url: `feedback/denyFeedback`,
    method: 'POST',
    data: data
  })
}
/**
 * 反馈满意度排名
 * @param data
 */
export function feedbackRanking(data) {
  return request({
    url: `feedback/analysis`,
    method: 'GET',
    params: data
  })
}

/**
 * 获取内部需求列表
 * @param data
 */
export function requirementPage(data) {
  return request({
    url: 'requirement/page',
    method: 'GET',
    params: data
  })
}

/**
 * 内部需求详情
 * @param data
 */
export function requirementInfo(id) {
  return request({
    url: `/requirement/${id}`,
    method: 'GET'
  })
}
/**
 * 内部需求修改状态 回复
 * @param data
 */
export function requirementSave(data) {
  return request({
    url: '/requirement/save',
    method: 'POST',
    data
  })
}
