<template>
  <div class="app-container">
    <div class="filter-container">
<!--      <el-select v-model="listQuery.clientCode" placeholder="产品线" clearable class="filter-item" style="width: 140px;">-->
<!--        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
<!--      </el-select>-->
      <ProductLineSelect v-model="listQuery.clientCode"></ProductLineSelect>
<!--      <ClassTypeSelect v-model="listQuery.classTypeId" ></ClassTypeSelect>-->
      <ClassTypeByClientSelect v-model="listQuery.classTypeId" :client-code="listQuery.clientCode"></ClassTypeByClientSelect>
      <SubjectSelect v-model="listQuery.subjectId"></SubjectSelect>
      <StatusSelect v-model="listQuery.status" ></StatusSelect>
<!--      <el-date-picker-->
<!--        v-model="followDate"-->
<!--        type="daterange"-->
<!--        format="yyyy-MM-dd"-->
<!--        value-format="yyyy-MM-dd"-->
<!--        range-separator="至"-->
<!--        start-placeholder="更新开始时间"-->
<!--        end-placeholder="更新结束时间"-->
<!--      />-->
      <el-button v-waves class="filter-item" type="primary" plain size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" plain size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:knowledge:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="编号" show-overflow-tooltip prop="id" width="120px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
      <af-table-column label="标题" prop="title" />
      <af-table-column label="科目" prop="subjectName" show-overflow-tooltip width="120px" />
      <af-table-column label="班型" prop="classTypeName" />
      <af-table-column label="所属产品线" prop="clientName" />
      <af-table-column label="状态" prop="clientName" width="120" >
        <template slot-scope="scope">
          <EnableStatusTag :status="scope.row.status"></EnableStatusTag>
        </template>
      </af-table-column>
      <af-table-column label="排序" prop="sort" width="120"/>
      <el-table-column label="创建时间" prop="createTime" width="160px" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:knowledge:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['curriculum:knowledge:opera']" type="primary" size="mini" @click="enable(row)">{{ row.status===1?'禁用':'启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改教师弹框-->
    <AddKnowledgeModel ref="teacher" @addTeacherList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getSubjectCatalogPage, enableSubjectCatalog } from '@/api/classType'
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
import EnableStatusTag from '@/components/StatusTag/EnableStatusTag.vue'
import StyleTag from '@/components/StatusTag/StyleTag.vue'
import StatusSelect from '@/components/Select/StatusSelect.vue'
import SubjectSelect from '@/components/Select/SubjectSelect.vue'
import ClassTypeSelect from '@/components/Select/ClassTypeSelect.vue'
import AddStyleModel from '@/views/curriculum/components/addStyleModel.vue'
import ProductLineSelect from '@/components/Select/ProductLineSelect.vue'
import AddKnowledgeModel from '@/views/curriculum/components/addKnowledgeModel.vue'
import ClassTypeByClientSelect from '@/components/Select/ClassTypeByClientSelect.vue'
export default {
  name: 'KnowledgeModel',
  components: {
    ClassTypeByClientSelect,
    AddKnowledgeModel,
    ProductLineSelect,
    AddStyleModel,
    ClassTypeSelect,
    SubjectSelect,
    StatusSelect,
    StyleTag,
    EnableStatusTag,
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        status:null,
      },
      // subjectsAll: [],
      // clientCode: [],
      enableList: enableList,
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      // this.getAllSubjects()
      // this.getCode()
      this.getList()
    })
  },
  methods: {
    // getCode() {
    //   clientCode().then(res => {
    //     const clientCodes = res.data || []
    //     this.clientCode = clientCodes.filter(item => item.level === 1)
    //   })
    // },
    // getAllSubjects() {
    //   getAllSubjects().then(res => {
    //     if (res.code === '000000') {
    //       this.subjectsAll = res.data
    //     }
    //   })
    // },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)

      await getSubjectCatalogPage(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = '新增'
      this.$refs.teacher.isEdit = false
      this.$refs.teacher.flags = 1
      this.$refs.teacher.listQuery.status = 1
    },
    handleUpdate(row) {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = `修改`
      this.$refs.teacher.getTeacherDetail(row.id)
      this.$refs.teacher.isEdit = false
      this.$refs.teacher.flags = 0
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    getDetail(row) {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = `详情`
      this.$refs.teacher.getTeacherDetail(row.id)
      this.$refs.teacher.isEdit = true
      this.$refs.grade.flags = -1
    },
    enable(row) { // 启用/禁用
      const title = row.status === 1 ? '确认禁用吗?' : '确定要启用吗?'
      const tips = row.status === 1 ? '禁用成功' : '启用成功'
      this.$confirm(`${title}`, {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        enableSubjectCatalog(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${tips}`
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>
.codes{
  font-weight: bold;
  color: #0a76a4;
}
</style>
