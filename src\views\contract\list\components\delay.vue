<template>
  <div class="app-container bgGrey">
    <el-form ref="detailForm" :model="delayContract" label-width="200px" :rules="delayRules" :disabled="!isEdit">
      <!--    延期合同 -->
      <el-row :gutter="20">
        <el-col :lg="{span:22}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议编号：">
                    <span>{{ delayContract.oldContractId }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议名称：">
                    <span>{{ delayContract.oldContractName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议加盟项目：">
                    <span>{{ delayContract.oldContractProjectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议签订日期：">
                    <el-date-picker v-model="delayContract.oldContractSignDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" :disabled="rescissionType == 2" />
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="12">
                  <el-form-item label="原协议期限：">
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="原协议开始时间"
                      end-placeholder="原协议结束时间"
                      value-format="yyyy-MM-dd"
                      :disabled="rescissionType == 2"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="延期协议期限：" required>
                    <el-date-picker
                      v-model="delayTimeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="延期协议开始时间"
                      end-placeholder="延期协议结束时间"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议有效期/月份：" prop="oldContractEffective">
                    <el-input v-model="delayContract.oldContractEffective" placeholder="请输入原协议有效期" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="延期协议有效期/月份：" prop="delayContractEffective">
                    <el-input v-model="delayContract.delayContractEffective" placeholder="请输入延期协议有效期" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="有效期延长/月份：" prop="delayMonth">
                    <el-input v-model="delayContract.delayMonth" placeholder="请输入有效期延长月份" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="甲方：">
                    <el-input v-model="delayContract.firstParty" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="甲方负责人：">
                    <el-input v-model="delayContract.firstPartyDelegate" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="乙方：" prop="secondParty">
                    <el-input v-model="delayContract.secondParty" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="乙方负责人：" prop="secondPartyDelegate">
                    <el-input v-model="delayContract.secondPartyDelegate" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="乙方身份证/统一信用代码：" prop="secondPartyNo">
                    <el-input v-model="delayContract.secondPartyNo" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="本协议签署时间：">
                    <span> {{ delayContract.signingTime }} </span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit">保 存</el-button>
    </div>

  </div>
</template>

<script>
import { getContractDetail, delayContract } from '@/api/contract'
import { validCreditCode, oldContractEffective, oldContractDelay, delayContractEffective } from '@/utils/validate.js'
export default {
  name: 'DelayContract',
  props: {
  },
  data() {
    return {
      detail: {},
      delayContract: {},
      rescissionType: '',
      delayRules: {
        oldContractEffective: { required: true, trigger: 'blur', validator: oldContractEffective },
        delayMonth: { required: true, trigger: 'blur', validator: oldContractDelay },
        delayContractEffective: { required: true, trigger: 'blur', validator: delayContractEffective },
        secondParty: { required: true, message: ' ', trigger: 'blur' },
        secondPartyNo: { required: true, validator: validCreditCode, trigger: 'blur' },
        secondPartyDelegate: { required: true, message: ' ', trigger: 'blur' }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      timeArr: [],
      delayTimeArr: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.rescissionType = this.$route.query.type // 2：普通合同解约 3：校区项目解约
    this.getDetail()
    const tagsName = this.isEdit ? '延期-编辑合同' : '延期-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = res.data
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.endRevokeContract = res.data.endRevokeContract || {} // 校区解约合同详情
        const tmpObj = JSON.parse(JSON.stringify(that.detail.endRevokeContract))
        if (Number(that.rescissionType) === 3) {
          that.detail.revokeContract = tmpObj
        }
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        that.delayContract = res.data.delayContract || {} // 延期合同详情
        that.timeArr = that.delayContract.oldContractBeginTime !== null && that.delayContract.oldContractEndTime !== null ? [that.delayContract.oldContractBeginTime, that.delayContract.oldContractEndTime] : []
        that.delayTimeArr = that.delayContract.delayContractBeginTime !== null && that.delayContract.delayContractEndTime !== null ? [that.delayContract.delayContractBeginTime, that.delayContract.delayContractEndTime] : []

      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
     * 确认修改信息
     */
    confirmEdit() {
      if (!this.delayTimeArr || (this.delayTimeArr && this.delayTimeArr.length < 2)) {
        this.$message({
          message: '延期协议期限不能为空',
          type: 'warning'
        })
        return
      }
      this.$refs.detailForm.validate(valid => {
        if (valid) {
          const params = Object.assign({}, this.delayContract, { id: this.id, oldContractBeginTime: this.timeArr[0] || '', oldContractEndTime: this.timeArr[1] || '', delayContractBeginTime: this.delayTimeArr[0], delayContractEndTime: this.delayTimeArr[1] })
          delayContract(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    }

  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
</style>
