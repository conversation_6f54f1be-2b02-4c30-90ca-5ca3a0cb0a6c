import request from '@/utils/request'
/**
 * 获取商品列表
 * @param data
 */
export function getCharge(data) {
  return request({
    url: 'stip/order/chargeTotal',
    method: 'POST',
    data: data
  })
}
/**
 * 获取盒子总览列表
 * @param data
 */
export function getResourceList(data) {
  return request({
    url: 'stip/device/queryDevicePages',
    method: 'POST',
    data: data
  })
}
/**
 * 获取终端跨区设置
 * @param data
 */
export function getOpenSetting(data) {
  return request({
    url: `stip/device/getOpenSetting/${data}`,
    method: 'GET'
  })
}
/**
 * 保存终端跨区设置
 * @param data
 */
export function saveOpenSetting(data) {
  return request({
    url: 'stip/device/saveOpenSetting/',
    method: 'POST',
    data: data
  })
}
/**
 * 获取盒子总览启用禁用跨区
 * @param data
 */
export function interregionalOpen(id) {
  return request({
    url: 'stip/device/isOpen',
    method: 'GET',
    params: id
  })
}
/**
 * 获取盒子总览智能终端状态
 * @param data
 */
export function isStatus(id) {
  return request({
    url: 'stip/device/isStatus',
    method: 'GET',
    params: id
  })
}
/**
 * 获取资源中心-学生总览
 * @param data
 */
export function schoolLists(data) {
  return request({
    url: 'stip/userProjects/page',
    method: 'POST',
    data: data
  })
}
/**
 * 获取学生总览-开通班型
 * @param data
 */
export function classDetail(studentId) {
  return request({
    url: `stip/userProjects/getUserClassInfo/${studentId}`,
    method: 'GET'
  })
}
/**
 * 获取资源中心-盒子总览操作记录
 * @param data
 */
export function resourceDeviceLogs(id) {
  return request({
    url: `stip/device/listResourceDeviceLogs/${id}`,
    method: 'GET'
  })
}
/**
 * 盒子总览跨区校验的经纬度
 * @param data
 */
export function getDeviceCheckRegion(id) {
  return request({
    url: `stip/device/getDeviceCheckRegion/${id}`,
    method: 'GET'
  })
}
/**
 * 获取收益统计
 * @param data
 */
export function querySchoolIncomeStatisticsPage(data) {
  return request({
    url: 'market/schoolBalance/querySchoolIncomeStatistics/page',
    method: 'POST',
    data: data
  })
}

/**
 * 查询校区活动余额
 * @param data
 */
export function getSchoolBalance(id) {
  return request({
    url: `market/schoolBalance/getSchoolBalance`,
    method: 'GET',
    params: id
  })
}

/**
 * 分页查询校区提现审核列表
 * @param data
 */
export function getSchoolApplyVerifyPage(data) {
  return request({
    url: `market/schoolBalance/getSchoolApplyVerifyPage`,
    method: 'POST',
    data: data
  })
}
/**
 * 提现审核
 * @param data
 */
export function applyVerify(data) {
  return request({
    url: `market/schoolBalance/balance/applyVerify`,
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询校区活动余额提现明细
 * @param data
 */
export function getSchoolBalanceApplyDetails(data) {
  return request({
    url: `market/schoolBalance/getSchoolBalanceApplyDetails`,
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询校区活动余额收益明细
 * @param data
 */
export function getSchoolBalanceIncomeDetails(data) {
  return request({
    url: 'market/schoolBalance/getSchoolBalanceIncomeDetails',
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询校区活动余额明细
 * @param data
 */
export function getSchoolBalanceDetails(data) {
  return request({
    url: 'market/schoolBalance/getSchoolBalanceDetails',
    method: 'POST',
    data: data
  })
}
/**
 * 分页查询校平板管理
 * @param data
 */
export function querySchoolPadPages(data) {
  return request({
    url: 'stip/pad/querySchoolPadPages',
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询激活管理
 * @param data
 */
export function queryPadPages(data) {
  return request({
    url: 'stip/pad/queryPadPages',
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询激活管理
 * @param data
 */
export function uploadPadSerialNumber(data) {
  return request({
    url: 'stip/pad/uploadPadSerialNumber',
    method: 'POST',
    data: data
  })
}
