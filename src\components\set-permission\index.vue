<template>
  <el-dialog title="权限设置" :visible.sync="dialogRolePermission" :close-on-click-modal="!dialogRolePermission" width="80%">
    <el-row :gutter="10">
      <el-col :span="4" class="permission-item-body">
        <div class="permission-title">菜单/按钮授权：</div>
        <el-tree
          ref="proTree"
          :data="rolePermission.menuTree"
          show-checkbox
          node-key="id"
          :default-expanded-keys="rolePermission.menuIds"
          :default-checked-keys="rolePermission.menuIds"
          :props="defaultProps"
        />
      </el-col>
      <el-col :span="12" class="permission-item-body">
        <div class="permission-title">
          <span>授权方式：</span>
          <el-radio-group v-model="rolePermission.regionType" @change="getMethods">
            <el-radio :label="1">省份</el-radio>
            <el-radio :label="2">城市</el-radio>
            <el-radio :label="3">校区</el-radio>
          </el-radio-group>
        </div>
        <!-- 选择省份 -->
        <div v-if="rolePermission.regionType===1" class="choice-provinces">
          <el-select filterable v-model="rolePermission.provinceIds" multiple placeholder="请选择省份">
            <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </div>
        <!-- 选择城市 -->
        <div v-if="rolePermission.regionType===2" class="choice-provinces">
          <el-tree
            ref="proTreeCity"
            :data="treeCitys"
            show-checkbox
            node-key="id"
            :default-expanded-keys="rolePermission.cityIds"
            :default-checked-keys="rolePermission.cityIds"
            :props="props1"
          />
        </div>
        <!-- 选择校区 -->
        <div v-if="rolePermission.regionType===3" class="choice-provinces">
          <el-button size="mini" type="text" class="mb10" icon="el-icon-plus" @click="addSchool=true,addProjectShool()">添加校区</el-button>
          <el-table
            v-loading="listLoading"
            :data="listSchools"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="校区项目编号" prop="institutionCode" />
            <el-table-column label="关联机构ID" prop="agencyId" />
            <el-table-column label="合伙人名称" prop="clueName" />
            <el-table-column label="校区名称" prop="institutionName" />
            <el-table-column label="加盟项目" prop="projectName" />
            <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="100px" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="delSchoolData(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="4" class="permission-item-body">
        <div class="permission-title">项目权限：</div>
        <el-select filterable v-model="rolePermission.projectIds" multiple placeholder="请选择授权项目">
          <el-option
            v-for="item in rolePermission.projects"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-col>
      <el-col :span="4" class="permission-item-body">
        <div class="permission-title">意见反馈权限：</div>
        <el-select filterable v-model="rolePermission.categoryIds" multiple placeholder="请选择授权的意见反馈">
          <el-option
            v-for="item in rolePermission.categories"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogRolePermission = false">取 消</el-button>
      <el-button v-if="type==='role'" type="primary" @click="updateRolePermission">确 定</el-button>
      <el-button v-if="type==='employee'" type="primary" @click="updateRolePermission">确 定</el-button>
    </div>
    <!-- 添加校区的弹框 -->
    <el-dialog :visible.sync="addSchool" title="添加校区" :close-on-click-modal="!addSchool" width="60%" append-to-body>
      <div class="school-search">
        <div style="width:75%;">
          <area-picker :area-list="areaList" :level="'3'" area-style="'width:80%'" class="filter-item" @getAreaList="getAreaList" />
        </div>
        <el-select filterable v-model="projectId" placeholder="请选择加盟项目" style="width:200px">
          <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
        </el-select>
        <el-button v-waves class="filter-item search-school" type="primary" size="mini" style="flex-shrink:0" @click="handleFilter">
          查询
        </el-button>
        <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
          重置
        </el-button>
      </div>
      <el-table
        v-loading="addListLoading"
        :data="addListData"
        border
        fit
        stripe
        highlight-current-row
        style="width: 100%;"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <af-table-column label="校区项目编号" prop="institutionCode" />
        <af-table-column label="关联机构ID" prop="agencyId" />
        <af-table-column label="合伙人名称" prop="clueName" />
        <af-table-column label="校区名称" prop="institutionName" />
        <af-table-column label="加盟项目" prop="projectName" />
      </el-table>
      <pagination
        v-if="total>0"
        :total="total"
        :page.sync="listQuery.pageIndex"
        :limit.sync="listQuery.pageSize"
        @pagination="addProjectShools"
      />
      <div class="add-school-opera">
        <el-button type="primary" size="mini" @click="confirmSchool">确定</el-button>
        <el-button type="default" size="mini" @click="cancelSchool">取消</el-button>
      </div>
    </el-dialog>
    <!-- 添加校区的弹框 -->
  </el-dialog>
</template>

<script>
import { queryRolePermisson, setRolePermission, getEnabledRoleIns, getCityTrees } from '@/api/system-setting'
import AreaPicker from '@/components/area-picker'
import { getArea, getAllProject } from '@/api/common'
import Pagination from '@/components/Pagination'
export default {
  name: 'PermissionDialog', // type： employee 员工； role： 角色
  components: { AreaPicker, Pagination },
  directives: {},
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      roleId: '',
      defaultProps: { // 树形结构的权限
        children: 'childes',
        label: 'name'
      },
      dialogRolePermission: false, // 弹窗是否显示
      rolePermission: {
        menuTree: [], // 菜单权限树
        provinces: [], // 所有的省份
        projects: [], // 所有的项目
        menuIds: [], // 已选择的菜单
        projectIds: [], // 已选择的项目id
        provinceIds: [], // 已选择的省份
        categoryIds: [], // 已选择的意见反馈
        cityIds: []
      },
      listLoading: false,
      listSchools: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      addListLoading: false,
      addListData: [],
      total: 0,
      multipleSelection: [], // 选中的数据
      addSchool: false,
      sheng: [],
      props1: {
        children: 'childRegions',
        label: 'name',
        id: 'id'
      },
      cityLists: [],
      projectList: [],
      projectId: null,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      treeCitys: []
    }
  },
  created() {
    this.getsheng()
    this.getCityTrees()
    this.getProject()
  },
  methods: {
    /**
     * 获取该角色的权限树
     * id : 角色id
     */
    getPermissionList(id) {
      const that = this
      that.roleId = id
      queryRolePermisson(id).then(res => {

        that.rolePermission = res.data
        that.listSchools = res.data.roleInstitutions && res.data.roleInstitutions.length > 0 ? res.data.roleInstitutions : []
        that.dialogRolePermission = true
        // 解决父子关联问题
        this.$nextTick(() => {
          if (that.$refs.proTree && that.rolePermission.menuIds.length > 0) {
            that.$refs.proTree.setCheckedKeys([])
            that.rolePermission.menuIds.forEach((item) => {
              this.$refs.proTree.setChecked(item, true, false)
            })
          }

          if (that.$refs.proTreeCity && that.rolePermission.cityIds && that.rolePermission.cityIds.length) {
            that.$refs.proTreeCity.setCheckedKeys([])
            that.rolePermission.cityIds.forEach((item) => {
              that.$refs.proTreeCity.setChecked(item, true, false)
            })
          }
        })
      })
    },

    /**
     * 更改权限
     */
    updateRolePermission() {
      const that = this
      const nodeArr = that.$refs.proTree.getCheckedNodes(false, true) || []
      const ids = nodeArr.map(item => {
        return item.id
      })
      const cityArr = that.rolePermission.regionType === 2 && that.$refs.proTreeCity ? that.$refs.proTreeCity.getCheckedNodes(false, true) : []
      const idsCity = cityArr.length > 0 ? cityArr.map(item => {
        return item.id
      }) : []
      let regionIdsList = []
      if (that.rolePermission.regionType === 3) {
        regionIdsList = that.listSchools.length > 0 ? that.listSchools.map(item => item.id) : []
      } else if (that.rolePermission.regionType === 1) {
        regionIdsList = that.rolePermission.provinceIds
      } else {
        regionIdsList = idsCity
      }
      const params = {
        menuIds: ids,
        proIds: that.rolePermission.projectIds,
        categoryIds: that.rolePermission.categoryIds,
        roleId: that.roleId,
        regionType: that.rolePermission.regionType,
        regionIds: regionIdsList
      }
      that.$confirm('是否确认修改该角色权限, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setRolePermission(params).then(res => {
          that.$message({
            type: 'success',
            message: '修改成功!'
          })
          that.dialogRolePermission = false
          that.$emit('refresh')
        })
      }).catch(() => {

      })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    getsheng() {
      const _this = this
      getArea(0).then(res => {
        _this.sheng = res.data // 将获取的数据赋值
      }).catch(err => {

      })
    },
    getMethods(val) {

      if (val === 1) {
        this.getsheng()

      } else if (val === 2) {
        this.getCityTrees()
      } else {

      }
    },
    getCityTrees() {
      getCityTrees().then(res => {
        if (res.code === '000000') {
          this.treeCitys = res.data
        }
      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.addProjectShools()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.projectId = null
      this.areaList = {}
      this.addProjectShools()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    addProjectShools() { // 查询角色可选的校区项目权限
      const params = Object.assign({}, {
        projectId: this.projectId,
        roleId: this.roleId
      }, this.areaList, this.listQuery)
      this.addListLoading = true
      getEnabledRoleIns(params).then(res => {
        if (res.code === '000000') {
          this.addListData = res.data.records || []
          this.total = res.data.total
          this.addListLoading = false
        }
      })
    },
    addProjectShool() {
      this.multipleSelection = []
      this.addProjectShools()
    },
    confirmSchool() {
      const selectList = []
      this.multipleSelection && this.multipleSelection.length > 0 ? this.multipleSelection.forEach(item => {
        const selectObj = {}
        selectObj['id'] = item.id
        selectObj['agencyId'] = item.agencyId
        selectObj['clueName'] = item.clueName
        selectObj['institutionCode'] = item.institutionCode
        selectObj['projectName'] = item.projectName
        selectObj['institutionName'] = item.institutionName
        selectList.push(selectObj)
      }) : []
      this.listSchools = [...this.listSchools, ...selectList]
      this.addSchool = false
    },
    cancelSchool() {
      this.multipleSelection = []
      this.addSchool = false
    },
    delSchoolData(index) {

      this.$confirm('确定要删除词条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listSchools.splice(index, 1)
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    max-height: 500px;
    overflow-y: auto;
  }
  .permission-title{
    margin-bottom: 15px;
  }
  .mb10{
    margin-bottom: 10px;
  }
  .school-search{
    display: flex;
    width: 100%;
    margin-bottom: 15px;
  }
  .search-school{
    flex-shrink:0
  }
  .add-school-opera{
    margin: 15px 0;
    display: flex;
    justify-content: center;
    align-content: center;
  }
</style>
