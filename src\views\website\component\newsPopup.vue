<template>
  <div v-show="newsPop" class="edit-one-bg">
    <div class="edit-one-title">
      <div class="assing-info">
        <h3>
          <span>{{ newsTitle }}</span>
          <em class="el-icon-close" @click="dataInit()" />
        </h3>
        <el-form ref="oneTitleForms" :model="listQuery" :rules="rules" label-width="120px">
          <el-form-item label="标题" prop="title">
            <el-input v-model="listQuery.title" placeholder="请输入标题" maxlength="200" />
          </el-form-item>
          <el-form-item label="副标题" prop="subtitle">
            <el-input v-model="listQuery.subtitle" placeholder="请输入副标题" maxlength="200" />
          </el-form-item>
          <el-form-item label="标题图片链接	" prop="titleImage">
            <el-input v-model="listQuery.titleImage" placeholder="请输入标题图片链接" maxlength="250" />
          </el-form-item>
          <el-form-item label="内容链接" prop="contentUrl">
            <el-input v-model="listQuery.contentUrl" placeholder="请输入内容链接" maxlength="250" />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="listQuery.type" placeholder="类型" filterable clearable class="filter-item">
              <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="listQuery.sort" type="number" placeholder="请输入排序" maxlength="100" />
          </el-form-item>
          <el-form-item label="热门栏目显示" prop="hotShowFlag">
            <el-select v-model="listQuery.hotShowFlag" placeholder="热门栏目显示" filterable clearable class="filter-item">
              <el-option v-for="item in hotShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="最新栏目显示" prop="newShowFlag">
            <el-select v-model="listQuery.newShowFlag" placeholder="最新栏目显示" filterable clearable class="filter-item">
              <el-option v-for="item in newShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="本栏目显示" prop="selfShowFlag">
            <el-select v-model="listQuery.selfShowFlag" placeholder="本栏目显示" filterable clearable class="filter-item">
              <el-option v-for="item in selfShowFlagList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="dataInit()">取消</el-button>
          <el-button type="primary" size="mini" @click="confirmNews">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { editInformation, getInformation, addInformation } from '@/api/website'
export default {
  name: 'NewsPopup',
  data() {
    return {
      newsPop: false,
      newsTitle: '',
      newsFlag: '',
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入标题' },
        subtitle: { required: true, trigger: 'blur', message: '请输入副标题' },
        titleImage: { required: true, trigger: 'blur', message: '请输入标题图片链接' },
        contentUrl: { required: true, trigger: 'blur', message: '请输入内容链接' },
        type: { required: true, trigger: 'change', message: '请输入类型' }
      },
      id: '',
      listQuery: { hotShowFlag: 1, newShowFlag: 1, selfShowFlag: 1 },
      typeList: [{ title: '三陶资讯', id: 1 }, { title: '家长须知', id: 2 }, { title: '学员干货', id: 3 }],
      hotShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }],
      newShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }],
      selfShowFlagList: [{ title: '显示', id: 1 }, { title: '不显示', id: 0 }]
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    refresh() { this.$forceUpdate() },
    addNews() { this.id = '' },
    // 取消、关闭弹窗
    dataInit() {
      this.newsPop = false
      this.listQuery = {}
    },
    getNews(ids) {
      this.id = ids
      getInformation(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
        } else {
          this.$message({ type: 'error', message: res.msg })
        }
      }).catch((res) => {
        this.$message({ type: 'error', message: res.msg })
      })
    },
    confirmNews() {
      const that = this
      this.$refs.oneTitleForms.validate((valid) => {
        if (valid) {
          const data = { contentUrl: this.listQuery.contentUrl,
            hotShowFlag: this.listQuery.hotShowFlag,
            newShowFlag: this.listQuery.newShowFlag,
            selfShowFlag: this.listQuery.selfShowFlag,
            sort: this.listQuery.sort,
            subtitle: this.listQuery.subtitle,
            title: this.listQuery.title,
            titleImage: this.listQuery.titleImage,
            type: this.listQuery.type,
            id: this.id
          }
          if (that.newsFlag === 'create') {
            addInformation(data).then(res => {
              if (res.code === '000000') {
                that.$message({ type: 'success', message: '添加成功' })
                that.listQuery = {}; this.newsPop = false
                that.$emit('refreshList')
              }
            }).catch((res) => {
              that.$message({ type: 'error', message: res.msg })
            })
          } else {
            editInformation(data).then(res => {
              if (res.code === '000000') {
                that.$message({ type: 'success', message: '修改成功' })
                that.listQuery = {}; this.newsPop = false
                that.$emit('refreshList')
              }
            }).catch((res) => {
              that.$message({ type: 'error', message: res.msg })
            })
          }
        } else {
          that.$message({
            type: 'warning',
            message: '请输入必填项'
          })
        }
      })
    }

  }
}
</script>

<style scoped>

</style>
