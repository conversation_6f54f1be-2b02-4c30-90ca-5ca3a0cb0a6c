<template>
  <el-dialog :visible.sync="gradePop" :title="gradeTitle" :close-on-click-modal="!gradePop" width="60%" @close="changeInit">
    <el-form ref="gradeForm" :model="listQuery" :rules="rules" label-width="100px">
      <el-form-item label="年级" prop="name">
        <el-input v-model="listQuery.name" placeholder="请输入年级" maxlength="20" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="年级状态">
        <el-select v-model="listQuery.status" placeholder="请选择年级状态" clearable class="filter-item" :disabled="isEdit" filterable>
          <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="年级排序">
        <el-input v-model.number="listQuery.sort" placeholder="年级排序" :disabled="isEdit" maxlength="7" />
      </el-form-item>
      <div v-if="!isEdit" class="assign-operas">
        <el-button type="infor" size="mini" @click="gradePop=false,cancelSubjects()">取消</el-button>
        <!--新增年级-->
        <el-button v-if="flags===1" type="primary" size="mini" @click="custormSubjects">确定</el-button>
        <!--修改年级-->
        <el-button v-if="flags===0" type="primary" size="mini" @click="editSubjects">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import {
  enableList
} from '@/utils/field-conver'
import { addGrade, gradeDetail, editGrades } from '@/api/classType'
export default {
  name: 'AddSubject',
  data() {
    return {
      gradeTitle: '',
      gradePop: false,
      enableList: enableList,
      listQuery: {
        status: 1
      },
      rules: {
        name: { required: true, trigger: 'blur', message: '请输入年级' }
      },
      isEdit: false,
      flags: -1
    }
  },
  mounted() {

  },
  methods: {
    cancelSubjects() {
      if (this.$refs.gradeForm) {
        this.$refs.gradeForm.clearValidate()
      }
      this.listQuery = {}
    },
    custormSubjects() {
      this.$refs.gradeForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery)
          addGrade(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.$emit('addGradeList')
              this.gradePop = false
              this.listQuery.name = ''
              this.listQuery.sort = ''
              this.listQuery.status = 1
              this.$refs.gradeForm.clearValidate()
            }
          }).catch(() => {

          })
        } else {
          return false
        }
      })
    },
    changeInit() {
      this.listQuery = {}
      if (this.$refs.gradeForm) {
        this.$refs.gradeForm.clearValidate()
      }
    },
    editSubjects() {
      this.$refs.gradeForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery)
          editGrades(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.$emit('addGradeList')
              this.gradePop = false
              this.listQuery.name = ''
              this.listQuery.sort = ''
              this.$refs.gradeForm.clearValidate()
            }
          }).catch(() => {

          })
        } else {
          return false
        }
      })
    },
    gradeDetail(ids) {
      gradeDetail(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
