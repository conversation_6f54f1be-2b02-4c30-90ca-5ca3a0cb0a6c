<template>
  <el-dialog :visible.sync="paperListPop" :title="paperListTitle" :close-on-click-modal="!paperListPop" width="60%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="paperForms" :model="paperForm" :rules="rules" label-width="100px">
        <el-form-item label="试卷名称" prop="name">
          <el-input v-model="paperForm.name" placeholder="请输入试卷名称" maxlength="50" />
        </el-form-item>
        <el-form-item label="产品线" prop="clientCode">
          <el-select v-model="paperForm.clientCode" placeholder="请选择产品线" clearable class="filter-item" filterable>
            <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="科目" prop="subjectId">
          <el-select v-model="paperForm.subjectId" placeholder="请选择科目" clearable class="filter-item" @change="getClassList" filterable>
            <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="班型" prop="classTypeId">
          <el-select v-model="paperForm.classTypeId" placeholder="请选择班型" clearable class="filter-item" @change="changeClass" filterable>
            <el-option v-for="item in classList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联课程" prop="courseId">
          <el-select v-model="paperForm.courseId" placeholder="请选择关联课程" clearable class="filter-item" @change="getCourseForce" filterable>
            <el-option v-for="item in courseList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="assign-operas">
      <el-button type="infor" size="mini" @click="paperListPop=false,changeInit()">取消</el-button>
      <el-button type="primary" size="mini" @click="custormEditClass">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCourse, addPaper, getDetailPaper, editPaper, getClassType } from '@/api/admissions'
export default {
  name: 'PaperPop',
  props: {
    subjectsList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      paperListPop: false,
      paperListTitle: '',
      enableFlagsUpdate: false,
      paperForm: {},
      rules: {
        name: { required: true, trigger: 'blur', message: '请输入试卷名称' },
        subjectId: { required: true, trigger: 'blur', message: '请选择科目' },
        classTypeId: { required: true, trigger: 'blur', message: '请选择班型' },
        courseId: { required: true, trigger: 'blur', message: '请选择关联课程' },
        clientCode: { required: true, trigger: 'blur', message: '请选择关联产品线' }
      },
      courseList: [],
      isEdit: false,
      classList: [],
      productCodeList: []
    }
  },
  watch: {
    courseList: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.paperForm.courseId = ''
        }
      }
    },
    classList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.paperForm.classTypeId = ''
          this.courseList = []
        }
      },
      deep: true
    },
    'paperForm.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.paperForm.classTypeId = ''
          this.courseList = []
        }
      }
    },
    'paperForm.classTypeId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.paperForm.courseId = ''
          this.courseList = []
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    getCourseList(subjectId) { //
      getCourse(subjectId).then(res => {
        if (res.code === '000000') {
          this.courseList = res.data
        }
      }).catch(() => {

      })
    },
    getClass(subjectId) {
      if (subjectId) {
        getClassType(subjectId).then(res => {
          if (res.code === '000000') {
            this.classList = res.data
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    getClassList() { // 查询班型
      this.getClass(this.paperForm.clientCode, this.paperForm.subjectId)
    },
    changeClass(val) {
      if (val && this.paperForm.subjectId) {
        this.getCourseList(val, this.paperForm.subjectId)
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择关联的班型或者科目'
        })
      }
    },
    custormEditClass() { // 新增试卷
      this.$refs.paperForms.validate((valid) => {
        const params = Object.assign({}, this.paperForm, { id: this.paperForm.id ? this.paperForm.id : '' })
        if (this.isEdit) { // 修改
          editPaper(params).then(res => { // 新增
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.paperForm = {}
              this.paperListPop = false
              this.$emit('addPaper')
            }
          })
        } else {
          addPaper(params).then(res => { // 新增
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.paperForm = {}
              this.paperListPop = false
              this.$emit('addPaper')
            }
          })
        }
      })
    },
    getDetailPaper(ids) { // 试卷详情
      getDetailPaper(ids).then(res => {
        if (res.code === '000000') {

          this.paperForm = res.data
          this.getCourseList(res.data.subjectId)
          this.getClass(res.data.subjectId)
        }
      }).catch(() => {

      })
    },
    changeInit() {
      this.paperForm = {}
      this.classList = []
      this.courseList = []
      if (this.$refs.paperForms) {
        this.$refs.paperForms.clearValidate()
      }
    },
    getCourseForce() {
      this.$forceUpdate()
    }

  }
}
</script>

<style scoped>

</style>
