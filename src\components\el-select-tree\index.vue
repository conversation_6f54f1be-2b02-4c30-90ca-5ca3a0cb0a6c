<template>
  <div class="el-select-tree">
    <el-popover
      ref="elPopover"
      v-model="visible"
      transition="el-zoom-in-top"
      popper-class="el-select-tree__popover"
      trigger="click"
      :disabled="disabled"
      :placement="placement"
      @after-enter="handleScroll()"
    >
      <el-scrollbar
        v-if="dataLength"
        ref="scrollbar"
        wrap-class="el-select-dropdown__wrap"
        view-class="el-select-dropdown__list"
        :class="{ 'is-empty': dataLength === 0 }"
      >
        <el-tree
          ref="elTree"
          class="el-select-tree__list"
          :props="props"
          :node-key="propsValue"
          :show-checkbox="multiple"
          :expand-on-click-node="multiple"
          :data="data"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="nodeClick"
          @check-change="checkChange"
        >
          <div
            slot-scope="{ data }"
            class="el-select-tree__item"
            :class="treeItemClass(data)"
          >
            {{ data[propsLabel] }}
          </div>
        </el-tree>
      </el-scrollbar>
      <p v-else class="el-select-tree__empty">
        无数据
      </p>

      <!-- trigger input -->
      <el-input
        ref="reference"
        slot="reference"
        v-model="obj.selectedLabel"
        readonly
        suffix-icon="el-icon-arrow-down"
        :size="size"
        :disabled="disabled"
        :placeholder="placeholder"
      />
    </el-popover>
  </div>
</template>

<script>
import { treeFind, treeEach } from '@/utils/select-tree'
export default {
  name: 'ElSelectTree',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    checkStrictly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    props: {
      type: Object,
      default() {
        return {
          value: 'value',
          label: 'label',
          children: 'children'
        }
      }
    },
    placement: {
      type: String,
      default: 'bottom-start'
    },
    size: {
      type: String,
      default: 'small'
    },
    popoverMinWidth: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Number, String, Array],
      default: ''
    },
    disabledValues: {
      type: Array,
      default() {
        return []
      }
    },
    data: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      obj: {
        selectedLabel: ''
      }
    }
  },
  computed: {
    dataLength() {
      return this.data.length
    },
    propsValue() {
      return this.props.value
    },
    propsLabel() {
      return this.props.label
    },
    defaultExpandedKeys() {
      return Array.isArray(this.value) ? this.value : [this.value]
    }
  },
  watch: {
    value(newVal,oldVal) {
      if(newVal === null || newVal === ''){
        this.obj.selectedLabel = ''
      }
      this.setSelected()
    },
    data(newVal,oldVal) {
      this.setTreeDataState()
    },
    disabledValues() {
      this.setTreeDataState()
    }
  },
  created() {
    if (this.multiple && !Array.isArray(this.value)) {
      console.error(
        '[el-select-tree]:',
        'props `value` must be Array if use multiple!'
      )
    }
    this.setTreeDataState()
  },
  mounted() {
    this.setSelected()
    // set the `popper` default `min-width`
    this.$nextTick(() => {
      const popper = this.$refs.elPopover.$refs.popper
      let width
      if (!this.popoverMinWidth) {
        const clientWidth = this.$el.clientWidth
        if (!clientWidth) {
        }
        width = clientWidth
      } else {
        width = this.popoverMinWidth
      }
      width && (popper.style.minWidth = width + 'px')
    })
  },
  methods: {
    // 触发滚动条的重置
    handleScroll() {
      this.$refs.scrollbar && this.$refs.scrollbar.handleScroll()
    },
    nodeClick(data, node, component) {
      const children = data.children
      const value = data[this.propsValue]
      if (children && children.length && !this.checkStrictly) {
        component.handleExpandIconClick()
      } else if (!this.disabledValues.includes(value) && !this.multiple) {
        if (value !== this.value) {
          // this.$set(this.selectedLabel, data[this.propsLabel])
          this.$emit('change', value)
          // this.obj.selectedLabel = data[this.propsLabel]
          // Vue.set(vm.someObject, 'b', 2)
          this.$set(this.obj, 'selectedLabel', data[this.propsLabel])
        }
        this.visible = false
      }
    },
    checkChange() {
      // debugger
      this.$emit('change', this.$refs.elTree.getCheckedKeys(true))
      this.setSelectedLabel()
    },
    checkSelected(value) {
      // //debugger
      if (this.multiple) {
        return this.value.includes(value)
      } else {
        return this.value === value
      }
    },
    setSelected() {
      // this.obj.selectedLabel = ''
      const propsValue = this.propsValue
      const value = this.value

      if (String(value).length) {
        if (this.multiple) {
          this.$nextTick(() => {
            this.$refs.elTree.setCheckedKeys(this.value)
            this.setSelectedLabel()
          })
        } else {
          const selectedNode = treeFind(this.data, (node) =>{return this.checkSelected(node[propsValue])},{
            children: this.$props.props.children
          })
          if (selectedNode) {
            this.obj.selectedLabel = selectedNode[this.propsLabel]
          }
        }
      }
    },
    setTreeDataState() {
      // debugger
      const disabledValues = this.disabledValues
      const propsValue = this.props.value
      treeEach(this.data, (node) => {
        node.disabled = disabledValues.includes(node[propsValue])
      })
    },
    setSelectedLabel() {
      // debugger
      const elTree = this.$refs.elTree
      const selectedNodes = elTree.getCheckedNodes(true)
      this.obj.selectedLabel = selectedNodes.map((item) => item.label).join(',')
    },
    treeItemClass(data) {
      // debugger
      const value = data[this.propsValue]
      return {
        selected: this.multiple ? false : this.checkSelected(value),
        'is-disabled': this.disabledValues.includes(value)
      }
    }
  }
}
</script>

<style lang="scss">
</style>
