<template>
  <el-dialog :visible.sync="resetPop" :title="resetTitle" :close-on-click-modal="!resetPop" width="30%" @close="cancelPwd()">
    <el-form label-width="80px">
      <el-form-item label="新密码" prop="pwd">
        <el-input v-model="pwd" oninput="value=value.replace(/\s/g,'')" type="password" placeholder="6-16位，必须包含数字、符号、字母" maxlength="16" minlength="6" />
      </el-form-item>
      <div class="assign-operas">
        <el-button type="info" size="mini" @click="cancelPwd()">取消</el-button>
        <el-button type="primary" size="mini" @click="confirmPwd">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import { resetPwd } from '@/api/schoolCampus'
export default {
  name: 'ResetPop',
  data() {
    return {
      pwd: '',
      resetPop: false,
      resetTitle: '重置密码',
      schoolId: '',
      rulesPassword: {
        pwd: { required: true, trigger: 'blur', message: '6-16位，包含数字，符号，字母(区分大小写)' }
      }
    }
  },
  methods: {
    cancelPwd() {
      this.resetPop=false
      this.pwd = ''
    },
    confirmPwd() {
      const reg = /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{6,16}$))/
      if (this.pwd && this.schoolId) {
        if (!reg.test(this.pwd)) {
          this.$message({
            type: 'warning',
            message: '请输入6~16位新密码包含数字，符号，字母且区分大小写'
          })
        } else {
          resetPwd(this.pwd, this.schoolId).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '密码重置成功!'
              })
              this.resetPop = false
              this.pwd = ''
            }
          }).catch(() => {

          })
        }
      } else {
        this.$message({
          type: 'warning',
          message: '请输入新密码'
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
