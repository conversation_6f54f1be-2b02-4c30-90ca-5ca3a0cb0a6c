<template>
  <div>
    <div class="area-picker" :style="areaStyle">
      <el-row>
        <el-col class="area-item" :xs="{span:24}" :sm="{span:level==4 ? 6 : 8}">
          <el-select v-model="areaList.provinceId" filterable placeholder="省" clearable style="width: 96%" :disabled="isEditSchool" @change="getcity(areaList.provinceId,true)">
            <el-option value="">全部</el-option>
            <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col class="area-item" :xs="{span:24}" :sm="{span:level==4 ? 6 : 8}">
          <el-select v-model="areaList.cityId" filterable placeholder="市" clearable style="width: 96%" :disabled="isEditSchool" @change="getarea(areaList.cityId,true)">
            <el-option value="">全部</el-option>
            <el-option v-for="item in shi" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col class="area-item" :xs="{span:24}" :sm="{span:level==4 ? 6 : 8}">
          <el-select v-model="areaList.areaId" filterable placeholder="区/县" clearable style="width: 96%" :disabled="isEditSchool" @change="getcounty(areaList.areaId,true)">
            <el-option value="">全部</el-option>
            <el-option v-for="item in qu" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col v-if="level==4" class="area-item" :xs="{span:24}" :sm="{span:6}">
          <el-select v-model="areaList.countyId" filterable placeholder="乡/镇" clearable style="width: 96%" :disabled="isEditSchool">
            <el-option value="">全部</el-option>
            <el-option v-for="item in zhen" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import {
  getArea
} from '@/api/common'
export default {
  name: 'AreaPicker',
  props: {
    isEditSchool: {
      type: Boolean,
      default: false
    },
    areaList: {
      type: Object,
      default: function() {
        return {}
      }
    },
    level: {
      type: String,
      default: '1'
    },
    areaStyle: {
      type: String,
      default: ''
    },
    getAreaName: {
      type: Boolean,
      default: function() {
        return false
      },
      required: false
    }
  },
  data() {
    return {
      sheng: [],
      shi: [],
      qu: [],
      zhen: []
    }
  },
  watch: {
    // 监听父组件传值的变化
    areaList: {
      handler: function(val, oldval) {
        this.getcity(val.provinceId, false)
        this.getarea(val.cityId, false)
        this.level === '4' && this.getcounty(val.areaId, false)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getsheng()
  },
  methods: {
    // 获取省
    getsheng() {
      const _this = this
      // const params = {
      //   id: 0,
      //   type: 1
      // }
      getArea(0).then(res => {
        _this.sheng = res.data // 将获取的数据赋值
      }).catch(err => {

      })
    },
    // 点击地区获取市
    getcity(id, is) {
      if (is) {
        // 判断是否点击上一级
        this.areaList.areaId = ''
        this.areaList.cityId = ''
        this.areaList.countyId = ''
        this.$emit('initCreateSchool')
      }
      if (!id) {
        // this.$set('areaList', 'provinceId', '')
        this.postData()
        return
      }
      const _this = this
      getArea(id).then(res => {
        _this.shi = res.data // 将获取的数据赋值
      }).catch(err => {

      })
      _this.postData()
    },
    // 点击城市获取区
    getarea(id, is) {
      const _this = this
      if (is) {
        // 判断是否点击上一级
        this.areaList.areaId = ''
        this.areaList.countyId = ''
      }
      if (!id) {
        _this.postData()
        return
      }
      getArea(id).then(res => {
        _this.qu = res.data
        if(this.getAreaName){
          this.$emit('getAreaNames', res.data)
        }
        _this.postData()
      }).catch(err => {

      })
    },
    // 点击区获取城镇
    getcounty(id, is) {
      const _this = this
      if (is) {
        //  判断是否点击上一级
        _this.areaList.countyId = ''
      }
      if (!id) {
        return
      }
      getArea(id).then(res => {
        _this.zhen = res.data // 将获取的数据赋值
        _this.postData()
      }).catch(err => {

      })
    },
    changeLocationValue(list, val) {
      if (list && list.length > 0 && val) {
        let obj = {}
        obj = list.find((item) => {
          return item.id === val
        })
        return obj.name || ''
      }
    },
    // 向父组件传值
    postData() {
      const _this = this
      this.$forceUpdate()
      setTimeout(function() {
        _this.$emit('getAreaList', _this.areaList)
        const names = {
          provinceName: _this.changeLocationValue(_this.sheng, _this.areaList.provinceId),
          cityName: _this.changeLocationValue(_this.shi, _this.areaList.cityId),
          areaName: _this.changeLocationValue(_this.qu, _this.areaList.areaId),
          countyName: _this.changeLocationValue(_this.zhen, _this.areaList.countyId)
        }
        _this.$emit('getAreaName', names)
      }, 10)
    }
  }
}
</script>
<style scoped lang="scss">
  /*.area-picker {*/
  /*  display: flex;*/
  /*  align-items: center;*/
  /*  justify-content: center;*/
  /*  flex-direction: row;*/
  /*}*/
  /*.area-item {*/
  /*  flex: 1;*/
  /*}*/
  /*@media only screen and (max-width: 470px) {*/
  /*  .area-picker {*/
  /*    display: block;*/
  /*  }*/
  /*}*/
</style>
