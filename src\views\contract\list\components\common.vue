<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" :rules="comRules" label-width="130px" :disabled="!isEdit">
      <!--    01普高，02艺考，03烨晨 -->
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" />
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">交接单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约期限：" label-width="110px">
                    <span v-if="detail.contractOrder.signStartTime">{{ detail.contractOrder.signStartTime }}</span>
                    <span v-if="detail.contractOrder.signEndTime">-{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">签约人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：" >
                    <span>{{ detail.contractSignatory ? detail.contractSignatory.userName : '' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory ? detail.contractSignatory.idCard : '' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory ? detail.contractSignatory.phone : '' }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">企业资质信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise ? detail.contractEnterprise.enterpriseName : '' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise ? detail.contractEnterprise.creditCode : '' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise ?  detail.contractEnterprise.enterpriseLegal : '' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise ? detail.contractEnterprise.enterpriseAddress : '' }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!--1:加盟,2:续费, 3:加盟升级-->
      <el-row :gutter="22">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span class="bolder">合同签约信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同编号：" label-width="130px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同名称：" label-width="130px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：" label-width="130px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="区域类型：" label-width="130px">
                    <el-radio-group v-model="detail.cooperationType" disabled>
                      <el-radio :label="1">区县独家</el-radio>
                      <el-radio :label="0">区县单点</el-radio>
                      <el-radio :label="2">乡镇独家</el-radio>
                      <el-radio :label="3">乡镇单点</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="130px">
                    <!-- 状态 1 待专员打款 2 财务审核 3 财务审核不通过 4 市场审核 5 市场审核不通过 6 合伙人提交资质  7 运营审批资质 8 运营编辑合同 9 资质不通过 10 待签署 11 已签署 12 已作废 13 已生效 14 解约中  15 已解约 16 解约中 17 已解约 18 已退款 19 待运营专员审批 20 异常 21 创建合同模版，确认合同类型  22 撤消 23 已创建电子合同 -->
                    <el-radio-group v-model="detail.contractType" class="radios" :disabled="![21, 6,7,8,9].includes(detail.orderStatus)">
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本：">
                    <div>{{ detail.versionType === 2 ? '预签' : '正式' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" :sm-offset="12">
                  <el-form-item label="我方签约主体：">
                    <div>{{ detail.contractOrder.signPartyName || '暂无'}}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" v-if="isShowContract">
                  <el-form-item label="初高中合同：" >
                      <el-checkbox  disabled v-model="detail.businessVersion"></el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col v-if="detail.projectName!=='芝麻艺考'" :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="AI系统:" prop="normalContract.hasAi">
                    <el-radio-group v-model="detail.normalContract.hasAi" class="radios">
                      <el-radio :label="1">包含</el-radio>
                      <el-radio :label="0">不包含</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同期限：" required>
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                      :disabled="detail.versionType != 1"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="履约保证金（门头保证金）/元:" prop="normalContract.openingDeposit" class="tips-top">
                    <el-input v-model="detail.normalContract.openingDeposit" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="年度合作费/元:" label-width="130px" prop="normalContract.cooperationFee">
                    <el-input v-model="detail.normalContract.cooperationFee" type="number" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.contractOrder.businessType===2" :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="续约套餐内容:" label-width="130px" prop="normalContract.packageContent">
                    <el-input v-model="detail.normalContract.packageContent" placeholder="请输入续约套餐内容" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.contractOrder.businessType!==2" :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="季度最低课耗/时:" label-width="130px" prop="normalContract.courseConsumption">
                    <el-input v-model="detail.normalContract.courseConsumption" placeholder="请输入季度最低课耗/时" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="年度最低课耗/时:" label-width="130px" prop="normalContract.lowestCourseConsumption">
                    <el-input v-model="detail.normalContract.lowestCourseConsumption" placeholder="请输入年度最低课耗/时" type="number" />
                  </el-form-item>
                </el-col>
                <el-col v-if="detail.cooperationType===1||detail.cooperationType===2" :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合作机构家数:" label-width="130px" required>
                    <el-input v-model="detail.normalContract.partnerNums" placeholder="请输入合作机构的最低家数" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="公办校：" label-width="130px">
                    <el-input v-model="publicSchool" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区地址：" label-width="130px">
                    <el-input v-model="schoolAddress" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="补充内容:" label-width="130px" required>
                    <el-input v-model="remark" type="textarea" :row="2" show-word-limit maxlength="300" placeholder="请输入补充内容，若无补充内容，请输入无" @input="refresh" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <!-- <el-button type="primary" @click="confirmEdit(1)">保存草稿</el-button> -->
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>
  </div>
</template>

<script>
import {
  getContractDetail,
  modifyContractDetail,
  getCreatContract
} from '@/api/contract'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  orderStatusList,
  contractClass,
  getContractStatus
} from '@/utils/field-conver'

export default {
  name: 'CommonContract',
  // components: { AreaPicker },
  props: {},
  data() {
    return {
      businessType: '',
      channel: '',
      isShowContract: false, // 是否显示初高中合同
      detail: {},
      comRules: {
        contractType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        cooperationType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        schoolAddress: {
          required: true,
          message: '请输入校区地址',
          trigger: 'blur'
        },
        normalContract: {
          openingDeposit: {
            required: true,
            message: '请输入履约保证金（门头保证金）',
            trigger: 'blur'
          },
          cooperationFee: {
            required: true,
            message: '请输入年度合作费',
            trigger: 'blur'
          },
          packageContent: {
            required: true,
            message: '请输入续约套餐内容 ',
            trigger: 'blur'
          },
          courseConsumption: {
            required: true,
            message: '请输入三个月内最低课耗',
            trigger: 'blur'
          },
          lowestCourseConsumption: {
            required: true,
            message: '请输入年度最低课耗小时数 ',
            trigger: 'blur'
          },
          hasAi: {
            required: true,
            message: '请选择AI系统',
            trigger: 'blur'
          }
        }
      },
      remark: '',
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      timeArr: [],
      publicSchool: '',
      schoolAddress: ''
    }
  },
  watch: {
    $route(to, from) {
      this.$router.go(0)
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    const title = this.$route.query.title
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? '编辑合同' : '合同详情'
    this.setTagsViewTitle(title + '-' + tagsName)
  },
  mounted() {

  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() { // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        that.publicSchool = that.detail.publicSchool || ''
        that.schoolAddress = that.detail.schoolAddress
        that.remark = that.detail.remark
        this.detail.businessVersion = this.detail.businessVersion === 1
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.normalContract !== null) {
          this.detail.normalContract = res.data.normalContract
        } else {
          this.detail.normalContract = {
            openingDeposit: 0,
            cooperationFee: 0,
            packageContent: '',
            courseConsumption: '',
            lowestCourseConsumption: '',
            partnerNums: ''
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = that.id
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
        // 是否显示初高中合同
        this.isShowContractBtn();
      })
    },
    isShowContractBtn() {
      // 三陶教育 且正式
      if (this.detail.projectId === 1 && this.detail.versionType === 1) {
        this.isShowContract = true;
      } else {
        this.isShowContract = false;
        this.detail.businessVersion = ''
      }
    },
    getCreatContract() { // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.publicSchool = that.detail.publicSchool || ''
        that.schoolAddress = that.detail.schoolAddress
        that.remark = that.detail.remark
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.normalContract !== null) {
          this.detail.normalContract = res.data.normalContract
        } else {
          this.detail.normalContract = {
            openingDeposit: 0,
            cooperationFee: 0,
            packageContent: '',
            courseConsumption: '',
            lowestCourseConsumption: '',
            partnerNums: ''
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = res.data.contractId
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
       * 确认修改信息
       */
    confirmEdit(num) {

      const that = this

      // const publicSchool = that.detail.cooperationType === 1 ? '' : that.detail.publicSchool
      if (!that.detail.contractType) {
        that.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!that.remark) {
        that.$message({
          type: 'warning',
          message: '补充内容必填!'
        })
        return
      }
      if (!that.timeArr || (that.timeArr && that.timeArr.length < 2)) {
        that.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }
      if ((that.detail.cooperationType === 1 || that.detail.cooperationType === 2) && (that.detail.normalContract.partnerNums == null || that.detail.normalContract.partnerNums === '')) {
        that.$message({
          message: '合作机构家数不能为空',
          type: 'warning'
        })
        return
      }
      //是否确认提交
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          const data = Object.assign(that.detail, {
            operateType: num,
            startTime: that.timeArr[0],
            endTime: that.timeArr[1],
            normalContractDTO: that.detail.normalContract,
            remark: that.remark,
            publicSchool: that.publicSchool ? that.publicSchool : '',
            schoolAddress: that.schoolAddress ? that.schoolAddress : ''
          })
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '编辑成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          })
        }
      })
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.bolder{
    font-weight: bolder;
    font-size: 16px;
}
  .el-row {
    margin-bottom: 4px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }

  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  /deep/  .tips-top .el-form-item__error {
    margin-top: -20px;
  }
  /deep/  .tips-top .el-input{
    margin-top: 10px;
  }
</style>
