<template>
  <div>
    <div class="exercises" v-for="testType in [3,1,2]" :key="testType">
      <!-- 1=课后作业  2=讲义例题 -->
      <el-card class="box-card" shadow="always">
        <div slot="header" class="clearfix card-title">
          <h2>{{ testType === 1 ? '课后作业' :  testType===3?  '课后作业-新题库' : '讲义例题' }}</h2>
        </div>
        <div class="card-content">
          <CourseInfo v-if="testType===1&&loading===false" :course-id="courseId" :course-info="typeOneInfo" :test-type="testType"
                      @call="initData"></CourseInfo>
          <CourseInfo v-if="testType===2&&loading===false" :course-id="courseId" :course-info="typeTwoInfo" :test-type="testType"
                      @call="initData"></CourseInfo>
          <CourseInfo v-if="testType===3&&loading===false" :course-id="courseId" :course-info="typeThirdInfo" :test-type="testType"
                      @call="initData"></CourseInfo>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import { getHomeworkDetailByCourseId, addHomework, updateHomework } from '@/api/exercises'
import exercisesPdf from './components/exercisesPdf.vue'
import exercisesTopic from './components/exercisesTopic.vue'
import CourseInfo from '@/views/curriculum/components/courseInfo.vue'

export default {
  name: 'Exercises',
  components: {
    CourseInfo,
    exercisesPdf,
    exercisesTopic
  },
  data() {
    return {
      activeName: 'exercisesPdf',
      courseInfo: null,
      typeOneInfo: null,
      typeTwoInfo: null,
      typeThirdInfo:null, //第三方题库
      loading: true,
      loadmsg: '数据请求中'
    }
  },
  computed: {
    courseId() {
      return this.$route.params && this.$route.params.id
    }
  },
  mounted() {
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      getHomeworkDetailByCourseId(this.courseId).then(res => {
        this.loading = false
        if (res.code === '000000') {
          // 获取到courseInfoList
          this.courseInfoList = res.data
          this.courseInfoList.forEach(item => {
            if (item.testType === 1) {
              this.typeOneInfo = item
            }
            else if (item.testType === 2) {
              this.typeTwoInfo = item
            }   else if (item.testType === 3) {
              this.typeThirdInfo = item
            }
          })

          if (!this.courseInfoList) {
            this.$message({
              type: 'warning',
              message: '暂无试题信息！'
            })
          }
        }
      })
    },
  }
}
</script>
<style scoped>
.exercises {
    position: relative;
    //background: #fff;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
}

.card-title {
    border-left: 8px solid #3B91FF;
    padding-left: 20px;
    box-sizing: border-box;
}

.card-content {
    padding-top: 10px;
}
</style>
