<template>
  <div>
    <el-select
            v-model="tmpId"
            placeholder="选择知识点"
            @focus="showTree = true"
            readonly
            multiple
            clearable
            collapse-tags
    >
      <el-option v-if="!subjectId" label="请先选择科目" value=""></el-option>
      <el-option
              :hidden="true"
              v-for="item in selectedNodes"
              :key="item.id"
              :label="item.name"
              :value="Number(item.id)"
      ></el-option>
    </el-select>
    <el-dialog
            top="0"
            title="选择知识点"
            :visible.sync="showTree"
            @close="handleClose"
            width="30%">
      <el-input
              placeholder="输入关键字进行过滤"
              v-model="filterText">
      </el-input>
      <el-tree
              ref="tree"
              :data="treeData"
              show-checkbox
              node-key="id"
              empty-text="请先选择科目"
              :filter-node-method="filterNode"
              :props="defaultProps"
              @check-change="handleCheckChange"></el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getPointFromXKBySubjectId } from '@/api/exercises'

export default {
  name: 'KPointFromXKSelect',
  data() {
    return {
      showTree: false,
      filterText: '',
      selectedNodes: [],
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  model: {
    prop: 'targetIds',
    event: 'change',
  },
  props: {
    subjectId: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    targetIds: {
      type: [String, Number, Array],
      required: false,
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetIds ? this.targetIds : []
      },
      set(val) {
        this.handleChange(val)
      }
    }
  },
  watch: {
    subjectId: 'loadTree', // 监听 gradeId 的变化，年级变化后，对应的班级需要重新加载
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    loadTree() {
      this.treeData = []
      this.selectedNodes = []
      this.tmpId = []
      if (this.subjectId)
        getPointFromXKBySubjectId(this.subjectId).then(
          res => {
            if (res.code === '000000') {
              const data = res.data
              // 根据节点的id和parent_id来组成树状结构
              this.treeData = this.buildTree(JSON.parse(data))
              this.loading = false
            }
          }
        )
    },
    // 构建树状结构的函数
    buildTree(data) {
      let tree = []
      let map = {}
      // 初始化map
      data.forEach(item => {
        map[item.id] = { ...item, children: [] }
      })
      data.forEach(item => {
        const { id, name, parent_id } = item
        const node = map[id]

        if (parent_id === null || parent_id === 0) {
          // 根节点
          tree.push(node)
        }
        else {
          // 非根节点，加入父节点的children
          if (map[parent_id]) {
            map[parent_id].children.push(node)
          }
        }
      })
      return tree
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleCheckChange(node, checkedNodes) {
      // 获取所有选中的节点
      this.selectedNodes = this.$refs.tree.getCheckedNodes()
    },
    handleConfirm() {
      this.tmpId = this.selectedNodes.map(node => Number(node.id))
      this.showTree = false
    },
    handleChange(value) {
      const selectedOptionIds = this.selectedNodes.map(node => Number(node.id))
      if (!selectedOptionIds)
        return
      return this.$emit('change', value,  this.selectedNodes)
    },
    handleClose() {
      this.showTree = false
    },
    handleBlur() {
      // 保证弹窗不会因为输入框失去焦点而关闭
      setTimeout(() => {
        if (!this.showTree) {
          this.$refs.tree.setCheckedNodes(this.selectedNodes)
        }
      }, 100)
    }
  },
}
</script>
<style scoped>
/deep/ .el-dialog__body {
    padding: 0 20px;
}

.el-input {
    width: 100%;
}

.el-dialog {
    padding: 0;
}
</style>
