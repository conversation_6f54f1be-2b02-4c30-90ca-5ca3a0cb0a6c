<template>
  <div class="app-container story-add">
    <el-row>
      <el-form id="story-add" :model="listQuery" label-width="80px">
        <div class="story-search">
          <el-col :sm="{span:24}" :md="{span:24}">
            <el-form-item label="故事标题" style="width:40%;" required>
              <el-input v-model="listQuery.title" placeholder="请输入故事标题（推荐使用课程名称或编号）" />
            </el-form-item>
          </el-col>
          <el-col :sm="{span:24}" :md="{span:24}" class="story-subjects">
            <el-form-item label="科目" class="story-list" required>
              <el-select filterable v-model="listQuery.subject" placeholder="选择科目" clearable class="filter-item" style="width: 100%;">
                <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="题目类型" class="story-list" required>
              <el-select filterable v-model="listQuery.questionType" placeholder="选择题目类型" clearable class="filter-item" style="width: 100%;">
                <el-option v-for="item in questions" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="{span:24}" :md="{span:24}">
            <el-form-item label="状态" required>
              <el-radio-group v-model="listQuery.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="99">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </div>
        <el-col :sm="{span:24}" :md="{span:24}" class="relation-list">
          <h4 class="common-title">
            <span>关联课程</span>
          </h4>
          <div class="relation-subject">
            <span>{{ relationSubject }}</span>
            <el-select filterable v-model="listQuery.subjectId" placeholder="请选择科目" clearable class="filter-item" style="width: 220px;margin-left:10px;" @change="getClassType">
              <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
            <el-select filterable v-model="listQuery.classTypeId" placeholder="请选择班型" clearable class="filter-item" style="width: 220px;margin-left:10px;" @change="courseStory">
              <el-option v-for="item in classList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
            <el-select filterable v-model="listQuery.courseId" placeholder="请选择课程" clearable class="filter-item" style="width: 370px;margin-left:10px;margin-right:10px;" @change="refresh">
              <el-option v-for="item in courseList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
            <el-time-picker
              v-model="listQuery.ejectTime"
              popper-class="time-picker"
              value-format="mm:ss"
              placeholder="请选择故事弹出时间"
              format="mm:ss"
            />
          </div>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:24}" class="relation-list">
          <div>
            <h4 class="common-title">
              <span>故事情节</span>
              <em>可以批量上传后，进行拖动排序。最多上传20张图片</em>
            </h4>
            <div id="one" class="story-pics clear" style="min-height:120px;">
              <div v-if="storyList.length<20" class="upload-btn" @click="uploadStory(1)">
                <i class="el-icon-plus" />
              </div>
              <!-- 故事情节的图片 -->

              <draggable v-model="storyList" :animation="500" @end="moveEnd">
                <div v-for="(item,index) in storyList" :key="item.id" class="drage-img">
                  <img :src="item.src">
                  <p class="img-opera">
                    <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,item.src)" />
                    <span class="el-icon-delete" @mousedown.stop="delImg(index)" />
                  </p>
                </div>
              </draggable>

              <!-- 故事情节的图片 -->
            </div>
            <div id="couse-title">
              <h4 class="common-title pd10">
                <span>题目</span>
                <em>至多1张图片</em>
              </h4>
              <div class="story-pics clear" style="min-height:120px;">
                <div v-if="!drageTitle" class="upload-btn" @click="uploadStory(2)">
                  <i class="el-icon-plus" />
                </div>
                <div v-if="drageTitle" class="drage-img">
                  <img :src="drageTitle" class="title-img">
                  <p class="img-opera">
                    <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,drageTitle)" />
                    <span class="el-icon-delete" @click.stop="delTitle" />
                  </p>
                </div>
              </div>
              <!-- 自定义按钮 -->
              <div v-if="listQuery.questionType!==2" class="btns-custom">
                <span class="common-btns" style="padding-top:14px;">选项：</span>
                <div class="btns">
                  <el-checkbox-group v-model="btnList" @change="getVal">
                    <el-checkbox v-for="(item,index) in btnNames" :key="index" :label="item.choice">
                      <em>{{ item.choice }}</em>
                      <el-input v-model="item.inner" placeholder="自定义按钮名称（可选）" />
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div v-if="listQuery.questionType!==2&&answerList.length>0" class="btns-custom">
                <span class="common-btns">正确答案：</span>
                <div class="btns">
                  <el-radio-group v-model="answer" @change="getAnswer">
                    <el-radio v-for="(item,index) in answerList" :key="index" :label="item" />
                  </el-radio-group>
                </div>
              </div>
            </div>

            <div id="correct">
              <!-- 回答正确显示 -->
              <h4 class="common-title pd10">
                <span>回答正确显示：</span>
                <em>可以批量上传后，进行拖动排序，至多5张图片</em>
              </h4>
              <div class="story-pics" style="min-height:120px;">
                <div v-if="correctAnswerImg.length<5" class="upload-btn" @click="uploadStory(3)">
                  <i class="el-icon-plus" />
                </div>
                <draggable v-model="correctAnswerImg" :animation="500" @end="moveEnd">
                  <div v-for="(item,index) in correctAnswerImg" :key="index" class="drage-img">
                    <img :src="item.src" class="title-img">
                    <p class="img-opera">
                      <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,item.src)" />
                      <span class="el-icon-delete" @click.stop="delCorrect(index)" />
                    </p>
                  </div>
                </draggable>

              </div>
            </div>
            <div id="error">
              <!-- 回答错误显示 -->
              <h4 class="common-title pd10">
                <span>回答错误显示：</span>
                <em>可以批量上传后，进行拖动排序，至多5张图片</em>
              </h4>
              <div class="story-pics" style="min-height:120px;">
                <div v-if="errorAnswerImg.length<5" class="upload-btn" @click="uploadStory(4)">
                  <i class="el-icon-plus" />
                </div>
                <draggable v-model="errorAnswerImg" :animation="500" @end="moveEnd">
                  <div v-for="(item,index) in errorAnswerImg" :key="index" class="drage-img">
                    <img :src="item.src" class="title-img">
                    <p class="img-opera">
                      <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,item.src)" />
                      <span class="el-icon-delete" @click.stop="delError(index)" />
                    </p>
                  </div>
                </draggable>

              </div>
            </div>
          </div>
        </el-col>
        <el-col class="ml15">
          <el-button type="default" size="mini" @click="cancelStory">取消</el-button>
          <el-button type="primary" size="mini" @click="confirmStory">保存</el-button>
        </el-col>
      </el-form>
    </el-row>
    <!-- 批量上传图片弹框 -->
    <batch-opera ref="batchStory" @getPic="getPic" @getTitle="getTitle" @getCorrect="getCorrect" @getError="getError" />
    <!-- 图片放大弹框 -->
    <div v-if="amplificationImg" class="amplification">
      <img :src="amplificationImg">
      <span class="el-icon-circle-close amplification-closed" @click="amplificationImg=''" />
    </div>
  </div>
</template>
<script>
import batchOpera from './batchOpera'
import draggable from 'vuedraggable'
import { addStory, getSubjects, courseStory, storyDetail, editStory, classStory } from '@/api/admissions'
export default {
  name: 'AddStory',
  inject: ['reload'],
  components: {
    batchOpera,
    draggable
  },
  data() {
    return {
      listQuery: {},
      relationSubject: '烨晨',
      storyList: [],
      amplificationImg: '',
      drageTitle: null,
      btnList: [],
      a: '',
      b: '',
      c: '',
      d: '',
      answer: '',
      correctAnswerImg: [],
      errorAnswerImg: [],
      questions: [
        {
          id: 1,
          title: '选择题'
        },
        {
          id: 2,
          title: '背诵题'
        }
      ],
      subjectsList: [],
      classList: [],
      courseList: [],
      btnNames: [
        {
          choice: 'A',
          inner: ''
        },
        {
          choice: 'B',
          inner: ''
        },
        {
          choice: 'C',
          inner: ''
        },
        {
          choice: 'D',
          inner: ''
        }
      ],
      id: null,
      isEdit: null,
      answerList: []
    }
  },
  watch: {
    classList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.classTypeId = ''
          this.listQuery.courseId = ''
        }
      },
      deep: true
    },
    'listQuery.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.classTypeId = ''
        }
      }
    },
    'listQuery.classTypeId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.courseId = ''
        }
      }
    },
    'listQuery.questionType': {
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {
        if (newVal === 2) {
          this.btnNames = [
            {
              choice: 'A',
              inner: ''
            },
            {
              choice: 'B',
              inner: ''
            },
            {
              choice: 'C',
              inner: ''
            },
            {
              choice: 'D',
              inner: ''
            }
          ]
          this.answerList = []
          this.btnList = []
        }
      }
    },
    $route: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal.params.id) {
          this.storyDetail(this.$route.params.id)
        }
      }
    }
  },
  mounted() {
    const name = localStorage.getItem('storyName')
    this.setTagsViewTitle(name)
    this.getSubjects()
    this.isEdit = this.$route.params.isEdit
    if (this.$route.params.isEdit) {
      this.id = this.$route.params.id
      this.storyDetail(this.$route.params.id)
    } else {
      this.$set(this.listQuery, 'status', 1)
    }
  },
  methods: {
    setTagsViewTitle(name) {
      const cRoute = Object.assign({}, this.$route)
      const title = '故事'
      const route = Object.assign({}, cRoute, { title: `${name}-${title}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    uploadStory(imgFlag) {
      this.$refs.batchStory.storyFlag = true
      this.$refs.batchStory.imgFlag = imgFlag
      this.$refs.batchStory.imgList = []
      this.$refs.batchStory.storyImgs = imgFlag === 1 ? this.storyList : null
      this.$refs.batchStory.currentImgs = imgFlag === 3 ? this.correctAnswerImg : null
      this.$refs.batchStory.errorImgs = imgFlag === 4 ? this.errorAnswerImg : null
    },
    getPic(imgs) { // 调用上传图片的组件获取上传的图片
      this.storyList.push(...imgs)
    },
    getCorrect(imgs) {
      this.correctAnswerImg.push(...imgs)
    },
    getError(imgs) {
      this.errorAnswerImg.push(...imgs)
    },
    getTitle(img) {
      this.drageTitle = img.src
    },
    amplification(e, urls) {
      this.amplificationImg = urls
    },
    delImg(index) {
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.storyList.splice(index, 1)
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    delCorrect(index) {
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.correctAnswerImg.splice(index, 1)
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    delTitle() {
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.drageTitle = ''
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    delError(index) {
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.errorAnswerImg.splice(index, 1)
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getClassType() { // 查询班型
      if (this.listQuery.subjectId) {
        classStory(this.listQuery.subjectId).then(res => {
          if (res.code === '000000') {
            this.classList = res.data
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    courseStory() {
      if (this.listQuery.subjectId && this.listQuery.classTypeId) {
        courseStory(this.listQuery.classTypeId, this.listQuery.subjectId).then(res => {
          if (res.code === '000000') {
            const courseArr = []
            res.data && res.data.length > 0 ? res.data.forEach(item => {
              const course = {}
              course['id'] = item.id
              course['title'] = `${item.title ? item.title : ''}--${item.videoLength !== null && item.videoLength > 0 ? this.secTotimes(item.videoLength) : ''}`
              courseArr.push(course)
            }) : []
            this.courseList = courseArr || []
          }
        }).catch(() => {

        })
      }
      this.$forceUpdate()
    },
    cancelStory() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
      })
    },
    confirmStory() { // 新增故事试题
      const that = this


      if (!that.listQuery.title) {
        that.$message({
          message: '请输入故事标题',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.subject) {
        that.$message({
          message: '请选择科目',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.questionType) {
        that.$message({
          message: '请选择题目类型',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.status) {
        that.$message({
          message: '请选择状态',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.subjectId) {
        that.$message({
          message: '请选择关联科目',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.classTypeId) {
        that.$message({
          message: '请选择关联班型',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.courseId) {
        that.$message({
          message: '请选择关联课程',
          type: 'error'
        })
        return false
      }
      if (!that.listQuery.ejectTime) {
        that.$message({
          message: '请选择关故事弹出时间',
          type: 'error'
        })
        return false
      }
      if (that.storyList.length === 0) {
        that.$message({
          message: '请上传故事情节的相关图片',
          type: 'error'
        })
        return false
      }
      if (that.storyList.length > 20) {
        that.$message({
          message: '故事情节的相关图片不能大于20张',
          type: 'error'
        })
        return false
      }
      if (!that.drageTitle) {
        that.$message({
          message: '请上传故事题目的相关图片',
          type: 'error'
        })
        return false
      }
      if (that.btnList.length < 2 && that.listQuery.questionType !== 2) {
        that.$message({
          message: '选项至少需要选择2项',
          type: 'error'
        })
        return false
      }
      if (!that.answer && that.listQuery.questionType !== 2) {
        that.$message({
          message: '请选择正确答案',
          type: 'error'
        })
        return false
      }
      if (that.correctAnswerImg.length === 0) {
        that.$message({
          message: '请上传故事正确答案的相关图片',
          type: 'error'
        })
        return false
      }
      if (that.correctAnswerImg.length > 5) {
        that.$message({
          message: '故事正确答案的相关图片不能大于5张',
          type: 'error'
        })
        return false
      }
      if (that.errorAnswerImg.length === 0) {
        that.$message({
          message: '请上传故事错误答案的相关图片',
          type: 'error'
        })
        return false
      }
      if (that.errorAnswerImg.length > 5) {
        that.$message({
          message: '故事错误答案的相关图片不能大于5张',
          type: 'error'
        })
        return false
      }
      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const titleImg = {
        imageType: 1,
        url: that.drageTitle,
        optionKey: '',
        optionValue: '',
        imageId: ''
      }

      const storyImg = []// 故事情节
      that.storyList.forEach(item => {
        const objs = {}
        if (item.src) {
          objs.imageType = 2
          objs.url = item.src
          objs.optionKey = ''
          objs.optionValue = ''
          objs.imageId = ''
          storyImg.push(objs)
        }
      })

      const correctImg = []// 正确答案
      that.correctAnswerImg.filter(item => item.src !== undefined)
      that.correctAnswerImg.filter(item => {
        const objsC = {}
        objsC.imageType = 3
        objsC.url = item.src
        objsC.optionKey = ''
        objsC.optionValue = ''
        objsC.imageId = ''
        correctImg.push(objsC)
      })
      const errorImg = []// 错误答案
      that.errorAnswerImg.filter(item => item.src !== undefined)
      that.errorAnswerImg.filter(item => {
        const objsE = {}
        objsE.imageType = 4
        objsE.url = item.src
        objsE.optionKey = ''
        objsE.optionValue = ''
        objsE.imageId = ''
        errorImg.push(objsE)
      })
      const btns = []// 按钮选项
      that.btnList.forEach((item, index) => {
        const objBtn = {}
        that.btnNames.forEach(items => {
          if (item === items.choice) {
            objBtn['optionKey'] = items.choice
            objBtn['optionValue'] = items.inner
            objBtn['imageType'] = 5
            objBtn['url'] = ''
            objBtn['imageId'] = ''
          }
        })
        btns.push(objBtn)
      })
      const imageList = [...storyImg, ...correctImg, ...errorImg, ...btns, titleImg]
      const data = Object.assign({}, that.listQuery, {
        answer: that.answer,
        imageList: imageList,
        ejectTime: that.timeToSec(that.listQuery.ejectTime),
        clientCode: 200,
        id: that.id ? that.id : null
      })
      if (that.id && that.isEdit) { // 修改
        editStory(data).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: '编辑成功',
              type: 'success'
            })
            that.listQuery = {}
            that.answer = ''
            that.storyList = []
            that.btnList = []
            that.errorAnswerImg = []
            that.correctAnswerImg = []
            that.drageTitle = ''
            that.btnNames = [
              {
                choice: 'A',
                inner: ''
              },
              {
                choice: 'B',
                inner: ''
              },
              {
                choice: 'C',
                inner: ''
              },
              {
                choice: 'D',
                inner: ''
              }
            ]
            that.answerList = []
            loading.close()
            setTimeout(() => {
              that.closeIt()
            }, 500)
          }
        }).catch(() => {
          loading.close()

        })
      } else {
        addStory(data).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: '新增成功',
              type: 'success'
            })
            that.listQuery = {}
            that.answer = ''
            that.storyList = []
            that.btnList = []
            that.errorAnswerImg = []
            that.correctAnswerImg = []
            that.drageTitle = ''
            that.btnNames = [
              {
                choice: 'A',
                inner: ''
              },
              {
                choice: 'B',
                inner: ''
              },
              {
                choice: 'C',
                inner: ''
              },
              {
                choice: 'D',
                inner: ''
              }
            ]
            that.answerList = []
            loading.close()
            setTimeout(() => {
              that.closeIt()
            }, 500)
          }
        }).catch(() => {
          loading.close()

        })
      }
    },
    timeToSec(time) { // 时分秒转为秒
      var min = time.split(':')[0]
      var sec = time.split(':')[1]
      var s = Number(min * 60) + Number(sec)
      return s
    },
    secTotime(s) { // 秒转为是时分秒
      var t
      var min = parseInt(s / 60)
      const min1 = min < 10 ? min.toString().padStart(2, '00') : min
      var sec = s % 60
      const sec1 = sec < 10 ? sec.toString().padStart(2, '00') : sec
      t = `${min1}:${sec1}`
      return t
    },
    secTotimes(s) { // 秒转为是时分秒
      var t
      var min = parseInt(s / 60)
      const min1 = min < 10 ? min.toString().padStart(2, '00') : min
      var sec = s % 60
      const sec1 = sec < 10 ? sec.toString().padStart(2, '00') : sec
      t = `${min1}分${sec1}秒`
      return t
    },
    storyDetail(id) { // 故事详情
      storyDetail(id).then(res => {
        if (res.code === '000000') {
          this.id = res.data.id
          this.answer = res.data.answer ? res.data.answer : ''
          this.listQuery = {
            classTypeId: res.data.classTypeId,
            courseId: res.data.courseId,
            ejectTime: res.data.ejectTime ? this.secTotime(res.data.ejectTime) : '',
            questionType: res.data.questionType,
            status: res.data.status ? res.data.status : 1,
            subject: res.data.subject,
            subjectId: res.data.subjectId,
            title: res.data.title
          }
          this.drageTitle = res.data.subjectUrl || ''
          const storyList = []
          res.data.storyline && res.data.storyline.length > 0 ? res.data.storyline.forEach(item => {
            const storys = {}
            storys['src'] = item
            storys['imageType'] = 2
            storyList.push(storys)
          }) : []
          this.storyList = storyList

          const correctAnswer = []
          res.data.correctUrl && res.data.correctUrl.length > 0 ? res.data.correctUrl.forEach(item => {
            const corrects = {}
            corrects['src'] = item
            corrects['imageType'] = 3
            correctAnswer.push(corrects)
          }) : []
          this.correctAnswerImg = correctAnswer

          const errorAnswer = []
          res.data.errorUrl && res.data.errorUrl.length > 0 ? res.data.errorUrl.forEach(item => {
            const errors = {}
            errors['src'] = item
            errors['imageType'] = 4
            errorAnswer.push(errors)
          }) : []
          this.errorAnswerImg = errorAnswer

          this.btnList = res.data.options && res.data.options.length > 0 ? res.data.options.map(item => item.optionKey) : []
          this.answerList = this.btnList.length > 0 ? this.btnList : []
          res.data.options && res.data.options.length > 0 ? res.data.options.forEach(items => {
            if (items.optionKey === 'A') {
              this.btnNames[0].inner = items.optionValue
              this.btnNames[0].choice = items.optionKey
            } else if (items.optionKey === 'B') {
              this.btnNames[1].inner = items.optionValue
              this.btnNames[1].choice = items.optionKey
            } else if (items.optionKey === 'C') {
              this.btnNames[2].inner = items.optionValue
              this.btnNames[2].choice = items.optionKey
            } else if (items.optionKey === 'D') {
              this.btnNames[3].inner = items.optionValue
              this.btnNames[3].choice = items.optionKey
            }
          }) : []
          this.getSubjects()
          this.getClassType(res.data.subjectId)
          this.courseStory(res.data.classTypeId, res.data.subjectId)
        }
      }).catch(() => {

      })
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 100)
      })
    },
    refresh() {
      this.$forceUpdate()
    },
    getVal(val) {

      this.answerList = val
    },
    getAnswer(val) {

    },
    moveEnd(ele) {

    }
  }
}
</script>
<style scoped lang="scss">
.story-search{
    border-bottom: 1px #E7E7E7 solid;
    height: 145px;
}
.story-subjects{
    display: flex;
    .story-list{
        width:20%
    }
}
/deep/ .story-search .el-form-item{
    margin-bottom: 10px !important;
}
.relation-list{
    margin-bottom: 20px;
    margin-left: 15px;
    .common-title{
        padding-bottom: 10px;
        span{
            color: #606266;
            font-size: 16px;
            font-weight: bold;
        }
        em{
            color: #A09F9F;
            font-size: 14px;
        }

    }
}
.story-pics{
  width: 100%;
  .upload-btn{
    float: left;
    width: 120px;
    height: 120px;
    line-height: 108px;
    border-radius: 4px;
    border:1px #108FFB dashed;
    text-align: center;
    margin-right: 20px;
    cursor: pointer;
    i{
        color: #108FFB;
        font-size: 20px;
    }
  }
}
.drage-img{
  position: relative;
  display: inline-block;
  width:120px;
  height:120px;
  margin-right: 20px;
  margin-bottom: 20px;
}
.drage-img img{
  position: absolute;
  top: 0;
  left: 0;
  width:120px;
  height:120px;
}
.title-img{
  width: 120px;
  height: 120px;
  border-radius: 4px;
  border:1px #E9E9E9 solid;
}
.storys{
    width: 120px;
    height: 120px;
    overflow: hidden;
    margin-right: 10px;
    border-radius: 4px;
    border:1px #E9E9E9 solid;
    float: left;
    &.first-child{
      margin-left: 120px;
    }
  img{
      width: 120px;
      height: 120px;
  }
  }
  .img-opera{
    position:absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 30px;
    padding-top: 6px;
    background: rgba($color: #000000, $alpha: 0.6);
    display: flex;
    align-content: center;
    justify-content: center;
    span{
      font-size: 20px;
      color: #fff;
      cursor: pointer;
      &:first-child{
        padding-right: 15px;
      }
    }
  }
.relation-subject{
    display: flex;
    padding-left: 8px;
    span{
        padding: 5px 10px;
        color: #333;
        font-size: 14px;
        border-radius: 4px;
        background: #F6F6F6;
    }
}
.amplification{
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: 0.6);
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  img{
    width: 600px;
    height: 600px;
    align-items: center;
  }
  .amplification-closed{
    position: absolute;
    top: 12%;
    right: 20%;
    color: #fff;
    font-size: 50px;
    cursor: pointer;
  }
}
.btns-custom{
  display: flex;
  margin-bottom: 10px;
}
.common-btns{
  color: #333;
  font-size: 14px;
  padding-top: 5px;
  padding-right: 10px;
}
.btns{
  width: 92%;
  display: flex;
  margin-top: 8px;
}
.pd10{
  padding-top: 10px;
}
#one{
  margin-bottom: 10px;
}
#couse-title{
  margin-bottom: 10px;
}
#correct{
  margin-bottom: 10px;
}
#error{
  margin-bottom: 10px;
}
.ml15{
  margin-left: 15px;
}
::v-deep .el-time-spinner.has-seconds .el-time-spinner__wrapper{
  display:none !important;
  opacity: 0;
}
/deep/ .el-time-spinner.has-seconds .el-time-spinner__wrapper{
  width:50% !important;
}

</style>
<style>
.time-picker .el-time-spinner > .el-time-spinner__wrapper:first-child{
  display:none !important;
}
.time-picker .el-time-spinner.has-seconds .el-time-spinner__wrapper{
  width:50% !important;
}
</style>
