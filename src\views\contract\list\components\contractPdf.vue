<template>
  <div>
    <el-dialog v-el-drag-dialog v-loading.fullscreen.lock="fullscreenLoading" width="800px" title="电子合同" class="pdfDialog" :visible.sync="dialogPdf" :close-on-click-modal="!dialogPdf">
      <el-form label-width="100px">
        <el-row>
          <el-col :sm="24" :md="24" :lg="24">
            <el-form-item label="合同名称：">
              <label>{{ contractData.contractName }}</label>
            </el-form-item>
            <el-form-item label="合伙人：">
              <label>{{ contractData.customer }}</label>
            </el-form-item>
            <el-form-item label="加盟区域：">
              <label>{{ contractData.joinArea }}</label>
            </el-form-item>
            <el-form-item label="加盟项目：">
              <label>{{ contractData.project }}</label>
            </el-form-item>
            <el-form-item label="电子合同：">
              <el-button type="primary">
                <a :href="pdfDetail.pdfUrl" download="电子合同">下载合同</a>
              </el-button>
            </el-form-item>
            <div v-for="(item,index) in pdfDetail.imgList" :key="index">
              <img :src="item">
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { viewContractPdf } from '@/api/contract'
export default {
  name: 'ContractPdf',
  components: {},
  directives: { elDragDialog },
  props: {},
  data() {
    return {
      dialogPdf: false,
      contractData: {},
      fullscreenLoading: false,
      pdfDetail: {
        imgList: []
      }
    }
  },
  methods: {
    getDetail(row) {
      const id = row.id
      this.contractData = row
      this.fullscreenLoading = true
      viewContractPdf(id).then(res => {
        if (res.code === '000000') {
          this.pdfDetail = res.data
          this.dialogPdf = true
        }
        this.fullscreenLoading = false
      }).catch(error => {

        this.fullscreenLoading = false
      })
    }

  }
}
</script>

<style scoped lang="scss">
  .pdfDialog img {
    width: 100%;
  }
  /deep/ .el-dialog__body {
    height: 700px;
    overflow-y: scroll;
  }
  /deep/ .el-form-item {
    margin-bottom: 10px;
  }
</style>
