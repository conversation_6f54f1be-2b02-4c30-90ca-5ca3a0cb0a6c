<template>
  <div class="app-container">
    <div class="filter-container" v-if="testType===1 || testType===2"> <el-button v-waves class="filter-item" type="primary" @click="openForm">添加题目</el-button></div>
   <div class="filter-container" v-if="testType===3">
    <GenerateQuestionFromXk  :course-base-info="courseInfo" @refresh="getQueryQuestionList"/>
   </div>
    <el-table
      v-loading="listLoading"
      border
      fit
      stripe
      default-expand-all
      highlight-current-row
      style="width: 100%;"
      :data="topicList"
    >
      <el-table-column type="expand" width="40" v-if="testType===3">
        <template slot-scope="scope">
          <div class="content-box">
            <QuestionView :question="scope.row" ></QuestionView>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="题号" align="center" width="80" prop="num" />
      <el-table-column label="题号类型" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1" class="ml-2" type="success">客观题</el-tag>
          <el-tag v-else class="ml-2">主观题</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="答案" align="center" prop="answer"   v-if="testType!==3"/>
      <el-table-column label="是否多选题	" align="center" prop="isMultiple">
        <template slot-scope="scope">
          {{ scope.row.isMultiple == 0? '否': '是' }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp"
        fixed="right"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <span style="color: #409EFF" title="编辑" @click="editForm(scope.row)">编辑</span>
          <span style="color: #409EFF" title="删除" @click="deleteForm(scope.row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 习题答案对话框 -->
    <el-dialog :title="submitForm.id ? '编辑题目': '添加题目'" :visible.sync="submitFormDialog" width="600px" center append-to-body>
      <el-form
        ref="formRef"
        :model="submitForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="题号" prop="num">
          <el-input v-model="submitForm.num" maxlength="10" placeholder="请输入题号" show-word-limit type="text" @input="numHandlerChange" />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="submitForm.type">
            <el-radio :label="1" :disabled="submitForm.id? true: false">客观题</el-radio>
            <el-radio :label="2" :disabled="submitForm.id? true: false">主观题</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-for="(domain, index) in submitForm.answers" v-show="submitForm.type==1">
          <el-form-item
            v-if="domain.delFlag != 1 && submitForm.type==1"
            :key="'answers'+index"
            :label="'选项' + (index+1)"
            :prop="'answers.' + index + '.optionName'"
            :rules="{required: true,message: '选项不能为空',trigger: 'blur'}"
          >
            <el-input v-model="domain.optionName" placeholder="请输入选项">
              <template #prepend>
                <el-checkbox v-model="domain.isAnswer" label="正确" />
              </template>
              <template v-if="index > 3" #append><el-button type="danger" plain @click.prevent="removeAnswer(domain)">删除</el-button></template>
            </el-input>
          </el-form-item>
        </template>

        <template v-for="(domain, index) in submitForm.subjeanswers" v-show="submitForm.type==2">
          <el-form-item
            v-show="submitForm.type==2"
            :key="'subjeanswers'+index"
            :label="'选项' + (index+1)"
            :prop="'answers.' + index + '.optionName'"
            :rules="{required: true,message: '选项不能为空',trigger: 'blur'}"
          >
            <el-input v-model="domain.optionName" placeholder="请输入选项">
              <template #prepend>
                <el-checkbox v-model="domain.isAnswer" label="正确" />
              </template>
            </el-input>
          </el-form-item>
        </template>

        <el-form-item v-show=" submitForm.answers && submitForm.answers.length < 10 && submitForm.type==1"><el-button @click="addAnswer">添加选项</el-button></el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="submitForm.sort" :min="1" :max="2000" />
        </el-form-item>

        <el-form-item label="答案解说" prop="answer">
          <el-input v-model="submitForm.answer" type="textarea"  :autosize="{minRows: 4}" placeholder="请输入答案解说" show-word-limit  />
        </el-form-item>
        <el-form-item label="阿里云VID" prop="vid">
          <el-input v-model="submitForm.vid" maxlength="200" placeholder="请输入阿里云VID" show-word-limit type="text" />
        </el-form-item>
        <el-form-item label="关联课程题目id" prop="vid">
          <el-input v-model="submitForm.courseQuestionId" maxlength="200" placeholder="请输入关联课程题目id" show-word-limit type="number" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="submitForm.remark" maxlength="300" placeholder="请输入备注" show-word-limit type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormAction('formRef')">确 定</el-button>
        <el-button @click="submitFormDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryQuestionList, queryQuestionById, addQuestion, updateQuestion, deleteQuestion } from '@/api/exercises'
import GenerateQuestionFromXk from '@/views/curriculum/componentsXK/TabGenerateQuestionFromXk.vue'
import QuestionView from '@/views/curriculum/componentsXK/QuestionView.vue'
export default {
  components: { QuestionView, GenerateQuestionFromXk },
  props: {
    courseInfo: {
      type: Object,
      required: true
    },
    testType: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      listLoading: false,
      submitFormDialog: false,
      topicList: [],
      formRules: {
        num: { required: true, message: '请输入题号', trigger: ['blur', 'change'] },
        sort: { required: true, message: '请输入排序', trigger: ['blur', 'change'] }
      },

      submitForm: {
        id: '',
        type: 1,
        answers: [],
        subjeanswers: [],
        isMultiple: 1
      }
    }
  },

  mounted() {
    this.getQueryQuestionList()
  },

  methods: {
    getQueryQuestionList() {
      this.listLoading = true
      queryQuestionList(this.courseInfo.id).then(res => {
        this.listLoading = false
        if (res.code === '000000') {
          this.topicList = res.data
        }
      })
    },

    openForm() {
      Object.keys(this.submitForm).forEach((item, index) => {
        this.submitForm[item] = ''
      })
      this.submitForm.homeworkId = this.courseInfo.id
      this.submitForm.type = 1
      this.submitForm.sort = 1
      this.submitForm.answers = []
      this.submitForm.subjeanswers = []
      // 选项
      this.submitForm.answers.push({ optionName: 'A', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.answers.length + 1 })
      this.submitForm.answers.push({ optionName: 'B', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.answers.length + 1 })
      this.submitForm.answers.push({ optionName: 'C', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.answers.length + 1 })
      this.submitForm.answers.push({ optionName: 'D', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.answers.length + 1 })

      this.submitForm.subjeanswers.push({ optionName: '会', homeworkId: this.homeworkId, isAnswer: true, sort: this.submitForm.subjeanswers.length + 1 })
      this.submitForm.subjeanswers.push({ optionName: '不会', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.subjeanswers.length + 1 })

      this.submitFormDialog = true
    },
    editForm(row) {
      this.submitForm = Object.assign({}, row)
      queryQuestionById(row.id).then(res => {
        if (res.code === '000000') {
          this.submitForm = res.data
          if (this.submitForm.answers) {
            this.submitForm.answers.forEach((item, index) => {
              if (item.isAnswer === 1) {
                item.isAnswer = true
              } else {
                item.isAnswer = false
              }
            })
            if (this.submitForm.type === 2) {
              this.submitForm.subjeanswers = this.submitForm.answers
              this.submitForm.answers = []
            }
          }
          this.submitFormDialog = true
        }
      })
    },
    deleteForm(row) {
      this.$confirm('确认删除？', {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        deleteQuestion(row).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '确认删除成功'
            })
            this.getQueryQuestionList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    submitFormAction() {

      let optionNameflag = true
      if (this.submitForm.type === 1) {
        this.submitForm.isMultiple = 0
        this.submitForm.answers.forEach((item, index) => {
          if (!item.optionName) {
            this.$message({
              type: 'warning',
              message: '请填写答案项！'
            })
            optionNameflag = false
          }
          if (item.isAnswer) {
            item.isAnswer = 1
            this.submitForm.isMultiple++
          } else {
            item.isAnswer = 0
          }
        })
        if (this.submitForm.isMultiple === 0) {
          this.$message({
            type: 'warning',
            message: '请选择答案项！'
          })
          optionNameflag = false
        } else if (this.submitForm.isMultiple === 1) {
          this.submitForm.isMultiple = 0
        } else {
          this.submitForm.isMultiple = 1
        }
      } else {
        this.submitForm.subjeanswers.forEach((item, index) => {
          if (item.isAnswer) {
            item.isAnswer = 1
          } else {
            item.isAnswer = 0
          }
        })
        this.submitForm.isMultiple = 0
        this.submitForm.answers = this.submitForm.subjeanswers
      }
      if (optionNameflag) {
        this.submitFormDialog = false
        if (this.submitForm.id) {
          updateQuestion(this.submitForm).then(res => {
            if (res.code === '000000') {
              this.getQueryQuestionList()
            }
          })
        } else {
          addQuestion(this.submitForm).then(res => {
            if (res.code === '000000') {
              this.getQueryQuestionList()
            }
          })
        }
      }
    },
    removeAnswer(answer) {
      const index = this.submitForm.answers.indexOf(answer)
      if (index !== -1) {
        if (answer.id) {
          answer.delFlag = 1
        } else {
          this.submitForm.answers.splice(index, 1)
        }
      }
    },
    addAnswer() {
      this.submitForm.answers.push({ optionName: '', homeworkId: this.homeworkId, isAnswer: false, sort: this.submitForm.answers.length + 1 })
    },
    numHandlerChange(e) {

      if (!isNaN(Number(e))) {
        this.submitForm.sort = e
      } else {
        this.submitForm.sort = 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.exercises-pdf {
  position: relative;
  background: #fff;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  padding: 15px;
  margin-top: 10px;
}
.content-box{
  padding:10px 0;
  margin:0 80px 0 120px;
  background: #F7F7F7;
  border: 1px solid #E6E6E6;
}
/deep/ .el-table__expanded-cell{
  //background: #F7F7F7;
  padding:0;
}
</style>
