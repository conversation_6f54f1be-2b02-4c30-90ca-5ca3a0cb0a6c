<template>
  <div>
    <el-row v-if="courseInfo" v-loading="loading">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-col :span="12">
            <h2 style="margin-bottom: 10px">
              {{ courseInfo.title }}
            </h2>
          </el-col>
          <el-col :span="12">
            <span style="font-size: 14px;margin-right: 10px;line-height: 20px;">状态:</span>
            <el-radio-group v-model="courseInfo.status" label="状态" @change="updateHomework('status')">
              <el-radio style="margin-right: 5px" :label="1">上架</el-radio>
              <el-radio :label="2">下架</el-radio>
            </el-radio-group>
            <span style="font-size: 14px;margin-right: 10px;margin-left: 80px;line-height: 20px;">模式:</span>
            <el-radio-group v-model="courseInfo.homeworkType" label="模式" @change="updateHomework('homeworkType')">
              <el-radio style="margin-right: 5px" :label="1">答题模式</el-radio>
              <el-radio :label="2">批改模式</el-radio>
            </el-radio-group>
          </el-col>
        </el-col>
      </el-row>
    </el-row>
    <!--如果没有则显示绑定试题按钮，让用户进行初始化-->
    <div v-if="!loading && !courseInfo && testType !==3">
      <el-button v-waves class="filter-item" type="primary" @click="addHomework">初始化试题</el-button>
    </div>
    <el-tabs v-if="courseInfo" v-model="activeName">
<!--      <el-tab-pane label="生成习题" v-if="testType===3" name="exercisesTopicXK">-->
<!--        <GenerateQuestionFromXk v-if="activeName === 'exercisesTopicXK'" :course-base-info="courseInfo" />-->
<!--      </el-tab-pane>-->
      <el-tab-pane label="习题" name="exercisesTopic">
        <exercisesTopic v-if="activeName === 'exercisesTopic'" :test-type="testType" :course-info="courseInfo" />
      </el-tab-pane>
      <el-tab-pane label="答案附件" name="exercisesPdf">
        <exercisesPdf v-if="activeName === 'exercisesPdf'" :course-info="courseInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import exercisesPdf from '@/views/curriculum/components/exercisesPdf.vue'
import exercisesTopic from '@/views/curriculum/components/exercisesTopic.vue'
import { addHomework, updateHomework } from '@/api/exercises'
import GenerateQuestionFromXk from '@/views/curriculum/componentsXK/TabGenerateQuestionFromXk.vue'

/**
 * 课程管理>习题>题目组件
 */
export default {
  name: 'CourseInfo',
  components: {
    GenerateQuestionFromXk,
    exercisesTopic,
    exercisesPdf
  },
  data() {
    return {
      activeName: 'exercisesTopic',
      // courseId: '', // 当前的id
      loading: false,
      loadmsg: '数据请求中'
    }
  },
  props: {
    courseInfo: {
      type: [Object],
      required: false,
      default: null
    },
    courseId: {
      type: [String, Number],
      required: false
    },
    testType: {
      type: [String, Number],
      required: true
    }
  },
  methods: {
    addHomework() {
      this.loading = true
      addHomework({ courseId: this.courseId, testType: this.testType }).then(res => {
        this.loading = false
        if (res.code === '000000') {
          this.$emit('call')
        }
      })
    },
    updateHomework(_type) {
      const param = {
        homeworkId: this.courseInfo.id,
        homeworkType: this.courseInfo.homeworkType,
        status: this.courseInfo.status,
        testType: this.testType,
        type: _type
      }
      updateHomework(param).then(res => {
        if (res.code === '000000') {
          this.$emit('call')
        }
      })
    }
  }
}
</script>
