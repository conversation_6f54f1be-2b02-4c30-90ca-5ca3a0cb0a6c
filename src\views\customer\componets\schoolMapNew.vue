<template>
  <el-dialog
    v-if="innerVisible"
    v-el-drag-dialog
    width="900px"
    title="设置定位点"
    :close-on-click-modal="!innerVisible"
    :visible.sync="innerVisible"
    @close="mapSettingCancel"
  >
    <el-form  label-width="140px" >
      <el-row :gutter="22">
        <el-col>
          <el-form-item label="签约区域：" required>
            <area-picker
              :area-list="areaList"
              :level="'4'"
              area-style="width:100%"
              class="filter-item"
              @getAreaList="getAreaList"
              @getAreaName="getAreaName"
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="详细地址：" prop="addressDetail" >
            <el-input v-model="addressDetail" maxlength="50">
<!--              <el-button slot="append" @click="getMap">点击查看</el-button>-->
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :sm="24" :md="6" :lg="12">
          <el-form-item label="经度：" prop="lng">
            <el-input v-model="lng" placeholder="输入经度" oninput="value=value.replace(/[^0-9.]/g,'')"/>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :md="6" :lg="12">
          <el-form-item label="纬度：" prop="lat">
            <el-input v-model="lat" placeholder="输入纬度" oninput="value=value.replace(/[^0-9.]/g,'')" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :md="24" :lg="24">
          <el-form-item label="" size="mini">
            <el-input v-model="coordinate" placeholder="请复制高德地图经纬度到此处，点击转换即可自动代填" maxlength="100" >
              <el-button slot="append"  @click="setGPS">转换</el-button>
            </el-input>
            <el-link type="primary" target="_blank" href="https://lbs.amap.com/tools/picker">点击打开高德地图获取经纬度<i class="el-icon-map-location el-icon--right"></i></el-link>
          </el-form-item>

        </el-col>
        <el-col>
          <el-form-item label="有效半径(米)：" prop="defaulRadius" style="width: 40%">
            <el-input v-model="defaulRadius" type="number" oninput="if(value>1000)value=1000" min="0" max="1000" maxlength="4" placeholder="请输入有效半径" />
          </el-form-item>
        </el-col>

      </el-row>

<!--      <div v-if="lng&&lat&&center.length>0" class="map">-->
<!--        <el-amap ref="map" vid="amapDemo" :center="center" :zoom="zoom" :events="events" class="amap-demo">-->
<!--          <el-amap-marker :position="center" :icon="icon" :events="events" />-->
<!--          <el-amap-circle-->
<!--            :center="center"-->
<!--            :radius="defaulRadius"-->
<!--            :fill-opacity="0.3"-->
<!--            :stroke-weight="0"-->
<!--            :fill-color="'#409EFF'"-->
<!--            :stroke-color="'#409EFF'"-->
<!--            :events="events"-->
<!--          />-->
<!--        </el-amap>-->
<!--      </div>-->
<!--      <div v-else class="no-data">未获取到经纬度，请重新选择地址</div>-->
    </el-form>
<!--    <div v-if="lng&&lat&&center.length>0" slot="footer" class="dialog-footer text-center">-->
    <div  slot="footer" class="dialog-footer text-center">
      <el-button @click="mapSettingCancel">取 消</el-button>
      <el-button type="primary" :disabled="!hadGetLngLat" @click="confirmLngLat">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import AreaPicker from '@/components/area-picker'
import axios from 'axios'
import Icon from '@/assets/img/marker.png'
export default {
  name: 'CreateNewSchool',
  components: {
    AreaPicker
  },
  directives: {
    elDragDialog
  },
  data() {
    const self = this
    return {
      hadGetLngLat: true,
      zoom: 13,
      coordinate:'',
      center: [121.59996, 31.197646],
      defaulRadius: 300, // 默认招生范围半径300米，最大1000米
      icon: Icon,
      events: {
        init: (o) => {},
        'moveend': () => {},
        'zoomchange': () => {},
        'click': (e) => {
          self.viewIp(e)
        }
      },
      innerVisible: false,
      areaNameStr: '',
      addressDetail: '',
      lng: '',
      lat: '',
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      }
    }
  },
  created() {
    // this.init()
  },
  methods: {
    setGPS(){
      if(this.coordinate){
        const arr = this.coordinate.split(',')
        this.lng = arr[0]
        this.lat = arr[1]
      }
    },
    /**
       * 点击获取地址
       * @returns {Promise<any>}
       */
    viewIp(e) {
      const {
        lng,
        lat
      } = e.lnglat

      this.lng = lng
      this.lat = lat
      // 这里通过高德 SDK 完成。
      const geocoder = new AMap.Geocoder({
        radius: 2500,
        extensions: 'all'
      })
      const that = this
      geocoder.getAddress([lng, lat], function(status, result) {
        if (status === 'complete' && result.info === 'OK') {
          if (result && result.regeocode) {
            // self.address = result.regeocode.formattedAddress;
            that.addressDetail = result.regeocode.formattedAddress ? result.regeocode.formattedAddress : ''
            that.center = [lng, lat]
          }
        }
      })
    },
    /**
       * 通过地址获取坐标
       * @returns {Promise<any>}
       */
    getMap() {
      const that = this
      const url = 'https://restapi.amap.com/v3/geocode/geo'
      const address = this.areaNameStr + this.addressDetail
      const params = {
        key: '8c0a773c6b49528f726dce0d937a2e73',
        address: address
      }
      if (!that.areaList.provinceId || !that.areaList.cityId || !that.areaList.areaId) {
        that.$message({
          type: 'warning',
          message: '请选择签约区域'
        })
      } else if (!that.addressDetail) {
        that.$message({
          type: 'warning',
          message: '请输入详细地址'
        })
      } else {
        axios.get(url, {
          params: params
        }).then(res => {

          if (res.data.status === '1') {
            const geocodes = res.data.geocodes

            if (geocodes.length > 0) {
              const fGeocode = geocodes.length > 0 ? geocodes[0] : undefined
              that.center = fGeocode ? fGeocode.location.split(',') : []
              that.lng = that.center.length > 0 ? that.center[0] : ''
              that.lat = that.center.length > 0 ? that.center[1] : ''
              that.hadGetLngLat = true
            } else {
              that.$message({
                type: 'warning',
                message: '未获取到经纬度'
              })
              that.center = []
              that.lng = ''
              that.lat = ''
            }
          } else {
            // that.$message({
            //   type: 'warning',
            //   message: ''
            //   // message: '地址获取失败'
            // })
            that.center = []
            that.lng = ''
            that.lat = ''
          }
        })
      }
    },
    /**
       * 获取经纬度
       */

    confirmLngLat() {
      if (!this.areaList || !this.areaList.provinceId || !this.areaList.cityId || !this.areaList.areaId) {
        this.$message({
          type: 'warning',
          message: '请选择签约区域'
        })
        return
      }
      if (!this.addressDetail) {
        this.$message({
          type: 'warning',
          message: '请输入详细地址'
        })
        return
      }
      if (!this.lng) {
        this.$message({
          type: 'warning',
          message: '请输入经度'
        })
        return
      }
      else if(this.lng<73 || this.lng>136){
        this.$message({
          type: 'warning',
          message: '经度超出范围，经度在73°E~136°E之间'
        })
        return
      }

      if (!this.lat) {
        this.$message({
          type: 'warning',
          message: '请输入纬度'
        })
        return
      }
      else if(this.lat<3 || this.lat>54){
        this.$message({
          type: 'warning',
          message: '纬度超出范围，纬度在3°N~54°N之间'
        })
        return
      }

      if (!this.defaulRadius) {
        this.$message({
          type: 'warning',
          message: '请输入有效半径'
        })
        return
      }
      if (this.defaulRadius < 0) {
        this.$message({
          type: 'warning',
          message: '有效半径不能小于零'
        })
        return
      }
      if (this.defaulRadius > 1000) {
        this.$message({
          type: 'warning',
          message: '有效半径最大1000米'
        })
        return
      }
      this.$emit('refresh', {
        addressDetail: this.addressDetail,
        lng: this.lng,
        lat: this.lat,
        areaList: this.areaList,
        defaulRadius: this.defaulRadius
      })
      // this.innerVisible = false
      this.mapSettingCancel()
    },
    getLonAndLat(area, address, defaulRadius) {

      this.innerVisible = true
      if (address) {
        this.areaList = area
      } else {
        this.areaList = {
          provinceId: '',
          cityId: '',
          areaId: '',
          countyId: ''
        }
      }
      this.addressDetail = address
      this.defaulRadius = defaulRadius
    },
    getMapData(areas, addressDetail) { // 从修改校区带地址，根据地址获取经纬度
      //
      //
      // const that = this
      // const urls = 'https://restapi.amap.com/v3/geocode/geo'
      // const addressMap = areas + addressDetail
      // const datas = {
      //   key: '8c0a773c6b49528f726dce0d937a2e73',
      //   address: addressMap
      // }
      // axios.get(urls, {
      //   params: datas
      // }).then(res => {
      //   if (res.data.status === '1') {
      //     const geocodes = res.data.geocodes
      //     if (geocodes.length > 0) {
      //       const fGeocode = geocodes.length > 0 ? geocodes[0] : undefined
      //       that.center = fGeocode ? fGeocode.location.split(',') : []
      //       that.lng = that.center && that.center.length > 0 ? that.center[0] : ''
      //       that.lat = that.center && that.center.length > 0 ? that.center[1] : ''
      //       that.hadGetLngLat = true
      //     } else {
      //       that.$message({
      //         type: 'warning',
      //         message: '未获取到经纬度'
      //       })
      //       that.center = []
      //       that.lng = ''
      //       that.lat = ''
      //     }
      //   } else {
      //     // that.$message({
      //     //   type: 'warning',
      //     //   message: ''
      //     // })
      //     that.center = []
      //     that.lng = ''
      //     that.lat = ''
      //   }
      // })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
      this.areaList.countyId = data.countyId
    },
    getAreaName(name) {
      this.areaNameStr = ''
      this.areaNameStr = (name.cityName || '') + (name.areaName || '') + (name.countyName || '') + (name.zhenName ||
          '')
    },
    mapSettingCancel() {
      this.coordinate=''
      this.innerVisible = false
    }
  }
}
</script>

<style scoped>
  .amap-demo {
    height: 500px;
    width: 100%;
  }
  .no-data{
    text-align: center;
    font-size: 18px;
    color: #666;
    padding: 20px 0;
  }
</style>
