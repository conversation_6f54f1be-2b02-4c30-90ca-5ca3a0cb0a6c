<template>
  <el-dialog
    title="跟进详情"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="70%"
    @opened="opened"
    :before-close="handleClose">

      <div class="filter-container" @keydown.enter="getList">
        <el-input  class="filter-item" style="width: 140px;" clearable v-model.trim="queryParams.clueCode" placeholder="客户编号" />
        <el-input  class="filter-item" style="width: 180px;" clearable v-model.trim="queryParams.mobile" placeholder="客户名称/手机号" />
        <area-picker :area-list="queryParams" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
        <el-button class="filter-item" size="mini" type="primary" @click="queryList" >查询</el-button>
        <el-button class="filter-item" size="mini" @click="handleReset">重置</el-button>
      </div>

        <el-table  v-loading="tableData.loading"  height="500" :data="tableData.list" border fit stripe highlight-current-row>
          <el-table-column label="序号" type="index" width="50"></el-table-column>
          <el-table-column label="客户编号" width="80" prop="clueCode"></el-table-column>
          <el-table-column label="客户名称" width="80" prop="customer"></el-table-column>
          <el-table-column label="所在区域" prop="areaName"></el-table-column>
          <el-table-column label="跟进内容" width="450" prop="remark"  :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <el-tooltip
                placement="top"
                effect="light"
                :content="scope.row.remark"
              >
                <div class="st-line-clamp-2 st-leading-6 st-max-h-[3rem] st-break-all">
                {{ scope.row.remark }}
              </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="跟进时间" width="150" prop="followTime"></el-table-column>
          <el-table-column label="下次跟进时间"  width="150" prop="nextFollowTime"></el-table-column>
          <el-table-column label="跟进人" prop="createBy"></el-table-column>
        </el-table>
        <pagination
          v-show="tableData.total>0"
          :total="tableData.total"
          layout="total, sizes, prev, pager, next"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
  </el-dialog>
</template>

<script>

import AreaPicker from "@/components/area-picker/index.vue";
import {selectFollowUpLogDetail} from "@/api/customer";
import Pagination from "@/components/Pagination/index.vue";

export default {
  name: "FlowDetailList",
  components: {Pagination, AreaPicker},
  data() {
    return {
      dialogVisible:false,
      queryParams:{
        pageIndex:1,
        pageSize:10,
        createUserId: '',
        mobile:"",
        clueCode: '',
        provinceId: '',
        cityId: '',
        areaId: '',
        startDate: '',
        endDate: ''
      },
      tableData:{
        loading: false,
        list:[],
        total:0
      },
    }
  },
  methods:{
    //查询列表
    queryList(){
      this.queryParams.pageIndex = 1;
      this.getList();
    },
    getList(){
      this.tableData.loading =  true;
      selectFollowUpLogDetail(this.queryParams).then(res=>{
        if(!res.data){
          this.queryParams.pageIndex = 1;
          this.tableData.list = [];
          this.tableData.total = 0;
        }else{
          this.tableData.list = res.data.records
          this.tableData.total = res.data.total
        }
      }).finally(()=>{
        this.tableData.loading = false;
      })
    },
    handleReset(){
      this.queryParams.clueCode = "";
      this.queryParams.provinceId = "";
      this.queryParams.cityId = "";
      this.queryParams.areaId = "";
      this.queryParams.mobile = "";
      this.queryParams.pageIndex = 1;
      this.getList();
    },
    handleClose(){
      this.dialogVisible = false;
      this.queryParams.clueCode = "";
      this.queryParams.provinceId = "";
      this.queryParams.cityId = "";
      this.queryParams.areaId = "";
      this.queryParams.mobile = "";
      this.tableData.list = [];
      this.tableData.total = 0;
      this.queryParams.pageIndex = 1;
    },
    showDialog(item){
      this.dialogVisible = true;
      this.queryParams.createUserId = item.createUserId
      this.queryParams.startDate = item.startDate;
      this.queryParams.endDate = item.endDate;
    },
    opened(){
      this.getList()
    },
    getAreaList(data) {
      this.queryParams.provinceId = data.provinceId;
      this.queryParams.cityId = data.cityId;
      this.queryParams.areaId = data.areaId;
    },

  }
}

</script>

<style scoped>

</style>
