export function checkValidArray(data) {
  return !!(Array.isArray(data) && data.length)
}
export function treeEach(data, callback) {
  var props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {
    children: 'children'
  }
  var children;

  (function recursive(data, parent) {
    data.forEach(function(node, index, arr) {
      children = node[props.children] // if callback false, skip children

      if (callback(node, index, arr, parent) !== false && checkValidArray(children)) {
        recursive(children, node)
      }
    })
  })(data, null)
}
export function treeFind(data, callback) {
  var props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {
    children: 'children'
  }
  var children
  var node
  var find = null
  return (function recursive(data, parent) {
    var len = data.length
    var index
    for (index = 0; index < len; index++) {
      node = data[index]
      if (callback(node, index, data, parent)) {
        find = node
        break
      }
      children = node[props.children]
      if (checkValidArray(children)) {
        node = recursive(children, node)
        if (node) {
          find = node
          break
        }
      }
    }

    return find
  }(data, null))
}
