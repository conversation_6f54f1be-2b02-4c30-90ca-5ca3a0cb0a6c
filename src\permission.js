import router from '@/router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/auth-redirect', '/passwordInfo', '/wxLogin', '/codeLogin'] // no redirect whitelist，, '/passwordInfo'

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()
  // 确定用户是否已登录
  const hasToken = getToken()
  if (hasToken) {
    // 如果去的是login 页面，直接进入 不做免登陆。 免登陆可以在此判断：next进入home页
    if (to.path === '/login') {
      next()
      NProgress.done()
    } else {
      // 确定用户是否已通过获得其权限
      const hasMenus = store.getters.menus && store.getters.menus.length > 0
      if (hasMenus) {
        next()
      } else {
        await store.dispatch('user/getMenus')
        await store.dispatch('user/getUserInfo')
        // get user info
        // generate accessible routes map based on roles
        const accessRoutes = await store.dispatch('permission/generateRoutes', store.getters.menus)
        // 动态添加可访问路由  如果没有token 需要将menus删除 重新赋值，这样才能进入地址的替换
        router.addRoutes(accessRoutes)
        next({ ...to, replace: true })
      }
    }
  } else {
    // 免登陆白名单
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      // 如果在特定的页面之外，访问时token失效会退回到登录页.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
