<template>
  <el-dialog :visible.sync="fileUploadFlag" :close-on-click-modal="!fileUploadFlag" title="导入新设备" width="40%" @close="closedImport">
    <div class="questions-info">
      <div class="question-tips">
        <a title="点击下载批量导入序列号模板" href="序列号模板.xlsx" target="_blank" download="序列号模板.xlsx">导入模板下载</a>
      </div>
      <div class="hello">
        <div class="upload">
          <div v-if="imgList.length===0" class="upload_warp">
            <div class="upload_warp_left" @click="fileClick">
              <img src="../../../assets/img/upload.png">
            </div>
            <div class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
              或者将文件拖到此处
            </div>
          </div>
          <div class="upload_warp_text">
            <i v-if="imgList.length>0">选中{{ imgList.length }}张文件，</i><i v-if="size">共{{ bytesToSize(size) }}</i>
          </div>
          <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
          <div v-show="imgList.length!=0" class="upload_warp_img">
            <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
              <div class="upload_warp_img_div_top">
                <div class="upload_warp_img_div_text">
                  {{ item.file.name }}
                </div>
                <img src="../../../assets/img/del.png" class="upload_warp_img_div_del" @click="fileDel(index)">
              </div>
              <img :src="item.file.src">
            </div>
          </div>
        </div>
      </div>
      <div class="basis-info">
        <el-button size="mini" type="primary" @click="addQuestionInfo">上传设备</el-button>
        <el-button size="mini" type="infor" @click="cancelSubmit">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { uploadPadSerialNumber } from '@/api/charge'
export default {
  directives: { elDragDialog },
  data() {
    return {
      progressStatus: null,
      percentage: 0,
      imgList: [],
      fileUploadFlag: false,
      size: 0,
      showFlag: false,
      subFlag: false,
      showWarning: false,
      showModal: false,
      showB: false,
      showProgress: false
    }
  },
  methods: {
    closedImport() {},
    // 拖拽上传文档
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {

      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        this.fileAdd(files[i])
        // 判断是否为文件夹
        // if (files[i].type !== '') {
        //   this.fileAdd(files[i])
        // } else {
        //   // 文件夹处理
        //   this.folders(fileList.items[i])
        // }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小

      const fileArr = file.name.split('.')
      if ((fileArr[fileArr.length - 1] !== 'xlsx') && (fileArr[fileArr.length - 1] !== 'xls')) {
        this.$message({
          message: '请上传表格格式的模板',
          type: 'error'
        })
        return false
      }
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = require('./excel.png')

        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(index) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      this.imgList.splice(index, 1)
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    },
    addQuestionInfo() {
      const that = this
      var formData = new FormData()
      formData.append('studentFile', that.imgList[0].file)

      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      uploadPadSerialNumber(formData).then(res => {
        if (res.code === '000000') {

          that.tips = res.msg ? res.msg : ''
          loading.close()
          setTimeout(() => {
            that.$message({
              message: '导入成功',
              type: 'success'
            })
            that.fileUploadFlag = false
            that.$emit('refresh')
            that.imgList = []
            that.size = 0
          }, 200)
        }
      }).catch((error) => {

        that.size = 0
        that.imgList = []
        that.tips = error.msg ? error.msg : ''
        setTimeout(() => {
          loading.close()
        }, 500)
      })
    },
    cancelSubmit() {
      this.size = 0
      this.imgList = []
      this.fileUploadFlag = false
      this.$emit('refresh')
    }
  }
}
</script>
<style scoped lang="scss">
  .course-detail{
    position: relative;
  }
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 130px;
    color: #999;
  }

  .upload_warp_left img {
    margin-top: 32px;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
  }

  .upload_warp {
    margin: 14px;
    height: 130px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
  .progress-pop{
    position: absolute;
    right: 10px;
    bottom: 20%;
    width: 500px;
    height: 100px;
    border-radius: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  .progress-title{
    padding-bottom: 8px;
  }
  .warning{
    color: red;
  }
  .question-tips{
    display: flex;
    justify-content:flex-end;
    margin-bottom: 20px;
    a{
      color: #1890ff;
      font-size:14px;
    }
  }
  .basis-info{
      display: flex;
      justify-content: center;
      margin-top: 20px;
  }
</style>
