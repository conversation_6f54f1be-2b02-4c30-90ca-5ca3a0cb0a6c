<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      width="50%"
      :visible.sync="dialogSchool"
    >
    <template #title>
        <div class="dialog-title">
          <div class="title">选择校区</div>
          <div class="dialog-subtitle">以下为校区签约地址和实际经营地址，请慎重选择！</div>
        </div>
      </template>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-table
            v-loading="schoolListLoading"
            :data="schoolList"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column type="index" label="#" align="center" width="40" />
            <el-table-column label="校区名称" prop="schoolName" width="150" show-overflow-tooltip />
            <el-table-column label="校区编号" prop="schoolCode" width="80" />
            <el-table-column label="校区地址" show-overflow-tooltip min-width="300">
              <template slot-scope="{row}">
                <div>
                  <span v-if="row.provinceName">{{ row.provinceName }}-</span>
                  <span v-if="row.cityName">{{ row.cityName }}-</span>
                  <span v-if="row.areaName">{{ row.areaName }}-</span>
                  <span v-if="row.countyName">{{ row.countyName }}-</span>
                  <span v-if="row.address">{{ row.address }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="handleAdd(row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-row>
          <el-col :span="24">
            <!--            <router-link :to="'/customer/list/'" class="link-type">-->
            <div class="add-customer link-type" @click="createSchools"><i class="el-icon-plus" style="font-size: 12px" />&nbsp;新增校区</div>
            <!--            </router-link>-->
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <create-new-school ref="createSchoolDialog" :type="schoolOption" :customer-id="customerId" :title="createSchoolTitle" @success="getLists" />
  </div>
</template>

<script>
import { getCustomerSchoolList } from '@/api/customer'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import CreateNewSchool from '../../../customer/componets/createSchool'
export default {
  name: 'ChooseSchool',
  directives: { elDragDialog },
  components: { CreateNewSchool },
  props: {
  },
  data() {
    return {
      customerId: '',
      schoolOption: '',
      createSchoolTitle: '',
      dialogSchool: false,
      schoolListLoading: false,
      schoolList: []
    }
  },
  methods: {

    /**
     * 创建校区弹窗
     * */
    createSchools() {
      this.schoolOption = 'create'
      this.createSchoolTitle = '添加校区'
      this.$refs.createSchoolDialog.openDialog()
    },
    // /**
    //  * 获取我的校区
    //  * @param id
    //  */
    // getSchoolList(id) {
    //   const params = { clueId: id || this.currentCustomerId }
    //   this.schoolListLoading = true
    //   getCustomerSchoolList(params).then(res => {
    //     this.schoolList = res.data
    //     this.schoolListLoading = false
    //   })
    // },
    /**
     * 获取校区列表
     * */
    getLists(obj) {
      const that = this
      if (obj) {
        that.customerId = obj.clueId + ''
      }
      getCustomerSchoolList(obj || { clueId: that.customerId }).then(res => {
        that.schoolList = res.data
        that.dialogSchool = true
      })
    },
    /**
     * 关联客户
     */
    handleAdd(row) {
      this.$emit('chooseSuccess', row)
      this.dialogSchool = false
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
  }

  /deep/ .el-input--medium .el-input__inner {
    border-radius: 18px;
  }

  .add-customer {
    border-radius: 18px;
    height: 36px;
    background-color: #F8F8F8;
    color: #539FFF;
    font-size: 15px;
    line-height: 36px;
    text-align: center;
    font-weight: 500;
  }
  .dialog-title {
    display: flex;
    .title {
      line-height: 24px;
      font-size: 18px;
      color: #303133
    }
    .dialog-subtitle {
      background-color: #FEF3EE;
      font-size: 14px;
      padding: 5px 10px;
      margin: 0 auto;
      color: #E67B42;
    }
  }
</style>
