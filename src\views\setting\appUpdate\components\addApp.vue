<template>
  <el-dialog :visible.sync="appPop" :title="appTitle" :close-on-click-modal="!appPop" width="60%" @close="cancelClass">
    <div class="assing-info">
      <el-form ref="appForm" :model="listQuery" :rules="rules" label-width="100px">
        <el-form-item label="省份" prop="provinceId">
          <el-select v-model="listQuery.provinceId" placeholder="请选择省份" filterable clearable class="filter-item" :disabled="isEdit">
            <el-option value="">全部</el-option>
            <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户端" prop="clientCode">
          <el-select v-model="listQuery.clientCode" placeholder="请选择客户端" filterable clearable class="filter-item" :disabled="isEdit">
            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="listQuery.version" placeholder="请输入版本号" maxlength="30" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="更新类型">
          <el-radio-group v-model="listQuery.doing" :disabled="isEdit">
            <el-radio :label="1">非强制性</el-radio>
            <el-radio :label="2">强制性</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="更新链接" prop="updateUrl">
          <el-input v-model="listQuery.updateUrl" placeholder="请输入更新链接" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="更新内容" prop="updateMessage">
          <el-input v-model="listQuery.updateMessage" type="textarea" placeholder="请输入更新内容" maxlength="255" :disabled="isEdit" show-word-limit />
        </el-form-item>
        <el-form-item label="升级状态">
          <el-radio-group v-model="listQuery.status" :disabled="isEdit">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="99">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="appPop=false,cancelClass()">取消</el-button>
      <!--新增-->
      <el-button v-if="flags===1" type="primary" size="mini" :disabled="enableFlagsCreate" @click="custormClass">确定</el-button>
      <!--修改-->
      <el-button v-if="flags===0" type="primary" size="mini" :disabled="enableFlagsUpdate" @click="editApp">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getArea } from '@/api/common'
import { clientCode } from '@/api/classType'
import { addAppList, getAppDetail, editAppList } from '@/api/system-setting'
export default {
  name: 'AddApp',
  data() {
    return {
      appPop: false,
      appTitle: '',
      listQuery: {},
      rules: {
        provinceId: { required: true, trigger: 'blur', message: '请选择省份' },
        clientCode: { required: true, trigger: 'blur', message: '请选择客户端' },
        version: { required: true, trigger: 'blur', message: '请输入版本号' },
        doing: { required: true, trigger: 'blur', message: '请选择更新类型' },
        updateUrl: { required: true, trigger: 'blur', message: '请输入更新链接' },
        updateMessage: { required: true, trigger: 'blur', message: '请输入更新内容' }
      },
      allClassType: [],
      sheng: [],
      clientCode: [],
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false,
      enableFlagsUpdate: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getsheng()
      this.getCode()
    })
  },
  methods: {
    // 获取省
    getsheng() {
      const _this = this
      getArea(0).then(res => {
        _this.sheng = res.data // 将获取的数据赋值
      }).catch(err => {

      })
    },
    getCode() { // 1产品线 2客户端
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 2) || []
      })
    },
    cancelClass() {
      this.listQuery = {}
      if (this.$refs.appForm) {
        this.$refs.appForm.clearValidate()
      }
    },
    getAppDetail(ids) { // 获取app的详情
      const param = {
        id: ids
      }
      getAppDetail(param).then(res => {
        this.listQuery = res.data || {}
      })
    },
    custormClass() {
      this.enableFlagsCreate = true
      this.$refs.appForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery)
          addAppList(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.appPop = false
              this.listQuery = {}
              this.enableFlagsCreate = false
              this.$emit('updateApp')
            }
          }).catch(() => {
            this.enableFlagsCreate = false

          })
        } else {
          this.enableFlagsCreate = false
          return false
        }
      })
    },
    editApp() {
      this.enableFlagsUpdate = true
      this.$refs.appForm.validate((valid) => {
        if (valid) {
          if (this.listQuery.id) {
            const params = Object.assign({}, this.listQuery, { id: this.listQuery.id })
            editAppList(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.appPop = false
                this.listQuery = {}
                this.enableFlagsUpdate = false
                this.$emit('updateApp')
              }
            }).catch(() => {
              this.enableFlagsUpdate = false

            })
          }
        } else {
          this.enableFlagsUpdate = false
          return false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
