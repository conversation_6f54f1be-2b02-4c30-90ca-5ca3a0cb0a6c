<template>
  <el-dialog :visible.sync="questionIshow" :title="addQuestionTitle" :close-on-click-modal="!questionIshow" width="60%" @close="changeInit">
    <div class="filter-container">
      <el-input
        v-model="listQuery.questionCode"
        placeholder="题号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.outlineId" placeholder="大纲" filterable clearable class="filter-item" style="width: 200px;" @change="getKeynote">
        <el-option v-for="item in outline" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.keynoteId" placeholder="考点" filterable clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in keynote" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <div v-for="(item,index) in questionList" id="questions-pop" :key="index" class="questions-list">
      <div class="questions-list-title">
        <div>
          <span class="questions-title-left">
            <em>题号:{{ item.questionCode }}</em>
          </span>
          <el-tag type="warning">选择题-{{ item.viewType===1?'批量导入':'普通新增' }}</el-tag>
        </div>
        <div class="questions-btns">
          <el-button v-if="item.include!==1" type="primary" size="mini" @click="getQuestionItem(item)">选择</el-button>
          <el-button v-if="item.include===1" type="success" size="mini">已包含</el-button>
        </div>
      </div>
      <div class="questions-knowledge">
        <h2 v-if="item.question&&item.viewType!==1">{{ item.question }}</h2>
        <h2 v-if="item.question&&item.viewType===1" v-html="item.question" />
        <img v-if="item.questionImage&&item.questionImage!==null" :src="item.questionImage" style="width:100%">
        <div class="questions-knowledge-list"><i>知识点:</i><el-tag v-for="(itemKnowledge,i) in item.keynoteList" :key="i" type="info" size="mini">{{ itemKnowledge }}</el-tag></div>
      </div>
    </div>
    <p v-show="questionList.length===0" class="no-data">暂无数据</p>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getQuestionList, getQuestionOutline, getQuestionKeynote, addQuestion } from '@/api/admissions'
export default {
  name: 'QuestionPop',
  components: {
    Pagination
  },
  data() {
    return {
      questionIshow: false,
      addQuestionTitle: '添加习题',
      enableList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 20
      },
      questionList: [],
      paperId: null,
      outline: [],
      keynote: [],
      subjectId: null,
      clientCode: null
    }
  },
  watch: {
    outline: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.outlineId = ''
          this.listQuery.keynoteId = ''
        }
      },
      deep: true
    },
    keynote: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.keynoteId = ''
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList(this.paperId, this.subjectId, this.clientCode)
    })
  },
  methods: {
    async getList(paperId, subjectId, clientCode) {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery, { paperId: this.paperId, subjectId: this.subjectId, clientCode: this.clientCode })
      getQuestionList(params).then(res => {

        if (res.code === '000000') {
          this.questionList = res.data.records || []
          this.total = res.data.total
          this.listLoading = false
          this.$nextTick(() => {
            if (this.commonsVariable.isMathjaxConfig) {
              this.commonsVariable.initMathjaxConfig()
            }
            this.commonsVariable.MathQueue('questions-pop')
          })
        }
      }).catch(() => {

      })
    },
    getQuestionOutline(subjectId) { // 大纲
      getQuestionOutline(subjectId).then(res => {
        if (res.code === '000000') {
          this.outline = res.data || []
        }
      }).catch(() => {

      })
    },
    getKeynote(val) {
      if (val) {
        this.getQuestionKeynote(val)
      } else {
        this.keynote = []
      }
    },
    getQuestionKeynote(outlineId) { // 考点
      getQuestionKeynote(outlineId).then(res => {
        if (res.code === '000000') {
          this.keynote = res.data || []
        }
      }).catch(() => {

      })
    },
    changeInit() {
      this.questionIshow = false
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList(this.paperId, this.subjectId, this.clientCode)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList(this.paperId, this.subjectId, this.clientCode)
    },
    getQuestionItem(row) {
      this.$confirm('确定选择此题?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        addQuestion(this.paperId, row.id).then(res => {
          if (res.code === '000000') {
            this.$emit('getQuestions', this.paperId)
            this.getList(this.paperId, this.subjectId, this.clientCode)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
