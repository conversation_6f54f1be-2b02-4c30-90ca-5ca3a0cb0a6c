import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 60 * 60 * 1000, // 请求超时时间
  retry: 2, // 请求超时时再次请求的次数
  retryInterval: 1000
})

// request interceptor
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['Authorization'] = getToken()
    }
    return config
  },
  error => {
    //
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * 如果您想要获取诸如头或状态之类的http信息
   * 请返回response=>response
  */

  /**
   * 通过自定义代码确定请求状态
   * 这里只是一个例子
   * 您还可以通过HTTP状态代码判断状态
   */
  response => {
    const res = response.data
    if (response.config.responseType === 'blob' && res.type === 'text/xml') {
      return response
    } else if (response.config.responseType === 'blob' && res.type === 'application/json') {
      return Promise.reject(response)
    }
    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== '000000') {
      Message({
        message: res.msg || '网络错误',
        type: 'error',
        duration: 5 * 1000
      })
      // return Promise.reject(new Error(res.msg || 'Error'))
      return Promise.reject(res)
    } else {
      return res
    }
  },
  error => {
    //
    const msg = ((error.message).indexOf('timeout') > -1) ? '网络连接超时，请稍后重试' : error.message
    Message({
      message: msg,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
