<template>
  <el-select v-model="tmpId" filterable clearable placeholder="请选择年份" :disabled="disabled">
    <el-option
            v-for="item in optionList"
            :key="item.id"
            filterable
            :label="item.name"
            :value="item.id">
    </el-option>
  </el-select>
</template>
<script>
export default {
  name: 'YearSelect',
  data: function () {
    return {
      optionList:[  ]
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId) : ''
      },
      set(val) {
        this.handleChange(val)
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getSubjectType(row) {
      return row.name
    },
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      //往optionList中加入最近20年的选项
      let now = new Date().getFullYear()
      for (let i = 0; i < 20; i++) {
        this.optionList.push({
          id: now - i,
          name: (now - i) +'年'
        })
      }
    },
    changeParent(val) {
      this.optionList = []
      this.total = 0
      this.tmpId = ''
      this.getList()
    }
  }
}
</script>
<style scoped>
</style>
