<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.version"
        placeholder="版本号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.provinceId" placeholder="省" filterable clearable style="width: 150px;">
        <el-option value="">全部</el-option>
        <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="客户端" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.doing" placeholder="更新类型" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in updateTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="升级状态" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.updateTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="升级时间"
        style="width: 150px"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:appUpdate:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="升级编号" show-overflow-tooltip prop="id" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </el-table-column>
      <af-table-column label="省份" prop="provinceName" show-overflow-tooltip />
      <af-table-column label="版本号" prop="version" show-overflow-tooltip />
      <af-table-column label="客户端" prop="clientName" show-overflow-tooltip />
      <af-table-column label="更新类型" prop="doing" :formatter="getDoing" />
      <af-table-column label="升级状态" prop="status" :formatter="getStatus" />
      <af-table-column label="更新链接" prop="updateUrl" />
      <af-table-column label="更新内容" prop="updateMessage" />
      <af-table-column label="升级时间" prop="updateTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:appUpdate:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-if="row.productTypeName==='流量包'" v-permission="['setting:appUpdate:opera']" type="primary" size="mini" @click="handleSet(row)">停用</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <add-app ref="appUpdate" @updateApp="getList" />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import AddApp from './components/addApp'
import { getArea } from '@/api/common'
import { clientCode } from '@/api/classType'
import { getAppList } from '@/api/system-setting'
import { enableList, updateTypes, converseEnToCn } from '@/utils/field-conver'
export default {
  name: 'AppUpdate',
  components: {
    Pagination,
    AddApp
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      sheng: [],
      enableList: enableList,
      updateTypes: updateTypes
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
      this.getsheng()
    })
  },
  methods: {
    // 获取省
    getsheng() {
      const _this = this
      getArea(0).then(res => {
        _this.sheng = res.data // 将获取的数据赋值
      }).catch(err => {

      })
    },
    getCode() { // 1产品线 2客户端
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 2) || []
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign({}, {
        pageIndex: that.listQuery.pageIndex,
        pageSize: that.listQuery.pageSize
      }, this.listQuery)

      await getAppList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleCreate() {
      this.$refs.appUpdate.appPop = true
      this.$refs.appUpdate.appTitle = '新增省份'
      this.$refs.appUpdate.isEdit = false
      this.$refs.appUpdate.flags = 1
    },
    handleUpdate(row) {
      this.$refs.appUpdate.appPop = true
      this.$refs.appUpdate.appTitle = '修改App升级'
      this.$refs.appUpdate.getAppDetail(row.id)
      this.$refs.appUpdate.isEdit = false
      this.$refs.appUpdate.flags = 0
    },
    getDoing(row) {
      return converseEnToCn(this.updateTypes, row.doing)
    },
    getStatus(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    handleSet(row) {

    },
    getDetail(row) { // 获取列表详情getAppDetail
      this.$refs.appUpdate.appPop = true
      this.$refs.appUpdate.appTitle = 'App详情'
      this.$refs.appUpdate.getAppDetail(row.id)
      this.$refs.appUpdate.isEdit = true
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
