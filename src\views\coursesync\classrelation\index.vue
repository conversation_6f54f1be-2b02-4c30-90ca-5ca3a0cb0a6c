<template>
  <div class="app-container class-relation">
    <div class="filter-container">
      <div class="wrap">
        <h2>{{ seriesName }}- {{ mgrClassTypeName }}</h2>
        <el-button v-waves class="filter-item" type="primary" size="mini" @click="addClassReltaion">新增同步班型</el-button>
      </div>
      <span>上次更新时间:{{ lastUpdateTime }}</span>
    </div>
    <el-table ref="assignTab" v-loading="listLoading" :data="records" border fit stripe highlight-current-row style="width: 100%;" class="table">
      <el-table-column label="#" type="index" width="40" align="center" />
      <el-table-column label="产品线" prop="clientCodeName" align="center" />
      <el-table-column label="同步系列" prop="classSeriesName" align="center" />
      <el-table-column label="同步班型" prop="classTypeName" align="center" />
      <el-table-column label="课程同步数量" prop="syncNums,mgrCourseNums" align="center">
        <template slot-scope="{row}">
          {{ row.syncNums }} / {{ row.mgrCourseNums }}
        </template>
      </el-table-column>
      <el-table-column label="最后操作人" prop="lastOperator" align="center" />
      <el-table-column label="最后同步时间" prop="lastSyncTime" align="center" />
      <el-table-column label="操作" prop="" align="center">
        <template slot-scope="scope">
          <div class="sync-opera" @click="syncCourse(scope.row)">
            <em>课程同步</em>
            <i v-show="scope.row.isShow" class="circle" />
          </div>
          <el-button type="primary" size="mini" @click="deleteClassReltaion(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParam.pageIndex" :limit.sync="queryParam.pageSize" @pagination="getClassTypeRelationPage" />

    <!--  新增弹框-->
    <class-relation-pop ref="relations" @addRelationList="getClassTypeRelationPage" />
  </div>
</template>

<script>

import Pagination from '@/components/Pagination'
import { pageClassTypeRelation, deleteClassTypeRelation } from '@/api/courseSyncApi'
import classRelationPop from '@/views/coursesync/classrelation/classRelationPop'

export default {
  name: 'ClassTypeRelation',
  components: {
    Pagination,
    classRelationPop
  },
  data() {
    return {
      total: 0,
      records: [],
      listLoading: true,
      queryParam: {
        pageIndex: 1,
        pageSize: 10
      },
      mgrClassTypeId: '',
      mgrClassTypeName: '',
      seriesName: '',
      clientCodeName: '',
      lastUpdateTime: '',
      syncClassObj: JSON.parse(localStorage.getItem('syncClass'))
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      const syncObject = JSON.parse(localStorage.getItem('syncObject'))
      this.mgrClassTypeId = syncObject.mgrClassTypeId
      this.mgrClassTypeName = syncObject.mgrClassTypeName
      this.seriesName = syncObject.seriesName
      this.clientCodeName = syncObject.clientCodeName
      this.lastUpdateTime = syncObject.lastUpdateTime
      this.mgrClassTypeId = syncObject.mgrClassTypeId
      this.getClassTypeRelationPage()
    })
  },
  methods: {
    async getClassTypeRelationPage() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.queryParam, {
        mgrClassTypeId: this.mgrClassTypeId
      })

      await pageClassTypeRelation(params).then(rep => {
        const arr = []
        rep.data.records.forEach(item => {
          let objs = {}
          objs = Object.assign({}, item, {
            isShow: !!((item.courseLastUpdateTime && item.lastSyncTime) && ((new Date(Date.parse(item.courseLastUpdateTime.replace('-', '/'))) > (new Date(Date.parse(item.lastSyncTime.replace('-', '/')))))))
          })
          arr.push(objs)
        })
        that.records = arr
        that.total = rep.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },

    addClassReltaion() {
      this.$refs.relations.courseMgrClassTypeId = this.mgrClassTypeId
      const classLeft = {
        seriesName: this.seriesName,
        clientCodeName: this.clientCodeName,
        mgrClassTypeName: this.mgrClassTypeName
      }
      this.$refs.relations.classLeft = classLeft
      this.$refs.relations.relationPop = true
    },

    deleteClassReltaion(row) {
      if (row.syncNums > 0) {
        this.$message({
          type: 'warning',
          message: '该关联班型下已同步课程，不允许删除!'
        })
        return false
      }
      this.$confirm('是否确认删除该关联班型？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteClassTypeRelation(row.classTypeRelationId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          // 刷新列表
          this.getClassTypeRelationPage()
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    syncCourse(row) {
      localStorage.setItem('classTypeRelationId', row.classTypeRelationId)
      this.$router.push({
        name: 'CourseSyncPage',
        params: {
          classTypeRelationId: row.classTypeRelationId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.wrap{
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*margin-bottom: 10px;*/
}
.class-relation>>> .el-table .cell{
  display: flex;
}
.sync-opera{
    position: relative;
    color: #fff;
    background-color: #1890ff;
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 10px;
    .circle{
      position: absolute;
      top: 0px;
      right: 1px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: red;
    }
}
.codes{
  font-weight: bold;
  color: #0a76a4;
}
/deep/.table {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%
}
/deep/.table >>> .el-table__header-wrapper {
  height: 90px;
}
/deep/.table >>> .el-table__body-wrapper {
  height: calc(100% - 90px) !important;
}
</style>
