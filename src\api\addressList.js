import request from '@/utils/request'
/**
 * 获取区域列表
 * */
export function getAddressList(data) {
  return request({
    url: 'stip/regions/page',
    method: 'POST',
    data: data
  })
}
/**
 * 新增区域
 * */
export function addRegion(params) {
  return request({
    url: 'stip/regions',
    method: 'POST',
    data: params
  })
}
/**
 * 修改区域
 * */
export function editRegion(params) {
  return request({
    url: 'stip/regions',
    method: 'PUT',
    data: params
  })
}

