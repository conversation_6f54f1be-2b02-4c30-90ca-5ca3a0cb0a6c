import request from '@/utils/request'

/**
 * crm-获取试题详情
 * @param courseId
 */
export function getHomeworkDetailByCourseId(courseId) {
  return request({
    url: 'homework/getHomeworkDetailByCourseId?courseId=' + courseId,
    method: 'get'
  })
}
/**
 * crm-获取试题详情状态和模式更新
 * @param data
 */
export function updateHomework(data) {
  return request({
    url: 'homework/updateHomework',
    method: 'post',
    data
  })
}

/**
 * crm-添加试题详情
 * @param data
 */
export function addHomework(data) {
  return request({
    url: '/homework/addHomework',
    method: 'post',
    data: data
  })
}

/**
 * crm-获取答案Pdf列表
 * @param courseId
 */
export function queryPdfList(courseId, testType) {
  return request({
    url: 'homework/queryPdfList?homeworkId=' + courseId + '&testType=' + testType,
    method: 'get'
  })
}

/**
 * crm-获取学科网科目列表
 */
export function getSubjectFromXK(param) {
  return request({
    url: 'xkw/getSubject',
    method: 'get',
    params: param
  })
}

/**
 * crm-获取学科网教材版本
 */
export function getTextbookVersionsFromXK(param) {
  return request({
    url: 'xkw/getTextbookVersions',
    method: 'get',
    params: param
  })
}

/**
 * crm-获取学科网教材列表
 */
export function getTextbookFromXK(param) {
  return request({
    url: 'xkw/getTextbooks',
    method: 'get',
    params: param
  })
}

/**
 * crm-获取学科网章节
 */
export function getChapterFromXK(param) {
  return request({
    url: 'xkw/getCatalog',
    method: 'get',
    params: param
  })
}

/**
 * crm-获取年级列表
 */
export function getGradesFromXK(param) {
  return request({
    url: 'xkw/getGrades',
    method: 'get',
    params: param
  })
}

/**
 * crm-获取学科网试卷类型
 */
export function getPaperTypeFromXK() {
  return request({
    url: '/xkw/getPaperType',
    method: 'get'
  })
}

/**
 * crm-获取学科网科目的知识点
 */
export function getPointFromXKBySubjectId(subjectId) {
  return request({
    url: '/xkw/getKnowledgePoint?subjectId=' + subjectId,
    method: 'get'
  })
}

/**
 * crm-获取学科网试题
 */
export function getQuestionFromXK(data) {
  return request({
    url: '/xkw/getQuestion',
    data: data,
    method: 'post'
  })
}

/**
 * crm-获取学科网试题
 */
export function saveQuestionFromXK(data) {
  return request({
    url: '/xkw/saveQuestion',
    data: data,
    method: 'post'
  })
}

/**
 * crm-上传答案Pdf
 * @param data
 */
export function sumbitPdf(data) {
  return request({
    url: '/homework/sumbitPdf',
    method: 'post',
    data: data
  })
}
/**
 * crm-更新答案Pdf
 * @param data
 */
export function updatePdf(data) {
  return request({
    url: '/homework/updatePdf',
    method: 'post',
    data: data
  })
}
/**
 * crm-删除答案Pdf
 * @param data
 */
export function deletePdf(data) {
  return request({
    url: '/homework/deletePdf',
    method: 'post',
    data: data
  })
}

/**
 * crm-获取题目列表
 * @param homeworkId
 */
export function queryQuestionList(homeworkId) {
  return request({
    url: '/homework/queryQuestionList?homeworkId=' + homeworkId,
    method: 'get'
  })
}
/**
 * crm-获取题目详情
 * @param homeworkId
 */
export function queryQuestionById(questionId) {
  return request({
    url: '/homework/queryQuestionById?questionId=' + questionId,
    method: 'get'
  })
}

/**
 * crm-新增题目
 * @param data
 */
export function addQuestion(data) {
  return request({
    url: '/homework/addQuestion',
    method: 'post',
    data: data
  })
}

/**
 * crm-更新题目
 * @param data
 */
export function updateQuestion(data) {
  return request({
    url: '/homework/updateQuestion',
    method: 'post',
    data: data
  })
}

/**
 * crm-删除题目
 * @param data
 */
export function deleteQuestion(data) {
  return request({
    url: '/homework/deleteQuestion',
    method: 'post',
    data: data
  })
}
