<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.deliveryCode" placeholder="发货单编号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.shipNo" placeholder="快递单号" clearable class="filter-item" style="width: 200px;" />
      <el-input v-model="listQuery.orderCode" placeholder="订单编号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.clueCode" placeholder="客户编号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.searchField" placeholder="合伙人/电话/校区名称" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in projectList"
          :key="item.id"
          :label="item.projectName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="发货单状态" filterable class="filter-item" style="width: 160px;" clearable>
        <el-option
          v-for="item in shipStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.shipCode" placeholder="物流公司" filterable clearable class="filter-item" style="width: 160px;">
        <el-option
          v-for="item in expressList"
          :key="item.expressCode"
          :label="item.expressName"
          :value="item.expressCode"
        />
      </el-select>
      <area-picker :area-list="areaList" :level="'3'" area-style="width: 300px" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="deliveryRangeTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="发货开始日期"
        end-placeholder="发货结束日期"
        unlink-panels
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="发货单编号" prop="deliveryCode" show-overflow-tooltip />
      <af-table-column label="快递单号" prop="shipNo" />
      <af-table-column label="订单编号" prop="orderCode" show-overflow-tooltip />
      <af-table-column label="客户编号" prop="clueCode" show-overflow-tooltip />
      <af-table-column label="客户名称" prop="customer" />
      <af-table-column label="签约区域" width="180" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}{{ row.address }}
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="institution" />
      <af-table-column label="加盟项目" prop="projectName" />
      <el-table-column label="发货类型" prop="shipType" :formatter="getShipType" min-width="100" />
      <af-table-column label="发货单状态" prop="status" :formatter="changeShipStatus" />
      <af-table-column label="物流公司" prop="shipName" />
      <af-table-column label="发货时间" prop="deliveryTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="300" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['order:logistic:update']" type="primary" size="mini" @click="updateLogistic(row, 'view')">
            查看
          </el-button>
          <el-button v-if="row.status==1||row.status==3" v-permission="['order:logistic:update']" type="primary" size="mini" @click="updateLogistic(row)">
            修改
          </el-button>
          <el-button v-if="row.status==1" v-permission="['order:logistic:deliver']" type="primary" size="mini" @click="confirmLogistic(row)">
            确认发货
          </el-button>
          <el-button v-if="row.status==2" v-permission="['order:logistic:withdraw']" type="primary" size="mini" @click="withdraw(row,0)">
            撤回
          </el-button>
          <el-button v-if="row.status==2" v-permission="['order:logistic:synchronization']" type="primary" size="mini" @click="withdraw(row,1)">
            同步播客
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { getAllProject } from '@/api/common'
import { deliveryList, cofirmDelivery, getExpress, withdraw, synchronization } from '@/api/handover'
import Pagination from '@/components/Pagination'
import { converseEnToCn, getShipType, getShipStatus } from '@/utils/field-conver'
import AreaPicker from '@/components/area-picker'

export default {
  name: 'Logistics',
  components: { Pagination, AreaPicker },
  directives: {},
  data() {
    return {
      listLoading: false,
      shipStatus: getShipStatus,
      list: [],
      expressList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      projectList: [],
      deliveryTime: '',
      flags: -1,
      deliveryRangeTime: []
    }
  },
  created() {
    this.listLoading = false
    this.getList()
    this.getProject()
    this.getExpressList()
  },
  methods: {
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    dateFormat(row, column, cellValue, index) {
      const daterc = row[column.property]
      if (daterc != null) {
        const dateMat = new Date(daterc)
        const year = dateMat.getFullYear()
        const month = dateMat.getMonth() + 1
        const day = dateMat.getDate()
        const timeFormat = year + '-' + month + '-' + day
        return timeFormat
      }
    },
    updateLogistic(row, type) {
      this.$router.push({
        path: '/handover/logisticCreate',
        query: {
          orderId: row.id,
          type: type
        }
      })
    },
    confirmLogistic(row) {
      this.$confirm('是否确认发货?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = row.id
        cofirmDelivery(params).then(res => {
          if (res.code === '000000') {
            this.getList()
            this.$message({
              message: '发货成功',
              type: 'success'
            })
          }
        })
      }).catch(() => {
      })
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 重置
     * */
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.deliveryRangeTime = []
      this.getList()
    },
    getList() {
      const that = this
      const params = Object.assign(that.listQuery, that.areaList, { deliveryStartTime: that.deliveryRangeTime.length > 0 ? that.deliveryRangeTime[0] : '', deliveryEndTime: that.deliveryRangeTime.length > 0 && that.deliveryRangeTime[1] ? that.deliveryRangeTime[1] : '' })
      deliveryList(params).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    /**
     * 获取物流公司列表
     * @param row
     * @returns {*}
     */
    getExpressList() {
      getExpress().then(res => {
        if (res.code === '000000') {
          this.expressList = res.data
        }
      })
    },
    getShipType(row) {
      return converseEnToCn(getShipType, row.shipType)
    },
    changeShipStatus(row) {
      return converseEnToCn(getShipStatus, row.status)
    },
    withdraw(row, flags) {
      this.flags = flags
      const params = row.id
      if (flags === 0) { // 撤回
        this.$confirm('确定要撤回?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          withdraw(params).then(res => {
            if (res.code === '000000') {
              this.getList()
              this.$message({
                message: '撤回成功',
                type: 'success'
              })
            }
          }).catch(res => {

          })
        }).catch(() => {
        })
      } else if (flags === 1) { // 同步至播客系统
        this.$confirm('确定要同步至播客系统?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          synchronization(params).then(res => {
            if (res.code === '000000') {
              this.getList()
              this.$message({
                message: '同步成功',
                type: 'success'
              })
            }
          }).catch(res => {

          })
        }).catch(() => {
        })
      }
    }
  }
}
</script>

<style scoped>
</style>
