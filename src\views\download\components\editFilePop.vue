<template>
  <el-dialog :visible.sync="showEditFile" :title="editFile" :close-on-click-modal="!showEditFile" width="60%" @close="changeInit">
    <el-form label-width="120px" :model="fileForm">
      <el-row :gutter="10">
        <el-col :span="8" v-if="cover">
          <div>
            <div>所属目录</div>
            <el-tree
                    ref="tree"
                    :props="props"
                    :data="tree"
                    accordion
                    node-key="id"
                    :highlight-current="true"
                    :default-expanded-keys="treeExpandData"
                    :default-checked-keys="[fileForm.menuId]"
                    :current-node-key="fileForm.menuId"
                    show-checkbox
                    @check-change="handleCheckChange">
            </el-tree>
          </div>
        </el-col>
        <el-col :span="16">
          <el-form-item label="标题">
            <el-input v-model="fileForm.name" />
          </el-form-item>
          <!--封面-->
          <el-form-item label="文件" v-if="cover">
            <ObsUploader :single="true"
                         :on-success="setCover"
                         :on-remove="removeCover"
                         :limit="1"
                         business-path="santao_stip/crm/download/"
                         :file-list.sync="coverObj"
                         accept='.jpg, .jpeg, .png,.gif'
            />
          </el-form-item>
          <el-form-item label="排序">
            <el-input v-model="fileForm.sort" />
          </el-form-item>
          <el-form-item label="更新说明">
            <el-input v-model="fileForm.remark" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="confirmFile">确定</el-button>
            <el-button type="default" size="mini" @click="changeInit">取消</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
import { editFileList } from '@/api/download'
import ObsUploader from '@/components/upload/ObsUploader.vue'
import { getFileUrlArrayByObj } from '@/utils'


export default {
  name: 'EditFile',
  data() {
    return {
      editFile: '编辑文件',
      showEditFile: false,
      treeExpandData: [],   //通过接口获取默认展开节点id
      coverObj: {},
      fileForm: {},
      props: {
        label: 'menuName',
        children: 'children'
      }
    }
  },
  components: {
    ObsUploader
  },
  props: {
    cover: {
      type: Boolean,
      default: false
    },
    tree: {
      type: Array,
      default() {
        return []
      }
    },
    menuId: {
      type: Number,
      default: 0
    },
  },
  computed: {
    treeData: {
      get() {
        this.getAndExpandAll(this.menuId)
        return this.tree
      },
    }
  },
  watch: {
    showEditFile(val) {
      if (val) {
        this.coverObj = getFileUrlArrayByObj(this.fileForm, 'name', 'downloadLink')
      }
    },
  },
  methods: {
    // 通过节点的key（这里使用的是数据中的code属性，node-key="code"）获取并高亮显示指定节点，并展开其所有父级节点
    getAndExpandAll() {
      let nodeDataId = this.menuId || this.fileForm.menuId
      if (nodeDataId) {
        this.treeExpandData = []
        this.$nextTick(() => { // 等待树组件渲染完成再执行相关操作
          // 获取节点
          const node = this.$refs.tree.getNode(nodeDataId)
          if (node) {
            // 获取其所有父级节点
            this.getParentAll(node)
            if (this.nodeParentAll.length > 0) {
              // 将获取到的所有父级节点进行展开
              for (var i = 0, n = this.nodeParentAll.length; i < n; i++) {
                this.treeExpandData.push(this.nodeParentAll[i].data.id)
                // this.$refs.tree.store.nodesMap[this.nodeParentAll[i].data.code].expanded = true
              }
            }
            else {
              this.treeExpandData = [nodeDataId]
            }
            // 将节点高亮显示
            // this.$refs.tree.setCurrentKey(nodeDataId)
          }
        })
      }
    },
    // 获取所有父级节点
    getParentAll(node) {
      if (node) {
        this.nodeParentAll = []
        // 节点的第一个父级
        var parentNode = node.parent
        // level为节点的层级 level=1 为顶级节点
        for (var j = 0, lv = node.level; j < lv; j++) {
          if (parentNode.level > 0) {
            // 将所有父级节点放入集合中
            this.nodeParentAll.push(parentNode)
          }
          // 继续获取父级节点的父级节点
          parentNode = parentNode.parent
        }

        if (this.nodeParentAll.length > 1) {
          // 如果集合长度>1 则将数组进行倒叙.reverse() 其是就是将所有节点按照 从 顶级节点 依次往下排
          this.nodeParentAll.reverse()
        }
      }
    },
    changeInit() {
      this.showEditFile = false
      this.fileForm = {}
    },
    removeCover() {
      this.fileForm.downloadLink = ''
      this.coverObj = []
    },
    setCover(data, file, list) {
      this.fileForm.downloadLink = data.url
    },
    handleCheckChange(data, checked, indeterminate) {
      if (checked) {
        this.$refs.tree.setCheckedKeys([data.id])
        this.fileForm.menuId = data.id
      }
    },
    confirmFile() {
      const parameEdit = {
        clientCode: this.fileForm.clientCode,
        dataId: this.fileForm.id,
        downloadLink: this.fileForm.downloadLink,
        dataName: this.fileForm.name,
        remark: this.fileForm.remark,
        menuId: this.fileForm.menuId,
        sort: this.fileForm.sort
      }
      editFileList(parameEdit).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '修改成功！',
            type: 'success'
          })
          this.showEditFile = false
          this.fileForm = {}
          this.$emit('refresh')
        }
      }).catch(() => {

      })
    }
  }

}
</script>
<style>
</style>
