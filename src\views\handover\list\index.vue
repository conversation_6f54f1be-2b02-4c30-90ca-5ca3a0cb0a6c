<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.orderCode" placeholder="订单编号" clearable class="filter-item" style="width: 250px;"
                @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.clueCode" placeholder="客户编号" clearable class="filter-item" style="width: 250px;"
                @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.searchField" placeholder="合伙人/手机号/校区名称" clearable class="filter-item"
                style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.institutionCode" placeholder="校区项目编号" clearable class="filter-item" style="width: 250px;"
                @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="订单状态" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in orderStatusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.businessType" placeholder="交接单类型" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in businessList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <!-- <el-select v-model="listQuery.deliver" placeholder="合伙人物流状态" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in deliveryStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select> -->
      <!-- <el-select
        v-model="listQuery.recDeliver"
        placeholder="推荐人物流状态"
        class="filter-item"
        style="width: 150px;"
        clearable
      >
        <el-option v-for="item in recDeliveryStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select> -->
      <el-select v-model="listQuery.areaSingle" placeholder="区域类型" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in areaSingleList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <!-- <el-select v-model="listQuery.openFlowDataPackage" placeholder="流量包状态" class="filter-item" style="width: 150px;"
                 clearable>
        <el-option v-for="item in packageLists" :key="item.id" :label="item.val" :value="item.id" />
      </el-select> -->
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="signTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="签约开始日期"
        end-placeholder="签约结束日期"
        unlink-panels
      />
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table v-loading="listLoading" :data="orderList" border fit stripe highlight-current-row>
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="订单编号">
        <template slot-scope="scope">
          <span v-if="scope.row.businessType===5" class="link-type" @click="toDetailHandover(scope.row)">{{
              scope.row.oldOrderCode
            }}</span>
          <span v-else class="link-type" @click="toDetailHandover(scope.row)">{{ scope.row.orderCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="客户编号" width="80">
        <template slot-scope="{ row }">
          <router-link :to="{ path: '/customer/detail/'+row.clueId, query: {title:'客户-'+row.customer}}"
                       class="link-type">
            <span>{{ row.clueCode }}</span>
          </router-link>
        </template>
      </af-table-column>
      <af-table-column label="客户名称" prop="customer" />
      <af-table-column label="校区项目编号" prop="institutionCode" />
      <af-table-column label="校区编号" prop="schoolCode" />
      <el-table-column label="校区名称" prop="schoolName" width="200">
        <template slot-scope="{row}">
          <el-button type="text" @click="toSchoolDetail(row)">{{ row.schoolName }}</el-button>
        </template>
      </el-table-column>
      <af-table-column label="签约区域" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}
        </template>
      </af-table-column>
      <af-table-column label="加盟项目" prop="projectName" />
      <!--      <el-table-column label="订单状态" prop="status" :formatter="getOrderStatus" width="150" />-->
      <el-table-column label="订单状态" prop="status" :formatter="getOrderStatus" width="150">
        <template slot-scope="scope">
          <OrderStatusTag :status="scope.row.status"></OrderStatusTag>
        </template>
      </el-table-column>
      <el-table-column label="交接单类型" prop="businessType" :formatter="getBusinessType" width="150" />
      <!--      getRecDeliver-->
      <!-- <af-table-column label="合伙人物流状态" prop="deliver" :formatter="getDeliver" />
      <af-table-column label="流量包状态" prop="openFlowDataPackage ">
        <template slot-scope="scope">
          <span v-if="scope.row.openFlowDataPackage===0">未开通</span>
          <span v-if="scope.row.openFlowDataPackage===1">已开通</span>
          <span v-if="scope.row.openFlowDataPackage===2">无需开通</span>
        </template>
      </af-table-column>
      <af-table-column label="推荐人物流状态" prop="recDeliver" :formatter="getRecDeliver" /> -->
      <af-table-column label="区域类型" prop="areaSingle" :formatter="setAreaSingle" />
      <af-table-column label="订单金额" prop="payAmount" show-overflow-tooltip />
      <af-table-column label="实收金额" prop="realAmount" show-overflow-tooltip />
      <af-table-column label="退款金额" prop="refundAmount" show-overflow-tooltip />
      <af-table-column label="完款日期">
        <template slot-scope="{row}">
          <span>{{ row.completionTime | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </af-table-column>
      <af-table-column label="签约周期">
        <template slot-scope="{row}">
          <span>{{ row.signStartTime | parseTime('{y}-{m}-{d}') }} 至 {{ row.signEndTime | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </af-table-column>
      <af-table-column label="推荐人类型" prop="channel" :formatter="getChannelType" />
      <af-table-column label="推荐人" prop="recName" />
      <af-table-column label="创建时间" prop="createTime" show-overflow-tooltip />
      <af-table-column label="生效时间" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span>{{ row.validTime || '--' }}</span>
        </template>
      </af-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="250" fixed="right">
        <template slot-scope="{row}">
          <!--          1: 待专员打款 2：财务审核 3: 财务审核不通过 4: 市场审核 5: 市场审核不通过 6：合伙人提交资质 7: 运营审批资质 8: 运营创建合同 9: 运营审批资质不通过 10: 待签署 11: 已签署 12: 已作废 13: 已生效-->
          <el-button v-show="row.status < 6" v-permission="['order:list:update']" type="primary" size="mini"
                     @click="toUpdateHandover(row)">
            修改
          </el-button>
          <!-- 2024年7月9日 添加后付款订单逻辑：如果是后付款订单，并且订单金额不等于实收金额，则显示打款记录-->
          <el-button v-show="row.status < 4 || (row.postpaid===1 && row.payAmount!==row.realAmount)" v-permission="['order:list:payment']" type="primary" size="mini"
                     @click="orderPayRecord(row.id)">
            打款记录
          </el-button>
          <el-button v-if="row.status ==21" v-permission="['order:list:creatContract']" type="primary" size="mini"
                     @click="creatContract(row,'true',1)">
            创建合同
          </el-button>
          <el-popconfirm
            title="确定删除吗？"
            v-permission="['order:list:remove']"
            @confirm="handleDel(row)"
          >
            <el-button slot="reference" type="danger" size="mini">
              财务强删
            </el-button>
          </el-popconfirm>
          <el-dropdown
            v-permission="['order:list:payment','order:list:uploadQualification','order:list:quanlificationExamine', 'order:list:marketExamine','order:list:customerDelivery','order:list:contractView','order:list:marketSubmit','order:list:reSign']"
            trigger="click"
            @command="handleCommand"
          >
            <el-button type="" plain size="mini">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <!-- 上传资质：创建交接单 =》 合伙人提交资质之前 -->
              <el-dropdown-item v-show="row.status >8 && row.status <=13&&row.businessType!==4"
                                v-permission="['order:list:checkQualification']" :command="beforeHandleCommand('z',row)">查看资质
              </el-dropdown-item>
              <!-- <el-dropdown-item v-show="row.status ===6||row.status ===9" :command="beforeHandleCommand('shortcutupload',row)">
                一键上传
              </el-dropdown-item> -->
              <el-dropdown-item v-show="(row.status === 6 && (row.contractStatus === 0 ||  row.contractStatus === 1)) || row.status ===9" v-permission="['order:list:uploadQualification']"
                                :command="beforeHandleCommand('m',row)">上传资质
              </el-dropdown-item>
              <el-dropdown-item v-show="row.status===7" v-permission="['order:list:quanlificationExamine']"
                                :command="beforeHandleCommand('b',row)">资质审核
              </el-dropdown-item>
              <!-- <el-dropdown-item v-if="row.status === 4" v-permission="['order:list:marketExamine']" :command="beforeHandleCommand('c',row)">市场审核</el-dropdown-item> -->
              <el-dropdown-item v-show="row.status == 13 && (row.deliver == 0 || row.deliver == 1)"
                                v-permission="['order:list:customerDelivery']" :command="beforeHandleCommand('d',row)">客户发货
              </el-dropdown-item>
              <el-dropdown-item v-show="row.status == 13 && (row.recDeliver == 0 || row.recDeliver == 1)"
                                v-permission="['order:list:recommendDelivery']" :command="beforeHandleCommand('e',row)">推荐人发货
              </el-dropdown-item>
              <el-dropdown-item v-show="row.status > 7 && row.status != 9 && row.status != 21&&row.businessType!==4"
                                v-permission="['order:list:contractView']" :command="beforeHandleCommand('f',row)">查看合同
              </el-dropdown-item>
              <!-- <el-dropdown-item v-if="row.status == 5" v-permission="['order:list:marketSubmit']" :command="beforeHandleCommand('g',row)">市场重新提交</el-dropdown-item> -->
              <el-dropdown-item v-show="row.status == 15" v-permission="['order:list:reSign']"
                                :command="beforeHandleCommand('h',row)">重新签约
              </el-dropdown-item>
              <el-dropdown-item v-show="row.status <11||row.status == 21" v-permission="['order:list:undo']"
                                :command="beforeHandleCommand('j',row)">撤销
              </el-dropdown-item>
              <el-dropdown-item v-show="(row.status>=6&&row.status<13)||row.status===21||row.status===13" v-permission="['order:list:editData']"
                                :command="beforeHandleCommand('k',row)">数据修改
              </el-dropdown-item>
              <el-dropdown-item v-show="row.status===13&&row.openFlowDataPackage===0" v-permission="['order:list:sign']"
                                :command="beforeHandleCommand('q',row)">标记
              </el-dropdown-item>
              <el-dropdown-item v-show="row.isRefundCompleted!==1" v-permission="['order:list:refund']"
                                :command="beforeHandleCommand('l',row)">退款
              </el-dropdown-item>
              <el-dropdown-item v-show="(row.status ===6||row.status ===9) && row.contractStatus === 2" v-permission="['order:list:copyAddress']"
                                :command="beforeHandleCommand('certification',row)">实名认证
              </el-dropdown-item>


              <!-- 业绩部门 -->
              <el-dropdown-item
                :command="beforeHandleCommand('salesDept',row)"
                v-permission="['order:list:salesDept']"
              >
                业绩部门
              </el-dropdown-item>


            </el-dropdown-menu>
          </el-dropdown>

        </template>

      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      layout="total, sizes, prev, pager, next"
      @pagination="getList"
    />
    <payment-record ref="paymentRecord" @submit="getList" />
    <refund-records ref="refundRecords" @submit="getList" />
    <market-examine-order ref="marketExamine" @refresh="getList" />
    <el-dialog title="重新提交交接单" :visible.sync="reasonDialog" width="500px" class="reason-dialog"
               :close-on-click-modal="!reasonDialog">
      <el-row>
        <el-col :span="24">
          <el-input
            v-model="rejectReason"
            type="textarea"
            placeholder="请输入备注"
            :autosize="{ minRows: 4, maxRows: 6}"
            required
          />
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="confirmSubmitOrderAgain">确 定</el-button>
        <el-button @click="reasonDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 实名认证弹框 -->
    <el-dialog title="实名认证" :visible.sync="certificationPop" @close="cancelCertification">
      <p>{{ certificationUrl }}</p>
      <div class="certification-btns">
        <el-button size="mini" type="default" style="margin-right:10px" @click="cancelCertification">取消</el-button>
        <el-button size="mini" type="primary" class="tag-read" :data-clipboard-text="certificationUrl"
                   @click="copyCertification(certificationUrl,$event)">复制
        </el-button>
      </div>
    </el-dialog>
    <SetDept :visible.sync="salesDeptPop" :id="currentOrderId" :salesDept="salesDept" @success="getList"/>
  </div>
</template>


<script>

import {
  getAllProject
} from '@/api/common'
import {
  getOrderList,
  marketSubmitOrder,
  reSign,
  undo,
  flowDataPackage,
  certificationAdress, forceDeleteOrder
} from '@/api/handover'
import Pagination from '@/components/Pagination'
import {
  getAreaSingle,
  converseEnToCn,
  getDimissionStatus,
  orderStatusList,
  channelList,
  getDeliverType,
  getRecDeliveryType,
  businessList
} from '@/utils/field-conver'
import AreaPicker from '@/components/area-picker'
import PaymentRecord from './components/paymentRecord'
import RefundRecords from './components/refundRecords'
import MarketExamineOrder from './components/marketExamine'
import OrderStatusTag from '@/components/StatusTag/OrderStatusTag.vue'
import { checkBtnPermission } from '@/utils/permission'
import { forceDelPaymentRecord } from '@/api/payment'
import SchoolJoinStatusTag from '@/components/StatusTag/SchoolJoinStatusTag.vue'
import CustomerDetailLink from '@/components/link/CustomerDetailLink.vue'
import Collapse from '@/components/collapse/Collapse.vue'
import SetDept from './components/SetDeptDialog.vue'

export default {
  name: 'Handover',
  components: {
    Collapse, CustomerDetailLink, SchoolJoinStatusTag,
    OrderStatusTag,
    Pagination,
    AreaPicker,
    PaymentRecord,
    MarketExamineOrder,
    RefundRecords,
    SetDept
  },
  directives: {},


  computed: {
    $_isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  },
  data() {
    return {
      salesDeptPop: false,
      salesDept: '',
      listLoading: false,
      orderList: [], // 订单列表
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      currentOrderId: '',
      reasonDialog: false,
      rejectReason: '',
      signTime: [],
      projectList: [], // 项目列表
      orderStatusList: orderStatusList,
      channelList: channelList,
      areaSingleList: getAreaSingle,
      deliveryStatus: getDeliverType,
      recDeliveryStatus: getRecDeliveryType,
      total: 0,
      dialogRolePermission: false,
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      businessList: businessList,
      packageLists: [
        {
          id: 0,
          val: ' 未开通'
        },
        {
          id: 1,
          val: ' 已开通'
        },
        {
          id: 2,
          val: ' 无需开通'
        }
      ],
      certificationPop: false,
      certificationUrl: ''
    }
  },
  mounted() {
    if (this.$route.query.orderCode) {
      this.$set(this.listQuery, 'orderCode', this.$route.query.orderCode);
      this.handleFilter();
    }
    this.$nextTick(() => {
      this.getProject()
      this.getList()
    })
  },
  methods: {
    handleDel(row) {
      forceDeleteOrder(row.id).then(
        res => {
          if (res.code === '000000') {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.getList()
          }
        }
      )
    },
    toSchoolDetail(row) {
      if (checkBtnPermission(['customer:schoolProject:campus'])) {
        this.$router.push({
          name: 'Campusmanagement',
          query: {
            id: Number(row.institutionCode.substr(1,)),
            title: '校区-' + row.schoolName
          }
        })
      }
    },
    /**
     * 获取订单分页列表
     * */
    getList() {
      const that = this
      const params = Object.assign(that.listQuery, that.areaList)
      getOrderList(params).then(res => {
        that.orderList = res.data.records

        that.total = res.data.total
      })
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      if (this.signTime) {
        this.listQuery.signStartTime = this.signTime[0]
        this.listQuery.signEndTime = this.signTime[1]
      }
      else {
        this.listQuery.signStartTime = ''
        this.listQuery.signEndTime = ''
      }
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.signTime = []
      this.getList()
    },
    /**
     * 更多按钮
     * */
    handleCommand(command) {
      switch (command.type) {
        case 'b':
          this.$router.push({
            path: `/handover/qualifications/${ command.row.id }`,
            query: {
              type: 'auditQualification'
            }
          })
          break
        case 'c':
          this.toMarketExamine(command.row)
          break
        case 'd':
          this.customerDelivery(command.row)
          break
        case 'e':
          this.recDelivery(command.row)
          break
        case 'f':
          this.toDealContract(command.row)
          break
        case 'g':
          this.submitOrderAgain(command.row)
          break
        case 'h':
          this.reSign(command.row)
          break
        case 'm':
          this.$router.push({
            path: `/handover/qualifications/${ command.row.id }`,
            query: {
              type: 'uploadQualification',
              clueCode: `${ command.row.clueCode }`
            }
          })
          break
        case 'shortcutupload':
          this.$router.push({
            path: `/handover/qualifications/${ command.row.id }`,
            query: {
              type: 'shortcutUploadQualification',
              clueCode: `${ command.row.clueCode }`
            }
          })
          break
        case 'z':
          this.$router.push({
            path: `/handover/qualifications/${ command.row.id }`,
            query: {
              type: 'checkdQualification'
            }
          })
          break
        case 'j':
          this.undo(command.row)
          break
        case 'k':
          this.toEditHandover(command.row)
          break
        case 'q':
          this.sign(command.row)
          break
        case 'l':
          this.refundHandover(command.row)
          break
        case 'certification':
          this.certificationOpera(command.row)
          break
        case 'salesDept':
          this.handleSalesDept(command.row)
          break
      }
    },


    handleSalesDept(row) {
      this.salesDept = row.salesDept
      this.currentOrderId = row.id
      this.salesDeptPop = true
    },

    beforeHandleCommand(type, row) {
      return {

        'type': type,
        'row': row
      }
    },
    /**
     * 市场重新提交订单
     * */
    submitOrderAgain(row) {
      this.reasonDialog = true
      this.currentOrderId = row.id
    },
    confirmSubmitOrderAgain() {
      if (!this.rejectReason) {
        this.$message({
          message: '备注必填',
          type: 'warning'
        })
        return
      }
      const params = {
        id: this.currentOrderId,
        remark: this.rejectReason
      }
      marketSubmitOrder(params).then(res => {
        if (res.code === '000000') {
          this.reasonDialog = false
          this.$message({
            message: '交接单重新提交成功',
            type: 'success'
          })
          this.getList()
        }
      })
    },
    /**
     * 合同签署
     * */
    toDealContract(row) {
      this.$router.push({
        path: '/contract/index',
        query: {
          contractCode: row.orderCode
        }
      })
    },
    toDetailHandover(row) {
      if (row.businessType === 4) {
        this.$router.push({
          name: 'CustomerPurchase',
          query: {
            pjId: row.projectId, // 项目id
            spId: row.id, // 校区项目id
            slId: row.schoolId, // 校区id
            type: 'details',
            name: row.customer
          },
          params: {
            clueId: row.clueId
          }
        })
      }
      else if (row.businessType === 5) {
        this.$router.push({
          name: 'Refund',
          params: {
            id: row.id,
            isEdit: false
          }
        })
      }
      else {
        this.$router.push({
          name: 'HandoverDetail',
          query: {
            name: row.customer,
            orderId: row.id
          },
          params: {
            clueId: row.clueId
          }
        })
      }
    },
    /**
     * 跳转修改交接单页面
     * type:
     * create: 创建交接单
     * update: 修改交接单
     * renew: 续约
     * upgrade： 升级交接单
     * */
    toUpdateHandover(row) {
      if (row.businessType === 4) {
        this.$router.push({
          name: 'CustomerPurchase',
          query: {
            pjId: row.projectId, // 项目id
            spId: row.id, // 校区项目id
            slId: row.schoolId, // 校区id
            type: 'update',
            name: row.customer
          },
          params: {
            clueId: row.clueId
          }
        })
      }
      else {
        this.$router.push({
          name: 'HandoverEdit',
          query: {
            orderId: row.id,
            type: 'update',
            name: row.customer
          },
          params: {
            clueId: row.clueId
          }
        })
      }
    },
    toEditHandover(row) { // 数据修改
      this.$router.push({
        name: 'HandoverEdit',
        query: {
          orderId: row.id,
          type: 'editData',
          name: row.customer,
          status: row.status
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    /**
     * 客户发货
     * */
    customerDelivery(row) {
      this.$router.push({
        path: '/handover/invoice',
        query: {
          orderId: row.id,
          type: '1',
          name: row.customer
        }
      })
    },
    /**
     * 推荐人发货
     * */
    recDelivery(row) {
      this.$router.push({
        path: '/handover/invoice',
        query: {
          orderId: row.id,
          type: '2',
          name: row.customer
        }
      })
    },
    /**
     * 打开市场审批弹窗
     * */
    toMarketExamine(row) {
      this.$refs.marketExamine.getDetail(row)
    },
    /**
     * 打款记录
     **/
    orderPayRecord(row) {
      this.$refs.paymentRecord.getLists(row)
    },
    /**
     * 转换字段
     * @param row
     * @returns {*}
     */
    getOrderStatus(row) {
      return converseEnToCn(orderStatusList, row.status)
    },
    getDeliver(row) {
      return converseEnToCn(getDeliverType, row.deliver)
    },
    getRecDeliver(row) {
      return converseEnToCn(getDeliverType, row.recDeliver)
    },
    setAreaSingle(row) {
      return converseEnToCn(getAreaSingle, row.areaSingle)
    },
    getDimission(row) {
      return converseEnToCn(getDimissionStatus, row.dimission)
    },
    // 渠道
    getChannelType(data) {
      return converseEnToCn(channelList, data.channel)
    },
    getBusinessType(row) {
      return converseEnToCn(businessList, row.businessType)
    },
    creatContract(row, isEdit, flags) { // 创建合同模板调转,解约合同不需要,flags是1
      if (row.projectId === 1 && row.versionType === 3) { // 抢分合同
        const titlePoints = '抢分'
        this.$router.push({
          path: '/contract/points',
          query: {
            id: row.id,
            isEdit: isEdit,
            title: titlePoints,
            flags: flags
          }
        })
      }
      else if ((row.projectId === 1 || row.projectId === 3) && row.versionType === 9) { // 特色班型合同
        this.$router.push({
          path: '/contract/features',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags,
            projectId: row.projectId
          }
        })
      }
      else if (row.projectId === 3 && row.versionType === 8) { // 招生服务合同合同
        this.$router.push({
          path: '/contract/service',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }
      else if ((row.projectId === 8 ) && (row.versionType === 1 || row.versionType === 2)) {
        const title = '抖音云连锁'
        this.$router.push({
          path: '/contract/douyin',
          query: {
            id: row.id,
            isEdit: isEdit,
            title: title,
            flags: flags
          }
        })
      }
      else if ((row.projectId === 1 || row.projectId === 2||  row.projectId === 3  ) && (row.versionType === 1 || row.versionType === 2)) {
        const title = row.projectId === 1 ? '三陶普高' : (row.projectId === 2 ? '芝麻艺考' :'烨晨中学')
        this.$router.push({
          path: '/contract/common',
          query: {
            id: row.id,
            isEdit: isEdit,
            title: title,
            flags: flags
          }
        })
      }
      else if (row.projectId === 3 && (row.versionType === 5 || row.versionType === 6)) { // 5 烨晨市代/6 烨晨渠道
        const title = row.projectId === 3 && row.versionType === 5 ? '烨晨市代' : '烨晨渠道'
        this.$router.push({
          path: '/contract/ycNew',
          query: { id: row.id, isEdit: isEdit, title: title, flags: flags, versionType: row.versionType }
        })
      }
      else if (row.projectId === 3 && row.versionType === 4) {
        this.$router.push({ path: '/contract/yc', query: { id: row.id, isEdit: isEdit, flags: flags } })
      }
      else if (row.projectId === 4 && row.versionType !== 3) {
        this.$router.push({
          path: '/contract/txt',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }
      else if (row.projectId === 5 && row.versionType !== 3) {
        this.$router.push({
          path: '/contract/ai',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }
      else if (row.projectId === 6 && row.versionType !== 3) {
        this.$router.push({
          path: '/contract/jt',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }
      else if (row.versionType === 7) {
        this.$router.push({
          path: '/contract/entrance',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }  else if (row.projectId ===8 ) {
        this.$router.push({
          path: '/contract/douyin',
          query: {
            id: row.id,
            isEdit: isEdit,
            flags: flags
          }
        })
      }
    },
    /**
     * 重新签约
     * */
    reSign(row) {
      const orderId = row.id
      this.$confirm('确定要重新签约', '提示?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reSign(orderId).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '签约成功,请到合同列表查看',
              type: 'success'
            })
            this.getList()
          }
        }).catch(res => {
        })
      }).catch(action => {
      })
    },
    undo(row) {
      const id = row.id
      this.$confirm('确定要进行撤销操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        undo(id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {

      })
    },
    sign(row) { // 标记
      this.$confirm('确认校区流量包已开通?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        flowDataPackage(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '校区流量包已开通'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    refundHandover(row) { // 退款
      this.$router.push({
        name: 'Refund',
        params: {
          id: row.id,
          isEdit: true
        }
      })
    },
    certificationOpera(row) { // 实名认证弹框
      this.certificationPop = true
      certificationAdress(row.id).then(res => {
        if (res.code === '000000') {
          this.certificationUrl = res.data
        }
      }).catch(() => {

      })
    },
    copyCertification(url, e) {

      var clipboard = new this.Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制

        // 释放内存
        clipboard.destroy()
      })
    },
    cancelCertification() {
      this.certificationPop = false
      this.certificationUrl = ''
    }
    // refund(row) {
    //   this.$refs.refundRecords.dialogFollow = true
    // }
  }
}
</script>
<style scoped>
.certification-btns {
  display: flex;
  justify-content: center;
}
</style>
