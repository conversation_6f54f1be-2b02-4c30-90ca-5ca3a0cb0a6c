<!--URL配置弹框-->
<template>
    <div class="app-container">
      <div class="filter-container">
        <el-button v-waves class="filter-item" type="primary" @click="handleAdd">
          新增
        </el-button>
      </div>
      <el-table v-loading="tableData.loading" :data="tableData.data" border stripe  highlight-current-row>
        <el-table-column label="菜单URL" prop="urlPath"/>
        <el-table-column label="状态" prop="status">
          <template #default="{row}">
            <span v-if="row.status === 1">启用</span>
            <span v-if="row.status === 99">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark"/>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button v-waves class="filter-item" size="mini" :type="scope.row.status === 1?'warning':'primary'" @click="handleEnable(scope.row)">
              <span v-if="scope.row.status === 1">禁用</span>
              <span v-if="scope.row.status === 99">启用</span>
            </el-button>
            <el-button size="mini" v-waves class="filter-item" type="danger" @click="handleDel(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="tableData.total>0" :total="tableData.total" :page.sync="queryParam.pageIndex" :limit.sync="queryParam.pageSize" @pagination="fetchData" />

      <!-- 新增/修改菜单弹框 -->
      <el-dialog
        title="菜单URL配置"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="dialogConfig.visible"
        width="30%"
        @close="close"
      >
        <el-form ref="formRef"  :model="formData" :rules="formRules" label-width="120px">
          <el-form-item label="菜单URL" prop="urlPath">
            <el-input v-model="formData.urlPath" placeholder="请输入菜单URL" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="99">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
import {addMenuUrl, menuPageQry, updMenuUrl,delMenuUrl} from "@/api/system-setting.js";
import Pagination from "@/components/Pagination/index.vue";
export default {
  name: 'UrlConfig',
  components: {Pagination},
  computed:{
  },
  data() {
    return {
      dialogConfig: {
        visible:false,
        title:'',
      },
      queryParam: {
        pageIndex:1,
        pageSize:10
      },
      tableData: {
        data:[],
        total:0,
        loading:false
      },
      formData:{
        menuId:"",
        urlPath:"",
        status:1,
        remark:"",
      },
      formRules:{
        urlPath:{
          required: true,
          message: '请输入菜单URL',
          trigger: 'blur'
        },
        status:{
          required: true,
          message: '请选择状态',
          trigger: 'blur'
        }
      }
    }
  },

  created() {
    this.formData.menuId = this.$route.query.menuId;
    this.fetchData();
  },

  methods: {
    /**
     *  分页查询列表
     */
    fetchData(){
      this.tableData.loading = true
      menuPageQry(this.formData.menuId).then(res => {
        if (res.code === '000000') {
          this.tableData.data = res.data;
          this.tableData.total = res.data.total
        }
      }).finally(() => {
        this.tableData.loading = false
      })
    },


    /**
     * 启用/禁用
     */
    handleEnable(row){
      this.$confirm(`确认${row.status === 1? '禁用' : '启用'}该菜单吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        updMenuUrl(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: row.status === 1? '禁用成功' : '启用成功',
              type:'success'
            })
            row.status = row.status === 1 ? 99 : 1;
          }else{
            this.$message({
              message: res.message,
              type:'error'
            })
          }
        }).catch(error => {
          this.$message({
            message: "修改失败",
            type:'error'
          })
        })
      })
    },

    /**
     * 新增菜单窗口
     */
    handleAdd() {
      this.dialogConfig.visible = true
      this.dialogConfig.title = '新增菜单'
    },

    /**
     * 删除
     */
    handleDel(row){
      this.$confirm('确认删除该菜单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delMenuUrl(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '删除成功',
              type:'success'
            })
            this.fetchData();
          }
        })
      })
    },

    /**
     * 关闭弹框
     */
    close(){
      this.dialogConfig.visible = false
      this.$refs.formRef.resetFields();
    },

    /**
     * 新增/修改菜单
     */
    submit(){
      this.$refs.formRef.validate(valid => {
        if (valid) {
          addMenuUrl({
            menuId:this.formData.menuId,
            urlPath:this.formData.urlPath,
            status:this.formData.status,
            remark:this.formData.remark,
          }).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增成功',
                type: 'success'
              })
              this.dialogConfig.visible = false;
              this.$refs.formRef.resetFields();
              this.fetchData();
            }
          }).catch(() => {
            this.enableFlagsUpdate = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
