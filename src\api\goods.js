import request from '@/utils/request'
/**
 * 获取商品列表
 * @param data
 */
export function getGoodsList(data) {
  return request({
    url: 'stip/products/page',
    method: 'POST',
    data: data
  })
}
/**
 * 查询所有商品类型
 * @param data
 */
export function productTypes(data) {
  return request({
    url: 'stip/productTypes',
    method: 'get'
  })
}
/**
 * 查询商品详情
 * @param data
 */
export function getProDetail(id) {
  return request({
    url: `stip/products/${id}`,
    method: 'get'
  })
}
/**
 * 所有可用的流量包商品
 * @param data
 */
export function getPackage(classTypeId) {
  return request({
    url: `stip/products/flowPackageProducts?classTypeId=${classTypeId}`,
    method: 'get'
  })
}
/**
 * 新增流量包商品
 * @param data
 */
export function addPackage(data) {
  return request({
    url: 'stip/products',
    method: 'POST',
    data: data
  })
}
/**
 * 修改流量包商品
 * @param data
 */
export function editPackageInfo(data) {
  return request({
    url: 'stip/products',
    method: 'PUT',
    data: data
  })
}

/**
 * 开通或关闭科目计费
 * @param data
 * @returns {*}
 */
export function openSubject(data) {
  return request({
    url: 'stip/products/openSubject',
    method: 'PUT',
    data: data
  })
}
/**
 * 根据商品ID查询商品套餐-流量包
 * @param data
 */
export function getPackageLists(productId) {
  return request({
    url: `stip/packages/product/${productId}`,
    method: 'GET'
  })
}
/**
 * 保存商品套餐-流量包
 * @param data
 */
export function addPackageSet(data) {
  return request({
    url: 'stip/packages',
    method: 'POST',
    data: data
  })
}
/**
 * 删除商品套餐-流量包
 * @param data
 */
export function delPackage(id) {
  return request({
    url: `stip/packages/${id}`,
    method: 'DELETE'
  })
}
/**
 * 根据商品ID查询商品套餐-单一科目/班型打包
 * @param data
 */
export function getSinglePackage(productId) {
  return request({
    url: `stip/class/packages/product/${productId}`,
    method: 'GET'
  })
}
/**
 * 根据商品ID删除商品套餐-单一科目/班型打包
 * @param data
 */
export function delSinglePackage(id) {
  return request({
    url: `stip/class/packages/${id}`,
    method: 'DELETE'
  })
}
/**
 * 根据商品添加商品套餐-单一科目/班型打包
 * @param data
 */
export function addSinglePackage(data) {
  return request({
    url: `stip/class/packages`,
    method: 'POST',
    data: data
  })
}
