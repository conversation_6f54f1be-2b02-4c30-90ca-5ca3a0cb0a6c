<template>
  <el-dialog :visible.sync="subjectsPop" :title="subjectsTitle" :close-on-click-modal="!subjectsPop" width="60%" @close="changeInit">
    <el-form ref="subjects" :model="listQuery" :rules="rules" label-width="100px">
      <el-form-item label="科目名称" prop="title">
        <el-input v-model="listQuery.title" placeholder="请输入科目名称" maxlength="20" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="科目状态">
        <el-select v-model="listQuery.status" placeholder="请选择科目状态" clearable class="filter-item" :disabled="isEdit" filterable>
          <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="科目排序">
        <el-input v-model.number="listQuery.sort" placeholder="请输入科目排序" maxlength="7" :disabled="isEdit" />
      </el-form-item>
      <div v-if="!isEdit" class="assign-operas">
        <el-button type="infor" size="mini" @click="subjectsPop=false,cancelSubjects()">取消</el-button>
        <!--        新增科目-->
        <el-button v-if="flags===1" type="primary" size="mini" @click="custormSubjects">确定</el-button>
        <!--        编辑科目-->
        <el-button v-if="flags===0" type="primary" size="mini" @click="editSubjects">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import {
  enableList
} from '@/utils/field-conver'
import { addSubjects, editSubjects, getSubjectsDetail } from '@/api/classType'
export default {
  name: 'AddSubject',
  data() {
    return {
      subjectsTitle: '',
      subjectsPop: false,
      enableList: enableList,
      listQuery: {
        status: 1
      },
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入科目名称' }
      },
      isEdit: false,
      flags: -1
    }
  },
  methods: {
    getSubjectsDetail(id) {
      getSubjectsDetail(id).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
        }
      })
    },
    cancelSubjects() {
      if (this.$refs.subjects) {
        this.$refs.subjects.clearValidate()
      }
      this.listQuery.status = 1
      this.listQuery.title = ''
      this.listQuery.sort = ''
    },
    custormSubjects() {
      this.$refs.subjects.validate((valid) => {
        const param = Object.assign({}, this.listQuery)
        if (valid) {
          if (Number(this.listQuery.sort) < 100) {
            addSubjects(param).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.$emit('addClassList')
                this.subjectsPop = false
                this.listQuery.status = 1
                this.listQuery.title = ''
                this.listQuery.sort = ''
                this.$refs.subjects.clearValidate()
              }
            }).catch(() => {

            })
          } else {
            this.$message({
              type: 'warning',
              message: '排序请输入小于100的数字'
            })
          }
        } else {
          return false
        }
      })
    },
    editSubjects() {
      const param = Object.assign({}, this.listQuery)
      this.$refs.subjects.validate((valid) => {
        if (valid) {
          if (Number(this.listQuery.sort) < 100) {
            editSubjects(param).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.$emit('addClassList')
                this.subjectsPop = false
                this.listQuery.status = 1
                this.listQuery.title = ''
                this.listQuery.sort = ''
                this.$refs.subjects.clearValidate()
              }
            }).catch(() => {

            })
          } else {
            this.$message({
              type: 'warning',
              message: '排序请输入小于100的数字'
            })
          }
        } else {
          return false
        }
      })
    },
    changeInit() {
      this.listQuery.status = 1
      this.listQuery.title = ''
      this.listQuery.sort = ''
      if (this.$refs.subjects) {
        this.$refs.subjects.clearValidate()
      }
    }
  }
}
</script>

<style scoped>

</style>
