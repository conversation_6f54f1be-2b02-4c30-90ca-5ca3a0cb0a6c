'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Element Admin' // page title

//  如果端口设置为80，
//  使用管理员权限执行命令行。
//  例如，Mac:sudo npm run
//  您可以通过以下方法更改端口：
//  端口=9527 npm运行开发或npm运行开发--端口=9527
const port = process.env.port || process.env.npm_config_port || 9527 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  //  以多页模式构建应用程序。
  // pages: {
  //   index: {
  //     // page 的入口
  //     entry: 'src/main.js',
  //     // 模板来源
  //     template: 'public/index.html',
  //     chunks: ['chunk-libs', 'chunk-elementUI', 'chunk-commons', 'index','runtime'],
  //   },
  //   redirect: {
  //     entry: 'src/redirect.js',
  //     template: 'public/redirect.html',
  //     filename: 'redirect.html',
  //     title: '企业微信登录',
  //     // 传递自定义变量
  //     templateParameters: (compilation, assets, options) => {
  //       console.log("------------>>>",options);
  //       return {
  //         htmlWebpackPlugin: {
  //           options: {
  //             title: options.title,
  //             templateParameters: {
  //               // 注入 basesUrl 变量
  //               basesUrl: process.env.VUE_APP_BASE_API || ''
  //             }
  //           }
  //         }
  //       };
  //     },
  //     chunks: [],
  //   }
  // },
  //  是否使用包含运行时编译器的 Vue 构建版本
  runtimeCompiler: false,
  //  是否为 Babel 或 TypeScript 使用 thread-loader。该选项在系统的 CPU 有多于一个内核时自动启用，仅作用于生产构建，在适当的时候开启几个子进程去并发的执行压缩
  parallel: require('os').cpus().length > 1,
  lintOnSave: process.env.NODE_ENV === 'development',
  //  生产环境是否生成 sourceMap 文件，一般情况不建议打开
  productionSourceMap: false,
  transpileDependencies: [
    '@wecom/jssdk'
  ],
  devServer: {
    port: port,
    open: false,
    disableHostCheck: true,
    overlay: {
      warnings: false,
      errors: true
    },
    // 配置浏览器自动启动
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: { // http://pre.crm2.52santao.com/
        // target: `https://pre-sapi.52santao.com/crm`, // 测试环境
        // target: 'https://crm.52santao.com/api/v1/', // 生产环境
        // target: `http://127.0.0.1:${port}/mock`, 176
        // target: `http://dev.sapi.52santao.com/crm`, // 开发环境
        // target: `https://sapi.52santao.com/crm`, // 开发环境
        target: 'https://crm.52santao.com/aaa',
        changeOrigin: true,
        // secure: false,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
      // '/api/': {
      //   target: `http://*************:8081`,
      //   changeOrigin: true,
      //   pathRewrite: {}
      // }
    }
    // after: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    module: {
      rules: [
        {
          test: /\.js$/,
          loader: 'babel-loader',
          include: [
            // 包含 src 目录和 @wecom/jssdk 库
            resolve(__dirname, 'src'),
            resolve(__dirname, 'node_modules/@wecom/jssdk')
          ]
        }
      ]
    },
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === 'development',
        config => config.devtool('cheap-source-map')
      )

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial' // only package third parties that are initially dependent
              },
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
              },
              commons: {
                name: 'chunk-commons',
                test: resolve('src/components'), // can customize your rules
                minChunks: 3, //  minimum common number
                priority: 5,
                reuseExistingChunk: true
              }
            }
          })
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
