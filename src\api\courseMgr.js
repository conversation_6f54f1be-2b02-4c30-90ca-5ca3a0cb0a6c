import request from '@/utils/request'

export function getDrafts(param) {
  return request({
    url: '/course/sync/classTypes/page',
    method: 'POST',
    data: param
  })
}

export function getSyncCourse(param) {
  return request({
    url: '/course/sync/log/page',
    method: 'POST',
    data: param
  })
}

/**
 * 获取课程的章节信息
 * @param resourceId
 */
export function getCourseChapter(resourceId) {
  return request({
    url: `/stip/courses/videoNodes/${resourceId}`,
    method: 'GET'
  })
}

/**
 * 课程同步-课程同步
 * */

export function saveCourseChapter(data) {
  return request({
    url: `/stip/courses/editVideoNodes`,
    method: 'POST',
    data: data
  })
}

export function listSubjectCourseSyncNums(classTypeRelationId) {
  return request({
    url: `/course/sync/relation/listSubjectCourseSyncNums?classTypeRelationId=${classTypeRelationId}`,
    method: 'GET'
  })
}

export function listSubjectMembers(classTypeId, subjectId) {
  return request({
    url: `/course/manage/member/list?classTypeId=${classTypeId}&subjectId=${subjectId}`,
    method: 'GET'
  })
}
/**
 * 课程生产系统-查询审课课程详情
 * */

export function courseProductionDetail(id) {
  return request({
    url: `/course/manage/course/${id}`,
    method: 'GET'
  })
}

/**
 * 课程生产系统-查询博课程详情
 * */

export function blogDetail(id) { // /stip/courses/{id}
  return request({
    url: `/stip/courses/${id}`,
    method: 'GET'
  })
}
/**
 * 课程生产系统-查询附件同步记录
 * */

export function enclosureDetail(courseMgrId, syncLogId) {
  return request({
    url: `/course/sync/listAttachmentSyncLogs?courseMgrId=${courseMgrId}&syncLogId=${syncLogId || ''}`,
    method: 'GET'
  })
}
/**
 * 根据stip clientCode获取菜单树
 * */

export function getTreeData(clientCode) {
  return request({
    url: `/download/menu/getTree/${clientCode}`,
    method: 'GET'
  })
}

/**
 * 课程同步-课程同步
 * */

export function courseSync(data) {
  return request({
    url: `/course/sync`,
    method: 'POST',
    data: data
  })
}
/**
 * 课程同步-附件同步
 * */

export function enclosureSync(data) {
  return request({
    url: `/course/sync/attachment`,
    method: 'POST',
    data: data
  })
}
