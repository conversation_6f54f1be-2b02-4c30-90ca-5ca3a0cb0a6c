<template>
  <el-dialog :visible.sync="packageSetPop" :title="packageSetTitle" :close-on-click-modal="!packageSetPop" width="50%">
    <el-form label-width="120px">
      <div v-for="(item,index) in packageSets" :key="item.id">
        <el-form-item label="套餐名称">
          <el-input v-model="item.title" maxlength="50" />
        </el-form-item>
        <el-form-item label="套餐时长/小时" required>
          <el-input v-model.number="item.number" @input="refresh" />
        </el-form-item>
        <el-form-item label="套餐价格/元" required>
          <el-input v-model="item.price" type="number" @input="refresh" />
        </el-form-item>
        <el-form-item v-if="productTypeName==='单一科目'||productTypeName==='班型打包'" label="默认流量包" required>
          <el-select v-model="item.defaultTraffic" placeholder="默认流量包" filterable>
            <el-option v-for="items in packageLists" :key="items.id" :label="items.title" :value="items.id" />
          </el-select>
        </el-form-item>
        <div v-if="item.id" class="operas">
          <span class="el-icon-delete adds" @click="delPackage(item)" />
        </div>
        <div v-if="!item.id" class="operas">
          <span class="el-icon-delete adds" @click="delPackages(index)" />
        </div>
      </div>
      <div v-if="packageSets&&packageSets.length>0" class="package-btns">
        <el-button type="infor" size="mini" @click="packageSetPop=false">取消</el-button>
        <el-button type="primary" size="mini" @click="addPackageSet">确定</el-button>
      </div>
      <p class="operas">
        <em v-if="packageSets&&packageSets.length===0">暂时无套餐数据，请手动添加!</em>
        <a v-if="packageSets&&packageSets.length===0||packageSets&&packageSets.length<4" class="el-icon-circle-plus-outline adds" @click="addPackage" />
      </p>
    </el-form>
  </el-dialog>
</template>

<script>
import { getPackageLists, addPackageSet, delPackage, getSinglePackage, getPackage, delSinglePackage, addSinglePackage } from '@/api/goods'
export default {
  name: 'PackageSet',
  data() {
    return {
      packageSetPop: false,
      packageSetTitle: '套餐设置',
      packageSets: [],
      productId: 0,
      productTypeName: null,
      packageLists: [],
      classTypeId: null
    }
  },
  methods: {
    getPackageList(ids) { // 流量包的套餐数据
      getPackageLists(ids).then(res => {
        if (res.code === '000000') {

          this.packageSets = res.data
        }
      })
    },
    getSinglePackage(ids) { // 单一科目/班型打包
      getSinglePackage(ids).then(res => {
        if (res.code === '000000') {

          this.packageSets = res.data
          this.getPackage(this.classTypeId)
        }
      })
    },
    getPackage(classTypeId) { // 默认流量包
      getPackage(classTypeId).then(res => {
        if (res.code === '000000') {
          const arr = []
          for (let i = 0; i < res.data.length; i++) {
            const obj = {}
            obj.id = res.data[i].id
            obj.title = `${res.data[i].unitPrice}元,${res.data[i].productName}`
            arr.push(obj)
          }
          this.packageLists = arr || []
        }
      }).catch(() => {

      })
    },
    addPackage() {
      this.$confirm('确定要添加套餐?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const obj = {
          title: '',
          number: 0,
          price: 0
        }
        this.packageSets.push(obj)
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    delPackage(row) {
      const that = this
      if (row.id) {
        this.$confirm('确定删除该数据?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (that.productTypeName === '流量包') {
            delPackage(row.id).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '删除成功'
                })
                that.getPackageList(that.productId)
              }
            }).catch(() => {

            })
          } else {
            delSinglePackage(row.id).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '删除成功'
                })
                that.getSinglePackage(that.productId)
              }
            }).catch(() => {

            })
          }
        }).catch(() => {

        })
      }
    },
    addPackageSet() {

      const numberList = this.packageSets.map(item => item.number) || [] // 套餐时长

      const numFlag = numberList.every((item) => {
        return item || item === 0
      })

      const price = this.packageSets.map(item => item.price) || [] // 套餐价格
      const priceFlag = price.every((item) => {
        return item || item === 0
      })
      const defaultTraffics = this.packageSets.filter(item => item.defaultTraffic) || [] // 默认流量包
      if (this.productTypeName === '流量包') {
        if (this.packageSets && this.packageSets.length > 0 && numberList.length > 0 && price.length > 0 && numFlag && priceFlag && (this.packageSets.length === numberList.length) && (this.packageSets.length === price.length) && this.productId) {
          const params = {
            packages: this.packageSets,
            productId: this.productId
          }
          addPackageSet(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.$emit('packSets')
              this.packageSetPop = false
            }
          }).catch(() => {

          })
        } else if ((price.length === 0) || (this.packageSets.length !== price.length) || (!priceFlag)) {
          this.$message({
            type: 'warning',
            message: '请输入套餐价格'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '请输入套餐时长'
          })
        }
      } else if ((this.productTypeName === '单一科目' || this.productTypeName === '班型打包')) {
        if ((this.packageSets && this.packageSets.length > 0 && numberList.length > 0 && price.length > 0 && numFlag && priceFlag && (this.packageSets.length === numberList.length) && (this.packageSets.length === price.length) && (this.packageSets.length === defaultTraffics.length) && this.productId)) {
          const paramsSingle = {
            packages: this.packageSets,
            productId: this.productId
          }
          addSinglePackage(paramsSingle).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.$emit('packSets')
              this.packageSetPop = false
            }
          }).catch(() => {

          })
        } else if ((price.length === 0) || (this.packageSets.length !== price.length) || (!priceFlag)) {
          this.$message({
            type: 'warning',
            message: '请输入套餐价格'
          })
        } else if ((this.productTypeName === '单一科目' || this.productTypeName === '班型打包') && ((defaultTraffics.length === 0) || (this.packageSets.length !== defaultTraffics.length))) {
          this.$message({
            type: 'warning',
            message: '请选择默认流量包'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '请输入套餐时长'
          })
        }
      }
    },
    delPackages(index) {
      this.$confirm('确定删除该数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.packageSets.splice(index, 1)
      }).catch(() => {
        this.$message({
          type: 'type',
          message: '取消操作'
        })
      })
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped lang="scss">
.adds{
  font-size: 25px;
}
  .operas{
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-bottom: 10px;
    a{
      padding-left: 10px;
    }
  }
  .package-btns{
    display: flex;
    justify-content: center;
    align-items: center;
    padding:10px 0;
  }
</style>
