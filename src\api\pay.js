import request from '@/utils/request'
/**
 * 获取支付记录列表
 * @param data
 */
export function getPayList(data) {
  return request({
    url: 'trades/list',
    method: 'get',
    params: data
  })
}
/**
 * 新增支付记录列表
 * @param data
 */
export function addPayList(data) {
  return request({
    url: 'trades/add',
    method: 'POST',
    data: data
  })
}
/**
 * 支付记录详情
 * @param data
 */
export function PayDetail(id) {
  return request({
    url: 'trades/getTrade/' + id,
    method: 'get'
  })
}

/**
 * 修改支付记录
 * @param data
 */
export function editDetail(data) {
  return request({
    url: 'trades/edit',
    method: 'PUT',
    data: data
  })
}
/**
 * 删除支付记录
 * @param data
 */
export function deletePay(data) {
  return request({
    url: 'trades',
    method: 'DELETE',
    data: data
  })
}
