<template>
  <el-dialog
    :visible.sync="IntelligentPop"
    :title="IntelligentTitle"
    :close-on-click-modal="!IntelligentPop"
    width="90%"
  >
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="智能终端ID"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.serialNumber"
        placeholder="盒子序列号(可识别冒号)"
        class="filter-item"
        style="width: 160px;"
        @blur="changeSNCode"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userName"
        placeholder="学生姓名"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.userAccount"
        placeholder="学生账号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        class="filter-item"
        style="width: 150px;"
      >
        <el-option v-for="item in intelligentStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.deviceSource"
        placeholder="智能终端来源"
        clearable
        class="filter-item"
        style="width: 150px;"
      >
        <el-option v-for="item in deviceSources" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="listQuery.beginTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="创建开始日期"
        style="width:150px"
      />
      <el-date-picker
        v-model="listQuery.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="创建结束日期"
        style="width:150px"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['customer:schoolProject:IntelligentTerminal:add']" class="filter-item" type="primary"
                 size="mini" @click="IntelligentAddPop=true,handleAdd()">
        新增
      </el-button>
      <el-button :disabled="selectedBoxIds.length === 0" v-permission="['customer:schoolProject:IntelligentTerminal:del']" type="danger" size="mini" @click="handleBatchDelet">
        批量删除（ {{ selectedBoxIds.length }} ）
      </el-button>
    </div>
    <div v-permission="['customer:schoolProject:IntelligentTerminal:add']">
        <div class="syncBox">
          <label for="">智能终端同步：</label>
          <el-input v-model="targetSchoolId" style="width: 200px;" size="mini" placeholder="请输入要转入校区ID"></el-input>
          <el-button type="primary" plain size="mini" class="ml-10" @click="syncBox">同步（ {{ selectedBoxIds.length }} ）</el-button>
        </div>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <af-table-column label="智能终端ID" show-overflow-tooltip prop="id" />
      <af-table-column label="智能终端序列号" show-overflow-tooltip prop="serialNumber" />
      <af-table-column label="学生姓名" prop="userName" show-overflow-tooltip />
      <af-table-column label="学生账号" prop="userAccount" show-overflow-tooltip />
      <af-table-column label="状态" prop="status" :formatter="getIntelligentStatus" />
      <af-table-column label="智能终端来源" prop="deviceSource" :formatter="getDeviceSource" show-overflow-tooltip />
      <af-table-column label="最近登录时间" prop="loginTime" />
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp auto-fixed"
        min-width="230"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button v-permission="['customer:schoolProject:IntelligentTerminal:edit']" type="primary" size="mini"
                     @click="handleUpdate(row)">修改
          </el-button>
          <el-button v-permission="['customer:schoolProject:IntelligentTerminal:del']" type="danger" size="mini"
                     @click="handleDelet(row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      append-to-body
      :visible.sync="IntelligentAddPop"
      :title="IntelligentAddTitle"
      :close-on-click-modal="!IntelligentAddPop"
      width="60%"
      @close="cancelIntelligent"
    >
      <el-form ref="intelligentForms" :model="IntelligentForm" label-width="120px" :rules="rules">
        <el-form-item label="智能终端序列号" prop="serialNumber">
          <el-input v-if="flages === 0" v-model="IntelligentForm.serialNumber" placeholder="换行可批量添加智能终端序列号" />
          <el-input v-else type="textarea" :rows="5" v-model="IntelligentForm.serialNumber" placeholder="换行可批量添加智能终端序列号" />
        </el-form-item>
        <el-form-item label="智能终端来源" prop="deviceSource">
          <el-select
            v-model="IntelligentForm.deviceSource"
            placeholder="请选择智能终端来源"
            clearable
            class="filter-item"
          >
            <el-option v-for="item in deviceSources" :key="item.id" :label="item.itemName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="到期日期" prop="expiryTime">
          <el-date-picker
            v-model="IntelligentForm.expiryTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="到期日期"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="IntelligentForm.remark" type="textarea" maxlength="100" show-word-limit />
        </el-form-item>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="cancelIntelligent">取消</el-button>
          <!--新增-->
          <el-button v-if="flages===1" type="primary" size="mini" @click="custormIntelligent">确定</el-button>
          <!--修改-->
          <el-button v-if="flages===0" type="primary" size="mini" @click="editIntelligent">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </el-dialog>
</template>
<script>
import Pagination from '@/components/Pagination'
import { addlIntelligent, delIntelligent, getIntelligentList, syncBoxToOtherSchool, updateIntelligent, deleteDevices } from '@/api/schoolCampus'
import { getDataCode } from '@/api/common'
import { converseEnToCn, converseEnToCnList, intelligentStatus } from '@/utils/field-conver'
import { parseTime } from '@/utils'

export default {
  name: 'IntelligentPop',
  components: {
    Pagination
  },
  data() {
    return {
      IntelligentPop: false,
      IntelligentTitle: '智能终端列表',
      list: [],
      total: 0,
      targetSchoolId: '',//要转入的校区ID
      selectedBoxIds:[],//绑定的盒子id
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 50
      },
      rules: {
        serialNumber: { required: true, trigger: 'change', message: '请输入智能终端序列号' },
        expiryTime: { required: true, trigger: 'change', message: '请选择到期时间' },
        deviceSource: { required: true, trigger: 'change', message: '请选择智能终端来源' }
      },
      intelligentStatus: intelligentStatus,
      assignSatuts: [],
      IntelligentAddPop: false,
      IntelligentAddTitle: '',
      IntelligentForm: {},
      flages: -1,
      intelligentsId: '',
      mainSchoolId: '',
      deviceSources: []
    }
  },
  mounted() {
    this.$nextTick(() => {
    })
  },
  methods: {
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.assignTab.toggleRowSelection(row);
        });
      } else {
        this.$refs.assignTab.clearSelection();
      }
    },
    handleSelectionChange(val) {
      this.selectedBoxIds = val.map(item => item.id);
    },
    syncBox(){
      if(!this.targetSchoolId){
        this.$message.error('请选择要转入的校区')
        return
      }
      if(this.selectedBoxIds.length===0){
        this.$message.error('请选择要同步的盒子')
        return
      }
      const data={
        agencyId:this.targetSchoolId,
        terminalIdList:this.selectedBoxIds
      }
      syncBoxToOtherSchool(data).then(res => {
        if(res.code==='000000'){
          this.$message.success(`已同步 ${this.selectedBoxIds.length} 个设备`)
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { mainSchoolId: this.mainSchoolId })
      await getIntelligentList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    changeSNCode() {
      this.listQuery.serialNumber = this.listQuery.serialNumber.replace(/:/g, '')
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getIntelligentStatus(row) {
      return converseEnToCn(this.intelligentStatus, row.status)
    },
    getDeviceSource(row) {
      return converseEnToCnList(this.deviceSources, row.deviceSource)
    },
    // 获取3年后，格式为YYYY-MM-DD
    get3YearLater() {
      return parseTime(new Date(new Date().getTime() + 3 * 365 * 24 * 60 * 60 * 1000), '{y}-{m}-{d}')
    },

    handleUpdate(row) { // 修改
      this.flages = 0
      this.IntelligentAddPop = true
      this.IntelligentForm = JSON.parse(JSON.stringify(row))
      this.intelligentsId = row.id
      this.getSource('device_source_type')
    },
    handleDelet(row) {
      this.$confirm('确定要删除该条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delIntelligent(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.$emit('intelligentResh')
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    handleBatchDelet() {
      this.$confirm('确定要删除这些数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDevices(this.selectedBoxIds).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            // 由于全选删除会导致页码前进一位，但是没有数据，所以全选删除调到第一页
            this.listQuery.pageIndex = 1;
            this.$emit('intelligentResh');
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    handleAdd() { // 新增
      this.flages = 1
      if (!this.IntelligentForm) {
        this.IntelligentForm = {}
      }
      this.IntelligentForm.expiryTime = this.get3YearLater()
    },
    cancelIntelligent() {
      this.IntelligentAddPop = false
      this.IntelligentForm = {}
      if (this.$refs.intelligentForms) {
        this.$refs.intelligentForms.clearValidate()
      }
    },
    custormIntelligent() {
      this.$refs.intelligentForms.validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.IntelligentForm, { schoolId: this.mainSchoolId })
          data.serialNumberList = data.serialNumber.split("\n").filter(item => item.trim() !== "")
          delete data.serialNumber
          addlIntelligent(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.IntelligentForm.serialNumber = ''
              this.getList()
              this.$emit('intelligentResh')
              this.$refs.intelligentForms.clearValidate()
            }
          }).catch(() => {

          })
        }
        else {
          return false
        }
      })
    },
    editIntelligent() {
      this.$refs.intelligentForms.validate((valid) => {
        if (valid && this.intelligentsId) {
          const data = Object.assign({}, this.IntelligentForm, { id: this.intelligentsId, schoolId: this.mainSchoolId })
          updateIntelligent(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.IntelligentAddPop = false
              this.IntelligentForm = {}
              this.intelligentsId = ''
              this.getList()
              this.$emit('intelligentResh')
              this.$refs.intelligentForms.clearValidate()
            }
          }).catch(() => {

          })
        }
        else {
          return false
        }
      })
    },
    getSource(str) {
      getDataCode(str).then(res => {
        if (res.code === '000000') {
          const souceArr = []
          res.data && res.data.length > 0 ? res.data.forEach(item => {


            const sourceObj = {}
            sourceObj['id'] = item.code
            sourceObj['itemName'] = item.name
            souceArr.push(sourceObj)
          }) : []
          this.deviceSources = souceArr || []

        }
      }).catch(() => {

      })
    }
  }
}
</script>
<style scoped>
.syncBox{
  padding: 10px 20px;
  background: #f5f5f5;
  display: flex;
  align-items: baseline;
  label{
    margin: 0 10px;
  }
}
</style>
