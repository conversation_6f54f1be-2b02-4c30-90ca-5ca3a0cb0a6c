<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
              v-model.number="listQuery.id"
              placeholder="商品编码"
              class="filter-item"
              style="width: 150px;"
              type="number"
              @keyup.enter.native="handleFilter"
      />
      <el-input
              v-model="listQuery.productName"
              placeholder="商品名称"
              class="filter-item"
              style="width: 150px;"
              @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.classTypeId" placeholder="适用班型" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in classList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="产品线" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.productTypeId" placeholder="计费规则" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in productTypeList" :key="item.id" :label="item.typeName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="状态" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
              v-model="followDate"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="过期开始时间"
              end-placeholder="过期结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['goods:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增
      </el-button>
    </div>
    <el-table
            ref="assignTab"
            v-loading="listLoading"
            :data="list"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
    >
      <el-table-column label="商品编号" show-overflow-tooltip prop="id" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </el-table-column>
      <af-table-column label="商品名称" prop="productName" show-overflow-tooltip />
      <af-table-column label="适用班型/科目" width="250px">
        <template slot-scope="scope">
          <p v-if="scope.row.productTypeId===1&&scope.row.useTargetNames&&scope.row.useTargetNames.length>0">
            <span v-for="(item,index) in scope.row.useTargetNames" :key="index" class="subjects">{{ item }}</span>
          </p>
          <p v-else>
            <span><em v-if="scope.row.classTypeName">{{ scope.row.classTypeName }}</em><em
                    v-if="scope.row.classTypeName&&scope.row.subjectName">-</em><em
                    v-if="scope.row.subjectName">{{ scope.row.subjectName }}</em></span>
          </p>
        </template>
      </af-table-column>
      <af-table-column label="产品线" prop="clientName" show-overflow-tooltip />
      <af-table-column label="计费规则" prop="productTypeName" show-overflow-tooltip />
      <el-table-column label="按科计费" prop="openSubject" width="100px" align="center" >
      <!--加入说明表头的说明，按科计费是指开通后，校区每次为学生开通一个科目，将会扣除1剩余科目数-->
        <template slot="header" slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="按科计费开通后，校区每次为学生开通一个科目，将会扣除 1 剩余科目数，如果剩余科目数为0，则无法开通科目，请谨慎操作" placement="top">
            <i class="el-icon-question">科目计费</i>
          </el-tooltip>
        </template>
        <!--可操作switch-->
        <template slot-scope="scope">
          <el-switch
                  active-color="#13ce66"
                  :value="Number( scope.row.openSubject )"
                  :active-value="1"
                  :inactive-value="0"
                  v-if="scope.row.productTypeId===1"
                  @change="handleChange(scope.row)"
          />
        </template>
      </el-table-column>
      <af-table-column label="单价" prop="unitPrice" />
      <af-table-column label="流量包价格" prop="flowPackagePrice" />
      <af-table-column label="计费排序" prop="sort" />
      <af-table-column label="状态" prop="status" :formatter="getStatus" width="100px" />
      <af-table-column label="过期时间" prop="expireTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['goods:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-if="row.productTypeName==='流量包'||row.productTypeName==='单一科目'||row.productTypeName==='班型打包'"
                     v-permission="['goods:package']" type="primary" size="mini" @click="handleSet(row)">套餐设置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
    />
    <package-list ref="package" @addPackageList="getList" />
    <package-set ref="packageSet" @packSets="getList" />
  </div>
</template>
<script>
import PackageList from './components/packagePop'
import PackageSet from './components/packageSet'
import Pagination from '@/components/Pagination'
import { clientCode, getAllClassType } from '@/api/classType'
import { getPackage, getGoodsList, productTypes, openSubject } from '@/api/goods'
import {
  statusList,
  converseEnToCn
} from '@/utils/field-conver'
// import ScrollPane from '../../layout/components/TagsView/ScrollPane'
export default {
  name: 'Goods',
  components: {
    Pagination,
    PackageList,
    PackageSet
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      classTypeId: 0,
      statusList: statusList,
      productTypeList: [],
      classList: [],
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
      this.getList()
      this.getCode()
      this.productTypes()
      this.getAllClassType()
    })
  },
  methods: {
    handleChange(row) {

      let param = {
        id: row.id,
        openSubject: row.openSubject === 1 ? 0 : 1
      }

      openSubject(param).then(res => {
        //操作成功
        if (res.code === '000000') {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.getList()
        }
        else {
          this.$message({
            message: res.msg,
          })
        }
      })
    },
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 1) || []
      })
    },
    getAllClassType() {
      getAllClassType().then(res => {
        this.classList = res.data || []
      })
    },
    productTypes() { // 所有商品类型
      productTypes().then(res => {
        if (res.code === '000000') {
          this.productTypeList = res.data
        }
      })
    },
    getPackage(classTypeId, subjectId) {
      getPackage(classTypeId, subjectId).then(res => {
        if (res.code === '000000') {
          const arr = []
          for (let i = 0; i < res.data.length; i++) {
            const obj = {}
            obj.id = res.data[i].id
            obj.title = `${ res.data[i].unitPrice }元,${ res.data[i].productName }`
            arr.push(obj)
          }
          this.packageList = arr || []
        }
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign({}, {
        pageIndex: that.listQuery.pageIndex,
        pageSize: that.listQuery.pageSize
      }, this.listQuery, {
        expireStartTime: this.followDate[0] ? this.followDate[0] : '',
        expireEndTime: this.followDate[1] ? this.followDate[1] : ''
      })
      await getGoodsList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.package.packagePop = true
      this.$refs.package.packageTitle = '新增商品'
      this.$refs.package.isEdit = false
      this.$refs.package.flags = 1
      this.$refs.package.allClassType = []
    },
    handleUpdate(row) {
      this.$refs.package.packagePop = true
      this.$refs.package.packageTitle = `修改${ row.productName }`
      this.$refs.package.getProDetail(row.id)
      this.$refs.package.isEdit = false
      this.$refs.package.flags = 0
    },
    getStatus(row) {
      return converseEnToCn(this.statusList, row.status)
    },
    handleSet(row) {
      // row.productTypeName==='流量包'||row.productTypeName==='单一科目'||row.productTypeName==='班型打包'
      this.$refs.packageSet.packageSetPop = true
      if (row.productTypeName === '流量包') {
        this.$refs.packageSet.getPackageList(row.id)
      }
      else if (row.productTypeName === '单一科目' || row.productTypeName === '班型打包') {
        this.$refs.packageSet.getSinglePackage(row.id)
        this.$refs.packageSet.getPackage(row.classTypeId)
      }
      this.$refs.packageSet.productTypeName = row.productTypeName
      this.$refs.packageSet.productId = row.id
      this.$refs.packageSet.classTypeId = row.classTypeId
    },
    getDetail(row) { // 获取列表详情
      this.$refs.package.packagePop = true
      this.$refs.package.packageTitle = `${ row.productName }`
      this.$refs.package.getProDetail(row.id)
      this.$refs.package.isEdit = true
    }
  }
}
</script>
<style scoped>
.codes {
    font-weight: bold;
    color: #0a76a4;
}

.subjects {
}
</style>
