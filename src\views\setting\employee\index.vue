<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.searchField" placeholder="手机号/员工姓名" clearable class="filter-item" style="width: 160px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.deptId" placeholder="部门" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in parentDepartmentList"
          :key="item.id"
          :label="item.departmentName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.roleId" placeholder="角色" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in roleLists"
          :key="item.id"
          :label="item.roleName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.dimission" placeholder="是否在职" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in dimissionList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="entryTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="入职开始日期"
        end-placeholder="入职结束日期"
        unlink-panels
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:employee:create']" class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="系统用户名" prop="userName" />
      <af-table-column label="姓名" prop="realName" />
      <af-table-column label="员工档案编号" prop="userSn" show-overflow-tooltip />
      <af-table-column label="手机号" prop="mobile" />
      <af-table-column label="部门" prop="deptName" />
      <af-table-column label="角色" prop="roleNames" />
      <af-table-column label="身份证" prop="idCard" />
      <af-table-column label="性别" prop="gender" :formatter="getGender" />
      <af-table-column label="入职日期" prop="joinDate" />
      <af-table-column label="是否离职" prop="dimission" :formatter="getDimission" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:employee:view']" type="primary" size="mini" @click="handleQuery(row)">
            查看
          </el-button>
          <el-button v-permission="['setting:employee:update']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-dropdown v-drop-down-show @command="handleCommand">
            <el-button type="primary" size="mini">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-permission="['setting:employee:authorize']" :command="beforeHandleCommand('a',row)">授权</el-dropdown-item>
              <el-dropdown-item v-permission="['setting:employee:delete']" :command="beforeHandleCommand('b',row)">删除</el-dropdown-item>
              <el-dropdown-item v-show="!row.dimission" v-permission="['setting:employee:quit']" :command="beforeHandleCommand('c',row)">离职</el-dropdown-item>
              <el-dropdown-item v-show="row.dimission" :command="beforeHandleCommand('d',row)">复职</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <el-dialog title="权限设置" :visible.sync="dialogRolePermission" width="30%" :close-on-click-modal="!dialogRolePermission">
      <el-form ref="form" :model="rolePermission" label-width="100px">
        <el-form-item label="授权角色：">
          <el-select v-model="rolePermission.roleIds" multiple filterable placeholder="请选择授权角色" class="width-100">
            <el-option
              v-for="item in rolePermission.roles"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRolePermission = false">取 消</el-button>
        <el-button type="primary" @click="updateEmployeePermission">确 定</el-button>
      </div>
    </el-dialog>
    <!--    <employee-detail ref="employeeDetail" :parentdepartmentlist="parentDepartmentList" @refresh="getList" />-->
  </div>
</template>

<script>
import { getEmployeeList, getEmployeePermission, getDepartmentList, setEmployeePermission, deleteEmployee, dimission, reinstated, roleList } from '@/api/system-setting'
import Pagination from '@/components/Pagination'
import { genderList, converseEnToCn, getMarriedStatus, getDimissionStatus } from '@/utils/field-conver'
export default {
  name: 'Employee',
  components: { Pagination },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        searchField: '',
        deptId: '',
        dimission: '',
        joinBegin: '',
        joinEnd: ''
      },
      total: 0,
      dialogRolePermission: false,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      parentDepartmentList: [],
      dimissionList: getDimissionStatus,
      entryTime: '',
      roleLists: []
    }
  },
  // destroyed() {
  //   sessionStorage.removeItem('detail')
  // },
  // mounted() {
  //   this.getSchoolWebModuleMessageListFunc()
  // },
  created() {
    this.listLoading = false
    this.getList()
    this.getDepartmentList()
    this.roleList()
  },
  methods: {
    // getSchoolWebModuleMessageListFunc() {
    //   if (sessionStorage.getItem('detail')) {
    //
    //     // 如果有这个就读取缓存里面的数据
    //     this.listQuery.pageIndex = Number(sessionStorage.getItem('currentPageFalgs'))
    //     this.getList()
    //   } else {
    //
    //     this.listQuery.pageIndex = 1
    //     this.getList()
    //     // 这个主要是从其他页面第一次进入列表页，清掉缓存里面的数据
    //     sessionStorage.removeItem('currentPageFalgs')
    //   }
    // },
    roleList() {
      roleList().then(res => {
        this.roleLists = res.data || []
      })
    },
    /**
     * 部门列表
     * */
    getDepartmentList(arg) {
      const that = this
      that.detail = {}
      getDepartmentList({ 'pageSize': 9999 }).then(res => {
        that.parentDepartmentList = res.data.records
        arg = res.data.records
        that.departmentDetail = true
      })
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      if (this.entryTime) {
        this.listQuery.joinBegin = this.entryTime[0]
        this.listQuery.joinEnd = this.entryTime[1]
      } else {
        this.listQuery.joinBegin = ''
        this.listQuery.joinEnd = ''
      }
      this.getList()
    },
    /**
     * 新增员工
     * */
    handleAdd() {
      // this.$refs.employeeDetail.addDetail('新增')
      this.$router.push({ name: 'EmployeeDetail', params: { method: 1, parentDepartmentList: this.parentDepartmentList }})
    },
    /**
     * 更多按钮
     * */
    handleCommand(command) {
      switch (command.type) {
        case 'a':
          // 授权
          this.handleSetEmployeePermission(command.row.id)
          break
        case 'b':
          // 删除
          this.handleDelete(command.row.id)
          break
        case 'c':
          // 离职
          this.dimission(command.row.id)
          break
        case 'd':
          // 复职
          this.reinstated(command.row.id)
          break
      }
    },
    beforeHandleCommand(type, row) {
      return {
        'type': type,
        'row': row
      }
    },
    /**
     * 查看员工详情
     * */
    handleQuery(row) {
      // this.$refs.employeeDetail.getDetail(row.id, false)
      this.$router.push({ name: 'EmployeeDetail', params: { method: 0, parentDepartmentList: this.parentDepartmentList, id: row.id }})
    },
    getList() {
      const that = this
      const params = Object.assign({}, that.listQuery)
      getEmployeeList(params).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    /**
     * 修改员工信息
     * */
    handleUpdate(row) {
      this.$router.push({ name: 'EmployeeDetail', params: { method: 2, parentDepartmentList: this.parentDepartmentList, id: row.id }})
    },
    /**
     * 设置员工权限
     **/
    handleSetEmployeePermission(userId) {
      this.dialogRolePermission = true
      this.getEmployeePermission(userId)
    },
    /**
     * 删除员工
     * */
    handleDelete(row) {
      this.$confirm('', '确定删除？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }).then(() => {
        deleteEmployee(row).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(action => {

      })
    },
    /**
     * 离职员工
     * */
    dimission(row) {
      dimission(row).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        }
      })
    },
    /**
     * 复职员工
     * */
    reinstated(row) {
      reinstated(row).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        }
      })
    },
    /**
     * 获取员工的权限
     * id : 员工id
     */
    getEmployeePermission(id) {
      const that = this
      that.roleId = id
      getEmployeePermission(id).then(res => {
        that.rolePermission = res.data
        that.dialogRolePermission = true
      })
    },
    /**
     * 更新员工信息
     */
    updateEmployeePermission() {
      const that = this
      const params = this.rolePermission
      that.$confirm('是否确认修改该角色权限, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setEmployeePermission(params).then(res => {
          that.$message({
            type: 'success',
            message: '修改成功!'
          })
          that.dialogRolePermission = false
          that.getList()
        })
      })
    },
    /**
     * 转换性别、结婚状态
     * @param row
     * @returns {*}
     */
    getGender(row) {
      return converseEnToCn(genderList, row.gender)
    },
    getMarried(row) {
      return converseEnToCn(getMarriedStatus, row.isMarried)
    },
    getDimission(row) {
      return converseEnToCn(getDimissionStatus, row.dimission)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    }
  }
}
</script>

<style scoped>

</style>
