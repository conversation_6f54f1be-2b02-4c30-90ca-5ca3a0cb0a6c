<template>
  <div class="app-container">
    <div class="filter-container">
      <!-- <el-select v-model="listQuery.joinStatusList" placeholder="加盟状态" filterable multiple class="filter-item" style="width: 200px;" clearable>
        <el-option v-for="item in schoolJoinStatusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select> -->
      <el-input class="filter-item franchisePeriodCls"   v-model="listQuery.franchisePeriod" clearable  :maxlength="2" style="width: 120px;" placeholder="加盟年限">
        <template #suffix>
          <span>月</span>
        </template>
      </el-input>
      <area-picker :area-list="areaList" :level="'3'" :area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-select v-model="listQuery.areaSingle" placeholder="区域类型" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option label="单点" :value="0" />
        <el-option label="独家" :value="1" />
      </el-select>
      <el-select v-model="listQuery.customerLevel" placeholder="客户级别" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in customerLevelList"
          :key="item.key"
          :label="item.value"
          :value="item.key"
        />
      </el-select>
      <el-cascader
        placeholder="生命周期"
        v-model="listQuery.lifeCycle"
        :options="lifeCycleList"
        :props="{emitPath: false}"
        clearable></el-cascader>
      <el-date-picker
        style="width: 150px"
        :editable="false"
        v-model="listQuery.deadlineDate"
        type="month"
        value-format="yyyy-MM"
        :clearable="false"
        placeholder="启动期截至月">
      </el-date-picker>
      <el-date-picker
        v-model="schoolDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="校区到期开始时间"
        end-placeholder="校区到期结束时间"
      />
      <el-date-picker
        v-model="contractDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="合同到期开始时间"
        end-placeholder="合同到期结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
    </div>
    <div class="nums-tips">
      <div class="primary">总家数:
        <el-tag type="primary" size="mini">{{ institutionNums.All }}</el-tag>
      </div>
      <div class="success">启动家数:
        <el-tag type="success" size="mini">{{ institutionNums.start }}</el-tag>
      </div>
      <div class="primary">年总家数:
        <el-tag type="primary" size="mini">{{ institutionNums.YearAll }}</el-tag>
      </div>
      <div class="success">年启动家数:
        <el-tag type="success" size="mini">{{ institutionNums.YearStart }}</el-tag>
      </div>
    </div>
    <el-table v-loading="listLoading" :data="list" border fit stripe highlight-current-row>
      <af-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="校区项目编号" prop="institutionCode" />
      <af-table-column label="关联机构Id" prop="agencyId" show-overflow-tooltip />
      <af-table-column label="客户编号" prop="orderSn" width="80">
        <template slot-scope="{ row }">
          <router-link :to="{ path: '/customer/detail/'+row.clueId, query: {title:'客户-'+row.clueName}}"
                       class="link-type">
            <span>{{ row.clueCode }}</span>
          </router-link>
        </template>
      </af-table-column>
      <af-table-column label="客户级别" prop="customerLevel">
        <template slot-scope="{row}">
          {{
            (customerLevelList.find(item => item.key === row.customerLevel) || {}).value || '--'
          }}
        </template>
      </af-table-column>
      <af-table-column label="生命周期" prop="lifeCycle" >
        <template slot-scope="{ row }">
          {{row.lifeCycle ? getLifeCycleTitle(row.lifeCycle) : '--'}}
        </template>
      </af-table-column>
      <!-- <af-table-column label="(分)" prop="kehaoNumber" /> -->
     <af-table-column label="启动期课耗时" prop="startKehaoNumber" >
        <template slot-scope="{ row }">
          {{formatMinutes(row.startKehaoNumber)}}
        </template>
      </af-table-column>
      <af-table-column label="加盟年限(月)" prop="franchisePeriod" />
      <af-table-column label="学员数量" prop="studentNum" />
      <af-table-column label="盒子数量" prop="terminalNum" />
      <af-table-column label="校区编号">
        <template slot-scope="{ row }">
          <span class="link-type" @click="handleQuerySchool(row)">{{ row.schoolCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="客户名称" prop="clueName" show-overflow-tooltip />
      <af-table-column label="校区名称" prop="schoolName" />
      <af-table-column label="签约区域" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}
        </template>
      </af-table-column>
      <af-table-column label="区域类型" prop="areaJoinType">
        <template slot-scope="{row}">
          {{ areaJoinTypeList[row.areaJoinType] || '--'   }}
        </template>
      </af-table-column>
      <!-- <af-table-column label="加盟项目" prop="projectName" /> -->
      <!--      <af-table-column label="加盟状态" prop="joinStatus" :formatter="getSchoolJoinStatus" />-->
      <el-table-column label="加盟状态" prop="joinStatus">
        <template slot-scope="{row}">
          <SchoolJoinStatusTag :status="row.joinStatus"></SchoolJoinStatusTag>
        </template>
      </el-table-column>
      <af-table-column label="校区到期时间">
        <template slot-scope="{row}">
          <div>{{ row.schoolEndDate || '--' }}</div>
        </template>
      </af-table-column>
      <af-table-column label="合同到期时间">
        <template slot-scope="{row}">
          <div>{{ row.endDate || '--' }}</div>
        </template>
      </af-table-column>
      <!--      <af-table-column label="关联状态" prop="relationStatus" :formatter="getRelation" />-->
      <!--      <el-table-column label="订单状态" prop="status" :formatter="getOrderStatus" width="150" />-->
      <af-table-column label="创建时间">
        <template slot-scope="{row}">
          <div>{{ row.createTime || '--' }}</div>
        </template>
      </af-table-column>
    </el-table>
    <pagination
      v-if="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      layout="total, sizes, prev, pager, next"
      @pagination="getList"
    />
    <create-new-school ref="createSchoolDialog" :type="schoolOption" :customer-id="currentCustomerId" :title="createSchoolTitle" />
  </div>
</template>
<script>
import { pageByStart, pageByStartTj} from '@/api/school-project'
import {getAllProject} from '@/api/common'
import Pagination from '@/components/Pagination'
import {customerLevelList, institutionTypes, lifeCycleList, schoolJoinStatusList} from '@/utils/field-conver'
import CreateNewSchool from '@/views/customer/componets/createSchool.vue'
import AreaPicker from '@/components/area-picker'
import SchoolJoinStatusTag from '@/components/StatusTag/SchoolJoinStatusTag.vue'
import CustomerDetailLink from '@/components/link/CustomerDetailLink.vue'
import Collapse from '@/components/collapse/Collapse.vue'
import {cloneDeep} from 'lodash'

export default {
  name: 'SchoolProject',
  components: {
    Collapse,
    CustomerDetailLink,
    SchoolJoinStatusTag,
    Pagination,
    AreaPicker,
    CreateNewSchool,
  },
  data() {
    return {
      areaJoinTypeList: ['区县单点', '区县独家', '乡镇独家', '乡镇单点'], // 区域类型列表
      customerLevelList,
      lifeCycleList,
      listLoading: false,
      list: [],
      schoolJoinStatusList: schoolJoinStatusList,
      projectList: [],
      listQuery: {
        // deadlineDate: new Date(),
        deadlineDate: this.$moment().format('YYYY-MM'),
        pageIndex: 1,
        pageSize: 10
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      total: 0,
      schoolOption: '',
      createSchoolTitle: '',
      currentCustomerId: '', // 当前客户的id
      institutionTypes: institutionTypes,
      schoolDate: [],
      contractDate: [],
      institutionNums: {}
    }
  },
  created() {
    this.initData()
  },
  computed: {
    $_isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  },
  methods: {
    formatMinutes(minutes) {
      if (minutes === null || minutes === undefined || isNaN(minutes)) {
        return '--';
      }
      const h = Math.floor(minutes / 60);
      const m = minutes % 60;
      return `${h}时${m}分`;
    },
    initData() {
      this.getList()
      this.getProject()
      this.getCurrentDay()
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        deadlineDate: this.$moment().format('YYYY-MM')
      }
      this.schoolDate = []
      this.contractDate = []
      this.areaList = {}
      this.getList()
    },
    getList() {
      const that = this
      const params = Object.assign(cloneDeep(that.listQuery), that.areaList, {
        schoolStartDate: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[0] : '',
        schoolEndDate: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[1] : '',
        contractStartDate: that.contractDate && that.contractDate.length > 0 ? that.contractDate[0] : '',
        contractEndDate: that.contractDate && that.contractDate.length > 0 ? that.contractDate[1] : ''
      })
      that.listLoading = true
      if(params.hasOwnProperty('joinStatusList')){
        params.joinStatusList = params.joinStatusList.join(',')
      }
      params.deadlineDate =  params.deadlineDate + '-01';
      this.getpageByStartTj(params);
      pageByStart(params).then(res => {
        that.listLoading = false
        const list = res.data.records && res.data.records.length > 0 ? res.data.records : []
        that.total = res.data.total
        const projectArr = []
        const currentDays = that.getCurrentDay()
        list.forEach(item => { // institutionCode
          const projects = {}
          projects['id'] = item.id
          projects['customerLevel'] = item.customerLevel
          projects['institutionCode'] = item.institutionCode
          projects['agencyId'] = item.agencyId
          projects['agencyFlag'] = item.agencyFlag
          projects['agencyMobile'] = item.agencyMobile
          projects['areaJoinType'] = item.areaJoinType
          projects['lifeCycle'] = item.lifeCycle
          projects['startKehaoNumber'] = item.startKehaoNumber
          projects['terminalNum'] = item.terminalNum
          projects['studentNum'] = item.studentNum
          projects['franchisePeriod'] = item.franchisePeriod
          projects['areaName'] = item.areaName
          projects['cityName'] = item.cityName
          projects['clueCode'] = item.clueCode
          projects['clueId'] = item.clueId
          projects['clueName'] = item.clueName
          projects['countyName'] = item.countyName
          projects['createTime'] = item.createTime
          projects['endDate'] = item.endDate ? item.endDate : null
          projects['institutionName'] = item.institutionName
          projects['institutionType'] = item.institutionType
          projects['joinStatus'] = item.joinStatus
          projects['projectName'] = item.projectName
          projects['provinceName'] = item.provinceName
          projects['relationStatus'] = item.relationStatus
          projects['schoolAccount'] = item.schoolAccount
          projects['schoolCode'] = item.schoolCode
          projects['schoolEndDate'] = item.schoolEndDate
          projects['schoolId'] = item.schoolId
          projects['schoolName'] = item.schoolName
          projects['orderId'] = item.orderId
          projects['showRemind'] = item.schoolEndDate !== null ? that.getDaysBetween(item.schoolEndDate, currentDays) : 0
          projects['isSupplementContract'] = item.isSupplementContract
          projectArr.push(projects)
        })
        that.list = projectArr
      }).catch(error => {
        that.listLoading = false
      })
    },
    // 查询启动期总家数和启动家数
    getpageByStartTj (params) {
      pageByStartTj(params).then(res => {
        this.listLoading = false
        this.institutionNums = res.data && res.data.data ? res.data.data : {}
      }).catch(error => {
        this.listLoading = false
      })
    },
    /**
     * 查看校区
     * */
    handleQuerySchool(row) {
      this.schoolOption = 'query'
      this.createSchoolTitle = '校区详情'
      this.$refs.createSchoolDialog.getSchool(row.schoolId)
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
       // 获取生命周期名字
    getLifeCycleTitle(lifeCycle) {
      const values = lifeCycle.split(',');
      // 收集所有 value-label 映射（包括 children）
      const map = {};
      this.lifeCycleList.forEach(item => {
        map[item.value] = item.label;
        if (item.children) {
          item.children.forEach(child => {
            map[child.value] = child.label;
          });
        }
      });
      const labels = values.map(val => map[val]).filter(Boolean);
      return labels.join('、');
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    getCurrentDay() {
      const days = new Date()
      const year = days.getFullYear()
      const month = days.getMonth() + 1
      const date = days.getDate()
      return `${ year }-${ month < 10 ? '0' + month : month }-${ date < 10 ? '0' + date : date }`
    },
    getDaysBetween(sDate1, sDate2) {
      var dateSpan,
        iDays
      sDate1 = Date.parse(sDate1)
      sDate2 = Date.parse(sDate2)
      dateSpan = sDate2 - sDate1
      dateSpan = Math.abs(dateSpan)
      iDays = Math.floor(dateSpan / (24 * 3600 * 1000))
      return iDays
    },
    refreshData() {
      this.$forceUpdate()
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .el-select__tags{
  .el-select__input{
    background: transparent;
    border: none;
  }
}
/deep/ .franchisePeriodCls{
  .el-input__suffix{
    display: flex;
    align-items: center;
  }
}
.nums-tips {
  margin: 0 0 10px;
  display: flex;

  div {
    font-size: 14px;
    margin-right: 15px;

    span {
      margin-left: 5px;
      cursor: pointer;
    }

    &.primary {
      color: #1890ff;
    }

    &.success {
      color: #13ce66;
    }

    &.danger {
      color: #ff4949;
    }
  }
}
</style>
