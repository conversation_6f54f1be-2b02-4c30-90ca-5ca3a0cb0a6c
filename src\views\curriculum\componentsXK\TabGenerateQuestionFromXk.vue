<template>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-tabs tab-position="left" type="border-card" v-model="tab">
        <el-tab-pane label="按知识点" name="1">
          <div class="flex-warp flex-item-center">
            <span class="label must">科目</span>
            <span><SubjectFromXKSelect v-model="courseInfo.xkwSubjectId" @change="setTypeIds"
                                                                       :product-code="courseBaseInfo.productCode"></SubjectFromXKSelect></span>
            <span class="label must">知识点</span><span><KPointFromXKSelect v-model="courseInfo.kpointIds"
                                                                            :subject-id="courseInfo.xkwSubjectId"
                                                                            @change="setPoint"></KPointFromXKSelect></span>
          </div>
            <div class="flex-warp flex-item-center mt10">

            <span class="label">试卷类型</span><span><PaperTypeFromXKSelect v-model="courseInfo.paperTypeIds"
                                                                    :product-code="courseBaseInfo.productCode"></PaperTypeFromXKSelect></span>
            <span class="label">难度</span><span><QuestionDifficultyFromXKSelect
            v-model="courseInfo.difficultyLevels"></QuestionDifficultyFromXKSelect></span>
            <span class="label">年份</span><span><YearSelect v-model="courseInfo.year"></YearSelect></span><span class="label">
            <el-button type="primary"
                       @click="getQuestion">生成题目</el-button></span>
          </div>
        </el-tab-pane>
        <el-tab-pane label="按教材课程" name="2">
            <div class="flex-warp flex-item-center">
              <span class="label must">科目</span>
              <span>
                <SubjectFromXKSelect
                  v-model="courseInfo.xkwSubjectId"
                  @change="setTypeIds"
                  :product-code="courseBaseInfo.productCode"></SubjectFromXKSelect>
              </span>
              <span class="label must" v-if="courseBaseInfo.productCode===200">学制</span>
              <span v-if="courseBaseInfo.productCode===200">
                <EduSystemFromXKSelect v-model="courseInfo.division"
                                       :product-code="courseBaseInfo.productCode"></EduSystemFromXKSelect>
              </span>
              <span class="label must">年级</span>
              <span>
                  <GradeFromXKSelect v-model="courseInfo.gradeId"
                                     :division="courseInfo.division"
                                     :product-code="courseBaseInfo.productCode"></GradeFromXKSelect>
              </span>
              <span class="label must">版本</span>
              <span><TextBookVersionFromXKSelect v-model="courseInfo.textBookVersionId"
                                                 :product-code="courseBaseInfo.productCode"
                                                 :division="courseInfo.division"
                                                 :xkw-subject-id="courseInfo.xkwSubjectId"></TextBookVersionFromXKSelect>
              </span>
              <span class="label must">教材</span>
              <span>
                <TextBookFromXKSelect
                  v-model="courseInfo.textbookId"
                  :product-code="courseBaseInfo.productCode"
                                      :text-book-version-id="courseInfo.textBookVersionId"
                                      :xkw-subject-id="courseInfo.xkwSubjectId"
                                      :grade-id="courseInfo.gradeId"></TextBookFromXKSelect>
              </span>
              <span class="label must">课程</span>
              <span>
                <CatalogFromXKSelect v-model="courseInfo.catalogIdsTmp" :textbook-id="courseInfo.textbookId"></CatalogFromXKSelect>
              </span>
            </div>
            <div class="flex-warp flex-item-center mt10">

              <span class="label">试卷类型</span>
              <span>
                <PaperTypeFromXKSelect v-model="courseInfo.paperTypeIds"
                                           :product-code="courseBaseInfo.productCode"></PaperTypeFromXKSelect>
            </span>
              <span class="label">难度</span>
              <span><QuestionDifficultyFromXKSelect
                v-model="courseInfo.difficultyLevels"></QuestionDifficultyFromXKSelect>
              </span>
              <span class="label">年份</span>
              <span>
                <YearSelect v-model="courseInfo.year"></YearSelect>
              </span>
              <span class="label"><el-button type="primary" @click="getQuestion">生成题目</el-button></span>
            </div>
        </el-tab-pane>
      </el-tabs>
    </el-col>
    <QuestionViewDialog v-if="dialogOpen" :questions="questions" :query-param="xkQueryParams" @close="closeDialog" @ok="submitQuestion"></QuestionViewDialog>
  </el-row>
</template>
<script>import KPointFromXKSelect from '@/views/curriculum/componentsXK/KPointFromXKSelect.vue'
import PaperTypeFromXKSelect from '@/views/curriculum/componentsXK/PaperTypeFromXKSelect.vue'
import QuestionDifficultyFromXKSelect from '@/views/curriculum/componentsXK/QuestionDifficultyFromXKSelect.vue'
import SubjectFromXKSelect from '@/views/curriculum/componentsXK/SubjectFromXKSelect.vue'
import YearSelect from '@/views/curriculum/componentsXK/YearSelect.vue'
import { getQuestionFromXK, saveQuestionFromXK } from '@/api/exercises'
import QuestionViewDialog from '@/views/curriculum/componentsXK/QuestionViewDialog.vue'
import TextBookVersionFromXKSelect from '@/views/curriculum/componentsXK/TextBookVersionFromXKSelect.vue'
import EduSystemFromXKSelect from '@/views/curriculum/componentsXK/EduSystemFromXKSelect.vue'
import GradeFromXKSelect from '@/views/curriculum/componentsXK/GradeFromXKSelect.vue'
import TextBookFromXKSelect from '@/views/curriculum/componentsXK/TextBookFromXKSelect.vue'
import CatalogFromXKSelect from '@/views/curriculum/componentsXK/CatalogFromXKSelect.vue'

export default {
  name: 'GenerateQuestionFromXk',
  components: {
    CatalogFromXKSelect,
    TextBookFromXKSelect,
    GradeFromXKSelect,
    EduSystemFromXKSelect,
    TextBookVersionFromXKSelect,
    QuestionViewDialog,
    YearSelect,
    KPointFromXKSelect,
    PaperTypeFromXKSelect,
    QuestionDifficultyFromXKSelect,
    SubjectFromXKSelect,
  },
  props: { courseBaseInfo: {} },
  data() {
    return {
      tab: '1',
      courseInfo: {
        xkwSubjectId: null /*科目ID*/,
        paperTypeIds: [] /*试卷类型 []*/,
        difficultyLevels: null /*难度 []*/,
        kpointIds: [] /*知识点 []*/,
        gradeId: null, //年级ID
        division: 'G63',//学制ID 仅对初中有效
        textBookVersionId: null,//教材版本ID
        textbookId: null,//教材ID
        catalogIdsTmp: [],//章节ID
        noneChildKpointIds: [] /*不包含子节点的知识点ID集合 []*/,
        typeIds: [] /*试题类型ID [] ,来自于 xkwSubjectId 中的对象*/,
        year: null /*试题年份*/,
      },
      dialogOpen: false,
      questions: [],
      xkQueryParams:null
    }
  },
  methods: {
    getQuestion() {/* 科目和知识点必填 */
      if (!this.courseInfo.xkwSubjectId || this.courseInfo.xkwSubjectId === '') {
        this.$message({ type: 'warning', message: '请选择学科' })
        return
      }
      let param={}
      if(this.tab==='1'){
        if (!this.courseInfo.kpointIds || this.courseInfo.kpointIds.length === 0) {
          this.$message({ type: 'warning', message: '请选择知识点' })
          return
        }
        param = {
          ...this.courseInfo, ...this.courseBaseInfo,
          kpointIds: this.courseInfo.noneChildKpointIds,
          homeworkId: this.courseBaseInfo.id,
        }
      }else{
        if (!this.courseInfo.catalogIdsTmp || this.courseInfo.catalogIdsTmp.length === 0) {
          this.$message({ type: 'warning', message: '请选择章节' })
          return
        }
        param = {
          ...this.courseInfo, ...this.courseBaseInfo,
          catalogIds: this.courseInfo.catalogIdsTmp,
          homeworkId: this.courseBaseInfo.id,
        }
        delete param.kpointIds
      }

      getQuestionFromXK(param).then((res) => {
          if (res.code === '000000') {
            this.questions = res.data
            if(this.questions.length === 0)
            {
              this.$message({ type: 'warning', message: '未获取到相关试题！' })
              return
            }
            this.xkQueryParams = param
            this.dialogOpen = true
          }
        }
      )
    },
    closeDialog() {
      this.xkQueryParams = null
      this.dialogOpen = false
    },
    submitQuestion(questions) {
      const data = { courseId: this.courseBaseInfo.courseId, homeworkId: this.courseBaseInfo.id, questionList: questions }
      saveQuestionFromXK(data).then((res) => {
          if (res.code === '000000') {
            this.$message({ type: 'success', message: '保存成功' })
            this.$emit('refresh')
            this.dialogOpen = false
          }
        }
      )
    },
    setTypeIds(val, selectedOption) {
      this.courseInfo.xkwSubjectId = val
      this.courseInfo.typeIds = selectedOption.questionType
      /* 如果typeIds数组中包含3405，将对应的项改成340501 */
      // if (this.courseInfo.typeIds.includes(3405)) {
      //   const index = this.courseInfo.typeIds.indexOf(3405)
      //   this.courseInfo.typeIds[index] = 340501
      // }
      // if (this.courseInfo.typeIds.includes(3401)) {
      //   const index = this.courseInfo.typeIds.indexOf(3401)
      //   this.courseInfo.typeIds[index] = 340101
      // } /* 如果 typeIds 中有重复的项目，则删除重复项 */
      this.courseInfo.typeIds = [...new Set(this.courseInfo.typeIds)]
    },
    setPoint(val, selectedNodes) {
      this.courseInfo.kpointIds = val
      /*有子节点的不能传递，在查询时会出现问题，全部过滤掉*/
      const noneChildOptions = selectedNodes.filter((node) => !node.children || node.children.length === 0)
      this.courseInfo.noneChildKpointIds = noneChildOptions.map((node) => Number(node.id))
    },
  },
}</script>
<style scoped>/deep/ .el-tabs--border-card > .el-tabs__content {
  padding: 15px 0;
}

.label {
  margin-left: 5px;
  min-width: 70px;
  text-align: right;
  padding-right: 5px;
  font-size: 14px;
}

.must::before {
  content: "*";
  color: red;
  font-size: 20px;
  vertical-align: middle;
  line-height: 1;
  top: 2px;
  position: relative;
}
</style>
