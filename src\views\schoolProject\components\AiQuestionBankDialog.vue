<template>
    <el-dialog title="AI题库到期时间" :visible.sync="dialogVisible" width="500px" @closed="closed" destroy-on-close>
        <el-form label-width="120px" :model="form" :rules="rules" ref="ruleForm">
            <el-form-item label="状态：">
                <el-switch v-model="form.openQuestionBank" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>
            <el-form-item v-if="form.openQuestionBank" label="到期时间：" prop="questionBankExpiryDate">
                <el-date-picker value-format="yyyy-MM-dd" v-model="form.questionBankExpiryDate" :clearable="false"></el-date-picker>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { setAiQuestionBank } from '@/api/schoolCampus'
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                agencyId: '',
                openQuestionBank: false,
                questionBankExpiryDate: ''
            },
            rules: {
                questionBankExpiryDate: [{ required: true, message: '请选择到期时间' }]
            }
        }
    },
    methods: {
        open(v) {
            Object.assign(this.form, v)
            this.dialogVisible = true
        },
        handleSubmit() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    setAiQuestionBank(this.form).then(res => {
                        if (res.code === '000000') {
                            this.$message.success('设置成功')
                            this.$emit('refresh', this.form)
                            this.dialogVisible = false
                        }
                    })
                }
            })
        },
        closed() {
            Object.assign(this.form, {
                agencyId: '',
                openQuestionBank: false,
                questionBankExpiryDate: ''
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.el-form .el-switch {
    >>>.el-switch__core {
        width: 40px;
        height: 20px;
    }

    >>>.el-switch__core::after {
        top: 1px;
        width: 16px;
        height: 16px;
    }

    >>>.el-switch__core::after {
        top: 1px;
        width: 16px;
        height: 16px;
    }

}

.el-form .el-switch.is-checked {
    >>>.el-switch__core::after {
        margin-left: -17px !important;
    }
}
</style>