<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.userName"
        placeholder="学生姓名"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.grade" placeholder="年级" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientId" placeholder="客户端" clearable class="filter-item" style="width: 140px;" filterable>
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-date-picker
        v-model="listQuery.startTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="反馈开始时间"
      />
      <el-date-picker
        v-model="listQuery.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="反馈结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="学生反馈编号" show-overflow-tooltip prop="feedbackId" width="120px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.feedbackId }}</a>
        </template>
      </el-table-column>
      <el-table-column label="学生姓名" prop="studentName" show-overflow-tooltip width="120px" />
      <el-table-column label="学生账号" prop="studentAccount" show-overflow-tooltip width="120px" />
      <el-table-column label="年级" prop="gradeName" width="100px" />
      <el-table-column label="客户端" prop="clientName" width="120px" />
      <el-table-column label="反馈内容" prop="content" show-overflow-tooltip />
      <el-table-column label="反馈时间" prop="feedbackTime" width="140px" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { clientCode, grades } from '@/api/classType'
import { getStudents } from '@/api/feedback'
export default {
  name: 'StudentFeedback',
  components: {
    Pagination
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      grades: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
      this.getGrades()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 2) || []
      })
    },
    getGrades() {
      grades().then(res => {
        if (res.code === '000000') {
          this.grades = res.data
        }
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)

      await getStudents(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getDetail(row) { // 获取列表详情

    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
