import request from '@/utils/request'
/**
 * 查询一级标题列表(无分页)
 * @param data
 */
export function levelOneList(data) {
  return request({
    url: 'stip/questions/classify/levelOne',
    method: 'GET',
    params: data
  })
}
/**
 * 查询二级标题列表(无分页)
 * @param data
 */
export function levelTwo(id) {
  return request({
    url: `stip/questions/classify/levelTwo?levelOneId=${id}`,
    method: 'GET'
  })
}
/**
 * 常见问题列表
 * @param data
 */
export function faqList(data) {
  return request({
    url: 'stip/questions/page',
    method: 'GET',
    params: data
  })
}
/**
 * 删除问题列表
 * @param data
 */
export function delQuestion(id) {
  return request({
    url: `stip/questions/${id}`,
    method: 'DELETE'
  })
}
/**
 * 新增问题
 * @param data
 */
export function addQuestion(data) {
  return request({
    url: `stip/questions`,
    method: 'POST',
    data: data
  })
}
/**
 * 问题详情
 * @param data
 */
export function questionIn(id) {
  return request({
    url: `stip/questions/${id}`,
    method: 'GET'
  })
}
/**
 * 修改问题
 * @param data
 */
export function editQuestionIn(data) {
  return request({
    url: `stip/questions`,
    method: 'PUT',
    data: data
  })
}
/**
 * 删除标题
 * @param data
 */
export function delTitle(id) {
  return request({
    url: `stip/questions/classify/${id}`,
    method: 'DELETE'
  })
}
/**
 * 新增标题
 * @param data
 */
export function addTitle(data) {
  return request({
    url: `stip/questions/classify`,
    method: 'POST',
    data: data
  })
}
/**
 * 标题详情
 * @param data
 */
export function titleIn(id) {
  return request({
    url: `stip/questions/classify/${id}`,
    method: 'GET'
  })
}
/**
 * 修改标题
 * @param data
 */
export function editTitle(data) {
  return request({
    url: `stip/questions/classify`,
    method: 'PUT',
    data: data
  })
}
