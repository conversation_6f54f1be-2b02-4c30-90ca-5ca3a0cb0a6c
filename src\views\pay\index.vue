<template>
  <div class="app-container pay">
    <div class="filter-container">
      <el-input
        v-model="listQuery.institutionCode"
        placeholder="校区项目编号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.clueCode"
        placeholder="客户编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderCode"
        placeholder="订单编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.customer"
        placeholder="客户名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.accountType" placeholder="账户类型" filterable class="filter-item" clearable style="width: 220px;" @change="getAccountType('account_type')">
        <el-option v-for="item in accountList" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.payItem" placeholder="支付类目" filterable class="filter-item" clearable style="width: 220px;" @change="getAccountType2('pay_item')">
        <el-option v-for="item in payList" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.payType" placeholder="打款方式" filterable class="filter-item" clearable style="width: 220px;" @change="getAccountType3('pay_type')">
        <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="payDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="打款开始日期"
        end-placeholder="打款结束日期"
        style="width:300px"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleExport">
        批量导出
      </el-button>
      <el-button
        v-waves
        v-permission="['customer:payRecord:create']"
        class="filter-item"
        type="primary"
        size="mini"
        @click="addPop"
      >
        新增
      </el-button>
      <el-button
        v-waves
        v-permission="['customer:payRecord:batchDelete']"
        class="filter-item"
        type="primary"
        size="mini"
        @click="getTransferDialog"
      >
        批量删除
      </el-button>
    </div>
    <div class="total-money">
      <div v-if="refundAmount">
        <em>退款总金额/元：</em>
        <el-tag type="primary">{{ refundAmount.toLocaleString() }}</el-tag>
      </div>
      <div v-if="tradeAmount">
        <em>交易总金额/元：</em>
        <el-tag type="primary">{{ tradeAmount.toLocaleString() }}</el-tag>
      </div>
    </div>
    <el-table v-loading="listLoading" :data="list" border fit stripe highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" fixed="left" />
      <af-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="校区项目编号" prop="institutionCode" />
      <af-table-column label="客户编号" prop="clueCode" show-overflow-tooltip />
      <af-table-column label="订单编号" prop="orderCode" />
      <af-table-column label="校区项目名称" prop="institutionName" show-overflow-tooltip />
      <af-table-column label="客户名称" prop="customer" show-overflow-tooltip />
      <af-table-column label="签约区域" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.provinceName">{{ scope.row.provinceName }}</span>
          <span v-if="scope.row.cityName">{{ scope.row.cityName }}</span>
          <span v-if="scope.row.areaName">{{ scope.row.areaName }}</span>
        </template>
      </af-table-column>
      <af-table-column label="加盟项目" prop="projectType" show-overflow-tooltip />
      <af-table-column label="账户类型" prop="accountTypeName" show-overflow-tooltip />
      <af-table-column label="支付类目" prop="payItemName" />
      <af-table-column label="打款方式" prop="payTypeName" />
      <af-table-column label="交易金额" prop="tradeAmount" />
      <el-table-column label="打款时间" prop="payTime" width="150" />
      <af-table-column label="关联订单" prop="orderCode" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" min-width="230" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="['customer:payRecord:delete']" type="primary" size="mini" @click="deletePay=true,deleteSignle(scope.row)">
            删除
          </el-button>
          <el-button v-permission="['customer:payRecord:detail']" type="primary" size="mini" @click="getDetail(scope.row)">
            详情
          </el-button>
          <el-button v-if="scope.row.source==2" v-permission="['customer:payRecord:update']" type="danger" size="mini" @click="editDetailPop(scope.row)">
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!-- 新增支付记录/支付详情 -->
    <el-dialog :title="titlePop" :visible.sync="showAddPay" :close-on-click-modal="!showAddPay" @closed="initFlags">
      <el-form ref="payForm" :model="pay" :inline="true" label-width="110px" :rules="payRules">
        <el-row>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="账户类型" prop="accountType">
              <el-select v-model="pay.accountType" placeholder="账户类型" filterable clearable :disabled="flags===1" @change="getAccountType('account_type')">
                <el-option v-for="item in accountList" :key="item.id" :label="item.itemName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="校区项目编号" prop="institutionCode">
              <el-input v-model="pay.institutionCode" :disabled="flags===1" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="flags===1">
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="校区项目名称">
              <el-input v-model="pay.institutionName" disabled />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="客户编号">
              <el-input v-model="pay.clueCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="客户名称">
              <el-input v-model="pay.customer" disabled />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="加盟项目">
              <el-input v-model="pay.projectType" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="关联订单" prop="orderCode">
              <el-input v-model="pay.orderCode" :disabled="flags===1" />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="支付类目" prop="payItem">
              <el-select v-model="pay.payItem" placeholder="支付类目" filterable clearable :disabled="flags===1" @change="getAccountType2('pay_item')">
                <el-option v-for="item in payList" :key="item.id" :label="item.itemName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="交易流水号" prop="payNo">
              <el-input v-model="pay.payNo" :disabled="flags===1" />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="打款时间" prop="payTime">
              <el-date-picker
                v-model="pay.payTime"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                class="filter-item"
                placeholder="打款时间"
                :disabled="flags===1"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="打款方式" prop="payType">
              <el-select v-model="pay.payType" placeholder="打款方式" filterable clearable :disabled="flags===1" @change="getAccountType3('pay_type')">
                <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="交易金额" prop="tradeAmount">
              <el-input v-model="pay.tradeAmount" :disabled="flags===1" />
            </el-form-item>
          </el-col>
          <el-col v-if="pay.payItem===22" :sm="24" :md="12" :lg="12">
            <el-form-item label="收款人姓名" required>
              <el-input v-model="pay.tradeCustomer" :disabled="flags===1" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col v-if="pay.payItem===22" :sm="24" :md="12" :lg="12">
            <el-form-item label="收款卡号" required>
              <el-input v-model.number="pay.bankCard" :disabled="flags===1" maxlength="19" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="flags===0" slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="addPay('pay')">确认</el-button>
        <el-button @click="showAddPay=false,closedPop">关闭</el-button>
      </div>
      <div v-if="flags===2" slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="editPay">确认</el-button>
        <el-button @click="showAddPay=false,closedPop">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 新增支付记录 -->
    <!--删除弹框-->
    <el-dialog :visible.sync="deletePay" :close-on-click-modal="!deletePay" title="删除交易记录">
      <el-form>
        <el-form-item label="备注:" required>
          <el-input v-model.trim="remark" type="textarea" maxlength="255" show-word-limit />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="deleteEditPay">确认</el-button>
          <el-button @click="deletePay=false,deleteClosedPop()">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--删除弹框-->
  </div>
</template>

<script>
import {
  getPayList,
  addPayList,
  PayDetail,
  editDetail,
  deletePay
} from '@/api/pay'
import Pagination from '@/components/Pagination'
import { getPayType, getAllProject } from '@/api/common'
import AreaPicker from '@/components/area-picker'
export default {
  name: 'PayList',
  components: {
    Pagination,
    AreaPicker
  },
  directives: {},
  data() {
    var regu = /^T/
    var checkCode = (rule, value, callback) => {
      if (regu.exec(value)) {
        callback()
      } else {
        return false
      }
    }
    return {
      listLoading: false,
      list: [],
      accountList: [],
      payList: [],
      payTypes: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        institutionCode: '',
        accountType: '',
        clueCode: '',
        customer: '',
        orderCode: '',
        payItem: '',
        payType: ''
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      total: 0,
      schoolOption: '',
      createSchoolTitle: '',
      currentCustomerId: '', // 当前客户的id
      showAddPay: false,
      pay: {},
      titlePop: '新增支付记录',
      flags: -1,
      payRules: {
        accountType: {
          required: true,
          message: '账户类型必填项',
          trigger: 'blur'
        },
        institutionCode: {
          required: true,
          validator: checkCode,
          message: '校区项目编号必填项，且必须是以大写T开头',
          trigger: 'blur'
        },
        orderCode: {
          required: true,
          message: '关联订单必填项',
          trigger: 'change'
        },
        payItem: {
          required: true,
          message: '支付类目为必填项',
          trigger: 'change'
        },
        payNo: {
          required: true,
          message: '交易流水号为必填项',
          trigger: 'change'
        },
        payTime: {
          required: true,
          message: '打款时间为必填项',
          trigger: 'change'
        },
        payType: {
          required: true,
          message: '打款方式为必填项',
          trigger: 'change'
        },
        tradeAmount: {
          required: true,
          message: '交易金额为必填项',
          trigger: 'change'
        }
      },
      deletePay: false,
      multipleSelection: [], // 多选框被选中的row
      multipleSelectCustomerId: [], // 多选框选中的客户id
      remark: '',
      tradeIds: [], // 删除交易记录的id
      payDate: [],
      refundAmount: null,
      tradeAmount: null,
      userId: null,
      projectList: []
    }
  },
  created() {

  },
  mounted() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.userId = userInfo.userId
    this.getProject()
    this.getList()
    this.getAccountType('account_type')
    this.getAccountType2('pay_item')
    this.getAccountType3('pay_type')
  },
  methods: {
    /**
       * 查询列表
       * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.payDate = []
      this.getList()
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
       * 新增校区项目
       * */
    getList() {
      const that = this
      that.listLoading = false
      const data = Object.assign(that.listQuery, that.areaList, { payTimeStart: that.payDate && that.payDate.length > 0 ? that.payDate[0] : '', payTimeEnd: that.payDate && that.payDate.length > 0 ? that.payDate[1] : '' })
      getPayList(data).then(res => {
        that.list = res.data.listData.records
        that.total = res.data.listData.total
        that.refundAmount = res.data.refundAmount
        that.tradeAmount = res.data.tradeAmount
      })
    },
    getAccountType(str) {
      const that = this
      getPayType(str).then(res => {
        that.accountList = res.data
      })
    },
    getAccountType2(str) {
      const that = this
      getPayType(str).then(res => {
        that.payList = res.data
      })
    },
    getAccountType3(str) {
      const that = this
      getPayType(str).then(res => {
        that.payTypes = res.data
      })
    },
    addPay() {
      const that = this
      if (that.pay.payItem === 22 && !that.pay.tradeCustomer) {
        that.$message({
          type: 'warning',
          message: '收款人姓名必填'
        })
        return false
      }
      if (that.pay.payItem === 22 && !that.pay.bankCard) {
        that.$message({
          type: 'warning',
          message: '收款卡号必填'
        })
        return false
      }
      const params = Object.assign({}, that.pay)
      addPayList(params).then(res => {
        that.$message({
          type: 'success',
          message: '添加成功!'
        })
        that.getList()
        that.pay = {}
        that.showAddPay = false
        this.flags = -1
      }).catch(() => {

      })
      //
      // that.$refs.payForm.validate((valid) => {
      //
      //   if (valid) {
      //     addPayList(params).then(res => {
      //       that.$message({
      //         type: 'success',
      //         message: '添加成功!'
      //       })
      //       that.getList()
      //       that.pay = {}
      //       that.showAddPay = false
      //       this.flags = -1
      //     })
      //   } else {
      //
      //     return false
      //   }
      // })
    },
    editPay() {
      const that = this
      if (that.pay.payItem === 22 && !that.pay.tradeCustomer) {
        that.$message({
          type: 'warning',
          message: '收款人姓名必填'
        })
        return false
      }
      if (that.pay.payItem === 22 && !that.pay.bankCard) {
        that.$message({
          type: 'warning',
          message: '收款卡号必填'
        })
        return false
      }
      const params = Object.assign({
        accountType: that.pay.accountType,
        institutionCode: that.pay.institutionCode,
        orderCode: that.pay.orderCode,
        payItem: that.pay.payItem,
        payNo: that.pay.payNo,
        payTime: that.pay.payTime,
        payType: that.pay.payType,
        tradeAmount: that.pay.tradeAmount,
        id: that.pay.id,
        tradeCustomer: that.pay.tradeCustomer,
        bankCard: that.pay.bankCard
      })
      that.$refs.payForm.validate((valid) => {
        if (valid) {
          editDetail(params).then(res => {
            that.$message({
              type: 'success',
              message: '修改成功!'
            })
            that.showAddPay = false
            that.getList()
            this.flags = -1
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    addPop() { // 新增弹框
      this.showAddPay = true
      if (this.$refs.pay) { // 打开弹框清除验证信息
        this.$refs.pay.resetFields()
      }

      this.titlePop = '新增交易记录'
      this.flags = 0

      this.getAccountType('account_type')
      this.getAccountType2('pay_item')
      this.getAccountType3('pay_type')
      this.pay = {}
    },
    getDetail(obj) { // 打开详情弹框
      this.showAddPay = true
      if (this.$refs.pay) { // 打开弹框清除验证信息
        this.$refs.pay.resetFields()
      }
      this.titlePop = '交易详情'
      this.flags = 1

      PayDetail(obj.id).then(res => {
        if (res.code === '000000') {
          this.pay = res.data
        }
      }).catch(() => {

      })
    },
    editDetailPop(obj) {
      this.showAddPay = true

      this.titlePop = '修改交易'
      this.flags = 2

      PayDetail(obj.id).then(res => {
        if (res.code === '000000') {
          this.pay = res.data
        }
      }).catch(() => {

      })
    },
    closedPop() {
      if (this.$refs.pay) {
        this.$refs.pay.resetFields()
      }
      this.flags = -1
    },
    initFlags() {
      this.flags = -1
    },
    /**
     * 多选框的返回值
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.multipleSelectCustomerId = this.multipleSelection.map(item => {
        return item.id
      })
    },
    getTransferDialog(row) { // 批量删除
      if (row.id) {
        this.multipleSelectCustomerId = [row.id]
      }
      if (this.multipleSelectCustomerId && this.multipleSelectCustomerId.length > 0) {
        this.deletePay = true
        this.tradeIds = [...this.multipleSelectCustomerId]

      } else {
        this.$message({
          message: '请先选需要删除的数据 ',
          type: 'warning'
        })
      }
    },
    deleteSignle(row) {
      this.tradeIds = [row.id]
    },
    deleteEditPay() {

      if (this.remark && this.tradeIds.length > 0) {
        const params = { remark: this.remark, tradeIds: this.tradeIds }
        deletePay(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          this.remark = ''
          this.tradeIds = []
          this.getList()
          this.deletePay = false
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请输入必填项'
        })
      }
    },
    deleteClosedPop() {
      this.remark = ''
      this.tradeIds = []
      this.getList()
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'

      let url = exportUrl + 'trades/exportTrade?pageIndex=' + that.listQuery.pageIndex + '&pageSize=' + that.listQuery.pageSize + '&userId=' + that.userId
      if (that.payDate.length !== 2) {
        that.$message({
          type: 'warning',
          message: '请先选择打款开始时间和结束时间'
        })
      } else {
        url = url + '&provinceId=' + that.areaList.provinceId + '&cityId=' + that.areaList.cityId + '&areaId=' + that.areaList.areaId + '&institutionCode=' + that.listQuery.institutionCode + '&accountType=' + that.listQuery.accountType + '&clueCode=' + that.listQuery.clueCode + '&payTimeEnd=' + that.payDate[1] + '&payTimeStart=' + that.payDate[0] + '&customer=' + that.listQuery.customer + '&orderCode=' + that.listQuery.orderCode + '&payItem =' + that.listQuery.payItem + '&payType =' + that.listQuery.payType
        that.$confirm('确定导出数据?', {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.listLoading = true
          const params = Object.assign(that.listQuery, that.areaList, {
            payTimeStart: that.payDate[0] || '',
            payTimeEnd: that.payDate[1] || ''
          })
          getPayList(params).then(response => {
            that.listLoading = false
            that.list = response.data.listData.records
            that.total = response.data.listData.total
            that.refundAmount = response.data.refundAmount
            that.tradeAmount = response.data.tradeAmount
            if (url && that.list.length > 0) {
              a.href = url
              a.target = '_blank'
              document.body.appendChild(a)
              a.click()
            } else {
              setTimeout(() => {
                that.$message({
                  type: 'warning',
                  message: '没有可以导出的数据'
                })
              }, 500)
            }
          }).catch(() => {
            that.$message({
              type: 'warning',
              message: '没有可以导出的数据'
            })
          })
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      }
    },
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .operation-btns{
    display: flex;
    justify-content: center;
    padding-top: 15px;
  }
  .pay>>> .el-input.is-disabled .el-input__inner{
    background-color: #fff;
  }
  .total-money{
    display: flex;
    margin-bottom: 20px;
    div{
    em{
          color: #666;
        }
        &:first-child{
          padding-right: 10px;
        }
    }
  }
</style>
