<template>
  <el-dialog
    :visible.sync="teacherPop"
    :title="teacherTitle"
    :close-on-click-modal="!teacherPop"
    width="60%"
    destroy-on-close
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="teacherForm"
        :model="listQuery"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="适用产品线">
          <ProductLineSelect v-model="listQuery.clientCode"></ProductLineSelect>
        </el-form-item>
        <el-form-item label="适用班型" prop="classTypeId">
          <ClassTypeByClientSelect
            v-model="listQuery.classTypeId"
            :client-code="listQuery.clientCode"
          ></ClassTypeByClientSelect>
        </el-form-item>
        <!--        <el-form-item label="适用班型" prop="classTypeId">-->
        <!--          <ClassTypeSelect v-model="listQuery.classTypeId"></ClassTypeSelect>-->
        <!--        </el-form-item>-->
        <el-form-item label="适用科目" prop="subjectId">
          <SubjectSelect v-model="listQuery.subjectId"></SubjectSelect>
        </el-form-item>
        <!--        <el-form-item label="适用产品线" prop="clientCode">-->
        <!--          <el-select v-model="listQuery.clientCode" placeholder="请选择产品线" clearable class="filter-item" :disabled="isEdit">-->
        <!--            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="样式类型" prop="style">
          <StyleSelect v-model="listQuery.style"></StyleSelect>
        </el-form-item>
        <el-form-item label="是否有目录" prop="catalogFlag">
          <el-radio-group v-model="listQuery.catalogFlag">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <StatusSelect v-model="listQuery.status"></StatusSelect>
        </el-form-item>
        <!--        <el-form-item label="排序" prop="sort">-->
        <!--          <el-input v-model="listQuery.sort" style="width: 140px;" placeholder="请输入排序" maxlength="17" :disabled="isEdit" />-->
        <!--        </el-form-item>-->
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(teacherPop = false), cancelClass()"
        >取消</el-button
      >
      <!--      新增的确定-->
      <el-button v-if="flags === 1" type="primary" size="mini" @click="submit"
        >确定</el-button
      >
      <!--      修改的确定-->
      <el-button
        v-if="flags === 0"
        type="primary"
        size="mini"
        @click="editTeacher"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { addStyleModel, getStyleModelDetail } from "@/api/classType";
import { enableList } from "@/utils/field-conver";
import ClassTypeSelect from "@/components/Select/ClassTypeSelect.vue";
import SubjectSelect from "@/components/Select/SubjectSelect.vue";
import StatusSelect from "@/components/Select/StatusSelect.vue";
import StyleSelect from "@/components/Select/StyleSelect.vue";
import ProductLineSelect from "@/components/Select/ProductLineSelect.vue";
import ClassTypeByClientSelect from "@/components/Select/ClassTypeByClientSelect.vue";

export default {
  name: "addStyleModel",
  components: {
    ClassTypeByClientSelect,
    ProductLineSelect,
    StyleSelect,
    StatusSelect,
    SubjectSelect,
    ClassTypeSelect
  },
  data() {
    return {
      teacherTitle: "",
      teacherPop: false,
      listQuery: {
        clientCode: null,
        style: 1,
        status: 1,
        catalogFlag: 0
      },
      clients: [],
      customerUpdate: true,
      rules: {
        classTypeId: { required: true, trigger: "blur", message: "请选择班型" },
        subjectId: { required: true, trigger: "blur", message: "请选择科目" },
        status: { required: true, trigger: "blur", message: "请选择状态" },
        style: { required: true, trigger: "blur", message: "请选择样式类型" },
        catalogFlag: {
          required: true,
          trigger: "blur",
          message: "请选择是否有目录"
        }
      },
      enableList: enableList,
      subjectsAll: [],
      clientCode: [],
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false,
      enableFlagsUpdate: false,
      uuid: ""
    };
  },
  methods: {
    cancelClass() {
      if (this.$refs.teacherForm) {
        this.$refs.teacherForm.clearValidate();
      }
      this.listQuery = {};
    },
    submit() {
      this.enableFlagsCreate = true;
      this.$refs.teacherForm.validate(valid => {
        if (valid) {
          const params = Object.assign({}, this.listQuery);
          addStyleModel(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "操作成功"
                });
                this.$emit("addTeacherList");
                this.teacherPop = false;
                this.listQuery = {};
                this.enableFlagsCreate = false;
                this.$refs.classForms.clearValidate();
              }
            })
            .catch(() => {
              this.enableFlagsCreate = false;
            });
        } else {
          this.enableFlagsCreate = false;
          return false;
        }
      });
    },
    getTeacherDetail(ids) {
      // 获取教师详情
      getStyleModelDetail(ids).then(res => {
        if (res.code === "000000") {
          this.listQuery = res.data;
        }
      });
    },
    changeInit() {
      this.listQuery = {
        clientCode: null,
        style: 1,
        status: 1,
        catalogFlag: 0
      };
      // if (this.$refs.teacherForm) {
      //   this.$refs.teacherForm.clearValidate();
      // }
    },
    editTeacher() {
      this.enableFlagsUpdate = true;
      this.$refs.teacherForm.validate(valid => {
        if (valid) {
          const params = Object.assign({}, this.listQuery);
          addStyleModel(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "修改成功"
                });
                this.$emit("addTeacherList");
                this.teacherPop = false;
                this.listQuery = {};
                this.enableFlagsUpdate = false;
                this.$refs.classForms.clearValidate();
              }
            })
            .catch(() => {
              this.enableFlagsUpdate = false;
            });
        }
      });
    }
  }
};
</script>
<style scoped lang="scss">
em {
  font-style: normal;
}

.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}

.course-duration {
  display: flex;
  justify-content: space-around;

  em {
    padding-left: 8px;
    padding-right: 8px;
  }
}

.upload-imgs {
  position: relative;
  width: 118px;
  height: 118px;
  font-size: 14px;
  display: inline-block;
  padding: 10px;
  margin-right: 25px;
  border: 2px dashed #ccc;
  text-align: center;
  vertical-align: middle;
}

.upload-imgs .add {
  display: block;
  background-color: #ccc;
  color: #ffffff;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .add .iconfont {
  padding: 10px 0;
  font-size: 40px;
}

.upload-imgs .upload {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 118px;
  height: 118px;
  opacity: 0;
  cursor: pointer;
}

.upload-imgs .img {
  position: relative;
  width: 94px;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .img img {
  vertical-align: middle;
  width: 94px;
  height: 94px;
}

.upload-imgs .img .close {
  display: none;
}

.upload-imgs:hover .img .close {
  display: block;
  position: absolute;
  top: -10px;
  left: -10px;
  width: 118px;
  height: 118px;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 118px;
  font-size: 24px;
  color: #fff;
}

.img-upload {
  padding-right: 8px;
}
</style>
