<!---->
<template>
</template>
<script >
import {Loading} from "element-ui";

export default {
  data() {
    return {
    }
  },
  mounted() {
    const _this = this;
    // 创建 URL 对象
    const url = new URL(window.location.href);
    // 获取查询参数
    const params = new URLSearchParams(url.search);
    // 获取指定参数的值
    const code = params.get('code');
    const state = params.get('state');
    const loadingInstance  = Loading.service({
      lock: true,
      text: '登陆中...',
      background: 'rgba(0, 0, 0, 0.3)'
    })
    _this.$store.dispatch('user/wxLogin', {code:code}).then(res => {
      loadingInstance.close();
      // 创建 URL 对象
      const url = new URL(window.location.href);
      // 删除 code 和 state 参数
      url.searchParams.delete('code');
      url.searchParams.delete('state');
      window.location.href = url.href.replace('#/codeLogin', '');
    }).catch(err => {
      this.$alert(err.msg, '提示', {
        confirmButtonText: '关闭',
        callback: action => {
          window.close();
        }
      })
      loadingInstance.close();
    })
  }
}
</script>

<style scoped>

</style>
