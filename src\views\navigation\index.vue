<template>
  <div class="home">
    <div class="home-header">
      <div class="home-header-content" @click="goMenu('customer')">
        <div class="home-header-content-box">
          <img
            src="@/assets/home/<USER>"
            alt=""
            class="home-header-icon"
          />
          <div class="home-header-title">客户管理</div>
        </div>
      </div>
      <div class="home-header-content" @click="goMenu('schoolProject')">
        <div class="home-header-content-box">
          <img
            src="@/assets/home/<USER>"
            alt=""
            class="home-header-icon"
          />
          <div class="home-header-title">校区管理</div>
        </div>
      </div>
      <div class="home-header-content" @click="goMenu('handover')">
        <div class="home-header-content-box">
          <img
            src="@/assets/home/<USER>"
            alt=""
            class="home-header-icon"
          />
          <div class="home-header-title">交接单管理</div>
        </div>
      </div>
      <div class="home-header-content" @click="goMenu('contract')">
        <div class="home-header-content-box">
          <img
            src="@/assets/home/<USER>"
            alt=""
            class="home-header-icon"
          />
          <div class="home-header-title">合同管理</div>
        </div>
      </div>
    </div>
    <div class="todo">
      <span class="title">待办</span>
      <div class="todo-tabs">
        <el-tabs v-model="tableIndex" @tab-click="handleTabClick">
          <el-tab-pane
            :label="item"
            :name="String(index)"
            v-for="(item, index) in titleList"
          ></el-tab-pane>
        </el-tabs>
        <div class="todo-btns">
          <el-button
            size="small"
            :key="item"
            v-for="(item, index) in dateList"
            :type="dateIndex === index ? 'primary' : ''"
            @click="changeDateIndex(index)"
            >{{ item }}</el-button
          >
          <el-select
            size="small"
            v-model="queryParams.handleStatus"
            @change="queryReset"
            placeholder="请选择"
          >
            <el-option
              v-for="item in selectStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="todo-content">
        <div v-if="todoList && todoList.length" class="todo-content-list-box">
          <div
            class="todo-content-list"
            v-for="(item, index) in todoList"
            @click="goList(item)"
            :key="item.id"
          >
            <div
              class="todo-status"
              :class="{ isComplete: item.handleStatus === 2 }"
            >
              {{ item.handleStatus === 1 ? "未完成" : "已完成" }}
            </div>
            <div class="todo-content-info">
              <div class="todo-title">
                <span class="todo-title-info">{{ item.handleTitle }}</span>
                <div v-if="item.nextFollowTime" class="tips">
                  <img src="@/assets/home/<USER>" alt="" />
                  <span class="title-date">{{ item.nextFollowTime }}</span>
                </div>
              </div>
              <div class="todo-content-box" :title="item.handleRemark">
                {{ item.handleRemark }}
              </div>
              <div class="todo-footer">
                <div class="date">{{ item.createTime }}</div>
                <div class="date">{{ item.clueCustomer }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">暂无数据</div>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        :page-size="queryParams.pageSize"
        :current-page="queryParams.pageIndex"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
    <follow-list ref="followRef" @refresh="queryReset" />
  </div>
</template>

<script>
import FollowList from "@/views/customer/componets/followList";
import { pageList } from "@/api/home";

export default {
  name: "Navigation",
  components: {
    FollowList
  },
  data() {
    return {
      total: 0,
      tableIndex: "0",
      queryParams: {
        handleTypes: "1",
        handleStatus: 1, // 1.未处理 2.已处理
        pageIndex: 1,
        pageSize: 8
      },
      dateIndex: 0, // 当前选中的日期下标
      dateList: ["今日", "本周", "本月", "上月"],
      titleList: ["待跟进", "待完款", "待签约", "待续约"],
      tabList: [[1], [30, 31], [32, 33, 40, 41], [42]],
      selectStatusList: [
        {
          label: "未完成",
          value: 1
        },
        {
          label: "已完成",
          value: 2
        }
      ],
      todoList: []
    };
  },
  mounted() {
    this.getListData();
  },
  methods: {
    // 路由跳转
    goMenu(menu) {
      switch (menu) {
        case "customer":
          this.$router.push({ path: "/customer/list" });
          break;
        case "schoolProject":
          this.$router.push({ path: "/customer/schoolProject" });
          break;
        case "handover":
          this.$router.push({ path: "/handover/index" });
          break;
        case "contract":
          this.$router.push({ path: "/contract/index" });
          break;
      }
    },
    // 获取数据
    getListData() {
      let params = JSON.parse(JSON.stringify(this.queryParams));
      if (this.tableIndex === "0") {
        // 跟进 传nextFollowTimeStart  nextFollowTimeEnd
        let followUpDateList = this.disposeFollowUpDate("followUp");
        params.nextFollowTimeStart = followUpDateList.nextFollowTimeStart;
        params.nextFollowTimeEnd = followUpDateList.nextFollowTimeEnd;
      } else if (this.tableIndex === "3") {
        // 续约
        let followUpDateList = this.disposeFollowUpDate("renew");
        // let dateList = [7, 15, 30];
        params.nextFollowTimeStart = this.$moment().format("YYYY-MM-DD");
        params.nextFollowTimeEnd = followUpDateList.nextFollowTimeEnd;
      }
      pageList(params).then(res => {
        if (res.code === "000000") {
          this.total = res.data.total;
          this.todoList = res.data.records || [];
        }
      });
    },
    // 获取时间
    getDate(value) {
      let index = this.tabList.findIndex(subArray =>
        subArray.includes(value.handleType)
      );
      return value.createTime;
    },
    handleCurrentChange(val) {
      this.queryParams.pageIndex = val;
      this.getListData();
    },
    // 处理跟进时间
    disposeFollowUpDate(type) {
      let nextFollowTimeStart = "";
      let nextFollowTimeEnd = "";
      if (type === "followUp") {
        // 跟进
        switch (this.dateIndex) {
          case 0: // 今日
            nextFollowTimeStart = this.$moment().format("YYYY-MM-DD");
            nextFollowTimeEnd = this.$moment().format("YYYY-MM-DD");
            break;

          case 1: // 本周
            nextFollowTimeStart = this.$moment()
              .startOf("week")
              .add(1, "day")
              .format("YYYY-MM-DD"); // 周一开始
            nextFollowTimeEnd = this.$moment()
              .endOf("week")
              .add(1, "day")
              .format("YYYY-MM-DD"); // 周日结束
            break;

          case 2: // 本月
            nextFollowTimeStart = this.$moment()
              .startOf("month")
              .format("YYYY-MM-DD");
            nextFollowTimeEnd = this.$moment()
              .endOf("month")
              .format("YYYY-MM-DD");
            break;

          case 3: // 上月
            nextFollowTimeStart = this.$moment()
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD");
            nextFollowTimeEnd = this.$moment()
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD");
            break;

          default:
            break;
        }
      } else {
        // 续约
        switch (this.dateIndex) {
          case 0: // 未来7天（包含今天）
            nextFollowTimeEnd = this.$moment()
              .add(6, "days") // 今天 + 6 天 = 共7天
              .format("YYYY-MM-DD");
            break;

          case 1: // 未来15天（包含今天）
            nextFollowTimeEnd = this.$moment()
              .add(14, "days")
              .format("YYYY-MM-DD");
            break;

          case 2: // 未来30天（包含今天）
            nextFollowTimeEnd = this.$moment()
              .add(29, "days")
              .format("YYYY-MM-DD");
            break;

          default:
            break;
        }
      }

      return {
        nextFollowTimeStart,
        nextFollowTimeEnd
      };
    },
    disposeCode(id, type) {
      // 转字符串，左侧补0，长度固定6位
      const strNum = id.toString().padStart(6, "0");
      return type + strNum;
    },
    // 跳转列表
    goList(item) {
      // 跟进直接弹窗
      if (item.handleType === 1) {
        this.$refs.followRef.getLists(item.clueId);
      } else {
        const codeList = {
          30: ["orderId", "O"],
          31: ["orderId", "O"],
          32: ["orderId", "O"],
          33: ['contractId', 'CT'],
          40: ['contractId', 'CT'],
          41: ['contractId', 'CT'],
          42: ["institutionId", "T"]
        };
        let type = codeList[item.handleType];
        let id = this.disposeCode(item[type[0]], type[1]);
        switch (item.handleType) {
          case 30:
          case 32:
            this.$router.push({ path: "/handover", query: { orderCode: id } });
            break;
          case 31:
            this.$router.push({
              path: "/handover/paymentRecord",
              query: { orderCode: id }
            });
            break;
          case 33:
          case 40:
          case 41:
            this.$router.push({
              path: "/contract/index",
              query: { orderCode: id }
            });
            break;
          case 42:
            this.$router.push({
              path: "/customer/schoolProject",
              query: { orderCode: id }
            });
            break;
        }
      }
    },
    // tab切换
    handleTabClick(value) {
      if (value.index === "0") {
        // 跟进
        this.dateList = ["今日", "本周", "本月", "上月"];
      } else if (value.index === "3") {
        // 续约
        this.dateList = ["7天", "15天", "30天"];
      } else {
        // 支付  签约
        this.dateList = [];
      }
      this.dateIndex = 0;
      this.queryParams.handleTypes = this.tabList[value.index].join(",");
      this.queryReset();
    },
    // 分页重置
    queryReset() {
      this.queryParams.pageIndex = 1;
      this.queryParams.pageSize = 8;
      this.getListData();
    },
    changeDateIndex(index) {
      this.dateIndex = index;
      this.queryReset();
    }
  }
};
</script>
<style scoped lang="scss">
.home {
  width: 100%;
  display: flex;
  flex-direction: column;
  /* text-align: center; */
  justify-content: center;
  padding: 30px 20px;
  .home-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    width: 100%;
    height: 200px;
    background: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(48, 65, 86, 0.2);
    border-radius: 10px;
    .home-header-content {
      cursor: pointer;
      width: 370px;
      height: 140px;
      display: flex;
      align-items: center;
      justify-content: center;
      .home-header-content-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 220px;
        height: 106px;
        .home-header-title {
          font-size: 22px;
          color: #2b3036;
          font-weight: bold;
        }
      }
      &:first-child {
        background-image: url("~@/assets/home/<USER>");
      }
      &:nth-child(2) {
        background-image: url("~@/assets/home/<USER>");
      }
      &:nth-child(3) {
        background-image: url("~@/assets/home/<USER>");
      }
      &:last-child {
        background-image: url("~@/assets/home/<USER>");
      }
    }
  }
  .todo {
    margin-top: 20px;
    width: 100%;
    height: 590px;
    background: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(48, 65, 86, 0.2);
    border-radius: 10px;
    padding: 24px 30px 0;
    .title {
      display: flex;
      font-size: 22px;
      color: #2b3036;
    }
    .todo-tabs {
      position: relative;
      .todo-btns {
        display: flex;
        position: absolute;
        right: 0;
        top: 0;
        .el-button {
          margin-left: 0;
          border-radius: 0;
          border-left: 0;
          &:first-child {
            border-radius: 10px 0 0 10px;
            border-left: 1px;
          }
          &:nth-last-child(2) {
            border-radius: 0 10px 10px 0;
          }
        }
        .el-select {
          margin-left: 20px;
        }
      }
    }
    .todo-content,
    .todo-content-list-box {
      display: flex;
      flex-wrap: wrap;
      .todo-content-list {
        margin-bottom: 24px;
        width: 374px;
        height: 140px;
        margin-right: 30px;
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(48, 65, 86, 0.2);
        border-radius: 10px;
        overflow: hidden;
        .todo-status {
          width: 74px;
          height: 28px;
          background: #d9e9fc;
          border-radius: 10px 0px 10px 0px;
          line-height: 28px;
          color: #539fff;
          font-size: 16px;
          &.isComplete {
            background: #d9fce9;
            color: #44e06c;
          }
        }
        .todo-content-info {
          padding: 20px 22px;
          .todo-title {
            display: flex;
            align-items: center;
            .todo-title-info {
              font-size: 16px;
              color: #2b3036;
              font-weight: bold;
              margin-right: 10px;
            }
            .tips {
              display: flex;
              align-items: center;
            }
            .title-date {
              display: flex;
              color: #539fff;
              font-size: 18px;
              margin-left: 5px;
            }
          }
          .todo-content-box {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            white-space: nowrap;
            font-size: 16px;
            color: #666;
            padding: 6px 0 4px;
          }
          .date {
            display: flex;
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }
}
.el-pagination {
  display: flex;
  justify-content: flex-end;
  margin-right: 28px;
}
::v-deep .el-tabs__item {
  font-size: 18px;
  &.is-active {
    font-weight: bold;
  }
}
.no-data {
  width: 100%;
  height: 300px;
  text-align: center;
  line-height: 300px;
}
.todo-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
