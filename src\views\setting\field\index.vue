<template>
  <div class="app-container">
    <el-tabs type="border-card">
      <el-tab-pane label="快递管理">
        <express-detail />
      </el-tab-pane>
      <el-tab-pane label="加盟项目管理" :lazy="true">
        <project-detail />
      </el-tab-pane>
      <el-tab-pane label="项目产品管理" :lazy="true">
        <product-detail />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import expressDetail from './express/detail'
import projectDetail from './project/detail'
import productDetail from './product/detail'
export default {
  name: 'Field',
  components: { expressDetail, projectDetail, productDetail },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {

  }
}
</script>

<style scoped>

</style>
