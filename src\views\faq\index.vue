<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.questionId"
        placeholder="问题编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.searchField"
        placeholder="问题标题"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.levelOneId" placeholder="一级标题" filterable clearable class="filter-item" style="width: 140px;" @change="getTwo">
        <el-option v-for="item in oneList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.levelTwoId" placeholder="二级标题" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in twoList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['faq:creat']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
      <el-button v-waves v-permission="['faq:title']" class="filter-item" size="mini" type="primary" @click="handleTitle">标题管理</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="问题编号" show-overflow-tooltip prop="id" width="100px" />
      <af-table-column label="问题标题" show-overflow-tooltip prop="title" />
      <af-table-column label="一级标题" prop="levelOneName" show-overflow-tooltip />
      <af-table-column label="二级标题" prop="levelTwoName" show-overflow-tooltip />
      <af-table-column label="排序" prop="sort" />
      <af-table-column label="点击次数" prop="viewCount" />
      <af-table-column label="提交时间" prop="updateTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['faq:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['faq:del']" type="primary" size="mini" @click="handleDelet(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <one-title ref="addFaqTitles" @refreshList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import OneTitle from './components/addOneTitle'
import { faqList, levelOneList, levelTwo, delQuestion } from '@/api/faq'
export default {
  name: 'FAQ',
  components: {
    Pagination,
    OneTitle
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      enableList: [],
      oneTitleInfo: '',
      oneList: [],
      twoList: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.levelOneList()
    })
  },
  methods: {
    async getList() {
      const params = Object.assign({}, this.listQuery)
      faqList(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    levelOneList() {
      levelOneList().then(res => {
        if (res.code === '000000') {
          this.oneList = res.data
        }
      }).catch(() => {

      })
    },
    getTwo(val) {
      if (val) {
        levelTwo(val).then(res => {
          if (res.code === '000000') {
            this.twoList = res.data
          }
        }).catch(() => {

        })
      }
    },
    handleCreate() { // addFaqTitles
      this.$nextTick(() => {
        this.$refs.addFaqTitles.faqParentTitle = '新增问题'
        this.$refs.addFaqTitles.faqTitlePop = true
        this.$refs.addFaqTitles.editFlag = 'create'
      })
    },
    handleTitle() {
      this.$router.push({
        name: 'Title'
      })
    },
    handleUpdate(row) {
      this.$nextTick(() => {
        this.$refs.addFaqTitles.faqTitlePop = true
        this.$refs.addFaqTitles.faqParentTitle = `修改-${row.title}`
        this.$refs.addFaqTitles.questionIn(row.id)
        this.$refs.addFaqTitles.editFlag = 'update'
      })
    },
    handleDelet(row) {
      this.$confirm('确定要删除此条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delQuestion(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
