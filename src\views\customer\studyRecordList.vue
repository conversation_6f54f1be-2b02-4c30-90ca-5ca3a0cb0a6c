<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
              v-model="listQuery.agencyId"
              placeholder="机构Id"
              class="filter-item"
              style="width: 200px;"
              @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.schoolName"
        placeholder="校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
<!--      <el-input-->
<!--        v-model="listQuery.queryInfo"-->
<!--        placeholder="合伙人/手机号"-->
<!--        class="filter-item"-->
<!--        style="width: 200px;"-->
<!--        @keyup.enter.native="handleFilter"-->
<!--      />-->
<!--      <el-input-->
<!--        v-model="listQuery.schoolName"-->
<!--        placeholder="校区名称"-->
<!--        class="filter-item"-->
<!--        style="width: 200px;"-->
<!--        @keyup.enter.native="handleFilter"-->
<!--      />-->
      <el-input
        v-model="listQuery.userName"
        placeholder="人员名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
<!--      <el-select v-model="listQuery.productId" placeholder="产品线" clearable class="filter-item" style="width: 140px;">-->
<!--        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
<!--      </el-select>-->
<!--      <el-select v-model="listQuery.payType" placeholder="支付类型" clearable class="filter-item" style="width: 140px;">-->
<!--        <el-option v-for="item in institutionsPayType" :key="item.value" :label="item.label" :value="item.value" />-->
<!--      </el-select>-->
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="机构Id" prop="schoolId" show-overflow-tooltip  width="120px"/>
      <el-table-column label="校区名称" show-overflow-tooltip prop="schoolName" width="250px">
<!--        <template slot-scope="scope">-->
<!--          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.orderId }}</a>-->
<!--        </template>-->
      </el-table-column>

<!--      <af-table-column label="合伙人(机构账号)" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <span v-if="scope.row.partnerName&&scope.row.partnerAccount">{{ scope.row.partnerName }}({{ scope.row.partnerAccount }})</span>-->
<!--        </template>-->
<!--      </af-table-column>-->
      <el-table-column label="用户名称" prop="userName" show-overflow-tooltip width="150px"/>
<!--      <af-table-column label="产品线" prop="productName" />-->
      <el-table-column label="课程名称" prop="videoName"  />
      <el-table-column label="学习时长" prop="chargeNumber">
        <template slot-scope="scope">
          <span v-if="scope.row.seconds!==null">{{ (scope.row.seconds/60).toFixed(0) }}分钟 {{ scope.row.seconds%60 }}秒</span>
        </template>
      </el-table-column>
      <el-table-column label="观看时间" prop="createTime" />
<!--      <af-table-column label="支付金额/元" prop="orderPrice" />-->
<!--      <af-table-column label="备注" prop="remark" />-->
<!--      <af-table-column label="充值时间" prop="chargeTime" />-->
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination/index.vue'
import { getCharge } from '@/api/charge'
import { institutionsPayType } from '@/utils/field-conver'
import { getCustomerStudyList } from '@/api/customer'
export default {
  name: 'OrderList',
  components: {
    Pagination
  },
  props: {
    clientCode: {
      type: Number,
      default: 9,
    },
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        clientCode: 9
      },
      institutionsPayType: institutionsPayType,
      followDate: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  created() {
    this.listQuery.clientCode = this.clientCode
    // 默认查询本月的
    this.followDate = [this.$moment().startOf('month').format('YYYY-MM-DD'), this.$moment().endOf('month').format('YYYY-MM-DD')]
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startDate: this.followDate[0] ? this.followDate[0] : '', endDate: this.followDate[1] ? this.followDate[1] : '' })
      await getCustomerStudyList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.created()
      this.getList()
    },
    getStatus(row) {
      // return converseEnToCn(this.statusList, row.status)
    },
    getDetail(row) { // 获取列表详情

    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
