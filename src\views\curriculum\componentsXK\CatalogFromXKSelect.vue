<template>
  <el-select v-model="tmpId"  multiple clearable placeholder="请选择章节课程" :disabled="disabled" filterable>
    <el-option
            v-for="item in optionList"
            :key="item.id"
            filterable
            :label="item.name"
            :value="Number(item.id)">
    </el-option>
  </el-select>
</template>
<script>
import { getChapterFromXK } from '@/api/exercises'

export default {
  name: 'CatalogFromXKSelect',
  data: function () {
    return {
      optionList: []
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number,Array],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    textbookId:{
      type: [String, Number],
      required: false,
    }
  },
  watch: {
    textbookId(val) {
      this.handleChange('')
      this.getList()
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? this.targetId : []
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.id === value)
      // const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedOption)
    },
    getList() {
      if(!this.textbookId){
        this.optionList =[]
        return
      }
      this.loading = true
      getChapterFromXK({ 'textbookId': this.textbookId }).then(response => {
        if (response.code === '000000') {
          const data = response.data;
          // // 从字符串转换为json
          this.optionList = JSON.parse(data)
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped>
</style>
