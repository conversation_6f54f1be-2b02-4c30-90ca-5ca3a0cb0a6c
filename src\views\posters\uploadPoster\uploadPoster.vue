<template>
  <el-dialog :visible.sync="posterFlag" :title="posterTitle" :close-on-click-modal="!posterFlag" width="60%" @close="changeInit">
    <div class="filter-container">
      <el-select v-model="listQuery.dictCode" placeholder="海报分类" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in posterCate" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
    </div>
    <div class="hello">
      <div class="upload">
        <div class="upload_warp">
          <div class="upload_warp_left" @click="fileClick">
            <img src="../../../assets/img/upload.png">
          </div>
          <div class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
            或者将文件拖到此处
          </div>
        </div>
        <div class="upload_warp_text">
          <i v-if="imgList.length>0">选中{{ imgList.length }}张文件，</i><i v-if="size">共{{ bytesToSize(size) }}</i>
        </div>
        <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
        <div v-show="imgList.length!=0" class="upload_warp_img">
          <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
            <div class="upload_warp_img_div_top">
              <div class="upload_warp_img_div_text">
                {{ item.file.name }}
              </div>
              <img src="../../../assets/img/del.png" class="upload_warp_img_div_del" @click="fileDel(index)">
            </div>
            <img :src="item.file.src">
          </div>
        </div>
      </div>
      <div class="upload-opera">
        <el-button v-if="showUpload" size="mini" type="primary" @click="imgUpload">上传图片</el-button>
        <el-button v-if="savePoster" size="mini" type="primary" @click="confirmUpload">保存</el-button>
        <el-button size="mini" type="primary" @click="changeInit">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { uuid } from '@/utils/index'
import { addPoster } from '@/api/poster'
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { uploadSuccess } from '@/api/common'
export default {
  name: 'UploadPoster',
  props: {
    posterCate: {
      type: Array,
      default: () => {
        return []
      }
    },
    clientCode: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      posterFlag: false,
      posterTitle: '',
      imgList: [],
      size: 0,
      listQuery: {},
      uploadImg: [],
      savePoster: false,
      showUpload: true
    }
  },
  watch: {
    'imgList': {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.showUpload = true
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    changeInit() {
      this.listQuery = {}
      this.imgList = []
      this.posterFlag = false
      this.savePoster = false
    },
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {
      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        // 判断是否为文件夹
        if (files[i].type !== '') {
          this.fileAdd(files[i])
        } else {
          // 文件夹处理
          this.folders(fileList.items[i])
        }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = 'wenjian.png'
        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(index) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      this.imgList.splice(index, 1)
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    },
    imgUpload() {
      const validateImg = this.imgList.every(item => {
        return (item.file.size / 1024 / 1024).toFixed(3) < 5
      })
      if (!validateImg) {
        this.$message({
          type: 'warning',
          message: '请上传5M以内的图片'
        })
        return false
      }
      setTimeout(() => {
        this.$message({
          type: 'success',
          message: '请点击保存按钮上传海报'
        })
      }, 1500)
      this.imgList.forEach((item, index) => {

        this.posterUpload(item.file, index)
      })
    },
    confirmUpload() { // listQuery.clientCode
      const that = this
      if (!that.listQuery.dictCode) {
        that.$message({
          type: 'warning',
          message: '请选择海报分类'
        })
        return false
      }
      if (!that.listQuery.clientCode) {
        that.$message({
          type: 'warning',
          message: '请选择产品线'
        })
        return false
      }
      if (that.imgList === 0) {
        that.$message({
          type: 'warning',
          message: '请先上传海报'
        })
        return false
      }
      if (that.imgList.length > 0) {
        const uploadList = that.imgList.map(item => item !== null ? item.id : '')
        if (uploadList && uploadList.length > 0) {
          const params = {
            clientCode: that.listQuery.clientCode,
            dictCode: that.listQuery.dictCode,
            resourceIdList: uploadList
          }
          addPoster(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '上传成功'
              })
              that.posterFlag = false
              that.listQuery = {}
              that.imgList = []
              that.size = ''
              that.$emit('refresh')
            }
          }).catch(() => {
            that.savePoster = true
            that.showUpload = true

          })
        }
      }
    },
    posterUpload(file, i) {
      const that = this
      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const tempName = file.name.split('.')
      const ids = uuid()
      const fileName = `santao_stip/crm/poster/${ids}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {
          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/poster/${ids}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              setTimeout(() => {
                loading.close()
              }, 500)
              that.imgList[i].id = res.data.id
              that.imgList[i].src = res.data.url
              that.savePoster = true
              that.showUpload = false
            }
          }).catch(() => {
            that.showUpload = true
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 130px;
    color: #999;
  }

  .upload_warp_left img {
    margin-top: 32px;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
  }

  .upload_warp {
    margin: 14px;
    height: 130px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
</style>
