<template>
  <div>
    <el-dialog v-if="dialogSchoolProjectDetail" :close-on-click-modal="!dialogSchoolProjectDetail" top="0" :title="title" :visible.sync="dialogSchoolProjectDetail" width="700px">
      <el-form ref="detailForm" :model="detail" label-width="140px" :rules="schoolProjectRules">
        <el-row>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="选择客户：" prop="clueCode">
              <el-input v-model="detail.clueCode" disabled>
                <el-button slot="append" :disabled="optionsType === 'update'" @click="chooseCustomer">
                  <span>选择</span>
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="客户名称：" prop="clueName">
              <el-input v-model="detail.clueName" disabled />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="客户级别：" prop="customerLevel">
              <el-select v-model="detail.customerLevel" filterable class="filter-item" style="width: 100%;" clearable>
                <el-option
                  v-for="item in customerLevelList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="选择校区：" prop="schoolCode">
              <el-input v-model="detail.schoolCode" disabled>
                <el-button slot="append" :disabled="optionsType === 'update'" @click="chooseSchool">
                  <span>选择</span>
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="校区名称：" prop="schoolName">
              <el-input v-model="detail.schoolName" disabled  />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="校区地址：" prop="schoolAddress">
              <el-input v-model="detail.schoolAddress" disabled />
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="校区项目名称：" prop="institutionName">
              <el-input v-model="detail.institutionName" />
            </el-form-item>
          </el-col> -->
          <!--          <el-col :xs="{span:24}" :sm="{span:12}">-->
          <!--            <el-form-item label="校区名称：" prop="institutionName">-->
          <!--              <el-input v-model="detail.institutionName" />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="加盟状态：" prop="status">
              <el-select v-model="detail.status" filterable class="filter-item" style="width: 100%;" clearable>
                <el-option
                  v-for="item in schoolJoinStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}" v-if="detail.status == '2'">
            <el-form-item label="交接单ID：">
              <el-input v-model.number="detail.orderId" placeholder="请输入交接单id" @input="(value) => onOrderIdInput(value, 'orderId')" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="加盟项目：" prop="projectId">
              <el-select v-model="detail.projectId" filterable class="filter-item" style="width: 100%;" clearable>
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="区域类型：" prop="areaJoinType">
              <el-select v-model="detail.areaJoinType" filterable class="filter-item" clearable>
                <el-option
                  v-for="item in cooperationType"
                  :key="item.itemValue"
                  :label="item.itemName"
                  :value="item.itemValue"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="关联机构ID：">
              <el-input v-model="detail.agencyId" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="生命周期：" prop="lifeCycle">
              <el-cascader
                placeholder="生命周期"
                v-model="detail.lifeCycle"
                :options="lifeCycleList"
                :props="{ multiple: true, emitPath: false}"
                clearable>
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="校区项目ID：">
              <el-input v-model.number="detail.id" placeholder="请输入校区项目id" @input="(value) => onOrderIdInput(value, 'id')"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button v-if="optionsType === 'create'" type="primary" @click="confirmCreate">确 认</el-button>
        <el-button v-if="optionsType === 'update'" type="primary" @click="updateSchoolProjectInfo">确 认</el-button>
        <el-button @click="dialogSchoolProjectDetail = false">关 闭</el-button>
      </div>
    </el-dialog>
    <relate-customer ref="chooseCustomerDialog" title="选择客户" :is-choose="isChoose" @chooseOk="getCustomerChoose" />
    <choose-school ref="chooseSchool" @chooseSuccess="getSchoolCallBack" />
  </div>
</template>

<script>
import { addSchoolProjectDetail } from '@/api/school'
import { getShoolProjectDetail, updateShoolProjectList } from '@/api/school-project'
import RelateCustomer from '@/views/customer/componets/relateCustomer'
import ChooseSchool from '@/views/handover/list/components/choose-school'
import { schoolJoinStatusList, customerLevelList, lifeCycleList} from '@/utils/field-conver'
import { getAllProject, getPayType } from '@/api/common'
export default {
  name: 'SchoolProject',
  components: { RelateCustomer, ChooseSchool },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      optionsType: '',
      lifeCycleList,
      detail: {
      },
      cooperationType: [],
      customerName: '',
      dialogSchoolProjectDetail: false,
      projectList: [],
      isChoose: false,
      schoolJoinStatusList: schoolJoinStatusList,
      customerLevelList: customerLevelList,
      schoolProjectRules: {
        clueCode: { required: true, message: '选择客户必填', trigger: 'blur' },
        clueName: { required: true, message: '客户名称必填', trigger: 'blur' },
        schoolCode: { required: true, message: '选择校区必填', trigger: 'blur' },
        schoolName: { required: true, message: '校区名称必填', trigger: 'blur' },
        // institutionName: { required: true, message: '校区项目名称必填', trigger: 'blur' },
        schoolAddress: { required: true, message: '校区地址必填', trigger: 'blur' },
        status: { required: true, message: '加盟状态必填', trigger: 'blur' },
        lifeCycle: { required: true, message: '请选择生命周期', trigger: 'blur' },
        projectId: { required: true, message: '项目类型必填', trigger: 'blur' },
        customerLevel: {
          required: true,
          message: '请选择客户级别',
          trigger: 'blur'
        },
        // areaJoinType: { required: true, message: '区域类型必填', trigger: 'blur' }
      },
    }
  },
  created() {
    this.getProject()
    this.getAreaType('area_join_type')
  },
  methods: {
    /**
     * 获取校区项目详情
     * */
    schoolProjectDetail(id, type) {
      this.optionsType = type
      getShoolProjectDetail(id).then(res => {
        if (res.code === '000000') {
          this.dialogSchoolProjectDetail = true
          this.detail = res.data
          this.detail.lifeCycle = this.detail.lifeCycle ? this.detail.lifeCycle.split(',') : [];
          this.detail.institutionType = res.data.institutionType !== null ? JSON.stringify(res.data.institutionType) : '0'
          this.detail.schoolAddress = res.data.provinceName + res.data.cityName + res.data.areaName + ((res.data.countyName === 'null' || !res.data.countyName) ? '' : res.data.countyName)
        }
      })
    },
    onOrderIdInput(val, type) {
      // 去除非数字字符
      const onlyDigits = val.replace(/\D/g, '');
      // 转换为 number，如果为空则设为 0 或 null（看你业务需求）
      this.detail[type]  = onlyDigits ? Number(onlyDigits) : '';
    },
    /**
     * 新增校区项目
     */
    getDetail(type) {
      this.optionsType = type
      const that = this
      that.dialogSchoolProjectDetail = true
      that.detail = {
        customerLevel: 3
      }
    },
    /**
     * 添加校区
     */
    confirmCreate() {
      this.$refs['detailForm'].validate((valid) => {
        if (valid) {
          if (this.detail.projectId < 4 && !this.detail.agencyId) {
            this.$message({
              type: 'warning',
              message: '请输入关联机构ID'
            })
          } else {
            let params = JSON.parse(JSON.stringify((this.detail)));
            params.lifeCycle = params.lifeCycle.join(',');
            addSchoolProjectDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '新增成功'
                })
                this.dialogSchoolProjectDetail = false
                this.$emit('refresh')
              }
            }).catch(res => {

            })
          }
        } else {

          return false
        }
      })
    },
    /**
     * 修改校区
     * */
    updateSchoolProjectInfo() {
      this.$refs['detailForm'].validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify((this.detail)));
          params.lifeCycle = params.lifeCycle.join(',');
          updateShoolProjectList(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.dialogSchoolProjectDetail = false
              this.$emit('refresh')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    /**
     * 选择客户Id
     */
    chooseCustomer() {
      this.isChoose = true
      this.$refs.chooseCustomerDialog.getLists()
    },
    /**
     * 选择客户回调
     * */
    getCustomerChoose(data) {
      this.$set(this.detail, 'clueName', data.customer)
      this.$set(this.detail, 'clueCode', data.clueCode)
      this.detail.clueId = data.id
    },
    /**
     * 校区选择回调函数
     * */
    getSchoolCallBack(data) {
      const address = data.provinceName + data.cityName + data.areaName + data.countyName
      this.$set(this.detail, 'schoolName', data.schoolName)
      this.$set(this.detail, 'schoolAddress', address)
      this.$set(this.detail, 'schoolCode', data.schoolCode)
      this.detail.schoolId = data.id
    },
    /**
     * 获取客户的校区列表
     * */
    chooseSchool() {
      if (!this.detail.clueName) {
        this.$message({
          type: 'warning',
          message: '请先选择客户'
        })
        return
      }
      const params = {
        clueId: this.detail.clueId
      }
      this.$refs.chooseSchool.getLists(params)
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    getAreaType(str) {
      const that = this
      getPayType(str).then(res => {

        that.cooperationType = res.data
      })
    }
  }
}
</script>

<style>
  .el-input.is-disabled .el-input__inner{
   background-color: #fff;
   border-color: #dfe4ed;
   color: #666;
  }
</style>
