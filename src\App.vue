<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>

import {isWeComBrowser} from "@/utils";

export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    }
  },
  created() {
    // if(isWeComBrowser()){
    //   window.location.href= process.env.VUE_APP_REDIRECT_URI;
    // }
  },
  data() {
    return {
      isRouterAlive: true
    }
  },
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(() => {
        this.isRouterAlive = true
      })
    }
  }
}
</script>
