<template>
  <el-dialog :visible.sync="teacherPop" :title="teacherTitle" :close-on-click-modal="!teacherPop" width="60%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="teacherForm" :model="listQuery" :rules="rules" label-width="120px">
        <el-form-item label="教师名称" prop="name">
          <el-input v-model="listQuery.name" placeholder="请输入教师名称" maxlength="10" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="任教科目" prop="subjectId">
          <el-select v-model="listQuery.subjectId" placeholder="请选择任教科目" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="适用产品线" prop="clientCode">
          <el-select v-model="listQuery.clientCode" placeholder="请选择产品线" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="教师排序" prop="sort">
          <el-input v-model="listQuery.sort" placeholder="请输入教师排序" maxlength="17" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="教师状态" prop="status">
          <el-select v-model="listQuery.status" placeholder="请选择教师状态" clearable class="filter-item" :disabled="isEdit" filterable>
            <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="宣传视频" prop="videoResourceId">
          <el-input v-model="listQuery.vid" placeholder="请输入宣传视频链接" maxlength="20" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="视频时长">
          <div class="course-duration">
            <el-input v-model.number="listQuery.videoMinute" placeholder="视频时长" :disabled="isEdit" />
            <em>分</em>
            <el-input v-model.number="listQuery.videoSecond" placeholder="视频时长" :disabled="isEdit" />
            <em>秒</em>
          </div>
        </el-form-item>
        <el-form-item label="教师资格证编号" prop="certNo">
          <el-input v-model="listQuery.certNo" placeholder="请输入教师资格证编号" maxlength="17" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="上传教师资格证">
          <div class="upload-imgs">
            <div v-if="!certImgResource&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="upload($event)">
              <a class="add"><i class="iconfont icon-plus" /><p>点击上传</p></a>
            </div>
            <p class="img">
              <img v-if="certImgResource" :src="certImgResource">
              <a v-if="certImgResource&&!isEdit" class="close" @click="delImgA">
                <i class="el-icon-delete" />
              </a>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="上传教师头像">
          <div class="upload-imgs">
            <div v-if="!imageResource&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadHead($event)">
              <a class="add"><i class="iconfont icon-plus" /><p>点击上传</p></a>
            </div>
            <p class="img">
              <img v-if="imageResource" :src="imageResource">
              <a v-if="imageResource&&!isEdit" class="close" @click="delImgB">
                <i class="el-icon-delete" />
              </a>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="上传教师图片">
          <div class="upload-imgs">
            <div v-if="!bigImageResource&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadTeacher($event)">
              <a class="add"><i class="iconfont icon-plus" /><p>点击上传</p></a>
            </div>
            <p class="img">
              <img v-if="bigImageResource" :src="bigImageResource">
              <a v-if="bigImageResource&&!isEdit" class="close" @click="delImgC">
                <i class="el-icon-delete" />
              </a>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="教师简介">
          <el-input v-model="listQuery.introduction" placeholder="请输入教师简介" maxlength="500" show-word-limit type="textarea" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="教师详情">
          <el-input v-model="listQuery.teacherDetails" placeholder="请输入教师详情" maxlength="500" show-word-limit type="textarea" :disabled="isEdit" />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="teacherPop=false,cancelClass()">取消</el-button>
      <!--      新增的确定-->
      <el-button v-if="flags===1" type="primary" size="mini" @click="custormClass">确定</el-button>
      <!--      修改的确定-->
      <el-button v-if="flags===0" type="primary" size="mini" @click="editTeacher">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { clientCode, getAllSubjects, addTeacher, getTeacherDetail, editTeacher } from '@/api/classType'
import {
  enableList
} from '@/utils/field-conver'
import { uploadSuccess } from '@/api/common'
export default {
  name: 'AddClassPop',
  data() {
    return {
      teacherTitle: '',
      teacherPop: false,
      listQuery: {
        clientCode: []
      },
      clients: [],
      host: 'https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/',
      customerUpdate: true,
      rules: {
        name: { required: true, trigger: 'blur', message: '请输入教师名称' },
        subjectId: { required: true, trigger: 'blur', message: '请选择任教科目' },
        clientCode: { required: true, trigger: 'blur', message: '请选择适用产品线' },
        status: { required: true, trigger: 'blur', message: '请选择教师状态' }
      },
      enableList: enableList,
      subjectsAll: [],
      clientCode: [],
      isEdit: false,
      flags: -1,
      certImgResource: '', // 教师资格证图片
      certImgResourceId: '', // 教师资格证图片id
      imageResource: '', // 教师头像图片
      imageResourceId: '', // 教师头像图片id
      bigImageResource: '', // 教师图片
      bigImageResourceId: '', // 教师图片id
      enableFlagsCreate: false,
      enableFlagsUpdate: false,
      uuid: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode()
      this.getAllSubjects()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 1)
      })
    },
    getAllSubjects() {
      getAllSubjects().then(res => {
        this.subjectsAll = res.data
      })
    },
    cancelClass() {
      if (this.$refs.teacherForm) {
        this.$refs.teacherForm.clearValidate()
      }
      this.listQuery = {}
      this.certImgResource = ''
      this.certImgResourceId = ''
      this.imageResource = ''
      this.imageResourceId = ''
      this.bigImageResourceId = ''
      this.bigImageResource = ''
    },
    custormClass() {
      this.enableFlagsCreate = true
      this.$refs.teacherForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery, { bigImageResourceId: this.bigImageResourceId || '', certImgResourceId: this.certImgResourceId || '', imageResourceId: this.imageResourceId || '' })
          if (this.listQuery.vid && (!this.listQuery.videoMinute || !this.listQuery.videoSecond)) {
            this.$message({
              type: 'warning',
              message: '请输入视频时长'
            })
            this.enableFlagsCreate = false
          } else {
            addTeacher(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.$emit('addTeacherList')
                this.teacherPop = false
                this.listQuery = {}
                this.certImgResource = ''
                this.certImgResourceId = ''
                this.imageResource = ''
                this.imageResourceId = ''
                this.bigImageResourceId = ''
                this.bigImageResource = ''
                this.enableFlagsCreate = false
                this.$refs.classForms.clearValidate()
              }
            }).catch(() => {
              this.enableFlagsCreate = false

            })
          }
        } else {
          this.enableFlagsCreate = false
          return false
        }
      })
    },
    getTeacherDetail(ids) { // 获取教师详情
      getTeacherDetail(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
          this.certImgResource = res.data.certImgUrl || ''
          this.certImgResourceId = res.data.certImgResourceId || ''
          this.imageResource = res.data.imageUrl || ''
          this.imageResourceId = res.data.imageResourceId || ''
          this.bigImageResourceId = res.data.bigImageResourceId || ''
          this.bigImageResource = res.data.bigImageUrl || ''
        }
      })
    },
    changeInit() {
      this.listQuery = {}
      this.certImgResource = ''
      this.certImgResourceId = ''
      this.imageResource = ''
      this.imageResourceId = ''
      this.bigImageResourceId = ''
      this.bigImageResource = ''
      if (this.$refs.teacherForm) {
        this.$refs.teacherForm.clearValidate()
      }
    },
    editTeacher() {
      this.enableFlagsUpdate = true
      this.$refs.teacherForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery, { bigImageResourceId: this.bigImageResourceId || '', certImgResourceId: this.certImgResourceId || '', imageResourceId: this.imageResourceId || '' })
          if (this.listQuery.vid && (!this.listQuery.videoMinute || !this.listQuery.videoSecond)) {
            this.$message({
              type: 'warning',
              message: '请输入视频时长'
            })
            this.enableFlagsUpdate = false
          } else {
            editTeacher(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.$emit('addTeacherList')
                this.teacherPop = false
                this.listQuery = {}
                this.certImgResource = ''
                this.certImgResourceId = ''
                this.imageResource = ''
                this.imageResourceId = ''
                this.bigImageResourceId = ''
                this.bigImageResource = ''
                this.enableFlagsUpdate = false
                this.$refs.classForms.clearValidate()
              }
            }).catch(() => {
              this.enableFlagsUpdate = false

            })
          }
        }
      })
    },
    upload(e) { // 教师资格证图片
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的教师资格证不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.certImgResourceId = res.data.id
                that.certImgResource = res.data.url
              }
            })
          }
        })
      }
    },
    uploadHead(e) { // 教师头像
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的教师头像不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.imageResourceId = res.data.id
                that.imageResource = res.data.url
              }
            })
          }
        })
      }
    },
    uploadTeacher(e) { // 教师图片
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的教师图片不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.bigImageResourceId = res.data.id
                that.bigImageResource = res.data.url
              }
            })
          }
        })
      }
    },

    delImgA() {
      this.certImgResourceId = ''
      this.certImgResource = ''
    },
    delImgB() {
      this.imageResourceId = ''
      this.imageResource = ''
    },
    delImgC() {
      this.bigImageResourceId = ''
      this.bigImageResource = ''
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    }
  }
}
</script>

<style scoped lang="scss">
  em{
    font-style: normal;
  }
  .assign-operas{
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .course-duration{
    display: flex;
    justify-content: space-around;
  em{
    padding-left: 8px;
    padding-right: 8px;
  }
  }

  .upload-imgs{
    position: relative;
    width: 118px;
    height: 118px;
    font-size: 14px;
    display: inline-block;
    padding: 10px;
    margin-right: 25px;
    border: 2px dashed #ccc;
    text-align: center;
    vertical-align: middle;
  }
  .upload-imgs .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 94px;
    line-height: 94px
  }
  .upload-imgs .add .iconfont{
    padding: 10px 0;
    font-size: 40px;
  }
  .upload-imgs .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 118px;
    height: 118px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    position: relative;
    width: 94px;
    height: 94px;
    line-height: 94px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
    width: 94px;
    height: 94px;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:-10px;
    left: -10px;
    width:118px;
    height:118px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 118px;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
</style>
