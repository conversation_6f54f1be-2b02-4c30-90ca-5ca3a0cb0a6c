<template>
  <el-dialog :visible.sync="assingPop" title="客户资源指派" :close-on-click-modal="!assingPop" width="70%">
    <div class="assing-info">
      <el-form :model="assingForm">
        <el-row>
          <el-col :sm="24" :md="4" :lg="4" class="agency-filed">
            <el-input v-model="assingForm.agencyFiled" placeholder="请输入校区名称/手机号" />
          </el-col>
          <el-col :sm="24" :md="12" :lg="12" class="agency-filed">
            <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
          </el-col>
          <el-col :sm="24" :md="4" :lg="4">
            <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
              查询
            </el-button>
            <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      ref="assingTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" fixed="left" />
      <af-table-column label="合伙人" show-overflow-tooltip prop="contacts" />
      <af-table-column label="校区名称" show-overflow-tooltip prop="title" />
      <af-table-column label="所在区域" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.provinceName }}-{{ scope.row.cityName }}-{{ scope.row.areaName }}</span>
        </template>
      </af-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="assingForm.pageIndex"
      :limit.sync="assingForm.pageSize"
      @pagination="getList"
    />
    <div class="assign-operas">
      <el-button type="infor" size="mini" @click="cancelAssign">取消</el-button>
      <el-button type="primary" size="mini" @click="custormAssign">指派</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AreaPicker from '@/components/area-picker'
import Pagination from '@/components/Pagination'
import {
  getMechanismList,
  custormAssign
} from '@/api/mvp'
export default {
  components: {
    AreaPicker,
    Pagination
  },
  props: {
    clueIds: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      assingPop: false,
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      assingForm: {
        page: 1,
        pageSize: 10,
        agencyFiled: ''
      },
      list: [],
      total: 0,
      listLoading: true,
      multipleSelection: {}
    }
  },
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    /**
       * 获取省市区的地址
       * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
       * 多选框的返回值
       */
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$refs.assingTab.clearSelection()
        this.$refs.assingTab.toggleRowSelection(val.pop())
        this.multipleSelection = {}
      } else {
        this.multipleSelection = val.pop()
      }

    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.assingForm, that.areaList)
      await getMechanismList(params).then(res => {
        this.list = res.data.records
        this.total = res.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.assingForm.pageIndex = 1
      this.getList()
    },
    // 重置
    handleReset() {
      this.assingForm = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.getList()
    },
    custormAssign() {
      if (this.multipleSelection === undefined || this.multipleSelection === null || !this.multipleSelection.id) {
        this.$message({
          type: 'warning',
          message: '请先选择合伙人'
        })
      } else {
        const params = Object.assign({ agencyId: this.multipleSelection.id, clueIds: this.clueIds })
        this.$confirm('是否确认指派？该操作不可撤销！', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          custormAssign(params).then(res => {

            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '指派成功!'
              })
              this.$refs.assingTab.clearSelection()
              this.assingPop = false
              this.$emit('updateList')
            }
          }).catch(() => {

          })
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消指派操作'
          })
        })
      }
    },
    cancelAssign() {
      this.$confirm('确定取消指派操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'warning',
          message: '取消成功'
        })
        this.$refs.assingTab.clearSelection()
      }).catch(() => {

      })
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
  .assing-info {
    margin-bottom: 20px;

    .agency-filed {
      margin-right: 15px;
    }
  }

  .assign-operas {
    border-top: 1px #eaeaea solid;
    padding-top: 10px;
    display: flex;
  }

  /deep/.el-dialog__header {
    padding: 10px 20px;
  }

  /deep/.el-dialog__body {
    padding: 10px 20px 40px 20px;
  }
</style>
