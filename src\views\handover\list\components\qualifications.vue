<template>
  <div class="app-container bgGrey">
    <el-row v-if="showType === 'uploadQualification'|| showType === 'shortcutUploadQualification'" class="mb10">
      <el-col :lg="{span:24}">
        <span>快速上传：</span>
        <el-button type="success" plain icon="el-icon-user-solid" @click="toChooseQualification">选择已有资质</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mb10">
      <!--      个人资质-->
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>客户签约资质</span>
            <div v-if="showType==='auditQualification'" class="fr">审核状态：<el-tag :type="customerQualificationType">{{ setQualificationStatus(customer.status) }}</el-tag></div>
          </div>
          <div class="item">
            <el-form
              ref="customerForm"
              size="small"
              label-width="110px"
              :rules="customerRules"
              :model="customer"
              :disabled="customerUpdate"
            >
              <el-row>
                <el-col v-if="customer.status === 2 && customer.remark!==''&&showType==='auditQualification'" :span="24">
                  <el-form-item label="驳回理由：">
                    <el-tag type="danger">{{ customer.remark }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="客户名称：" prop="userName">
                    <el-input v-model="customer.userName" maxlength="8" minlength="2" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}" style="display: flex; align-items: center; justify-content: space-between;">
                  <el-form-item label="手机号：" prop="phone" style="width: 90%;">
                    <el-input v-model="customer.phone" maxlength="11" />
                  </el-form-item>
                  <el-tooltip class="item" effect="dark" content="本人身份证办理的手机号码" placement="top">
                    <i class="el-icon-question" style="font-size: 18px; padding: 0; padding-bottom: 10px; padding-left: 10px;"></i>
                  </el-tooltip>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="邮箱：" prop="email">
                    <el-input v-model="customer.email" maxlength="35" />
                  </el-form-item>
                </el-col> -->
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <div v-show="signType === 2">
                    <el-form-item label="人员类型：" prop="principalType">
                      <el-select v-model="customer.principalType" placeholder="请选择" filterable>
                        <el-option
                          v-for="item in principalTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div v-show="signType !== 2" style="visibility: hidden;">
                    <el-form-item label="人员类型：">
                      <!-- 占位但不显示内容 -->
                      <el-input disabled placeholder=" "></el-input>
                    </el-form-item>
                  </div>
                </el-col>

                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="证件类型：" prop="certType">
                    <el-select v-model="customer.certType" placeholder="请选择" filterable>
                      <el-option
                        v-for="item in certTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:8}">
                  <el-form-item label="证件号码：" prop="idCard">
                    <el-input v-model="customer.idCard" maxlength="18" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="银行卡：" prop="bankNumber">
                    <el-input v-model="customer.bankNumber" maxlength="19" />
                  </el-form-item>
                </el-col> -->
                <!-- <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="联系地址：" prop="contactAddress">
                    <el-input v-model="customer.contactAddress" maxlength="50" />
                  </el-form-item>
                </el-col> -->
                <el-col :xs="{span:24}" :sm="{span:10}">
                  <el-form-item label="收货地址：" prop="areaValid">
                    <area-picker :area-list="areaList" :level="'3'" area-style="width: 100%" class="filter-item" @getAreaList="getAreaList" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:14}">
                  <el-form-item label="详细地址：" prop="address">
                    <el-input v-model="customer.address" maxlength="50" @input="getAddress" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                  <el-form-item label="银行卡照片：" class="img-item">
                    <em class="items">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="handUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-exceed="exceed"
                      :on-remove="bankCardRemove"
                      list-type="picture-card"
                      :file-list="customer.bankCard"
                      :limit="1"
                      :class="{hide: customerUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col> -->
                <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                  <el-form-item label="身份证人像面：" class="img-item">
                    <em class="items l118">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="cardFrontUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-exceed="exceed"
                      :on-remove="idCardFrontRemove"
                      list-type="picture-card"
                      :file-list="customer.idCardFront"
                      :limit="1"
                      :class="{hide: customerUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}" class="required-items">
                  <el-form-item label="身份证国徽面：" class="img-item">
                    <em class="items l118">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="idCardBackUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-exceed="exceed"
                      :on-remove="idCardBackRemove"
                      list-type="picture-card"
                      :file-list="customer.idCardBack"
                      :limit="1"
                      :class="{hide: customerUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}" class="img-item required-items">
                  <el-form-item label="手持身份证：" class="img-item">
                    <em class="items">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="idCardHoldUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-exceed="exceed"
                      :on-remove="idCardHoldRemove"
                      list-type="picture-card"
                      :file-list="customer.idCardHold"
                      :limit="1"
                      :class="{hide: customerUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </el-form>
          </div>
          <div class="bottom clearfix text-center">
            <div v-if="showType==='auditQualification'">
              <el-button v-if="customer.status === 0" v-permission="['order:list:quanlificationExamine:pass']" type="primary" round @click="setCustomerInfo(1)">通过</el-button>
              <el-button v-if="customer.status === 0||customer.status === 1" v-permission="['order:list:quanlificationExamine:reject']" type="danger" round @click="showRejectDialog('1')">驳回</el-button>
              <el-button v-if="(customer.status === 2||customer.status === -1) && customerUpdate" v-permission="['order:list:quanlificationExamine:update']" type="primary" round @click="customerUpdate = false">修改</el-button>
              <el-button v-if="customer.status === 2 && !customerUpdate" type="primary" round @click="editCustomerInfo">确定上传</el-button>
              <el-button v-if="customer.status === 2 && !customerUpdate" round @click="customerUpdate = true">取消</el-button>
            </div>

            <div v-if="showType==='uploadQualification' ||showType==='shortcutUploadQualification'">
              <el-button v-if="(customer.status === 2||customer.status === -1) && customerUpdate" type="primary" round @click="customerUpdate = false">修改</el-button>
              <el-button v-else-if="customerUpdate" type="primary" round @click="customerUpdate=false">上传</el-button>
              <el-button v-if="!customerUpdate" type="primary" round @click="editCustomerInfo">确定上传</el-button>
              <el-button v-if="!customerUpdate" round @click="customerUpdate = true">取消</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row class="mb10" :gutter="10">
      <!--      校区资质-->
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>校区签约资质</span>
            <div v-if="showType==='auditQualification'" class="fr">审核状态：<el-tag :type="schoolQualificationType">{{ setQualificationStatus(school.status) }}</el-tag></div>
          </div>
          <div class="item">
            <el-form
              ref="schoolForm"
              size="small"
              label-width="120px"
              :rules="schoolRules"
              :model="school"
              :disabled="schoolUpdate"
            >
              <el-row>
                <el-col v-if="school.status === 2 && school.remark!==''" :span="24">
                  <el-form-item label="驳回理由：">
                    <el-tag type="danger">{{ school.remark }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:20}">
                  <el-form-item label="校区编号：">
                    {{school.schoolCode}}
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:16}">
                  <el-form-item label="校区名称：" prop="schoolName">
                    <el-input v-model="school.schoolName" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="办学年限：" prop="leaseDate">
                    <el-input v-model="school.leaseDate" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="在职老师数：" prop="teacherNumber">
                    <el-input v-model="school.teacherNumber" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="在校学生数：" prop="studentNumber">
                    <el-input v-model="school.studentNumber" />
                  </el-form-item>
                </el-col> -->
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约地址：" prop="address">
                    <div style="display: flex;">
                      <span>{{  school.provinceName }}{{ school.cityName }}{{ school.areaName }}{{ school.countyName }}</span>
                      <span style="display: none">{{  school }}</span>
                      <el-input disabled v-model="school.address" style="width: 790px; padding-left: 20px" /></div>
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="img-item required-items">
                  <el-form-item label="校区照片：" class="img-item">
                    <em class="items">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="schoolImgsUpload"
                      :on-preview="(file) => handleMorePictureCardPreview('schoolImgs', file)"
                      :on-exceed="exceed"
                      :on-remove="schoolImgsRemove"
                      list-type="picture-card"
                      :file-list="school.schoolImgs"
                      :limit="3"
                      :class="{hide: schoolUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="img-item required-items">
                  <el-form-item label="租赁合同：" class="img-item">
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="leaseContractUpload"
                      :on-preview="(file) => handleMorePictureCardPreview('leaseContract', file)"
                      :on-exceed="exceed"
                      :on-remove="leaseContractRemove"
                      list-type="picture-card"
                      :file-list="school.leaseContract"
                      :limit="3"
                      :class="{hide: schoolUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="img-item required-items">
                  <el-form-item label="办学许可证：" class="img-item">
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="schoolLicenseUpload"
                      :on-preview="(file) => handleMorePictureCardPreview('schoolLicense', file)"
                      :on-exceed="exceed"
                      :on-remove="schoolLicenseRemove"
                      list-type="picture-card"
                      :file-list="school.schoolLicense"
                      :limit="3"
                      :class="{hide: schoolUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>

              </el-row>
            </el-form>
          </div>
          <div class="bottom clearfix text-center">
            <div v-if="showType==='auditQualification'">
              <el-button v-if="school.status === 0" type="primary" round @click="setSchoolInfo(1)">通过</el-button>
              <el-button v-if="school.status === 0||school.status === 1" type="danger" round @click="showRejectDialog('2')">驳回</el-button>
              <el-button v-if="(school.status === 2||school.status === -1) && schoolUpdate" type="primary" round @click="schoolUpdate = false">修改</el-button>
              <el-button v-if="school.status === 2 && !schoolUpdate" type="primary" round @click="editSchoolInfo">确定上传</el-button>
              <el-button v-if="school.status === 2 && !schoolUpdate" round @click="schoolUpdate = true">取消</el-button>
            </div>
            <div v-if="showType==='uploadQualification' ||showType==='shortcutUploadQualification'">
              <el-button v-if="(school.status === 2||school.status === -1) && schoolUpdate" type="primary" round @click="schoolUpdate = false">修改</el-button>
              <el-button v-else-if="schoolUpdate" type="primary" round @click="schoolUpdate=false">上传</el-button>
              <el-button v-if="!schoolUpdate" type="primary" round @click="editSchoolInfo">确定上传</el-button>
              <el-button v-if="!schoolUpdate" round @click="schoolUpdate = true">取消</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row v-if="signType!==1" class="mb10" :gutter="10">
      <!--      企业资质-->
      <el-col :lg="{span:24}">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>企业签约资质</span>
            <div v-if="showType==='auditQualification'" class="fr">审核状态：<el-tag :type="enterpriseQualificationType">{{ setQualificationStatus(enterprise.status) }}</el-tag></div>
          </div>
          <div class="item">
            <el-form
              ref="enterpriseForm"
              size="small"
              label-width="120px"
              :rules="enterpriseRules"
              :disabled="enterpriseUpdate"
              :model="enterprise"
            >
              <el-row>
                <el-col v-if="enterprise.status === 2 && enterprise.remark!==''" :span="24">
                  <el-form-item label="驳回理由：">
                    <el-tag type="danger">{{ enterprise.remark }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="企业名称：" prop="enterpriseName">
                    <el-input v-model="enterprise.enterpriseName" maxlength="40" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="企业法人：" prop="enterpriseLegal">
                    <el-input v-model="enterprise.enterpriseLegal" minlength="2" maxlength="8" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="社会统一码：" prop="creditCode">
                    <el-input v-model="enterprise.creditCode" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="联系电话：" prop="contactPhone">
                    <el-input v-model="enterprise.contactPhone" maxlength="20" />
                  </el-form-item>
                </el-col> -->
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="注册地址：" prop="enterpriseAddress">
                    <el-input v-model="enterprise.enterpriseAddress" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="enterprise required-items">
                  <el-form-item label="营业执照：">
                    <em class="items">*</em>
                    <el-upload
                      :action="host"
                      :data="aliData"
                      :http-request="businessLicenseUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-exceed="exceed"
                      :on-remove="businessLicenseRemove"
                      list-type="picture-card"
                      :file-list="enterprise.businessLicense"
                      :limit="4"
                      :class="{hide: enterpriseUpdate}"
                    >
                      <i slot="default" class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="bottom clearfix text-center">
            <div v-if="showType==='auditQualification'">
              <el-button v-if="enterprise.status === 0" type="primary" round @click="setEnterpriseInfo(1)">通过</el-button>
              <el-button v-if="enterprise.status === 0||enterprise.status === 1" type="danger" round @click="showRejectDialog('3')">驳回</el-button>
              <el-button v-if="(enterprise.status === 2||enterprise.status === -1) && enterpriseUpdate" type="primary" round @click="enterpriseUpdate = false">修改</el-button>
              <el-button v-if="enterprise.status === 2 && !enterpriseUpdate" type="primary" round @click="editEnterpriseInfo">确定上传</el-button>
              <el-button v-if="enterprise.status === 2 && !enterpriseUpdate" round @click="enterpriseUpdate = true">取消</el-button>
            </div>
            <div v-if="showType==='uploadQualification' || showType==='shortcutUploadQualification'">
              <el-button v-if="(customer.status === 2||customer.status === -1) && customerUpdate" type="primary" round @click="customerUpdate = false">修改</el-button>
              <el-button v-else-if="enterpriseUpdate" type="primary" round @click="enterpriseUpdate=false">上传</el-button>
              <el-button v-if="!enterpriseUpdate" type="primary" round @click="editEnterpriseInfo">确定上传</el-button>
              <el-button v-if="!enterpriseUpdate" round @click="enterpriseUpdate = true">取消</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      title="预览图片"
      :visible.sync="imgDialog"
      width="35%"
      class="img-dialog"
    >
      <el-image style="width: 100%" :src="showImgSrc" fit="contain" />
    </el-dialog>
    <el-dialog
      :title="reasonTitle"
      :visible.sync="reasonDialog"
      width="500px"
      class="reason-dialog"
    >
      <el-row>
        <el-col :span="24">
          <el-input
            v-model="rejectReason"
            type="textarea"
            placeholder="请输入驳回理由"
            :autosize="{ minRows: 4, maxRows: 6}"
          />
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer text-center">
        <!-- 个人-->
        <el-button v-if="currentQualification === '1'" type="primary" @click="setCustomerInfo(2)">确 定</el-button>
        <!-- 校区-->
        <el-button v-if="currentQualification === '2'" type="primary" @click="setSchoolInfo(2)">确 定</el-button>
        <!-- 企业-->
        <el-button v-if="currentQualification === '3'" type="primary" @click="setEnterpriseInfo(2)">确 定</el-button>
        <el-button @click="reasonDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
      <el-image
        style="display: none;"
        :src="''"
        :preview-src-list="srcList"
        :initial-index="previewIndex"
        ref="hiddenImage"
      />
    <el-dialog :visible.sync="dialogVisible" :close-on-click-modal="!dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
     <el-dialog title="资质信息" :visible.sync="qualificationDialog" width="30%">
      <div  class="confirm-dialog">
        <span>是否使用最新资质信息上传？</span>
        <div>{{qualificationNameList[0]}}：{{qualificationNameList[1]}}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="qualificationDialog = false">取 消</el-button>
        <el-button type="primary" @click="confimCopy">确 定</el-button>
      </div>
    </el-dialog>
    <choose-qualification ref="chooseQualificationDialog" :choose-qualification-param="chooseQualificationParam" @chooseQualificationOk="(data) => getQualificationChoose(data, 'submit')" />
  </div>
</template>
<script>

import { getOSSClient } from '@/components/upload/getOSSTst'

import { editCustomerQualfication, editSchoolQualfication, editEnterpriseQualfication, editCustomerInfo, editSchoolInfo, editEnterpriseInfo, getQualficationDetail, orderListQualification } from '@/api/qualifications'
import { converseEnToCn, qualificationStatus, certType, principalType } from '@/utils/field-conver'
import { customer, validPhone, mail, idCard, validCreditCode, legalPerson, bankNumber } from '@/utils/validate.js'
import AreaPicker from '@/components/area-picker'
import ChooseQualification from './choose-qualification'

export default {
  name: 'Qualifications',
  components: { AreaPicker, ChooseQualification },
  directives: {},
  data() {
    const areaValid = (rule, value, callback) => {
      if (!this.areaList.areaId || !this.areaList.provinceId || !this.areaList.cityId) {
        return callback(new Error(' '))
      } else {
        callback()
      }
    }
    return {
      qualficationDetail:  {}, // 存储详情值
      qualificationDialog: false,
      qualificationNameList: ['', ''], // 弹窗里面个人或企业名称
      srcList: [],
      previewIndex: 0,
      currentQualification: '-1', // 当前审核的是哪个资质
      currentOrderId: '',
      customerQualificationType: '',
      schoolQualificationType: '',
      enterpriseQualificationType: '',
      customerUpdate: true, // 修改个人资质
      schoolUpdate: true, // 修改校区资质
      enterpriseUpdate: true, // 修改公司资质
      showImgSrc: '',
      imgDialog: false,
      reasonDialog: false,
      reasonTitle: '驳回理由',
      rejectReason: '', // 驳回的理由
      customer: {
        certType: '0'
      }, // 客户资质
      school: {}, // 校区
      enterprise: {}, // 企业
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      client:null,
      customerRules: {
        userName: { required: true, trigger: 'blur', validator: customer },
        address: { required: true, trigger: 'blur', message: '请输入详细地址' },
        phone: { required: true, trigger: 'blur', validator: validPhone },
        // bankNumber: { required: true, trigger: 'blur', validator: bankNumber },
        // email: { required: true, trigger: 'blur', validator: mail },
        idCard: { required: true, trigger: 'blur', validator: idCard },
        areaValid: { required: true, validator: areaValid, trigger: 'blur' },
        contactAddress: { required: true, trigger: 'blur', message: '请输入联系地址' },
        certType: { required: true, trigger: 'blur', message: '请选择证件类型' },
        principalType: { required: this.signType === 2, trigger: 'blur', message: '请选择人员类型' }
      },
      schoolRules: {
        leaseDate: { required: true, trigger: 'blur', message: '请输入办学年限 ' },
        teacherNumber: { required: true, trigger: 'blur', message: '请输入在职老师数 ' },
        studentNumber: { required: true, trigger: 'blur', message: '请输入学生人数 ' },
        schoolName: { required: true, trigger: 'blur', message: '请输入校区名称 ' },
        address: { required: true, trigger: 'blur', message: '请输入签约地址 ' }
      },
      enterpriseRules: {
        enterpriseName: [
          { required: true, trigger: 'blur', message: '请输入企业名称' },
          { pattern: /^[^\s]*$/, message: '企业名称不能包含空格' }
        ],
        creditCode: { required: true, trigger: 'blur', validator: validCreditCode },
        enterpriseLegal: { required: true, trigger: 'blur', validator: legalPerson },
        contactPhone: { required: true, trigger: 'blur', message: '请输入联系电话' },
        enterpriseAddress: { required: true, trigger: 'blur', message: '请输入企业注册地址' }
      },
      host: 'https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/',
      aliData: {
        name: '',
        key: 'upload/sign/' + '${filename}',
        success_action_status: '200'
      },
      showType: '',
      orderQualificationListsLoading: false,
      brankImg: '',
      orderId: 0,
      signType: null,

      certTypeList: certType,
      principalTypeList: principalType,
      uuids: '',
      chooseQualificationParam: {
        clueCode: '',
        contractTypeSign: null,
        title: '选择资质'
      },
      //  请求的资质详情
      detailQualfication: { school: {}, customer: {}, enterprise: {}, areaList: {}}
    }
  },
  computed: {},
  async mounted() {
    this.$set(this.customer, 'certType', '0');
    this.client= await getOSSClient('santtaojiaoyu');
  },
  created() {
    this.orderId = this.$route.params && this.$route.params.orderId
    this.showType = this.$route.query.type
    this.chooseQualificationParam.clueCode = this.$route.query.clueCode
    this.currentOrderId = this.$route.params && this.$route.params.orderId
    this.orderQualificationListsLoading = false
    this.getDetailQualfication(this.orderId)
    let tagsName = ''
    if (this.showType === 'auditQualification') {
      tagsName = '资质审核'
    } else if (this.showType === 'uploadQualification') {
      tagsName = '上传资质'
    } else if (this.showType === 'shortcutUploadQualification') {
      tagsName = '快捷上传资质'
    } else {
      tagsName = '查看资质'
    }
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },

    handUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 5
          this.customer.bankCard.push(obj)
        }
      }).catch((err) => {

      })
    },
    bankCardRemove(file) {
      this.customer.bankCard.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {
          this.client.delete(item.imagePath).then(res => {
            arr.splice(index, 1)
          }).catch(() => {

          })
          //

        }
      })
    },
    cardFrontUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}

      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 6
          this.customer.idCardFront.push(obj)
        }
      }).catch((err) => {

      })
    },
    idCardFrontRemove(file) {
      this.customer.idCardFront.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {
          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    idCardBackUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 7
          this.customer.idCardBack.push(obj)
        }
      }).catch((err) => {

      })
    },
    idCardBackRemove(file) {
      this.customer.idCardBack.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {
          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    idCardHoldUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 8
          this.customer.idCardHold.push(obj)
        }
      }).catch((err) => {

      })
    },
    idCardHoldRemove(file) {
      this.customer.idCardHold.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {
          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    leaseContractUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      //
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 2
          obj['name'] = file.name
          this.school.leaseContract.push(obj)
        }
      }).catch((err) => {

      })
    },
    leaseContractRemove(file) {

      this.school.leaseContract.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {

          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    schoolLicenseUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 3
          obj['name'] = file.name
          this.school.schoolLicense.push(obj)
        }
      }).catch((err) => {

      })
    },
    schoolLicenseRemove(file) {
      this.school.schoolLicense.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {

          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    schoolImgsUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 4
          obj['name'] = file.name
          this.school.schoolImgs.push(obj)
        }
      }).catch((err) => {

      })
    },
    schoolImgsRemove(file) {
      this.school.schoolImgs.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {

          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    businessLicenseUpload(params) {
      var file = params.file
      const uuid = this.get_uuid()
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url
          obj['type'] = 1
          this.enterprise.businessLicense.push(obj)
        }
      }).catch((err) => {

      })
    },
    businessLicenseRemove(file) {
      this.enterprise.businessLicense.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {

          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    },
    exceed() {
      this.$message({
        message: '超出文件上传个数',
        type: 'warning'
      })
    },
    handleMorePictureCardPreview(type, file) {
      const arr = type === 'schoolImgs' ? this.school.schoolImgs : type === 'leaseContract' ? this.school.leaseContract :  type === 'schoolLicense' ? this.school.schoolLicense : []
      this.srcList = arr.map(item => item.imagePath);
      this.previewIndex = arr.findIndex(item => {
        return (item.name && item.name === file.name) || (item.url && item.url === file.url);
      });
      this.$nextTick(() => {
        this.$refs.hiddenImage.showViewer = true;
      });
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    /**
     * 获取资质详情
     **/
    getDetailQualfication(orderId) { // 获取签约资质的详情
      getQualficationDetail(orderId).then(res => {
        if (res.code === '000000') {
          this.signType = res.data.signType || null
          // 个人信息
          this.customer = res.data.signatory || {certType: '0'}

          const deliveryAddress = res.data.deliveryAddress || { areaId: '', cityId: '', provinceId: '' }
          this.areaList = deliveryAddress
          if (res.data.signatory !== null) {
            this.customer.bankCard = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 5
            })
            this.customer.idCardFront = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 6
            })
            this.customer.idCardBack = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 7
            })
            this.customer.idCardHold = res.data.signatory && res.data.signatory.images.filter(item => {
              item.url = item.imagePath
              return item.type === 8
            })
            this.customerQualificationType = this.getQualifications(res.data.signatory.status)
          } else {
            this.customer.bankCard = []
            this.customer.idCardFront = []
            this.customer.idCardBack = []
            this.customer.idCardHold = []
          }

          this.$set(this.customer, 'address', res.data.deliveryAddress !== null && res.data.deliveryAddress.address ? res.data.deliveryAddress.address : '')
          this.customer.orderId = this.orderId
          // 学校信息
          //
          this.school = res.data.school || {}
          this.school.orderId = this.orderId
          if (res.data.school !== null) {
            this.school.leaseContract = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 2
            })
            this.school.schoolLicense = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 3
            })
            this.school.schoolImgs = res.data.school && res.data.school.images.filter(item => {
              item.url = item.imagePath
              return item.type === 4
            })
            this.schoolQualificationType = this.getQualifications(res.data.school.status)
          }

          // 企业信息
          this.enterprise = res.data.enterprise || {}
          this.enterprise.orderId = this.orderId
          this.enterprise.businessLicense = (res.data.enterprise && res.data.enterprise.images.length > 0) ? res.data.enterprise.images.filter(item => {
            item.url = item.imagePath
            return item.type === 1
          }) : []
          res.data.enterprise && (this.enterpriseQualificationType = this.getQualifications(res.data.enterprise.status))

          this.chooseQualificationParam.contractTypeSign = this.signType

          // this.$set(this.chooseQualificationParam, 'contractTypeSign', this.signType)
          // this.$set(this.chooseQualificationParam, 'title', 'title111111')
          // this.$set(this.chooseQualificationParam, 'clueCode', 'abcddd')
          //
          if (this.showType === 'shortcutUploadQualification' && !this.orderQualificationListsLoading || this.showType === 'uploadQualification') {
            this.getOrderQualificationLists()
          }
        }
      }).catch(res => {

      })
    },
    confimCopy () {
      this.disposeQualficationDetail();
      this.qualificationDialog = false;
      // 上传
      this.editCustomerInfo();
      this.editSchoolInfo();
      // 企业上传
      if (this.signType!==1) {
        this.editEnterpriseInfo();
      }
    },
    // 处理详情
    disposeQualficationDetail() {
      let res = this.qualficationDetail;
      this.signType = res.data.signType || null
      // 个人信息
      this.detailQualfication.customer = res.data.signatory || {}
      this.detailQualfication.areaList = res.data.deliveryAddress || { areaId: '', cityId: '', provinceId: '' }

      if (res.data.signatory !== null) {
        this.detailQualfication.customer.bankCard = res.data.signatory && res.data.signatory.images.filter(item => {
          item.url = item.imagePath
          return item.type === 5
        })
        this.detailQualfication.customer.idCardFront = res.data.signatory && res.data.signatory.images.filter(item => {
          item.url = item.imagePath
          return item.type === 6
        })
        this.detailQualfication.customer.idCardBack = res.data.signatory && res.data.signatory.images.filter(item => {
          item.url = item.imagePath
          return item.type === 7
        })
        this.detailQualfication.customer.idCardHold = res.data.signatory && res.data.signatory.images.filter(item => {
          item.url = item.imagePath
          return item.type === 8
        })
        // this.customerQualificationType = this.getQualifications(res.data.signatory.status)
      } else {
        this.detailQualfication.customer.bankCard = []
        this.detailQualfication.customer.idCardFront = []
        this.detailQualfication.customer.idCardBack = []
        this.detailQualfication.customer.idCardHold = []
      }

      this.detailQualfication.customer.address = res.data.deliveryAddress !== null && res.data.deliveryAddress.address ? res.data.deliveryAddress.address : ''

      // 学校信息
      this.detailQualfication.school = res.data.school || {}
      if (res.data.school !== null) {
        this.detailQualfication.school.schoolImgs = res.data.school && res.data.school.images.filter(item => {
          item.url = item.imagePath
          return item.type === 4
        })
      }
      this.detailQualfication.school.leaseContract = []
      this.detailQualfication.school.schoolLicense = []
      // 校区照片如果没有带  有就不带
      if (this.schoo && this.school.schoolImgs && this.school.schoolImgs.length > 0) {
        delete this.detailQualfication.school.schoolImgs;
      }
      // 企业信息

      this.detailQualfication.enterprise = res.data.enterprise || {}
      this.detailQualfication.enterprise.businessLicense = (res.data.enterprise && res.data.enterprise.images.length > 0) ? res.data.enterprise.images.filter(item => {
        item.url = item.imagePath
        return item.type === 1
      }) : []
      // res.data.enterprise && (this.enterpriseQualificationType = this.getQualifications(res.data.enterprise.status))

      // this.chooseQualificationParam.contractType = (this.signType !== 1) ? 2 : 1

      // this.detailQualfication.enterprise = this.enterprise
      this.getQualificationChoose(this.detailQualfication)
    },
    /**
     * 一键复制总请求详情
     * */
    doGetDetailQualfication(orderId) { // 获取签约资质的详情
      getQualficationDetail(orderId).then(res => {
        if (res.code === '000000' && this.showType === 'uploadQualification') {
          this.qualficationDetail = res;
            // 上传资质  如果有信息不弹窗，没信息第一条
          if (!this.customer.userName) {
            const isCompany = res.data.signType !== 1;
            const name = isCompany ? res.data.enterprise.enterpriseName : res.data.signatory.userName;
            const label = isCompany ? '企业名称' : '个人名称';

            this.qualificationNameList = [label, name];
            this.qualificationDialog = true;
          }

        }
      }).catch(res => {

      })
    },

    /**
       * 修改个人信息
       * */
    editCustomerInfo() {
      this.$refs['customerForm'].validate((valid) => {
        if (valid ) {
          const signatoryImg = [ ...this.customer.idCardFront, ...this.customer.idCardBack]
          const arrs = []
          signatoryImg.forEach(item => {
            const objs = {}
            objs['imagePath'] = item.imagePath
            objs['type'] = item.type
            arrs.push(objs)
          })
          const deliveryAddress = Object.assign(this.areaList, { address: this.customer.address })
          const principalTypeChoice = this.signType === 2 ? this.customer.principalType : 0
          const params = {
            bankNumber: this.customer.bankNumber,
            deliveryAddress: deliveryAddress,
            email: this.customer.email,
            idCard: this.customer.idCard,
            signatoryImg: arrs.filter(item => item.imagePath !== null),
            userName: this.customer.userName,
            orderId: this.customer.orderId || this.orderId,
            phone: this.customer.phone,
            contactAddress: this.customer.contactAddress,
            certType: this.customer.certType,
            principalType: principalTypeChoice
          }

          if ((this.customer.idCardFront && this.customer.idCardFront.length > 0) &&
              (this.customer.idCardBack && this.customer.idCardBack.length > 0) ) {
            editCustomerInfo(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '操作成功！',
                  type: 'success'
                })
                this.customerUpdate = true
                // TODO
                // this.getDetailQualfication(this.currentOrderId)
              }
            }).catch(res => {

            })
          } else {
            this.$message({
              type: 'error',
              message: '客户签约资质相关照片为必填'
            })
            return
          }
        }
      })
    },
    /**
       * 审核个人资质
       * */
    setCustomerInfo(type) {
      const params = {
        type: type,
        id: this.customer.id || this.orderId,
        remark: type === 1 ? '' : this.rejectReason
      }
      let msg = ''
      if (type === 1) {
        msg = '个人资质审核已通过'
      } else {
        msg = '个人资质审核已驳回'
        if (!this.rejectReason) {
          this.$message({
            message: '驳回理由必填',
            type: 'warning'
          })
          return
        }
      }
      editCustomerQualfication(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: msg,
            type: 'success'
          })
          this.getDetailQualfication(this.currentOrderId)
          this.reasonDialog = false
        }
      }).catch(res => {

      })
    },
    /**
       * 审核校区资质
       * */
    setSchoolInfo(type) {
      const params = {
        type: type,
        id: this.school.id || this.orderId,
        remark: this.rejectReason
      }
      let msg = ''
      if (type === 1) {
        msg = '校区资质审核已通过'
      } else {
        msg = '校区资质审核已驳回'
        if (!this.rejectReason) {
          this.$message({
            message: '驳回理由必填',
            type: 'warning'
          })
          return
        }
      }
      editSchoolQualfication(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: msg,
            type: 'success'
          })
          this.getDetailQualfication(this.currentOrderId)
          this.reasonDialog = false
        }
      }).catch(() => {

      })
    },
    /**
       * 修改校区信息
       * */
    editSchoolInfo() {
      const imgs = [...this.school.leaseContract, ...this.school.schoolLicense, ...this.school.schoolImgs]
      if (this.school.schoolImgs && this.school.schoolImgs.length === 0) {
        this.$message({
          type: 'error',
          message: '校区照片必填'
        })
        return
      }
      const params = {
        schoolName: this.school.schoolName,
        address: this.school.address,
        leaseDate: this.school.leaseDate,
        orderId: this.school.orderId || this.orderId,
        schoolImg: imgs.filter(item => item.imagePath !== null),
        studentNumber: this.school.studentNumber,
        teacherNumber: this.school.teacherNumber
      }
      editSchoolInfo(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '操作成功！',
            type: 'success'
          })
          this.schoolUpdate = true
          // TODO
          // this.getDetailQualfication(this.currentOrderId)
        }
      }).catch(res => {

      })
    },
    /**
       * 审核企业资质
       * */
    setEnterpriseInfo(type) {
      const params = {
        type: type,
        id: this.enterprise.id || this.orderId,
        remark: this.rejectReason
      }
      let msg = ''
      if (type === 1) {
        msg = '企业资质审核已通过'
      } else {
        msg = '企业资质审核已驳回'
        if (!this.rejectReason) {
          this.$message({
            message: '驳回理由必填',
            type: 'warning'
          })
          return
        }
      }
      editEnterpriseQualfication(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: msg,
            type: 'success'
          })
          this.getDetailQualfication(this.currentOrderId)
          this.reasonDialog = false
        }
      }).catch(() => {

      })
    },
    /**
       * 修改企业信息
       * */
    editEnterpriseInfo() {
      this.$refs['enterpriseForm'].validate((valid) => {
        if (valid) {
          const imgs = this.enterprise.businessLicense
          if (this.enterprise.businessLicense && this.enterprise.businessLicense.length === 0) {
            this.$message({
              type: 'error',
              message: '企业签约资质相关照片为必填'
            })
            return
          }
          const params = {
            contactPhone: this.enterprise.contactPhone,
            creditCode: this.enterprise.creditCode,
            enterpriseAddress: this.enterprise.enterpriseAddress,
            enterpriseLegal: this.enterprise.enterpriseLegal,
            enterpriseName: this.enterprise.enterpriseName,
            enterpriseImg: imgs.filter(item => item.imagePath !== null),
            orderId: this.enterprise.orderId || this.orderId
          }
          editEnterpriseInfo(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '操作成功！',
                type: 'success'
              })
              this.enterpriseUpdate = true
              // //TODO
              // this.getDetailQualfication(this.currentOrderId)
            }
          }).catch(res => {

          })
        } else {

          return false
        }
      })
    },
    /**
       * 弹窗
       * */
    showRejectDialog(num) {
      this.rejectReason = ''
      if (num === '1') {
        this.reasonTitle = '个人资质-驳回理由'
      } else if (num === '2') {
        this.reasonTitle = '校区资质-驳回理由'
      } else if (num === '3') {
        this.reasonTitle = '企业资质-驳回理由'
      }
      this.reasonDialog = true
      this.currentQualification = num
    },
    getQualifications(type) {
      let str = ''
      switch (type) {
        case 0:
          str = 'warning'
          break
        case 1:
          str = 'success'
          break
        case 2:
          str = 'danger'
          break
      }
      return str
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    getAddress() {
      this.$forceUpdate()
    },
    /**
       * img的弹窗
       * */
    showImgDialog(img) {
      this.showImgSrc = img
      this.imgDialog = true
    },
    deleteImage(i) {
      this.customer.bankCard[0].imagePath = ''
    },
    /**
       * 转换字段
       */
    setQualificationStatus(data) {
      return converseEnToCn(qualificationStatus, data)
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    },
    /**
     * 打开选择资质的弹窗
     **/
    toChooseQualification() {
      this.$refs.chooseQualificationDialog.getLists()
    },

    /**
     * 选择资质回调
     * */
    getQualificationChoose(data, isSubmit) {
      if (this.showType !== 'uploadQualification' && this.showType !== 'shortcutUploadQualification') {
        return
      }
      Object.assign(this.enterprise, data.enterprise);
      if (!(JSON.stringify(this.enterprise) === '{}')) {
        this.$set(this.enterprise, 'orderId', this.orderId)
      }
      this.school.schoolImgs = [...data.school.schoolImgs];
      if (!(JSON.stringify(this.school) === '{}')) {
        this.$set(this.school, 'orderId', this.orderId)
      }
      Object.assign(this.customer, data.customer);
      if (!(JSON.stringify(this.customer) === '{}')) {
        this.$set(this.customer, 'orderId', this.orderId)
      }
      if (JSON.stringify(this.areaList) === '{}') {
        this.areaList = data.areaList
      } else {
        const areaList = Object.keys(this.areaList).length > Object.keys(data.areaList).length ? this.areaList : data.areaList
        Object.keys(areaList).forEach(key => {
          if (!this.areaList[key] || this.areaList[key].length === 0) {
            this.$set(this.areaList, key, data.areaList[key])
          }
        })
      }
      // 使用的话直接提交
      if (isSubmit === 'submit') {
        // 上传
        this.editCustomerInfo();
        this.editSchoolInfo();
        // 企业上传
        if (this.signType!==1) {
          this.editEnterpriseInfo();
        }
      }
       this.$forceUpdate();
    },

    /**
     * 获取选择资质列表
     * */
    getOrderQualificationLists() {
      const params = {
        'pageIndex': 1,
        'pageSize': 10,
        'clueCode': this.chooseQualificationParam.clueCode,
        'contractType': this.chooseQualificationParam.contractTypeSign
      }
      orderListQualification(params).then(res => {
        this.orderQualificationListsLoading = true
        if (res.data && res.data.length > 0) {
         this.doGetDetailQualfication(res.data[0].id)
        }
      })
    }

  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-card .el-form-item {
    margin-bottom: 15px;
  }
  /deep/ .el-upload-list--picture-card .el-upload-list__item{
    width: 100px;
    height: 100px;
    margin: 0 5px 0 0;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain; /* 宽度优先，保证全部显示 */
      display: block;
    }
  }
  /deep/ .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 20px;
    line-height: 100px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
  .required-items{
    position: relative;
    .items{
      position: absolute;
      top: 0;
      left: -100px;
      font-size: 12px;
      color: red;
      &.l118 {
        left: -118px;
      }
    }
  }
.confirm-dialog {
  font-size: 16px;
  line-height: 30px;
}
</style>
