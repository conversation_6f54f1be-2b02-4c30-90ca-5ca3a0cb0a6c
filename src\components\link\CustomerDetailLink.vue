<template>
    <span>
       <router-link :to="{ path: '/customer/detail/'+id, query: {title:'客户-'+name}}"
                    class="link-type">
          <slot name="default">{{ name }}</slot>
        </router-link>
    </span>
</template>
<script>
export default {
  name: 'CustomerDetailLink',
  props:{
    name:{
      type:String,
      default:'',
    },
    id:{
      type:Number,
      default:'',
      required:true
    }
  }
}
</script>
<style scoped>
</style>
