<template>
  <el-dialog v-el-drag-dialog title="字典内容详情" :visible.sync="dictionaryDetailDialog" :close-on-click-modal="!dictionaryDetailDialog" :append-to-body="true" width="30%">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="120px"
      :rules="baseInfoRules"
    >
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="字典内容名称：" prop="itemName"><el-input v-model="detail.itemName" placeholder="字典内容名称" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="字典值：" prop="itemValue"><el-input v-model="detail.itemValue" placeholder="字典值" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="排序值：" prop="sortOrder"><el-input v-model="detail.sortOrder" placeholder="排序值" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="描述：" prop="description"><el-input v-model="detail.description" placeholder="描述" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="是否启用：" prop="valid">
            <el-select v-model="detail.valid" placeholder="是否启用" filterable>
              <el-option
                v-for="item in menuValidList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer text-center">
      <el-button type="primary" @click="confirmExpressDetail">确 定</el-button>
      <el-button @click="closeExpressDetail">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addDictionaryDetail, editDictionaryDetail } from '@/api/system-setting'
import { menuValidList } from '@/utils/field-conver'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'DrawerDialog',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    },
    'dictId': {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      dictionaryDetailDialog: false,
      detail: {},
      baseInfoRules: {
        itemName: { required: true, message: '字典内容名称必填', trigger: 'blur' },
        itemValue: { required: true, message: '字典值必填', trigger: 'blur' },
        sortOrder: { required: true, message: '排序值必填', trigger: 'blur' },
        description: { required: true, message: '描述必填', trigger: 'blur' },
        valid: { required: true, message: '是否启用必选', trigger: 'blur' }
      },
      menuValidList: menuValidList
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      const that = this
      that.detail = {}
      that.dictionaryDetailDialog = true
    },
    editDetail(data) {
      const that = this
      that.detail = data
      that.dictionaryDetailDialog = true
    },
    /**
     * 确认修改信息
     */
    confirmExpressDetail() {
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!that.isEdit) {
            that.detail.dictId = that.dictId
            addDictionaryDetail(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '新增成功！',
                  type: 'success'
                })
              }
              that.dictionaryDetailDialog = false
              that.$emit('refresh')
            })
          } else {
            editDictionaryDetail(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              }
              that.dictionaryDetailDialog = false
              that.$emit('refresh')
            })
          }
        }
      })
    },
    closeExpressDetail() {
      this.dictionaryDetailDialog = false
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
</style>
