import request from '@/utils/request'
/**
 * 获取区域列表
 * */
export function examinationPaper(data) {
  return request({
    url: 'stip/examPaper/list',
    method: 'GET',
    params: data
  })
}
/**
 * 查询科目
 * */
export function getSubjects() {
  return request({
    url: 'stip/examPaper/getSubject',
    method: 'GET'
  })
}
/**
 * 查询班型
 * */
export function getClassType(clientCode, subjectId) {
  return request({
    url: `stip/examPaper/getClassType?clientCode=${clientCode}&subjectId=${subjectId}`,
    method: 'GET'
  })
}
/**
 * 删除试卷
 * */
export function removePaper(id) {
  return request({
    url: `stip/examPaper/remove/${id}`,
    method: 'DELETE'
  })
}
/**
 * 获取关联课程
 * */
export function getCourse(classTypeId, clientCode, subjectId) {
  return request({
    url: `stip/examPaper/getCourse?classTypeId=${classTypeId}&clientCode=${clientCode}&subjectId=${subjectId || ''}`,
    method: 'GET'
  })
}
/**
 * 新增试卷
 * */
export function addPaper(data) {
  return request({
    url: `stip/examPaper/add`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取试卷列表详情
 * */
export function getDetailPaper(id) {
  return request({
    url: `stip/examPaper/get/${id}`,
    method: 'GET'
  })
}
/**
 * 修改试卷
 * */
export function editPaper(data) {
  return request({
    url: `stip/examPaper/edit`,
    method: 'PUT',
    data: data
  })
}
/**
 * 获取试卷详情 包括关联的试题
 * */
export function getExamPaperIncludeQuestions(ids) {
  return request({
    url: `stip/examPaper/getExamPaperIncludeQuestions?id=${ids}`,
    method: 'GET'
  })
}
/**
 * 移除试题
 * */
export function removeQuestion(paperId, questionId) {
  return request({
    url: `stip/examPaper/removeQuestion?paperId=${paperId}&questionId=${questionId}`,
    method: 'GET'
  })
}
/**
 * 移除排序
 * */
export function sortQuestion(data) {
  return request({
    url: `stip/examPaper/sortQuestion`,
    method: 'PUT',
    data: data
  })
}
/**
 * 添加试题--获取试题列表
 * */
export function getQuestionList(data) {
  return request({
    url: `stip/examQuestion/list`,
    method: 'GET',
    params: data
  })
}
/**
 * 试题--获取可选大纲
 * */
export function getQuestionOutline(subjectId) {
  return request({
    url: `stip/examQuestion/getOutline?subjectId=${subjectId}`,
    method: 'GET'
  })
}
/**
 * 试题--获取可选考点
 * */
export function getQuestionKeynote(outlineId) {
  return request({
    url: `stip/examQuestion/getKeynote?outlineId=${outlineId}`,
    method: 'GET'
  })
}
/**
 * 选择试题
 * */
export function addQuestion(paperId, questionId) {
  return request({
    url: `stip/examPaper/addQuestion?paperId=${paperId}&questionId=${questionId}`,
    method: 'POST'
  })
}
/**
 * 删除试题
 * */
export function removeQuestionItem(id) {
  return request({
    url: `stip/examQuestion/remove/${id}`,
    method: 'DELETE'
  })
}
/**
 * 试题详情
 * */
export function questionInfo(id) {
  return request({
    url: `stip/examQuestion/get/${id}`,
    method: 'GET'
  })
}
/**
 * 新增试题
 * */
export function addQuestionInfo(data) {
  return request({
    url: `stip/examQuestion/add`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取试题详情
 * */
export function getExamQuestion(id) {
  return request({
    url: `stip/examQuestion/get/${id}`,
    method: 'GET'
  })
}
/**
 * 修改试题
 * */
export function editExamQuestion(data) {
  return request({
    url: `stip/examQuestion/edit`,
    method: 'PUT',
    data: data
  })
}
/**
 * 批量导入试题
 * */
export function batchAdd(data) {
  return request({
    url: `stip/examQuestion/batchAdd`,
    method: 'POST',
    data: data
  })
}
/**
 * 导入试题查询
 * */
export function batchImportMissionStatus(missionId) {
  return request({
    url: `stip/examQuestion/batchImportMissionStatus?missionId=${missionId}`
  })
}
/**
 * 故事试题列表
 * */
export function storyList(data) {
  return request({
    url: `/stip/storyExam/page`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: data
  })
}
/**
 * 故事试题启用/禁用
 * */
export function enableStory(id) {
  return request({
    url: `/stip/storyExam/enable?id=${id}`,
    method: 'GET'
  })
}
/**
 * 删除故事试题
 * */
export function removeStory(id) {
  return request({
    url: `/stip/storyExam/remove?id=${id}`,
    method: 'GET'
  })
}
/**
 * 新增故事题
 * */
export function addStory(data) {
  return request({
    url: `/stip/storyExam`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取故事试题详情
 * */
export function storyDetail(id) {
  return request({
    url: `/stip/storyExam/queryDetail?id=${id}`,
    method: 'GET'
  })
}
/**
 * 修改故事试题
 * */
export function editStory(data) {
  return request({
    url: `/stip/storyExam/edit`,
    method: 'POST',
    data: data
  })
}
/**
 * 故事课程
 * */
export function courseStory(classTypeId, subjectId) {
  return request({
    url: `/stip/storyExam/getCourse?classTypeId=${classTypeId}&subjectId=${subjectId}`,
    method: 'GET'
  })
}
/**
 * 关联班型
 * */
export function classStory(subjectId) {
  return request({
    url: `/stip/storyExam/getClassType?subjectId=${subjectId}`,
    method: 'GET'
  })
}

/**
 * 上传讲义
 * */
export function editCourseNotes(data) {
  return request({
    url: `/stip/courses/editCourseNotes`,
    method: 'POST',
    data: data
  })
}
/**
 * 上传讲义
 * */
export function editCourseNotesV2(data) {
  return request({
    url: `/stip/courses/v2/editCourseNotes`,
    method: 'POST',
    data: data
  })
}

/**
 * 上传落实本
 * */
export function editCourseExercises(data) { // stip/courses/editCourseExercises
  return request({
    url: `/stip/courses/editCourseExercises`,
    method: 'POST',
    data: data
  })
}
