<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <!--        <search id="header-search" class="right-menu-item" />-->

        <error-log class="errLog-container right-menu-item hover-effect" />

<!--        <screenfull id="screenfull" class="right-menu-item hover-effect" />-->
<!--        <el-tooltip content="Global Size" effect="dark" placement="bottom">-->
<!--          <size-select id="size-select" class="right-menu-item hover-effect" />-->
<!--        </el-tooltip>-->
        <span>
          <el-button type="text" size="medium" icon="el-icon-connection" v-permission-menu="['order:list']" @click="toHandoverList">交接单</el-button>
          <el-button type="text" size="medium" icon="el-icon-copy-document" v-permission-menu="['contract:list']" @click="toContractList">合同列表</el-button>
          <el-button type="text" size="medium" icon="el-icon-school" v-permission-menu="['customer:schoolProject']" @click="toSchoolList">校区列表</el-button>
          <el-button type="text" size="medium" icon="el-icon-box" v-permission-menu="['resources:list']" @click="toBoxList">盒子总览</el-button>
        </span>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <!--          <img src="@/assets/img/avatar.gif" class="user-avatar">-->
          <span>{{ userInfo.mobile }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span style="display:block;" @click="bindWx">绑定企业微信</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span style="display:block;" @click="unBindWx">解绑企业微信</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span style="display:block;" @click="logout">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      title="绑定企业微信"
      :visible.sync="dialogVisible"
      width="300px"
      @open="dialogOpen">
      <div class="st-flex st-justify-center">
        <img class="st-m-auto" :src="qrCodeBase64" alt="企业微信绑定二维码">
      </div>
      <div class="st-flex st-justify-center st-items-center">
        <img class="st-w-[26px] st-h-[22px]" :src="qiwx" alt="icon">
        <p class="st-text-[14px] st-text-gray-400">请使用企业微信扫描二维码绑定账号</p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import ErrorLog from '@/components/ErrorLog'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import QRCode from 'qrcode';
import qiwx from '@/assets/img/qiwx.png'
import { checkMenuPermission } from '@/utils/permission'
import {getRedirectUri, unBindWx} from "@/api/wxLogin";
// import Search from '@/components/HeaderSearch'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    ErrorLog,
    Screenfull,
    SizeSelect
  },
  data() {
    return {
      qiwx:qiwx,
      dialogVisible: false,
      qrCodeBase64: ''
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'device',
      'userInfo'
    ])
  },
  methods: {
    bindWx(){
      this.dialogVisible = true;
    },
    unBindWx(){
      this.$confirm('确认解绑企业微信？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const result = await unBindWx();
        console.log(result);
        if(result.code === '000000'){
          this.$message({
            type: 'success',
            message: '解绑成功'
          })
        }else{
          this.$message({
            type:'error',
            message: result.message
          })
        }
      })
    },
    async dialogOpen() {
      const result = await getRedirectUri(process.env.VUE_APP_WX_REDIRECT_URI);
      try {
        this.qrCodeBase64 = await QRCode.toDataURL(result.data, {
          width: 250,
          height: 250
        });
      } catch (error) {
        console.error('生成二维码出错:', error);
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    toSchoolList(){
        this.$router.push({
          name: 'SchoolProject',
        })
    },
    toHandoverList(){
        this.$router.push({
          name: 'Handover',
        })
    },
    toContractList(){
        this.$router.push({
          name: 'Contract',
        })
    },
    toBoxList(){
        this.$router.push({
          name: 'ResourcesList',
        })
    },
    logout() {
      this.$store.dispatch('user/logout').then(res => {
        // ?redirect=${this.$route.fullPath}
        this.$router.push(`/login`)
      }).catch(res => {

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      color: #5a5e66;
      font-weight: bolder;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
