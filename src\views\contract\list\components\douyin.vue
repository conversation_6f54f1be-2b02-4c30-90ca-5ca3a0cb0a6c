<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="120px" :rules="jtRules" :disabled="!isEdit">
      <!--    06 加推 -->
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约区域：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" />
      <el-row v-if="detail.contractOrder!=null" :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约开始日期：" label-width="110px">
                    <span>{{ detail.contractOrder.signStartTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约结束日期：" label-width="110px">
                    <span>{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col v-if="detail.contractSignatory != null" :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col v-if="detail.contractEnterprise != null" :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="detail.contractSignatory != null" :gutter="24">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同编号：" label-width="120px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同名称：" label-width="120px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：" label-width="120px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="120px">
                    <el-radio-group v-model="detail.contractType" class="radios">
                      <el-radio :label="1">个人合同</el-radio>
                       <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本：">
                    <div>{{ detail.versionType === 2 ? '预签' : '正式' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同开始日期：" label-width="120px" porp="startTime">
                    <el-date-picker v-model="detail.startTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同结束日期：" label-width="120px" porp="endTime">
                    <el-date-picker v-model="detail.endTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" />
                  </el-form-item>
                </el-col> -->
                <el-col :sm="24" :lg="24">
                  <el-form-item label="合同期限：" required>
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :md="{span:6}" :sm="{span:24}">
                  <el-form-item label="服务费(元)：" prop="douyinContractDTO.serviceFee">
                    <el-input v-model="detail.douyinContractDTO.serviceFee" placeholder="" @input="updateView($event)" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :md="{span:24}" :sm="{span:24}" label-width="120px">
                  <el-form-item label="补充内容：" label-width="120px">
                    <el-input    type="textarea" :row="2" show-word-limit v-model="detail.remark" maxlength="255" show-word-limit />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>
  </div>
</template>

<script>
import {
  getContractDetail,
  modifyContractDetail,
  getCreatContract
} from '@/api/contract'
// import AreaPicker from '@/components/area-picker'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  orderStatusList,
  contractClass,
  getContractStatus
} from '@/utils/field-conver'

export default {
  name: 'DouyinContract',
  // components: { AreaPicker },
  props: {},
  data() {
    return {
      detail: {
        douyinContractDTO: {}
      },
      jtRules: {
        cooperationType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        contractType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        publicSchool: {
          required: true,
          message: ' ',
          trigger: 'blur'
        },
        schoolAddress: {
          required: true,
          message: ' ',
          trigger: 'blur'
        },
        douyinContractDTO: {
          price: {
            required: true,
            message: ' ',
            trigger: 'change'
          },
          serviceDuration: {
            required: true,
            message: ' ',
            trigger: 'change'
          },
          serviceFee: {
            required: true,
            message: ' ',
            trigger: 'change'
          },
        }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      timeArr: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? '抖音-编辑合同' : '抖音-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    /**
       * 公共刷新方法
       * */
    updateView(e) {
      this.$forceUpdate()
    },
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = res.data
        that.timeArr = [that.detail.startTime, that.detail.endTime] || []
        that.detail.id = that.id
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        if (res.data.douyinContract !== null) {
          that.detail.douyinContractDTO = res.data.douyinContract
        } else {
          that.detail.douyinContractDTO = {
            serviceFee: 0,
          }
        }
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getCreatContract() {
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = res.data
        that.timeArr = [that.detail.startTime, that.detail.endTime] || []
        that.detail.id = res.data.contractId
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        if (res.data.douyinContract !== null) {
          that.detail.douyinContractDTO = res.data.douyinContract
        } else {
          that.detail.douyinContractDTO = {
            serviceFee: 0,
          }
        }
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
       * 确认修改信息
       */
    confirmEdit(num) {
      const that = this
      if (!this.detail.contractType) {
        this.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!this.timeArr || (this.timeArr && this.timeArr.length < 2)) {
        this.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }

      that.$refs.detailForm.validate(valid => {
        if (valid) {
          const data = Object.assign(that.detail, {
            operateType: num,
            startTime: this.timeArr[0],
            endTime: this.timeArr[1]
          })
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '修改成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(res => {

          })
        } else {

          return false
        }
      })
    },
    /**
       * 校验其他信息模块
       */
    checkOtherInfo() {
      const res = true
      for (const key in this.detail.douyinContractDTO) {
        if (this.detail.douyinContractDTO[key]) {
          return false
        }
      }
      return res
    },
    changeNumber() {
      const str = '' + this.detail.douyinContractDTO.accountNums
      if (str.indexOf('.') !== -1) {
        const arr = str.split('')
        arr.splice(arr.length - 1)
        const str2 = arr.join('')
        this.detail.douyinContractDTO.accountNums = +str2
      }
    },
    changeYear() {
      const str = '' + this.detail.douyinContractDTO.serviceDuration
      if (str.indexOf('.') !== -1) {
        const arr = str.split('')
        arr.splice(arr.length - 1)
        const str2 = arr.join('')
        this.detail.douyinContractDTO.serviceDuration = +str2
      }
    }
  }
}
</script>

<style scoped>
  .app-container.bgGrey {
    background-color: #EAEAEA;
  }

  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }

  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }

  /deep/ .otherInfo .el-form-item__label {
    width: 160px !important;
  }

  /deep/ .otherInfo .el-form-item__content {
    margin-left: 160px !important;
  }
</style>
