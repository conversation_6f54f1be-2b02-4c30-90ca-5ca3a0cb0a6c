<template>
  <div class="app-container bgGrey">
    <el-row :gutter="10">
      <el-form
        ref="form"
        size="small"
        :model="customerInfo"
        label-width="100px"
        :rules="rules"
      >
        <el-col
          v-if="submitType === 'details'"
          :span="24"
          class="time-line-col"
        >
          <div class="el-row--flex flex-align-item-center">
            <div style="width: 85px; padding-left: 15px">审批进度：</div>
            <el-timeline :reverse="reverse">
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
              >
                {{ activity.label }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 24 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>客户信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }">
                  <el-form-item label="客户编号：">
                    <div>{{ customerInfo.clueCode }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }">
                  <el-form-item label="客户名称：">
                    <div>{{ customerInfo.customer }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }">
                  <el-form-item label="手机号码：">
                    <div>{{ customerInfo.mobile }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }">
                  <el-form-item label="机构区域：">
                    <div>
                      <span v-if="customerInfo.provinceName"
                        >{{ customerInfo.provinceName }} |
                      </span>
                      <span v-if="customerInfo.cityName"
                        >{{ customerInfo.cityName }} |
                      </span>
                      <span v-if="customerInfo.areaName">{{
                        customerInfo.areaName
                      }}</span>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col
                  :xs="{ span: 24 }"
                  :sm="{ span: 24 }"
                  :md="{ span: 24 }"
                >
                  <el-form-item label="机构地址：">
                    <div>
                      <span v-if="customerInfo.address">{{
                        customerInfo.address
                      }}</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col
          :xs="{ span: 24 }"
          :sm="{ span: 24 }"
          :md="{ span: 24 }"
          class="purchase-top"
        >
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <el-form-item label="加盟项目：" prop="id">
                    <el-select
                      v-model="joinProject.id"
                      class="filter-item"
                      disabled
                    >
                      <el-option
                        v-for="item in comProjectList"
                        :key="item.id"
                        :label="item.projectName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <el-form-item label="支付类目：" prop="payItem">
                    <el-select
                      v-model="customerInfo.payItem"
                      placeholder="支付类目"
                      clearable
                      class="filter-item"
                      :disabled="submitType === 'details'"
                      @change="refresh"
                    >
                      <el-option
                        v-for="item in payList"
                        :key="item.id"
                        :label="item.itemName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  :xs="{ span: 24 }"
                  :sm="{ span: 12 }"
                  :md="{ span: 8 }"
                  v-if="
                    payNumProject.includes(Number(customerInfo.payItem || 0))
                  "
                >
                  <el-form-item
                    label="购买数量："
                    prop="payNum"
                    :rules="[
                      {
                        required: true,
                        message: '购买数量不能为空',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input
                      v-model="customerInfo.payNum"
                      @input="refresh"
                      type="number"
                      :disabled="submitType === 'details'"
                      placeholder="线下课时费、AI考勤机、华为平板、智能终端时必填"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <!-- 如果是冲减，-->
                  <el-form-item
                    label="应付金额："
                    prop="payAmount"
                    v-if="isReduce"
                  >
                    <el-input
                      v-model="customerInfo.payAmount"
                      type="number"
                      placeholder="应付金额"
                      maxlength="7"
                      :disabled="submitType === 'details'"
                      @input="handlePayAmountInput"
                    />
                  </el-form-item>
                  <el-form-item label="应付金额：" prop="payAmount" v-else>
                    <el-input
                      v-model="customerInfo.payAmount"
                      type="number"
                      placeholder="应付金额"
                      maxlength="7"
                      :disabled="submitType === 'details'"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <el-form-item label="校区编码：" prop="schoolCode">
                    <el-input v-model="schoolInfo.schoolCode" disabled />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <el-form-item label="校区名称：" disabled>
                    <div>{{ schoolInfo.schoolName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 8 }">
                  <el-form-item label="签约区域：">
                    <div>
                      <span v-if="schoolInfo.provinceName"
                        >{{ schoolInfo.provinceName }} |
                      </span>
                      <span v-if="schoolInfo.cityName"
                        >{{ schoolInfo.cityName }} |
                      </span>
                      <span v-if="schoolInfo.areaName">{{
                        schoolInfo.areaName
                      }}</span>
                    </div>
                  </el-form-item>
                </el-col>
                <!-- <el-col :xs="{span:24}" :sm="{span:12}" :md="{span:8}">
                  <el-form-item label="完款时间：" prop="completionTime">
                    <el-date-picker
                      v-model="customerInfo.completionTime"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :default-value="customerInfo.completionTime"
                      placeholder="选择日期"
                      style="width: 100%"
                      :disabled="submitType==='details'"
                      @input="refresh"
                    />
                  </el-form-item>
                </el-col> -->
                <el-col
                  :xs="{ span: 24 }"
                  :sm="{ span: 24 }"
                  :md="{ span: 24 }"
                  style="margin-top: 20px"
                >
                  <el-form-item label="备注：">
                    <el-input
                      v-model="remark"
                      type="textarea"
                      placeholder=""
                      maxlength="255"
                      show-word-limit
                      :disabled="submitType === 'details'"
                      @input="refresh"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 24 }">
          <el-card class="box-card" shadow="hover" style="margin-top: 20px">
            <div slot="header" class="clearfix">
              <span>推荐人信息</span>
            </div>
            <div class="text">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="姓名："  prop="recClueId" :rules="{
                  required: true,
                  message: '请选择推荐人',
                  trigger: 'change'
                }">
                    <el-select
                      v-model="customerInfo.recClueId"
                      size="small"
                      filterable
                      clearable
                      remote
                      reserve-keyword
                      placeholder="请输入推荐人姓名"
                      :remote-method="getOriginUserList"
                      :loading="recommendLoading"
                      style="width: 100%"
                      :disabled="submitType === 'details'"
                    >
                      <el-option
                        v-for="item in recommendUserList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 24 }">
          <el-card class="box-card" shadow="hover" style="margin-top: 20px">
            <div slot="header" class="clearfix">
              <span>业绩部门</span>
            </div>
            <div class="text">
              <el-form-item
                label="业绩部门："
                :rules="{
                  required: true,
                  message: '请选择业绩部门',
                  trigger: 'change'
                }"
                prop="salesDept"
              >
                <PerformanceDepartment
                  :value.sync="customerInfo.salesDept"
                  :disabled="submitType !== 'add'"
                  :isFormItem="true"
                />
              </el-form-item>
            </div>
          </el-card>
        </el-col>

        <el-col
          v-if="submitType !== 'details'"
          :xs="{ span: 24 }"
          :sm="{ span: 24 }"
          :md="{ span: 24 }"
          class="purchase-btns"
        >
          <div @click="addHandover">确定</div>
        </el-col>
      </el-form>
    </el-row>
    <el-row v-if="submitType === 'details'" style="margin-top: 20px">
      <!--      打款记录-->
      <el-col :lg="{ span: 24 }">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>打款记录</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="paymentRecordList"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%"
                >
                  <el-table-column
                    type="index"
                    label="#"
                    align="center"
                    width="40"
                  />
                  <af-table-column label="打款金额" prop="payAmount" />
                  <af-table-column label="打款方式" prop="payTypeName" />
                  <af-table-column
                    label="打款时间"
                    width="150"
                    prop="payTime"
                  />
                  <af-table-column
                    label="审核状态"
                    prop="auditStatus"
                    :formatter="getAuditStatus"
                  />
                  <af-table-column label="交易流水号" prop="transactionNo" />
                  <af-table-column
                    label="备注"
                    prop="remark"
                    show-overflow-tooltip
                  />
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!--    发货记录-->
    <el-row v-if="submitType === 'details'" style="margin: 20px 0">
      <el-col :lg="{ span: 24 }">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>发货记录</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="deliveryList"
                  border
                  fit
                  stripe
                  highlight-current-row
                >
                  <el-table-column
                    type="index"
                    label="#"
                    align="center"
                    width="40"
                  />
                  <af-table-column label="客户名称" prop="customer" />
                  <af-table-column label="校区名称" prop="institution" />
                  <af-table-column label="客户手机号" prop="mobile" />
                  <af-table-column label="加盟项目" prop="projectName" />
                  <af-table-column label="签约区域" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      {{ row.provinceName }}{{ row.cityName }}{{ row.areaName
                      }}{{ row.countyName }}{{ row.adress }}
                    </template>
                  </af-table-column>
                  <af-table-column label="物流公司" prop="shipName" />
                  <af-table-column
                    label="快递单号"
                    prop="shipNo"
                    show-overflow-tooltip
                  />
                  <af-table-column
                    label="发货类型"
                    prop="shipType"
                    :formatter="setShipType"
                  />
                  <af-table-column
                    label="发货单状态"
                    prop="status"
                    :formatter="setShipStatus"
                  />
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!--    操作与审核-->
    <el-row v-if="submitType === 'details'">
      <el-col :lg="{ span: 24 }">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>操作记录</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="optionsLogsList"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%"
                >
                  <el-table-column
                    type="index"
                    label="#"
                    align="center"
                    width="40"
                  />
                  <af-table-column label="操作记录" prop="auditTypeName" />
                  <af-table-column label="操作备注" prop="remark" />
                  <af-table-column label="操作人" prop="createBy" />
                  <af-table-column label="操作时间" width="200">
                    <template slot-scope="{ row }">
                      <span>{{
                        row.createTime | parseTime("{y}-{m}-{d} {h}:{i}:{s}")
                      }}</span>
                    </template>
                  </af-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getAllProject, getPayType, getRecommentList } from "@/api/common";
import { getCustomerDetail } from "@/api/customer";
import { getSchoolDetail } from "@/api/school";
import {
  addRebuildOrder,
  addClassOrder,
  getOrderDetail,
  updateClassOrder,
  getOptionLogs,
  getOrderDeliveryRecord
} from "@/api/handover";
import {
  orderStatusActiveList,
  payMethod,
  converseEnToCn,
  auditStatus,
  getShipType,
  getShipStatus,
  channelList
} from "@/utils/field-conver";
import { getOrderPaymentRecordList } from "@/api/payment";
import PerformanceDepartment from "@/components/PerformanceDepartment/PerformanceDepartment.vue";

export default {
  name: "CustomerPurchase",
  components: {
    PerformanceDepartment
  },
  inject: ["reload"],
  data() {
    return {
      reverse: false,
      activities: [],
      customerInfo: {},
      schoolInfo: {},
      joinProject: {},
      comProjectList: [],
      payList: [],
      rules: {
        payAmount: [
          { required: true, message: "请填写应付金额", trigger: "change" },
          { validator: this.validatePayAmount, trigger: "change" }
        ],
        // completionTime: { required: true, message: '请选择完成时间 ', trigger: 'change' },
        payItem: {
          required: true,
          message: "请选择支付类目 ",
          trigger: "change"
        }
      },
      clueId: "", // 客户id
      pjId: "", // 项目id
      schoolId: "", // 校区id
      submitType: null,
      institutionId: "",
      ids: "",
      optionsLogsList: [], // 操作记录
      paymentRecordList: [], // 打款记录列表
      deliveryList: [],
      customerName: "",
      remark: "",
      //以下项目时，购买数量必填
      payNumProject: [18, 19, 111, 112],
      //   [{
      //   'id': 18,
      //   'itemName': '线下课时费',
      // },
      //   {
      //     'id': 19,
      //     'itemName': '智能终端',
      //   },
      //   {
      //     'id': 112,
      //     'itemName': 'AI考勤机',
      //   },
      //   {
      //     'id': 111,
      //     'itemName': '华为平板',
      //   }]
      isReduce: false,
      recommendLoading: false,
      recommendUserList: [],
      channelList
    };
  },
  created() {
    this.isReduce = !!this.$route.query.isReduce;
    this.getOriginUserList("")
  },
  mounted() {
    this.clueId = this.$route.params.clueId || ""; // 客户id
    this.joinProject.id = this.$route.query.pjId;
    this.schoolId = this.$route.query.slId || ""; // 校区id
    this.submitType = this.$route.query.type;
    this.customerName = this.$route.query.name;

    if (this.$route.query.type === "add") {
      this.getCustomerInfo(this.clueId);
      this.setDefaultSchoolInfo(this.schoolId);
      this.institutionId = this.$route.query.spId;
    } else if (this.$route.query.type === "update") {
      this.getOrderDetailInfo(this.$route.query.spId);
    } else if (this.$route.query.type === "details") {
      this.getOrderDetailInfo(this.$route.query.spId);
      this.getPayRecord(this.$route.query.spId);
      this.getHandoverLogs(this.$route.query.spId);
      this.getDeliveryList(this.$route.query.spId);
    }
    this.setTagsViewTitle(this.customerName); // 设置当前tab 名
    this.getProject();
    this.getAccountType2("pay_item");
  },
  methods: {
    getOriginUserList(query) {
      this.recommendLoading = true;
      getRecommentList({
        channel: 1,
        search: query
      }).then(res => {
        this.recommendLoading = false;
        this.recommendUserList = res.data;
      });
    },

    getOrderDetailInfo(ids) {
      // 交接单详情
      getOrderDetail(ids)
        .then(res => {
          if (res.code === "000000") {
            this.orderDetailInfo = JSON.parse(JSON.stringify(res.data));
            this.customerInfo = this.orderDetailInfo.clueInfo; // 合伙人信息
            this.schoolInfo = this.orderDetailInfo.joinSchool; // 项目所属校区资料
            this.joinProject = this.orderDetailInfo.joinProject; // 加盟的项目
            this.institutionId = this.orderDetailInfo.institutionId;
            this.ids = this.orderDetailInfo.id;
            this.customerInfo.payNum =
              (this.orderDetailInfo.payNum && this.orderDetailInfo.payNum) || 0;
            this.customerInfo.payItem =
              this.orderDetailInfo.payItem !== null
                ? this.orderDetailInfo.payItem
                : "";
            this.customerInfo.payAmount =
              this.orderDetailInfo.payAmount &&
              this.orderDetailInfo.payAmount !== null
                ? this.orderDetailInfo.payAmount
                : null;
            this.customerInfo.completionTime =
              this.orderDetailInfo.completionTime &&
              this.orderDetailInfo.completionTime !== null
                ? this.orderDetailInfo.completionTime
                : null;
            this.remark = this.orderDetailInfo.remark;
            this.customerInfo.salesDept = this.orderDetailInfo.salesDept;
            this.customerInfo.recClueId = this.orderDetailInfo.recClueId;
            if (this.submitType === "details") {
              this.setCurrentOrderStatus(this.orderDetailInfo.status); // 设置当前进度条被激活的状态
            }
          }
        })
        .catch(() => {});
    },
    /**
     * 获取客户详情
     * */
    getCustomerInfo(customerId) {
      getCustomerDetail(customerId).then(res => {
        this.customerInfo = res.data;
      });
    },
    /**
     * 设置默认校区信息
     * */
    setDefaultSchoolInfo(id) {
      const params = {
        schoolId: id
      };
      getSchoolDetail(params).then(res => {
        if (res.code === "000000") {
          this.schoolInfo = res.data;
        }
      });
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this;
      getAllProject()
        .then(res => {
          that.comProjectList = res.data;
        })
        .catch(() => {});
    },
    getAccountType2(str) {
      const that = this;
      getPayType(str).then(res => {
        that.payList = res.data;
      });
    },
    addHandover() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const data = Object.assign(
            {},
            {
              payAmount: this.customerInfo.payAmount,
              payItem: this.customerInfo.payItem,
              institutionId: this.institutionId,
              remark: this.remark,
              payNum: this.customerInfo.payNum || 0,
              id: this.ids ? this.ids : "",
              salesDept: this.customerInfo.salesDept,
              userId: this.customerInfo.recClueId,
            }
          );

          if (
            this.payNumProject.includes(Number(this.customerInfo.payItem || 0))
          ) {
            if (
              this.customerInfo.payNum === "" ||
              this.customerInfo.payNum === 0 ||
              this.customerInfo.payNum === undefined
            ) {
              this.$message({
                type: "error",
                message: "请填写购买数量"
              });
              return false;
            }
          }
          if (this.submitType === "add") {
            // 新增
            if (this.isReduce) {
              addRebuildOrder(data)
                .then(res => {
                  if (res.code === "000000") {
                    this.$message({
                      type: "success",
                      message: "添加成功"
                    });
                    this.closeIt();
                  }
                })
                .catch(() => {});
            } else {
              addClassOrder(data)
                .then(res => {
                  if (res.code === "000000") {
                    this.$message({
                      type: "success",
                      message: "添加成功"
                    });
                    this.closeIt();
                  }
                })
                .catch(() => {});
            }
          } else if (this.submitType === "update") {
            // 修改
            updateClassOrder(data)
              .then(res => {
                if (res.code === "000000") {
                  this.$message({
                    type: "success",
                    message: "添加成功"
                  });
                  this.back();
                }
              })
              .catch(() => {});
          }
        } else {
          return false;
        }
      });
    },
    back() {
      this.$store
        .dispatch("tagsView/delView", this.$route)
        .then(({ visitedViews }) => {
          this.$router.go(-1);
          setTimeout(() => {
            this.reload();
          }, 100);
        });
    },
    closeIt() {
      this.$store
        .dispatch("tagsView/delView", this.$route)
        .then(({ visitedViews }) => {
          this.$router.push({
            name: "Handover"
          });
          setTimeout(() => {
            this.reload();
          }, 100);
        });
    },
    refresh() {
      this.$forceUpdate();
    },
    setCurrentOrderStatus(status) {
      this.activities = [];
      status = status === 21 ? 4 : status;

      orderStatusActiveList.forEach((item, index, arr) => {
        if (status > (item.value || item.flag)) {
          item.type = "primary";
          item.icon = "el-icon-check";
        } else {
          if (index === 0) {
            item.icon = "el-icon-more";
            item.type = "primary";
          } else {
            const beCurrent =
              arr[index - 1] &&
              (arr[index - 1]["value"] || arr[index - 1]["flag"]);
            const beNext =
              arr[index + 1] &&
              (arr[index + 1]["value"] || arr[index + 1]["flag"]);
            if (status > beCurrent && (beNext ? status < beNext : true)) {
              item.icon = "el-icon-more";
              item.type = "primary";
            }
          }
        }
        if (
          (status === 19 && item.value === 19) ||
          (status === 11 && item.value === 11)
        ) {
          item.icon = "el-icon-check";
        }
        item.icon && this.activities.push(item);
      });
    },
    /**
     * 获取打款记录
     * */
    getPayRecord(id) {
      const params = { orderId: id };
      getOrderPaymentRecordList(params).then(res => {
        this.paymentRecordList = res.data;
      });
    },
    /**
     * 获取操作记录
     * */
    getHandoverLogs(id) {
      getOptionLogs(id).then(res => {
        this.optionsLogsList = res.data;
      });
    },
    /**
     * 获取订单的发货列表
     * */
    getDeliveryList(id) {
      getOrderDeliveryRecord(id).then(res => {
        if (res.code === "000000") {
          this.deliveryList = res.data;
        }
      });
    },
    setTagsViewTitle(name) {
      let title = "";
      switch (this.submitType) {
        case "create":
          title = "创建交接单";
          break;
        case "update":
          title = "修改交接单";
          break;
        case "renew":
          title = "交接单续约";
          break;
        case "upgrade":
          title = "加盟升级";
          break;
        case "details":
          title = "复购交接单";
          break;
        case "add":
          title = "创建复购交接单";
          break;
      }
      const currentRoute = Object.assign({}, this.$route); // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${title}-${name}`
      });
      this.$store.dispatch("tagsView/updateVisitedView", route);
    },
    getPayMethod(row) {
      return converseEnToCn(payMethod, row.payMethod);
    },
    getAuditStatus(data) {
      return converseEnToCn(auditStatus, data.auditStatus);
    },
    setShipType(data) {
      return converseEnToCn(getShipType, data.shipType);
    },
    setShipStatus(data) {
      return converseEnToCn(getShipStatus, data.status);
    },
    // 验证应付金额必须为负数
    validatePayAmount(rule, value, callback) {
      if (this.isReduce) {
        if (value && Number(value) >= 0) {
          callback(new Error("应付金额必须为负数"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    // 处理应付金额输入
    handlePayAmountInput(value) {
      if (value === "") {
        this.customerInfo.payAmount = "";
        return;
      }

      let num = Number(value);
      if (num > 0) {
        num = -num;
      }
      this.customerInfo.payAmount = num;
      this.refresh();
    }
  }
};
</script>
<style scoped>
.time-line-col {
  background-color: #ffffff;
  padding: 12px 30px;
  border-radius: 30px;
  margin-bottom: 20px;
}

/deep/ .el-timeline-item__tail {
  position: absolute;
  top: 8px;
  width: 100%;
  border-top: 2px solid #dfe4ed;
  left: 0;
  height: 0;
  border-left: 0;
}

/deep/ .el-timeline-item {
  position: relative;
  display: inline-block;
  padding-right: 10px;
  padding-bottom: 0;
  min-width: 94px;
}

/deep/ .el-timeline-item__wrapper {
  position: relative;
  padding: 10px 0;
  left: -3px;
  top: 16px;
}

/deep/ .el-timeline-item__node--normal {
  width: 17px;
  height: 17px;
}

/deep/ .el-timeline-item__content {
  font-size: 12px;
}
</style>
