<template>
  <el-dialog
    v-if="interregionalPop"
    v-el-drag-dialog
    width="70%"
    title="跨区校验"
    center
    :close-on-click-modal="!interregionalPop"
    :visible.sync="interregionalPop"
    @close="changeMap"
  >
    <div v-if="schoolMap&&locationMap" class="map">
      <div class="location-tips">
        <span>校区ID：<el-tag size="mini">{{ schoolId }}</el-tag></span>
        <span v-if="addressDetail">校区地址:<el-tag size="mini" type="info">{{ addressDetail }}</el-tag></span>
        <span v-if="areaJoinType">校区加盟类型:<el-tag size="mini"  type="info">{{ areaJoinType }}</el-tag></span>
        <span v-if="modelType">定位方式:<el-tag size="mini"  type="info">{{ modelType===1?'半径':'区域' }}</el-tag></span>
        <span v-if="defaulRadius">校区半径:<el-tag size="mini"  type="info">{{ defaulRadius }}米</el-tag></span>
      </div>
      <div class="location-tips">
        <span v-if="gpsTime">本次定位时间:<el-tag size="mini" type="success">{{ gpsTime }}</el-tag></span>
        <span v-else><el-tag type="danger">暂无定位信息</el-tag> </span>
        <span v-if="gpsAddress">定位地址:<el-tag size="mini" type="success">{{ gpsAddress }}</el-tag></span>
      </div>
      <el-amap ref="map" vid="amapDemo" resize-enable="resize" :center="schoolMap" :plugin="plugin" :zoom="zoom" :events="events" class="amap-demo">
        <el-amap-marker :position="schoolMap" :icon="icon" />
        <el-amap-marker :position="locationMap" :icon="hicon" />
        <el-amap-circle
          :center="schoolMap"
          :radius="defaulRadius"
          :fill-opacity="0.3"
          :stroke-weight="0"
          :fill-color="'#409EFF'"
          :stroke-color="'#409EFF'"
          :events="events"
        />
      </el-amap>
    </div>
    <div v-else class="no-data">暂无经纬度</div>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
// import axios from 'axios'
import Icon from '@/assets/img/marker.png'
import HIcon from '@/assets/img/hicon.png'
import { getDeviceCheckRegion } from '@/api/charge'
import  { checkMenuPermission } from '@/utils/permission'
export default {
  name: 'CreateNewSchool',
  components: {
  },
  directives: {
    elDragDialog
  },
  data() {
    return {

      hadGetLngLat: false,
      zoom: 15,
      center: [105, 35],
      schoolMap: [117.206199, 31.854824],
      locationMap: [121.59996, 31.197646],
      defaulRadius: 500, // 默认招生范围半径1000米
      icon: Icon,
      hicon: HIcon,
      events: {
        init: (o) => {},
        'moveend': () => {},
        'zoomchange': () => {}
      },
      plugin: ['ToolBar', {
        pName: 'MapType',
        defaultType: 0,
        events: {
          init(o) {

          }
        }
      }],
      interregionalPop: false,
      schoolId:'',
      areaNameStr: '',
      addressDetail: '',
      gpsAddress:'',
      gpsTime: '',
      lng: '',
      lat: '',
      modelType: null,
      resize: true,
      areaJoinType: ''
    }
  },
  created() {
    // this.init()
  },
  methods: {
    changeMap() {
      this.schoolMap = [117.206199, 31.854824]
      this.locationMap = [121.59996, 31.197646]
    },
    getDeviceCheckRegion(ids) {
      getDeviceCheckRegion(ids).then(res => {
        if (res.code === '000000') {

          this.schoolId=res.data.schoolId;
          this.schoolMap = res.data.schoolLongitude && res.data.schoolLatitude ? [res.data.schoolLongitude, res.data.schoolLatitude] : [117.206199, 31.854824]
          this.locationMap = res.data.deviceLongitude && res.data.deviceLatitude ? [res.data.deviceLongitude, res.data.deviceLatitude] : [121.59996, 31.197646]
          this.modelType = res.data.modelType !== null ? res.data.modelType : ''
          this.defaulRadius = res.data.locationRadius !== null ? res.data.locationRadius : ''
          this.areaJoinType = res.data.areaJoinType !== null ? res.data.areaJoinType : ''
          if (res.data.locationRadius >= 5) {
            this.zoom = 13
          } else {
            this.zoom = 15
          }

        }
      }).catch(() => {

      })
    }
  }
}
</script>

<style scoped lang="scss">
  .amap-demo {
    height: 600px;
    width: 100%;
  }
  .no-data{
    width: 100%;
    text-align: center;
    font-size: 18px;
    color: #666;
    padding: 20px 0;
  }
  .location-tips{
    color: #666;
    font-size: 14px;
    padding: 5px 0;
  }
  .location-tips span{
    padding-right: 15px;
  }
  /deep/ .el-dialog__body{
    padding: 0 20px 20px;
  }
</style>
