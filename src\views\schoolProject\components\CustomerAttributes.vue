<template>
  <div>
      <el-dialog title="设置客户属性"  :visible.sync="visible" @close="close" width="500px" destroy-on-close>
        <el-form :model="form" label-width="100px">
          <el-form-item label="客户级别：">
            <el-select v-model="form.customerLevel" placeholder="客户级别"  class="st-w-full" filterable  clearable>
              <el-option
                v-for="item in customerLevelList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="生命周期：">
            <el-cascader
              class="st-w-full"
              placeholder="生命周期"
              v-model="form.lifeCycle"
              :options="lifeCycleList"
              :props="{emitPath: false,multiple:true}"
              clearable></el-cascader>
          </el-form-item>
        </el-form>
        <el-footer>
          <div class="st-w-full flex st-justify-center st-mt-[20px]">
            <el-button type="info" @click="close">取消</el-button>
            <el-button type="primary" @click="onsubmit">确定</el-button>
          </div>
        </el-footer>
      </el-dialog>
  </div>
</template>
<script >
import {lifeCycleList,customerLevelList} from "@/utils/field-conver";
import {institutions} from "@/api/school-project";

export default {
  name: 'CustomerAttributes',
  data() {
    return {
      lifeCycleList,
      customerLevelList,
      form: {
        id:'',
        customerLevel: '',
        lifeCycle: [],
      }
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'dialogVisible',
    event: 'update:dialogVisible'
  },
  computed:{
    visible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    }
  },
  methods: {
   async onsubmit(){
      if(this.form.customerLevel === '' && this.form.lifeCycle.length === 0){
        this.$message.error('请设置客户级别和生命周期')
        return;
      }
      institutions({
           id: this.form.id,
           customerLevel: this.form.customerLevel, //客户级别
           lifeCycle: this.form.lifeCycle.join(','), //客户级别
        }).then(resp=>{
            this.$message.success('设置成功')
            this.$emit('refresh')
            this.close()
        })
    },
    close() {
      this.$emit('update:dialogVisible', false)
      this.form.id = '';
      this.form.customerLevel = '';
      this.form.lifeCycle =  [];
    },
    setForm({id,lifeCycle,customerLevel}) {
      this.form.id = id;
      this.form.lifeCycle = lifeCycle?.split(',');
      this.form.customerLevel = customerLevel;
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
