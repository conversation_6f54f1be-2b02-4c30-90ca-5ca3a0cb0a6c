import request from '@/utils/request'

/**
 * 订单详情查询接口
 * @param data
 */
export function getOrderDetail(data) {
  return request({
    url: 'order/getOrder/' + data,
    method: 'get'
  })
}
/**
 * 获取操作记录
 * @param data
 */
export function getOptionLogs(data) {
  return request({
    url: 'order/getAuditLog/' + data,
    method: 'get'
  })
}
/**
 * 交接单列表
 * @param data
 */
export function getOrderList(data) {
  return request({
    url: 'order/page',
    method: 'GET',
    params: data
  })
}

/**
 * 交接单列表
 * @param id
 */
export function forceDeleteOrder(id) {
  return request({
    url: `/order/deleteOrder/${id}`,
    method: 'DELETE'
  })
}
/**
 * 获取套餐详情
 * @param data
 */
export function getPolicyDetail(data) {
  return request({
    url: 'policy/getPolicy/' + data,
    method: 'GET'
  })
}
/**
 * 获取套餐详情
 * @param data
 */
export function getPolicyList(data) {
  return request({
    url: 'policy/list',
    method: 'GET',
    params: data
  })
}
/**
 * 创建交接单
 */
export function createOrder(data) {
  return request({
    url: 'order/createOrderV3',
    method: 'post',
    data: data
  })
}
/**
 * 创建三千星球交接单
 */
export function addSqXqOrder(data) {
  return request({
    url: 'order/addSqXqOrder',
    method: 'post',
    data: data
  })
}
/**
 * 编辑交接单
 */
export function editOrder(data) {
  return request({
    url: 'order/editOrder',
    method: 'PUT',
    data: data
  })
}
/**
 * 编辑三千星球交接单
 */
export function updateSqXqOrder(data) {
  return request({
    url: 'order/updateSqXqOrder',
    method: 'PUT',
    data: data
  })
}
/**
 * 编辑交接单产品信息
 */
export function editOrderProductDTO(data) {
  return request({
    url: 'order/editOrderProductDTO',
    method: 'PUT',
    data: data
  })
}
/**
 * 市场审核
 */
export function marketExamine(data) {
  return request({
    url: 'order/auditOrder',
    method: 'PUT',
    params: data
  })
}
/**
 * 获取发货信息
 */
export function getDeliveryDetail(orderId, type) {
  return request({
    url: 'order/' + orderId + '/delivery/' + type,
    method: 'GET'
  })
}
/**
 * 查询发货记录
 */
export function deliveryList(data) {
  return request({
    url: 'deliveries/page',
    method: 'GET',
    params: data
  })
}
/**
 * 查询发货单记录详情
 */
export function deliveryDetail(data) {
  return request({
    url: 'deliveries/' + data,
    method: 'GET'
  })
}
/**
 * 新增发货记录
 */
export function createDeliveryOrder(data) {
  return request({
    url: 'deliveries',
    method: 'post',
    data: data
  })
}
/**
 * 获取快递列表
 */
export function getExpress(data) {
  return request({
    url: 'express',
    method: 'get'
  })
}
/**
 * 新增发货单
 */
export function addDeliveryDetail(data) {
  return request({
    url: 'deliveries',
    method: 'POST',
    data: data
  })
}
/**
 * 获取订单发货记录列表
 */
export function getOrderDeliveryRecord(data) {
  return request({
    url: 'deliveries/order/' + data,
    method: 'GET'
  })
}
/**
 * 获取订单发货记录列表
 */
export function cofirmDelivery(data) {
  return request({
    url: 'deliveries/' + data,
    method: 'PUT'
  })
}
/**
 * 市场提交订单
 */
export function marketSubmitOrder(data) {
  return request({
    url: 'order/submitOrder',
    method: 'PUT',
    params: data
  })
}
/**
 * 发货单撤回
 */
export function withdraw(id) {
  return request({
    url: 'deliveries/invalid/' + id,
    method: 'PUT'
  })
}
/**
 * 同步至播客系统
 */
export function synchronization(id) {
  return request({
    url: 'deliveries/sync/' + id,
    method: 'PUT'
  })
}
/**
 * 重新签约
 */
export function reSign(orderId) {
  return request({
    url: 'contracts/reSign/' + orderId,
    method: 'PUT'
  })
}
/**
 * 撤销
 */
export function undo(id) {
  return request({
    url: 'order/cancel/' + id,
    method: 'PUT'
  })
}

/**
 * 新增班型复购交接单
 */
export function addClassOrder(data) {
  return request({
    url: 'order/addClassOrder',
    method: 'POST',
    data: data
  })
}

/**
 * 新增班型复购交接单
 */
export function addRebuildOrder(data) {
  return request({
    url: 'order/addRebuildOrder',
    method: 'POST',
    data: data
  })
}
/**
 * 修改班型复购交接单
 */
export function updateClassOrder(data) {
  return request({
    url: 'order/updateClassOrder',
    method: 'PUT',
    data: data
  })
}
/**
 * 标记流量包开通状态
 */
export function flowDataPackage(orderId) {
  return request({
    url: `order/openFlowDataPackage/${orderId}`,
    method: 'POST'
  })
}

/**
 * 交接单退款
 */
export function refundHandover(data) {
  return request({
    url: `order/refund`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取实名认证地址接口
 */
export function certificationAdress(orderId) {
  return request({
    url: `/auth/getAuthUrlV3/${orderId}`,
    method: 'GET'
  })
}

// 获取业绩归属部门
export function getPerformanceDepartment() {
  return request({
    url: '/dicts/items/sales_dept',
    method: 'GET'
  })
}

// /order/updateSalesDept
export function updateSalesDept(params) {
  return request({
    url: '/order/updateSalesDept',
    method: 'post',
    params
  })
}
