<template>
  <el-dialog :visible.sync="setPackagePop" :title="setPackageTitle" :close-on-click-modal="!setPackagePop" width="50%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="classForms" label-width="100px">
        <el-form-item v-if="flags===1" label="设置流量包" required>
          <el-col :span="20">
            <el-select v-model="productId" placeholder="请选择流量包" filterable class="filter-item" @change="getPackage">
              <el-option v-for="item in packageList" :key="item.id" :label="item.productName" :value="item.id" />
            </el-select>
          </el-col>
          <el-col :span="4" v-if="applyClassTypeList && applyClassTypeList.length >0" >
            <el-checkbox style="float:right;"    v-model="guesscheckAllListFlag" @change="guessAllChangeList">全选</el-checkbox>
          </el-col>
        </el-form-item>
        <div v-if="flags===0">
          <h3 style="display:contents;">
            <span>流量包名称：</span>
            <em>{{ productName }}</em>
          </h3>
          <div v-if="applyClassTypeList && applyClassTypeList.length >0" >
            <el-checkbox style="float:right;"   v-model="guesscheckAllListFlag" @change="guessAllChangeList">全选</el-checkbox>
          </div>
        </div>
        <div v-if="rangeType===1" class="packages-list" v-loading="packageLoading">
          <div v-for="(item,i) in applyClassTypeList" :key="item.id" class="package-list">
            <em class="package-title">{{ item.name }}</em>
            <el-checkbox
              v-model="guesscheckAllList[i]"
              @change="guessAllChange(guesscheckAllList[i],i,item.id)">全选</el-checkbox>
            <p>
              <el-checkbox-group
                v-model="checkedGuess[i]"
                @change="guessChange( item.id,checkedGuess[i],i)"
              >
                <el-checkbox
                  v-for="itemChildren in item.applySubjectList"
                  :key="itemChildren.id"
                  :label="itemChildren.id"
                  :checked="itemChildren.check"
                >{{ itemChildren.name }}</el-checkbox>
              </el-checkbox-group>
            </p>
          </div>
        </div>
        <div v-if="rangeType===2" class="packages-list">
          <el-checkbox-group v-model="applySubjectListChecked">
            <el-checkbox v-for="item in applySubjectList" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="changeInit">取消</el-button>
          <!--新增-->
          <el-button v-if="flags===1" type="primary" size="mini" @click="packageConfirm">确定</el-button>
          <!--修改-->
          <el-button v-if="flags===0" type="primary" size="mini" @click="editPackageConfirm">确定</el-button>
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
import { optionalPackage, packageList, setPackages, packageDetail, editFlowPackage } from '@/api/schoolCampus'
export default {
  name: 'SetPackage',
  inject: ['reload'],
  data() {
    return {
      setPackagePop: false,
      packageLoading: true,
      setPackageTitle: '',
      setPackaForm: {},
      packageList: [],
      rangeType: -1, // 适用类型（1.班型,2.科目)
      applyClassTypeList: [], // 班型
      applySubjectList: [], // 科目
      productId: '',
      classTypeId: [],
      applyClassList: [],
      guesscheckAllList: [], // 全选
      guesscheckAllListFlag: false, // 最大的全选
      isguessIndeterminate: [],
      checkedGuess: [],
      subjectsJson: [],
      applySubjectListChecked: [],
      schoolId: '',
      isEdit: false,
      flags: -1,
      productName: '', // 流量包名称
      packageId: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
    })
  },
  methods: {
    packageDetail(ids) {
      packageDetail(ids).then(res => {
        if (res.code === '000000') {
          this.packageLoading = false
          this.productId = res.data.productId
          this.productName = res.data.productName
          this.rangeType = res.data.rangeType
          const classList = []
          const applyClassTypeList = res.data.applyClassTypeList !== null && res.data.applyClassTypeList.length > 0 ? res.data.applyClassTypeList.filter(item => item !== null) : []
          for (let i = 0; i < applyClassTypeList.length; i++) {
            this.checkedGuess[i] = []
            const obj = {}
            obj['id'] = applyClassTypeList[i].id
            obj['name'] = applyClassTypeList[i].name
            const subList = []
            const checkedArr = []
            for (let n = 0; n < applyClassTypeList[i].applySubjectList.length; n++) {
              const subObj = {}
              subObj['id'] = applyClassTypeList[i].applySubjectList[n].id
              subObj['name'] = applyClassTypeList[i].applySubjectList[n].name
              if (applyClassTypeList[i].applySubjectList[n].check === 1) {
                subObj['check'] = true
                checkedArr.push(applyClassTypeList[i].applySubjectList[n].id)
              } else {
                subObj['check'] = false
              }
              subList.push(subObj)
            }
            if (checkedArr.length === applyClassTypeList[i].applySubjectList.length && applyClassTypeList[i].applySubjectList.length > 0) { // 控制全选
              // obj['check'] = true
              this.guesscheckAllList[i] = true
            } else if (applyClassTypeList[i].check !== 1) {
              // obj['check'] = false
              this.guesscheckAllList[i] = false
            }
            obj['applySubjectList'] = subList
            classList.push(obj)
          }
          this.applyClassTypeList = classList
          this.applySubjectList = res.data.applySubjectList !== null && res.data.applySubjectList.length > 0 ? res.data.applySubjectList.filter(item => item !== null) : []
          const SubjectListChecked = this.applySubjectList.filter(item => item.check === 1)
          this.applySubjectListChecked = SubjectListChecked && SubjectListChecked.length > 0 ? SubjectListChecked.map(item => item.id) : []
        }
      }).catch(() => {

      })
    },
    optionalPackage(schoolId) {
      if (schoolId) {
        const params = {
          schoolId: schoolId
        }
        optionalPackage(params).then(res => {
          if (res.code === '000000') {
            this.packageList = res.data || []
          }
        })
      }
    },
    getPackage(val) {
      if (val) {
        this.packageLoading = true
        packageList(val).then(res => {
          if (res.code === '000000') {
            this.$forceUpdate()
            this.packageLoading = false
            this.rangeType = res.data.rangeType
            const classListDefault = []
            if (res.data.applyClassTypeList !== null && res.data.applyClassTypeList && res.data.applyClassTypeList.length > 0) {
              for (let i = 0; i < res.data.applyClassTypeList.length; i++) {
                this.checkedGuess[i] = []
                const objDefault = {}
                objDefault['id'] = res.data.applyClassTypeList[i].id
                objDefault['name'] = res.data.applyClassTypeList[i].name
                const subListDefault = []
                const selectAll = []
                for (let n = 0; n < res.data.applyClassTypeList[i].applySubjectList.length; n++) {
                  const subObjDefault = {}
                  subObjDefault['id'] = res.data.applyClassTypeList[i].applySubjectList[n].id
                  subObjDefault['name'] = res.data.applyClassTypeList[i].applySubjectList[n].name
                  if (res.data.applyClassTypeList[i].applySubjectList[n].check === 1) {
                    this.$set(subObjDefault, 'check', true)
                    selectAll.push(res.data.applyClassTypeList[i].applySubjectList[n].id)
                  } else {
                    this.$set(subObjDefault, 'check', false)
                  }
                  subListDefault.push(subObjDefault)
                }
                if (selectAll.length === res.data.applyClassTypeList[i].applySubjectList.length && res.data.applyClassTypeList[i].applySubjectList.length > 0) { // 控制全选
                  // objDefault['check'] = true
                  this.guesscheckAllList[i] = true
                } else if (res.data.applyClassTypeList[i].check !== 1) {
                  // objDefault['check'] = false
                  this.guesscheckAllList[i] = false
                }
                objDefault['applySubjectList'] = subListDefault
                classListDefault.push(objDefault)
              }
              this.$forceUpdate()
              this.applyClassTypeList = classListDefault
            } else {
              this.applyClassTypeList = []
            }
            this.applySubjectList = res.data.applySubjectList !== null && res.data.applySubjectList.length > 0 ? res.data.applySubjectList.filter(item => item !== null) : []
          }
        })
      }
    },
    // 单个全选处理
    guessAllChange(bolean, index, classTypeId) {
      if (bolean) {
        var arr = []
        this.applyClassTypeList[index].applySubjectList.forEach(item => {
          arr.push(item.id)
        })
        this.checkedGuess[index] = arr
        this.$forceUpdate()
      } else {
        this.checkedGuess[index] = []
        this.$forceUpdate()
      }
    },

    // 全部全选处理
    guessAllChangeList(bool) {
      for (let i = 0; i < this.applyClassTypeList.length; i++) {
        if (bool) {
          var arr = []
          this.applyClassTypeList[i].applySubjectList.forEach(item => {
            arr.push(item.id)
          })
          this.checkedGuess[i] = arr
        } else {
          this.checkedGuess[i] = []
        }
        this.guesscheckAllList[i] = bool
      }
      this.$forceUpdate()
    },

    // 单选
    guessChange(val1, val2, index) {
      var checkedCount = val2.length
      var arrARy = this.applyClassTypeList.filter(res => res.id === val1)
      this.guesscheckAllList[index] =
        checkedCount === arrARy[0].applySubjectList.length
      this.isguessIndeterminate[index] =
        checkedCount > 0 && checkedCount < arrARy[0].applySubjectList.length
      // 总全部选择
      this.guesscheckAllListFlag = this.guesscheckAllList.every(x => x === true)
      this.$forceUpdate()
    },
    changeInit() {
      this.setPackagePop = false
      this.productId = ''
      this.schoolId = ''
      this.rangeType = ''
      this.packageId = ''
      this.applySubjectListChecked = []
      this.checkedGuess = []
    },
    packageConfirm() {
      const arr = []
      this.checkedGuess.forEach((res, i) => {
        if (res.length > 0) {
          let obj = {}
          obj = {
            classTypeId: this.applyClassTypeList[i].id,
            applySubjectList: res
          }
          arr.push(obj)
        }
      })
      if (!this.productId) {
        this.$message({
          type: 'warning',
          message: '请选择流量包'
        })
      } else
      if (this.schoolId && this.productId && this.rangeType) {
        const params = Object.assign({}, {
          productId: this.productId,
          rangeType: this.rangeType,
          schoolId: this.schoolId,
          applyClassTypeList: arr || [],
          applySubjectList: this.applySubjectListChecked || []
        })
        setPackages(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '设置成功'
            })
            this.setPackagePop = false
            this.productId = ''
            this.schoolId = ''
            this.rangeType = ''
            this.applySubjectListChecked = []
            this.checkedGuess = []
            this.$emit('updatePackage')
          }
        }).catch(() => {

        })
      }
      this.$forceUpdate()
    },
    editPackageConfirm() { // 修改
      const arr = []
      this.checkedGuess.forEach((res, i) => {
        if (res.length > 0) {
          const obj = {
            classTypeId: this.applyClassTypeList[i].id,
            applySubjectList: res
          }
          arr.push(obj)
        }
      })
      if (this.schoolId && this.productId && this.rangeType && this.packageId) {
        const params = Object.assign({}, {
          productId: this.productId,
          rangeType: this.rangeType,
          schoolId: this.schoolId,
          applyClassTypeList: arr || [],
          id: this.packageId,
          applySubjectList: this.applySubjectListChecked || []
        })
        editFlowPackage(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '修改成功'
            })
            this.setPackagePop = false
            this.productId = ''
            this.schoolId = ''
            this.rangeType = ''
            this.packageId = ''
            this.applySubjectListChecked = []
            this.checkedGuess = []
            this.$emit('updatePackage')
          }
        }).catch(() => {

        })
      }
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.assing-info{
  max-height: 600px;
  overflow-y: auto;
}
</style>
