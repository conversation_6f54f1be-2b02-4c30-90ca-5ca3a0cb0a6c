<template>
  <el-select v-model="tmpId" style="width: 140px;" filterable clearable placeholder="教师" no-data-text="请先选择班型和科目">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="item.name"
      :value="`${item.id}`">
      <div style="min-width: 200px;">
        <span class="fl">{{ item.name }}</span>
        <span class="subTitle">{{ item.id }}</span>
      </div>
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getTeacherByClassAndSubject, getTeachers } from '@/api/classType'

/**
 * 科目选择框
 */
export default {
  name: 'TeacherByCSSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    classTypeId: {
      type: [String, Number],
      required: false
    },
    subjectId: {
      type: [String, Number],
      required: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  watch: {
    classTypeId(val) {
      this.getList()
    },
    subjectId(val) {
      this.getList()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      if (!this.classTypeId || !this.subjectId) return
      const data= {
        classTypeId: this.classTypeId,
        subjectId: this.subjectId
      }
      this.loading = true
      getTeacherByClassAndSubject(data).then(res => {
        this.loading = false
        if (res.code === SUCCESS) {
          this.dataList = res.data
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.subTitle {
  float: right;
  color: #8492a6;
  font-size: 13px
}
</style>
