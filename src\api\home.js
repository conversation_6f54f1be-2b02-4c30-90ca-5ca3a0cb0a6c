// 首页相关接口
import request from '@/utils/request'

/**
 * 首页待办
 * @param data
 */
export function pageList(data) {
  return request({
    url: 'handleNotice/pageList/',
    method: 'get',
    params: data
  })
}

/**
 * 上传本地合同
 * @param data
 */
export function uploadLocalContract(data) {
  return request({
    url: `contracts/upOfflineContract?contractId=${data.id}&orderId=${data.orderId}`,
    method: 'POST',
    data: data.list
  })
}
