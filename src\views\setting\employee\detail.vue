<template>
  <div class="employeeDialog app-container bgGrey" title="员工信息详情">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="180px"
      :disabled="!isEdit"
      :rules="emploeeDetailRules"
    >
      <el-row :gutter="18">
        <el-col :lg="{ span: 24 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>必填信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="姓名：" prop="realName">
                    <el-input
                      v-model="detail.realName"
                      placeholder="请输入姓名"
                      maxlength="5"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="性别：" prop="gender">
                    <el-select
                      v-model="detail.gender"
                      placeholder="请输入性别"
                      class="filter-item"
                      style="width: 100%"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in genderList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="部门：" prop="deptId">
                    <el-select
                      v-model="detail.deptId"
                      placeholder="部门"
                      class="filter-item"
                      style="width: 100%"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in parentDepartmentList"
                        :key="item.id"
                        :label="item.departmentName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="身份证：" prop="idCard">
                    <el-input
                      v-model="detail.idCard"
                      placeholder="请输入身份证"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="手机号：" prop="mobile">
                    <el-input
                      v-model="detail.mobile"
                      placeholder="请输入手机号"
                      oninput="if(value.length>11)value=value.slice(0,11)"
                      onkeyup="this.value=this.value.replace(/\D/g,'')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="邮箱：" prop="email">
                    <el-input v-model="detail.email" placeholder="请输入邮箱" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="是否是外部人员：" prop="isExternal">
                    <el-switch v-model="detail.isExternal" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                  <el-form-item label="微信二维码：">
                    <div class="upload-imgs">
                      <div v-if="!uploadImg && isEdit">
                        <input
                          ref="inputerA"
                          type="file"
                          class="upload"
                          multiple
                          accept="image/png,image/jpeg,image/gif,image/jpg"
                          @change="upload($event)"
                        >
                        <a
                          class="add"
                        ><i class="iconfont icon-plus" />
                          <p>点击上传</p></a>
                      </div>
                      <p class="img">
                        <img v-if="uploadImg" :src="uploadImg">
                        <a
                          v-if="uploadImg && isEdit"
                          class="close"
                          @click="delImgB"
                        >
                          <i class="el-icon-delete" />
                        </a>
                      </p>
                    </div>
                    <em
                      v-if="!uploadImg && isEdit"
                    >建议上传的二维码图片大小为190*190像素</em>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :lg="{ span: 24 }">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>选填信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="工号：">
                    <el-input v-model="detail.workNo" placeholder="工号" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="政治面貌：">
                    <el-select
                      v-model="detail.politicsStatus"
                      placeholder="政治面貌"
                      class="filter-item"
                      style="width: 100%"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in politicsStatus"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="婚姻状况：">
                    <el-select
                      v-model="detail.isMarried"
                      placeholder="请输入婚姻状况"
                      clearable
                      class="filter-item"
                      style="width: 100%"
                      filterable
                    >
                      <el-option
                        v-for="item in getMarriedStatus"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="入职日期：">
                    <el-date-picker
                      v-model="detail.joinDate"
                      type="date"
                      placeholder="入职日期"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="员工档案编号：">
                    <el-input
                      v-model="detail.userSn"
                      placeholder="请输入员工档案编号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="工作年限：">
                    <el-input
                      v-model="detail.workSeniority"
                      placeholder="请输入工作年限"
                      oninput="if(value.length>2)value=value.slice(0,2)"
                      onkeyup="this.value=this.value.replace(/\D/g,'')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="最高学历：">
                    <el-input
                      v-model="detail.education"
                      placeholder="请输入最高学历"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="毕业院校：">
                    <el-input v-model="detail.school" placeholder="毕业院校" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="毕业时间：">
                    <el-date-picker
                      v-model="detail.graduationDate"
                      type="date"
                      placeholder="毕业时间"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="紧急联系人名称：">
                    <el-input
                      v-model="detail.emgrContactName"
                      placeholder="请输入紧急联系人名称"
                      maxlength="5"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="紧急联系人电话：">
                    <el-input
                      v-model="detail.emgrContactTel"
                      placeholder="请输入紧急联系人电话"
                      oninput="if(value.length>11)value=value.slice(0,11)"
                      onkeyup="this.value=this.value.replace(/\D/g,'')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="紧急联系人和员工关系：">
                    <el-input
                      v-model="detail.relationType"
                      placeholder="请输入紧急联系人和员工关系"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="银行卡号：">
                    <el-input
                      v-model="detail.bankNo"
                      placeholder="请输入银行卡号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="公积金账号：">
                    <el-input
                      v-model="detail.accumulationNo"
                      placeholder="请输入公积金账号"
                      onkeyup="this.value=this.value.replace(/\D/g,'')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="开户行：">
                    <el-input
                      v-model="detail.openingBank"
                      placeholder="请输入开户行"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="健康状况：">
                    <el-select
                      v-model="detail.health"
                      placeholder="健康状况"
                      class="filter-item"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in health"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="居住地址：">
                    <el-input
                      v-model="detail.homeAddress"
                      placeholder="请输入居住地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="出生年月日：">
                    <el-date-picker
                      v-model="detail.birthday"
                      type="date"
                      placeholder="出生年月日"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="工作性质：">
                    <el-select
                      v-model="detail.jobType"
                      placeholder="工作性质"
                      class="filter-item"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in jobType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="专业：">
                    <el-input v-model="detail.major" placeholder="请输入专业" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="合同开始日期：">
                    <el-date-picker
                      v-model="detail.contractStart"
                      type="date"
                      placeholder="合同开始日期"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="合同结束日期：">
                    <el-date-picker
                      v-model="detail.contractEnd"
                      type="date"
                      placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="合同年限：">
                    <el-input
                      v-model="detail.contractYears"
                      placeholder="请输入合同年限"
                      oninput="if(value.length>2)value=value.slice(0,2)"
                      onkeyup="this.value=this.value.replace(/\D/g,'')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="民族：">
                    <el-input
                      v-model="detail.nationality"
                      placeholder="请输入民族"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="籍贯：">
                    <el-input
                      v-model="detail.nativePlace"
                      placeholder="请输入籍贯"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="户籍地址：">
                    <el-input
                      v-model="detail.permanentAddress"
                      placeholder="请输入户籍地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="户口类型：">
                    <el-select
                      v-model="detail.residenceType"
                      placeholder="户口类型"
                      class="filter-item"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in residenceType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="公司办公电话：">
                    <el-input
                      v-model="detail.officeNum"
                      placeholder="请输入公司办公电话"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{ span: 24 }" :sm="{ span: 6 }">
                  <el-form-item label="线下员工档案编号：">
                    <el-input
                      v-model="detail.offlineNo"
                      placeholder="请输入线下员工档案编号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="this.$route.params.method !== 0"
        type="primary"
        @click="confirmEmployeeDetail('detailForm')"
      >确定</el-button>
      <el-button type="primary" @click="closeIt">关 闭</el-button>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import {
  getEmployeeDetail,
  addEmployeeDetail,
  editEmployeeDetail
} from '@/api/system-setting'
import {
  genderList,
  converseEnToCn,
  getMarriedStatus,
  politicsStatus,
  health,
  jobType,
  residenceType
} from '@/utils/field-conver'
import { validEmail } from '@/utils/validate'
import { uploadSuccess } from '@/api/common'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'EmployeeDetail',
  inject: ['reload'],
  directives: { elDragDialog },
  // props: {
  //   'parentdepartmentlist': {
  //     type: Array,
  //     default: function() { return [] }
  //   }
  // },
  data() {
    const validateEmail = (rule, value, callback) => {
      if (!validEmail(value)) {
        callback(new Error('邮箱格式错误'))
      } else {
        callback()
      }
    }
    return {
      detail: {},
      isEdit: false,
      genderList: genderList,
      getMarriedStatus: getMarriedStatus,
      politicsStatus: politicsStatus,
      health: health,
      jobType: jobType,
      residenceType: residenceType,
      isExternal: false,
      emploeeDetailRules: {
        realName: {
          required: true,
          message: '真实姓名必填',
          trigger: 'blur'
        },
        gender: {
          required: true,
          message: '性别必选',
          trigger: 'blur'
        },
        idCard: [
          { required: true, message: '身份证必填', trigger: 'blur' },
          { min: 18, max: 18, message: '身份证为18位' }
        ],
        mobile: [
          { required: true, message: '手机号必填', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号为11位' }
        ],
        deptId: {
          required: true,
          message: '部门必选',
          trigger: 'blur'
        },
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ]
      },
      parentDepartmentList: [],
      uploadImg: '',
      uuid: ''
    }
  },
  watch: {
    // 监听路由，只要路由有变化(路径，参数等变化)都执行下面的函数
    $route: {
      handler: function(val, oldVal) {
        const urlParams = this.$route.params
        if (urlParams.method === 0) {
          // 详情
          this.isEdit = false
          this.getDetail()
        } else if (urlParams.method === 2) {
          // 修改
          this.isEdit = true
          this.getDetail()
        } else if (urlParams.method === 1) {
          // 新增
          this.isEdit = true
          this.detail = {}
        }
      },
      deep: true
    }
  },
  created() {
    this.parentDepartmentList = this.$route.params.parentDepartmentList
    sessionStorage.setItem('detail', true)

    this.detail = {}
    this.isEdit = true
    if (this.$route.params.method === 0) {
      this.isEdit = false
      this.getDetail()
    } else if (this.$route.params.method === 2) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      const that = this
      const id = this.$route.params.id
      getEmployeeDetail(id).then(res => {
        that.detail = res.data
        res.data.isExternal === 1
          ? (this.detail.isExternal = true)
          : (this.detail.isExternal = false)
        that.uploadImg = res.data.wechatCodeImg || ''
      })
    },
    /**
     * 增加员工
     */
    addDetail(desc) {
      const that = this
      // this.$refs.detail.resetFields()
      // that.$refs['detailForm'].resetFields()

      that.isEdit = true
      that.detail = {}
    },
    /**
     * 转换性别、结婚状态
     * @param row
     * @returns {*}
     */
    getGender(gender) {
      return converseEnToCn(genderList, gender)
    },
    getMarried(isMarried) {
      return converseEnToCn(getMarriedStatus, isMarried)
    },
    /**
     * 确认修改信息
     */
    confirmEmployeeDetail(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.detail.isExternal === false) {
            this.detail.isExternal = 0
          } else {
            this.detail.isExternal = 1
          }
          const params = Object.assign({}, this.detail, {
            wechatCodeImg: this.uploadImg ? this.uploadImg : ''
          })
          if (this.$route.params.method === 1) {
            // 1-新增
            addEmployeeDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '新增成功！'
                })
                this.closeIt()
                this.uploadImg = ''
              }
            })
          } else if (this.$route.params.method === 2) {
            // 修改
            editEmployeeDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功！'
                })
                this.closeIt()
              }
            })
          }
        } else {

          return false
        }
      })
      /**
       * 通知父组件更新
       */
      this.$emit('refresh')
    },
    closeIt() {
      this.$store
        .dispatch('tagsView/delView', this.$route)
        .then(({ visitedViews }) => {
          this.$router.go(-1)
          setTimeout(() => {
            this.reload()
          }, 100)
        })
    },
    upload(e) {
      // 首页封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/employee/${that.uuid}.${
        tempName[tempName.length - 1]
      }`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的班型封面不能大于1M'
        })
      } else {
        obsClient.putObject(
          {
            Bucket: 'obs-d812',
            Key: `${fileName}`, // 文件名
            SourceFile: file // 文件路径
          },
          function(err, result) {
            if (err) {
              console.error('Error-->' + err)
            } else {
              const paramsUpload = Object.assign(
                {},
                {
                  imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/employee/${
                    that.uuid
                  }.${tempName[tempName.length - 1]}`,
                  resourceType: 'image'
                }
              )
              uploadSuccess(paramsUpload).then(res => {
                if (res.code === '000000') {
                  that.uploadImg = res.data.url
                }
              })
            }
          }
        )
      }
    },
    delImgB() {
      this.uploadImg = ''
    },
    get_uuid() {
      // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    }
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 10px;
}
/deep/ .el-card .el-card__header {
  position: relative;
}
.el-card__header .el-button {
  position: absolute;
  right: 20px;
  top: 10px;
}
.upload-imgs {
  position: relative;
  width: 118px;
  height: 118px;
  font-size: 14px;
  display: inline-block;
  padding: 10px;
  margin-right: 25px;
  border: 2px dashed #ccc;
  text-align: center;
  vertical-align: middle;
}
.upload-imgs .add {
  display: block;
  background-color: #ccc;
  color: #ffffff;
  height: 94px;
  line-height: 94px;
}
.upload-imgs .add .iconfont {
  padding: 10px 0;
  font-size: 40px;
}
.upload-imgs .upload {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 118px;
  height: 118px;
  opacity: 0;
  cursor: pointer;
}
.upload-imgs .img {
  position: relative;
  width: 94px;
  height: 94px;
  line-height: 94px;
}
.upload-imgs .img img {
  vertical-align: middle;
  width: 94px;
  height: 94px;
}
.upload-imgs .img .close {
  display: none;
}
.upload-imgs:hover .img .close {
  display: block;
  position: absolute;
  top: -10px;
  left: -10px;
  width: 118px;
  height: 118px;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 118px;
  font-size: 24px;
  color: #fff;
}
.img-upload {
  padding-right: 8px;
}
</style>
