<template>
  <el-select v-model="tmpId" filterable clearable placeholder="状态" style="width: 140px;" :disabled="disabled">
    <el-option
      v-for="item in optionList"
      :key="item.value"
      filterable
      :label="item.label"
      :value="Number(item.value)">
    </el-option>
  </el-select>
</template>
<script>
import { enableList } from '@/utils/field-conver'

/**
 * 班型科目样式选择框
 */
export default {
  name: 'StatusSelect',
  data: function () {
    return {
      //（1:教材版本和册，2:只有教材，3:都没有）
      optionList: enableList
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId) : null
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.value === value)
      return this.$emit('change', value, selectedOption)
    },
  }
}
</script>
<style scoped>
</style>
