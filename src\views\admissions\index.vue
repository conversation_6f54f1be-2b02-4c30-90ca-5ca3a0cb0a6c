<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="试卷名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select filterable v-model="listQuery.subjectId" placeholder="科目" clearable class="filter-item" style="width: 140px;" @change="getClassType">
        <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select filterable v-model="listQuery.classTypeId" placeholder="班型" clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in classList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientCode" filterable placeholder="产品线" clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['admissions:subjects:create']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="试卷名称" show-overflow-tooltip prop="name" />
      <af-table-column label="科目" show-overflow-tooltip prop="subjectName" />
      <el-table-column label="关联班型" prop="classTypeName" show-overflow-tooltip width="200px" />
      <el-table-column label="关联课程" prop="courseName" show-overflow-tooltip width="250px" />
      <el-table-column label="关联产品线" prop="clientCode" :formatter="getClientCode" show-overflow-tooltip width="250px" />
      <af-table-column label="题量" prop="questionNum" />
      <af-table-column label="状态" prop="status" :formatter="getStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['admissions:subjects:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['admissions:subjects:questions']" type="primary" size="mini" @click="getQuestions(row)">试题</el-button>
          <el-button v-permission="['admissions:subjects:del']" type="primary" size="mini" @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <paper-pop ref="paper" :subjects-list="subjectsList" @addPaper="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import PaperPop from './components/paperPop'
import { examinationPaper, getClassType, getSubjects, removePaper } from '@/api/admissions'
import { getSysClients } from '@/api/classType'
import { admissionStatus, converseEnToCn, converseEnToCode } from '@/utils/field-conver'
export default {
  name: 'Admissions',
  components: {
    Pagination,
    PaperPop
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      subjectsList: [],
      classList: [],
      admissionStatus: admissionStatus,
      productCodeList: []
    }
  },
  watch: {
    classList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.classTypeId = ''
        }
      },
      deep: true
    },
    'listQuery.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.classTypeId = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getSubjects()
      this.getSysClients()
    })
  },
  methods: {
    async getList() {
      const params = Object.assign({}, this.listQuery)
      examinationPaper(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleCreate() {
      this.$refs.paper.paperListPop = true
      this.$refs.paper.paperListTitle = '新增试卷'
      this.$refs.paper.productCodeList = this.productCodeList
      this.$refs.paper.isEdit = false
    },
    handleUpdate(row) {
      // this.getClassType()
      // this.getSubjects()
      this.$refs.paper.paperListPop = true
      this.$refs.paper.paperListTitle = '修改试卷'
      this.$refs.paper.isEdit = true
      this.$refs.paper.productCodeList = this.productCodeList
      this.$refs.paper.getDetailPaper(row.id)
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getClassType() { // 查询班型
      if (this.listQuery.subjectId) {
        getClassType(this.listQuery.subjectId).then(res => {
          if (res.code === '000000') {
            this.classList = res.data
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    getQuestions(row) {
      localStorage.setItem('ids', row.id)
      localStorage.setItem('paperName', row.name)
      this.$router.push({
        name: 'QuestionsDetail',
        params: {
          name: row.name,
          id: row.id,
          productCodeList: this.productCodeList
        }
      })
    },
    del(row) {
      const ids = row.id

      this.$confirm('确定要删除此条数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removePaper(ids).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getStatus(row) {
      return converseEnToCn(this.admissionStatus, row.status)
    },
    getClientCode(row) {
      return converseEnToCode(this.productCodeList, row.clientCode)
    },
    getSysClients() {
      const params = {
        level: 1
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.productCodeList = res.data || []
        }
      }).catch((error) => {

      })
    }
  }
}
</script>

<style scoped>

</style>
