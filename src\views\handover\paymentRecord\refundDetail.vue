<template>
  <div>
    <el-dialog v-el-drag-dialog title="退款记录详情" :visible.sync="getPaymentRecordDetail" :close-on-click-modal="!getPaymentRecordDetail" class="departmentDialog" width="40%">
      <el-form ref="detailForm" :model="detail" :rules="detailFormRules" label-width="140px">
        <el-row>
          <el-col :lg="{span:12}">
            <el-form-item label="当前收付编号：">
              <div>{{ detail.payRecordCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="订单编号：">
              <div>{{ detail.orderCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="应退金额：">
              <div>{{ detail.orderPayAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="已退款金额：">
              <div>{{ detail.orderRealAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="已退款编号：">
              <div v-show="detail.relationPayRecords&&detail.relationPayRecords.length>0">
                <a v-for="item in detail.relationPayRecords" :key="item.id" class="codes" @click="getRelationPaymentRecordDetail=true,currentDetail(item)">{{ item.payRecordCode }}</a>
              </div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="财务审核状态：">
              <div>{{ getAuditStatus(detail.auditStatus) }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="退款金额：">
              <div>{{ detail.payAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="退款方式：">
              <div>{{ detail.payTypeName }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="退款时间：">
              <div>{{ detail.payTime }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="收款人姓名：" prop="remark">
              <el-input v-model="detail.remark" type="text" />
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="银行卡号：" prop="remark">
              <el-input v-model="detail.remark" type="text" />
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="交易流水号：" prop="remark">
              <el-input v-model="detail.remark" type="textarea" />
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="退款状态：" prop="status">
              <el-radio-group v-model="detail.status">
                <el-radio :label="30">已到账</el-radio>
                <el-radio :label="40">未到账</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="备注：" prop="remark">
              <el-input v-model="detail.remark" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="confirmAudit">确 认</el-button>
        <el-button @click="getPaymentRecordDetail = false">关 闭</el-button>
      </div>
      <!--      其他打款信息的弹框-->
      <el-dialog v-el-drag-dialog title="退款记录详情" :visible.sync="getRelationPaymentRecordDetail" :close-on-click-modal="!getRelationPaymentRecordDetail" class="departmentDialog" width="40%" append-to-body>
        <el-form ref="detailForm" :model="currentPay" :rules="detailFormRules" label-width="140px">
          <el-row>
            <el-col :lg="{span:12}">
              <el-form-item label="当前收付编号：">
                <div>{{ currentPay.payRecordCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="订单编号：">
                <div>{{ currentPay.orderCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="应退金额：">
                <div>{{ currentPay.orderPayAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="已退款金额：">
                <div>{{ currentPay.orderRealAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:24}">
              <el-form-item label="已退款编号：">
                <div v-show="currentPay.relationPayRecords">
                  <span v-for="item in currentPay.relationPayRecords" :key="item.id" class="codes-list">{{ item.payRecordCode }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="财务审核状态：">
                <div>{{ getAuditStatus(currentPay.auditStatus) }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="退款金额：">
                <div>{{ currentPay.payAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="退款方式：">
                <div>{{ currentPay.payTypeName }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="退款时间：">
                <div>{{ currentPay.payTime }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="收款人姓名：">
                <div>{{ currentPay.payTime }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="银行卡号：">
                <div>{{ currentPay.payTime }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="交易流水号：">
                <div>{{ currentPay.transactionNo }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="退款状态：">
                <el-radio-group v-model="currentPay.status">
                  <el-radio :label="30">已到账</el-radio>
                  <el-radio :label="40">未到账</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:24}">
              <el-form-item label="备注：" prop="remark">
                <el-input v-model="currentPay.remark" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </el-dialog>
  </div>

</template>

<script>
import { confirmPaymentRecord, getOrderPaymentRecord } from '@/api/payment'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { converseEnToCn, auditStatus, payMethod } from '@/utils/field-conver'
export default {
  name: 'RefundRecordDetail',
  directives: {
    elDragDialog
  },
  props: {},
  data() {
    return {
      getPaymentRecordDetail: false,
      getRelationPaymentRecordDetail: false,
      detail: {},
      currentPay: {},
      detailFormRules: {
        status: [{ required: true, trigger: 'blur', message: '打款状态必选' }],
        remark: [{ required: true, trigger: 'blur', message: '备注必填' }]
      }
    }
  },
  created() {
  },
  methods: {
    getDetail(data) {
      const that = this
      const params = {
        id: data.id
      }
      getOrderPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          that.detail = res.data
          that.detail.relationPayRecords = res.data.relationPayRecords || []
          that.getPaymentRecordDetail = true
        }
      })
    },
    currentDetail(data) {
      const that = this
      const params = {
        id: data.id
      }
      getOrderPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          that.currentPay = res.data
          that.currentPay.relationPayRecords = res.data.relationPayRecords || []
          that.getPaymentRecordDetail = true
        }
      })
    },
    /**
       * 确认修改信息
       */
    confirmAudit() {
      const that = this
      that.$refs.detailForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, { id: that.detail.id, status: that.detail.status, remark: that.detail.remark })
          confirmPaymentRecord(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '审核成功!'
              })
              that.getPaymentRecordDetail = false
              /**
                 * 通知父组件更新
                 */
              this.$emit('refresh')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    getAuditStatus(status) {
      return converseEnToCn(auditStatus, status)
    },
    getPayMethod(status) {
      return converseEnToCn(payMethod, status)
    }
    // getRelationPay(ids) {
    //   const params = {
    //     id: ids
    //   }
    //   getOrderPaymentRecord(params).then(res => {
    //     if (res.result === '0000') {
    //       this.detail = res.data
    //       this.detail.relationPayRecords = res.data.relationPayRecords || []
    //       this.getPaymentRecordDetail = true
    //     }
    //   })
    // }
  }
}
</script>
<style scoped>
  .codes{
    padding-right: 8px;
    font-weight: bold;
    color: #0a76a4;
  }
  .codes-list{
    padding-right:8px;
  }
</style>
