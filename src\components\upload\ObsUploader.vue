<template>
  <div class="obsUpload">
    <el-upload
            action
            ref="upload"
            :http-request="upload"
            :list-type="listType"
            :accept="accept"
            :disabled="disabled"
            :file-list="fileList"
            :on-progress="onProgressHandler"
            :on-success="onSuccessHandler"
            :limit="limit"
            :before-upload="beforeUpload"
            :multiple="!single"
            :on-exceed="onExceed"
            :class="{ 'hide': hideUpload }"
            :on-remove="onRemoveHandle">
      <i class="el-icon-plus"></i>
      <div slot="file" slot-scope="{ file }">
        <span v-if="getFileTypeFn(file) === 'image'">
          <img class="img-preview-form" :src="file.url" alt="" />
        </span>
        <span v-else-if="getFileTypeFn(file) === 'pdf'" @click='downloadFile(file.url)'>
          <img class="img-preview-form" src='../../assets/img/pdf.png'  alt=""></img>
        </span>
        <span v-else-if="getFileTypeFn(file) === 'video'">
          <video :src="file.url" preload="none" class="video-preview-form" controls="controls">您的浏览器不支持视频播放</video>
        </span>
        <div class="el-upload-list__item-actions">
          <span v-if="getFileTypeFn(file) === 'image'" class="el-upload-list__item-preview" @click="showImgPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span v-else-if="getFileTypeFn(file) === 'image'||getFileTypeFn(file) === 'video'" class="el-upload-list__item-preview" @click="showImgPreview(file, true)">
            <i class="icon-guanwangfangwen"></i>
          </span>
          <span v-else-if="getFileTypeFn(file) === 'pdf'" class="el-upload-list__item-preview" @click='downloadFile(file.url)'>
            <i class="icon-guanwangfangwen" title='预览'>打开</i>
          </span>
          <span
                  v-if="handleImageClick !== undefined && getFileTypeFn(file) === 'image'"
                  class="el-upload-list__item-delete"
                  @click="handleImageClick(file)">
            <i class="icon-shengchanfengmian" title="设置封面"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="onRemoveHandle(file)" v-if="!disabled">
            <i class="el-icon-delete" title="移除"></i>
          </span>
        </div>
      </div>
      <div slot="tip" class="el-upload__tip" v-if="!disabled">{{ tipContent }}</div>
    </el-upload>
    <el-progress v-show="showProgress" :percentage="progress" :color="customColors"></el-progress>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { getFileUrlArrayByObj, getFileUrl, getFileTypeFn, getFileTypeArrayByAcceptStr,obsClient } from '@/utils/index';
import { uploadSuccess } from '@/api/common'
import { v4 as uuidv4 } from 'uuid';
import { GetRound } from '@/utils/field-conver'
import { uuid } from '@/utils'

export default {
  name: 'ObsUploader',
  computed: {
    ...mapGetters(['headers']),
    hideUpload() {
      return (this.single && this.fileList.length > 0)||(this.fileList.length === this.limit)|| this.disabled;
    },
    maxCount() {
      return this.single ? 1 : this.limit;
    },
    tipContent() {
      let content = '只能上传';
      const fileTypeArray = getFileTypeArrayByAcceptStr(this.accept);
      if (fileTypeArray.findIndex((item) => item === 'image') > -1) {
        content += '图片，';
      }
      if (fileTypeArray.findIndex((item) => item === 'video') > -1) {
        content += '视频，';
      }
      if (fileTypeArray.findIndex((item) => item === 'pdf') > -1) {
        content += 'PDF，';
      }

      return content + `最多${this.maxCount}个！( ${this.accept} )`;
    },
  },

  inject: ['showImgPreview'],
  props: {
    single: {
      type: Boolean,
      default: false,
    },
    listType: {
      type: String,
      default: 'picture-card',
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.gif',
    },
    onProgress: {
      type: Function,
    },
    onRemove: {
      type: Function,
    },
    onSuccess: {
      type: Function,
    },
    handleImageClick: {
      type: Function,
    },
    beforeUpload: {
      type: Function,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 10,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/download/
    // 其中：santao_stip/crm/download/ 为业务路径，斜杠结束
    bucket: {
      type: String,
      default() {
        return 'obs-d812';
      },
      required: false,
    },
    businessPath: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      showProgress: false,
      progress: 0,
      client: null,
      expirationDate: null,
      customColors: [
        {color: '#f56c6c', percentage: 20},
        {color: '#e6a23c', percentage: 40},
        {color: '#5cb87a', percentage: 60},
        {color: '#1989fa', percentage: 80},
        {color: '#6f7ad3', percentage: 100}
      ]
    };
  },
  methods: {
    getFileTypeFn,
    getFileUrl,
    getFileUrlArrayByObj,
    onRemoveHandle(e) {
      this.onRemove && this.onRemove(e);
    },
    onExceed(files, fileList) {
      this.$message.warning(`最多上传${this.maxCount}个文件`);
    },
    upload(options) {
      this.obsUpload(options);
    },
    // 普通上传
    async obsUpload(options) {
      let that = this;
      let file = options.file;
      // 文件路径前缀为年月日，文件名为uuid和后缀名
      // const datePath = new Date().toLocaleDateString().replace(/\//g, '-');
      const fileName = uuidv4();
      const fileType = file.type.split('/')[1];
      const fileFullName = `${fileName}.${fileType}`;
      const filePath = `${that.businessPath}${fileFullName}`;

      obsClient.putObject({
        Bucket: that.bucket,
        Key: `${filePath}`, // 文件名
        SourceFile: file,
        ProgressCallback: function(transferredAmount, totalAmount, totalSeconds) {
          that.showProgress = true
          // 获取上传进度百分比
          that.progress = Math.floor(transferredAmount * 100.0 / totalAmount)
          // if (transferredAmount * 100.0 / totalAmount === 100) {
            //  '文件上传完毕'
          // }
        }
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {
          const paramsUpload = Object.assign({}, {
            imageUrl: `https://${that.bucket}.obs.cn-north-1.myhuaweicloud.com/${filePath}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            that.showProgress = false
            if (res.code === '000000') {
              // that.resourcesImgId = res.data.id
              // that.resourcesImgUrl = res.data.url
              that.onSuccessHandler(res, file);
            }
          })
        }
      })
    },
    onProgressHandler(e, file, fileList) {
      this.onProgress && this.onProgress(e, file, fileList);
    },
    onSuccessHandler(res, file) {
        this.$refs.upload.fileList.push({ name: file.name, url: getFileUrl(res.data.url) });
        this.onSuccess && this.onSuccess(res.data, file, this.$refs.upload.fileList);
    },
    //新窗口打开文件
    downloadFile(url) {
      window.open(url);
    },
  },
};
</script>
<style scoped>
/deep/ .hide .el-upload--picture-card {
    display: none;
}
/deep/ .el-upload-list__item {
    transition: none !important;
}

.img-preview-form{
    width: 100%;
    height: 100%;
}

.el-upload-list__item-actions{
  z-index: 999;
}

</style>
