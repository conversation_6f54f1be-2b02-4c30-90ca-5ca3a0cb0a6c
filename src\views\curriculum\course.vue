<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="10">
       <el-col :span="24">
         <el-input
           v-model="listQuery.title"
           placeholder="课程名称"
           class="filter-item"
           style="width: 200px;"
           @keyup.enter.native="handleFilter"
         />
         <ProductLineSelect v-model="listQuery.clientCode"></ProductLineSelect>
         <ClassTypeByClientSelect v-model="listQuery.classTypeId" :clientCode="listQuery.clientCode"></ClassTypeByClientSelect>
         <SubjectByClassTypeSelect v-model="listQuery.subjectId" :class-type-id="listQuery.classTypeId"></SubjectByClassTypeSelect>
         <TeacherByCSSelect v-model="listQuery.teacherId" :class-type-id="listQuery.classTypeId" :subject-id="listQuery.subjectId"></TeacherByCSSelect>
          <!--    选择年级     -->
         <GradeSelect v-model="listQuery.gradeId" :client-code="listQuery.clientCode"></GradeSelect>
         <MaterialByClassTypeSelect v-model="listQuery.materialNewId" :clientCode="listQuery.clientCode" :subject-id="listQuery.subjectId" :class-type-id="listQuery.classTypeId"></MaterialByClassTypeSelect>
         <SubMaterialSelect v-model="listQuery.subMaterialId" :mainId="listQuery.materialNewId"></SubMaterialSelect>
       </el-col>
        <el-col :span="24">
          <!--      <el-select v-model="listQuery.classTypeId" placeholder="所属班型" filterable clearable class="filter-item" style="width: 140px;">-->
          <!--        <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />-->
          <!--      </el-select>-->
          <!--      <el-select v-model="listQuery.subjectId" placeholder="所属科目" filterable clearable class="filter-item" style="width: 140px;">-->
          <!--        <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />-->
          <!--      </el-select>-->
          <!--      <el-select v-model="listQuery.teacherId" placeholder="授课教师" filterable clearable class="filter-item" style="width: 140px;">-->
          <!--        <el-option v-for="item in teachers" :key="item.id" :label="item.name" :value="item.id" />-->
          <!--      </el-select>-->
          <KnowledgeByClassAndSubjectSelect v-model="listQuery.versionId" :class-type-id="listQuery.classTypeId" :subject-id="listQuery.subjectId"></KnowledgeByClassAndSubjectSelect>
          <el-select v-model="listQuery.materialId" placeholder="教材版本（旧）" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in materialsList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
<!--          <el-select v-model="listQuery.outlineId" placeholder="所属大纲/章" filterable clearable class="filter-item" style="width: 140px;">-->
<!--            <el-option v-for="item in outLinesList" :key="item.id" :label="item.title" :value="item.id" />-->
<!--          </el-select>-->
<!--          <el-select v-model="listQuery.keynoteId" placeholder="所属考点/节" filterable clearable class="filter-item" style="width: 140px;">-->
<!--            <el-option v-for="item in keynoteList" :key="item.id" :label="item.title" :value="item.id" />-->
<!--          </el-select>-->
<!--          <el-select v-model="listQuery.gradeId" placeholder="年级" filterable clearable class="filter-item" style="width: 140px;">-->
<!--            <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />-->
<!--          </el-select>-->
          <el-select v-model="listQuery.status" placeholder="课程状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in classType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="listQuery.isShow" placeholder="显示状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in isShow" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="listQuery.isSynHuawei" placeholder="同步状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in isSynHuawei" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="listQuery.billingType" placeholder="计时规则" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in billingType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
<!--          <el-select v-model="listQuery.courseSource" placeholder="课程渠道" filterable clearable class="filter-item" style="width: 140px;">-->
<!--            <el-option v-for="item in courseSource" :key="item.value" :label="item.label" :value="item.value" />-->
<!--          </el-select>-->
          <!--      <el-select v-model="listQuery.clientCode" placeholder="客户端" clearable class="filter-item" style="width: 140px;">-->
          <!--        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
          <!--      </el-select>-->
          <el-date-picker
            v-model="followDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="创建开始时间"
            end-placeholder="创建结束时间"
          />

      <el-button v-waves class="filter-item" plain type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" plain type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:course:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="课程编号" show-overflow-tooltip prop="id">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
<!--      <af-table-column label="课程封面" show-overflow-tooltip prop="imgUrl">-->
<!--        <template slot-scope="scope">-->
<!--          <img :src="scope.row.resourcesImgUrl" class="cover-img">-->
<!--        </template>-->
<!--      </af-table-column>-->
      <af-table-column label="课程名称" prop="title" show-overflow-tooltip />
      <af-table-column label="所属班型" prop="classTypeName" />
      <af-table-column label="所属科目" prop="subjectName" show-overflow-tooltip />
      <af-table-column label="授课教师" prop="teacherName" />
<!--      <af-table-column label="教材版本" prop="materialName" />-->
<!--      <af-table-column label="所属大纲" prop="outlineName" />-->
<!--      <af-table-column label="所属考点" prop="keynoteName" />-->
<!--      <af-table-column label="年级" prop="gradeName" />-->
      <af-table-column label="课程时长">
        <template slot-scope="scope">
          <span>{{ scope.row.videoMinute }}分</span>
          <span>{{ scope.row.videoSecond }}秒</span>
        </template>
      </af-table-column>
<!--      <af-table-column label="课程渠道" prop="courseSource" :formatter="getCourseStatusCN" />-->
      <af-table-column label="课程状态" prop="status" :formatter="getJoinStatusCN" />
      <af-table-column label="显示状态" prop="isShow" :formatter="getShowStatus" />
<!--      <af-table-column label="同步状态" prop="isSynHuawei" :formatter="getSynHuaweiStatus" />-->
      <af-table-column label="计时规则" prop="billingType" :formatter="getBillingStatus" />
      <af-table-column label="课程排序" prop="sort" />
      <af-table-column label="上线时间" prop="onlineDate" />
      <af-table-column label="讲义状态">
        <template slot-scope="scope">
          <el-button v-if="scope.row.notesResourceUrl" type="text" size="mini" style="padding: 0 !important;min-width: 20px" @click="handlOut(scope.row)">已上传</el-button>
          <span v-else>未上传</span>
        </template>
      </af-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="410" fixed="right">
        <template v-slot="{row}">
          <div :key="row.id">
            <el-button  type="text" size="mini" class="action-deal-btn"  v-permission="['curriculum:course:questions']" @click="toExercises(row)">习题</el-button>
            <el-button type="text" size="mini" class="action-deal-btn" @click="handUpload(row,'notes')">上传讲义</el-button>
            <el-button type="text" size="mini" class="action-deal-btn" @click="openChapterSetting(row)">切片</el-button>
            <el-button v-if="row.notesResourceUrl" type="text" size="mini" class="action-deal-btn" @click="handlOut(row)">查看讲义</el-button>
            <el-button v-permission="['curriculum:course:edit']" type="text" size="mini" class="action-deal-btn" @click="handleUpdate(row)">修改</el-button>
            <el-button v-if="row.isSynHuawei===0" v-permission="['curriculum:course:synchronous']" type="text" size="mini" class="action-deal-btn" @click="synchronous(row)">同步</el-button>
            <el-button v-permission="['curriculum:course:recommend']" type="text" size="mini" class="action-deal-btn" @click="joinRecommend(row)">{{ row.isRecommend===0?'加入推荐':'取消推荐' }}</el-button>
            <el-button v-permission="['curriculum:course:logs']" type="text" size="mini" class="action-deal-btn" @click="logsPop=true,handleLogs(row)">操作日志</el-button>
            <el-button v-permission="['curriculum:course:del']" type="text" size="mini" class="action-deal-btn" @click="handleDel(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!--操作日志的弹框-->
    <el-dialog title="操作日志" :visible.sync="logsPop" :close-on-click-modal="!logsPop" width="60%">
      <el-table
        v-loading="logLoading"
        :data="logs"
        border
        fit
        stripe
        highlight-current-row
      >
        <af-table-column label="操作人" show-overflow-tooltip prop="operatorName" />
        <af-table-column label="操作类型" show-overflow-tooltip prop="operateType" :formatter="getOperaStatus" />
        <af-table-column label="操作时间" prop="operateTime" show-overflow-tooltip />
      </el-table>
    </el-dialog>
    <!--    新增/修改教材弹框-->
    <course-pop ref="course" @addCourseList="getList" />
    <!-- 上传讲义 -->
    <!--    <hand-file ref="handFileOpera" @refreshList="getList" />-->
    <oss-hand-file ref="ossHandFileOpera" @refreshList="getList" />
    <ChapterSetting :resource-id="resourcesId" v-if="resourcesId" :title="courseTitle" @close="closeChapterSetting"></ChapterSetting>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import CoursePop from './components/coursePop'
import HandFile from './components/handFile.vue'
import OssHandFile from './components/ossHandFile.vue'
import { isShow, isSynHuawei, billingType, classType, operateTypes, converseEnToCn, courseSource } from '@/utils/field-conver'
import { getAllSubjects, grades, materialsList, clientCode, courseList, getTeachers, joinRecommend, synchronous, logs, getAllClassType, outlineLists, keynotesList, delCourse } from '@/api/classType'
import ChapterSetting from '@/views/curriculum/components/ChapterSetting.vue'
import ProductLineSelect from '@/components/Select/ProductLineSelect.vue'
import ClassTypeByClientSelect from '@/components/Select/ClassTypeByClientSelect.vue'
import SubjectByClassTypeSelect from '@/components/Select/SubjectByClassTypeSelect.vue'
import TeacherByCSSelect from '@/components/Select/TeacherByClassAndSubjectSelect.vue'
import MaterialByClassTypeSelect from '@/components/Select/MaterialByClassTypeSelect.vue'
import SubMaterialSelect from '@/components/Select/SubMaterialSelect.vue'
import KnowledgeByClassAndSubjectSelect from '@/components/Select/KnowledgeByClassAndSubjectSelect.vue'
import GradeSelect from "@/components/Select/GradeSelect.vue";
export default {
  name: 'Course',
  components: {
    GradeSelect,
    KnowledgeByClassAndSubjectSelect,
    SubMaterialSelect,
    MaterialByClassTypeSelect,
    TeacherByCSSelect,
    SubjectByClassTypeSelect,
    ClassTypeByClientSelect,
    ProductLineSelect,
    ChapterSetting,
    Pagination,
    CoursePop,
    HandFile,
    OssHandFile
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      isShow: isShow,
      isSynHuawei: isSynHuawei,
      billingType: billingType,
      subjectsAll: [],
      grades: [],
      materialsList: [],
      assignSatuts: [],
      classType: classType,
      clientCode: [],
      logsPop: false,
      logLoading: true,
      logs: [],
      teachers: [],
      operateTypes: operateTypes,
      outLinesList: [],
      allClassTypeList: [],
      keynoteList: [],
      allClassType: [],
      courseSource: courseSource,
      followDate: [],
      resourcesId:null, //课程章节使用
      courseTitle:null, //课程章节使用
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      // this.getAllSubjects()
      // this.getGrades()
      this.getMaterials()
      // this.getCode()
      this.getList()
      // this.getTeachers()
      // this.getAllClassType()
      // this.getOutlines()
      // this.keynotesList()
    })
  },
  methods: {
    //章节设置
    openChapterSetting(row){
      this.resourcesId=row.resourcesId
      this.courseTitle=row.title
    },
    closeChapterSetting(){
      this.resourcesId=null;
    },
    getAllClassType() { // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data
      })
    },
    getOutlines() { // 获取可用大纲
      outlineLists().then(res => {
        this.outLinesList = res.data
      })
    },
    keynotesList() { // 获取可用考点
      keynotesList().then(res => {
        this.keynoteList = res.data
      })
    },
    getAllSubjects() {
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data
        }
      })
    },
    getGrades() {
      grades().then(res => {
        if (res.code === '000000') {
          this.grades = res.data
        }
      })
    },
    getMaterials() {
      materialsList().then(res => {
        if (res.code === '000000') {
          this.materialsList = res.data
        }
      })
    },
    // getCode() {
    //   clientCode().then(res => {
    //     this.clientCode = res.data || []
    //     // this.clientCode = clientCodes.filter(item => item.level === 2)
    //   })
    // },
    // getTeachers() {
    //   getTeachers().then(res => {
    //     this.teachers = res.data
    //   })
    // },
    getJoinStatusCN(row) {
      return converseEnToCn(this.classType, row.status)
    },
    getShowStatus(row) {
      return converseEnToCn(this.isShow, row.isShow)
    },
    getBillingStatus(row) {
      return converseEnToCn(this.billingType, row.billingType)
    },
    getSynHuaweiStatus(row) {
      return converseEnToCn(this.isSynHuawei, row.isSynHuawei)
    },
    getOperaStatus(row) {
      return converseEnToCn(this.operateTypes, row.operateType)
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })
      await courseList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.course.coursePop = true
      this.$refs.course.courseTitle = '新增课程'
      this.$refs.course.isEdit = false
      this.$refs.course.flags = 1
      this.$set(this.$refs.course.listQuery, 'courseSource', 1)
    },
    handleUpdate(row) {
      this.$refs.course.coursePop = true
      this.$refs.course.courseTitle = '修改课程'
      this.$refs.course.courseTitle = `课程${row.title}`
      this.$refs.course.courseDetail(row.id)
      this.$refs.course.isEdit = false
      this.$refs.course.flags = 0
    },
    handleLogs(row) {
      logs(row.id).then(response => {

        this.logs = response.data || []
        this.logLoading = false
      }).catch(() => {

      })
    },
    getDetail(row) {
      this.$refs.course.coursePop = true
      this.$refs.course.courseTitle = `课程${row.title}`
      this.$refs.course.courseDetail(row.id)
      this.$refs.course.isEdit = true
    },
    synchronous(row) { // 同步
      this.$confirm(`确定要进行同步操作吗?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        synchronous(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    joinRecommend(row) {
      const recommendTitle = row.isRecommend === 0 ? '加入推荐' : '取消推荐'
      this.$confirm(`确定要${recommendTitle}吗?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        joinRecommend(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    handleDel(row) {
      this.$confirm('确定要删除此数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delCourse(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getCourseStatusCN(row) {
      return converseEnToCn(this.courseSource, row.courseSource)
    },
    handlOut(row) { // notesResourceUrl 有值--查看/上传
      window.open(row.notesResourceUrl)
    },
    handlExercises(row) {
      window.open(row.exercisesResourceUrl)
    },
    toExercises(row){
      this.$router.push({
        path:'/curriculum/exercises/'+row.id,
        query:{
          title:row.title
        }
      })
    },
    handUpload(row, type) { // 上传
      this.$store.commit('user/UPLOADTYPE', type)
      this.$refs.ossHandFileOpera.showUpload = true
      this.$refs.ossHandFileOpera.courseId = row.id
      this.$refs.ossHandFileOpera.uploadTitle = type === 'notes' ? '上传讲义' : '上传落实本'
      this.$refs.ossHandFileOpera.uploadType = type
      let name = 'jiangyi/'
      if (row.classTypeId) {
        name += row.classTypeId + '/'
      }
      if (row.subjectId) {
        name += row.subjectId + '/'
      }
      this.$refs.ossHandFileOpera.prefixFileName = name
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
  .action-deal-btn{
    padding: 0 !important;
    min-width: 20px;
  }
</style>
