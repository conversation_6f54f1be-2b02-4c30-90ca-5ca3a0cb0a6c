<template>
  <div class="app-container">
    <div class="terminal-title">
      <ul>
        <li v-for="(item,index) in terminalTitle" :key="index" :class="[showIndex===index?'default':'']" @click="getTerminal(item,index)">{{ item.name }}</li>
      </ul>
    </div>
    <div class="terminal-list">
      <el-tag type="primary">上部轮播:</el-tag>
      <div class="terminal-info">
        <div class="upload-btn" @click="uploadTerminal">
          <p class="el-icon-plus big" />
          <p class="small">添加轮播图</p>
        </div>
        <ul v-if="(bannerArea==='top'&&clientCode===102)|clientCode===202|clientCode===302" class="terminal-info-list">
          <li v-for="(item,n) in imgList" :key="n">
            <div class="upload-imgs">
              <div v-if="!item.imageUrl">
                <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'top')">
                <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                <p class="img-tips">图片规格：1155*666</p>
                <div class="webs">
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable @change.native.stop="getClassed">
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                  <div class="btn-terminal">
                    <el-button type="danger" size="mini" @click="delModuleTop(n)">删除栏位</el-button>
                  </div>
                </div>
              </div>
              <div class="img">
                <img v-if="item.imageUrl" :src="item.imageUrl">
                <div v-if="item.imageUrl" class="text">
                  <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'top')">
                  <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                </div>
                <p class="img-tips">图片规格：1155*666</p>
                <div v-if="item.imageUrl" class="webs">
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable @change.native.stop="getClassed">
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                  <div class="btn-terminal">
                    <el-button type="danger" size="mini" @click="delModuleTop(n)">删除栏位</el-button>
                  </div>
                </div>

              </div>
            </div>
          </li>
        </ul>

        <ul v-if="bannerArea==='old_top'&&clientCode===102" class="terminal-info-list">
          <li v-for="(item,n) in imgListOld" :key="n">
            <div class="upload-imgs">
              <div v-if="!item.imageUrl">
                <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'old')">
                <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                <p class="img-tips">图片规格：1256*480</p>
                <div>
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable>
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                  <div class="btn-terminal">
                    <el-button type="danger" size="mini" @click="delModuleOld(n)">删除栏位</el-button>
                  </div>
                </div>
              </div>
              <div class="img">
                <img v-if="item.imageUrl" :src="item.imageUrl">
                <div v-if="item.imageUrl" class="text">
                  <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'old')">
                  <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                </div>
                <p class="img-tips">图片规格：1256*480</p>
                <div v-if="item.imageUrl">
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable>
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                  <div class="btn-terminal">
                    <el-button type="danger" size="mini" @click="delModuleOld(n)">删除栏位</el-button>
                  </div>
                </div>

              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!--底部推荐-->

    <div v-if="bannerArea==='top'" class="terminal-list">
      <el-tag type="danger">底部推荐:</el-tag>
      <div class="terminal-info">
        <ul class="terminal-info-list">
          <li v-for="(item,n) in imgListBottom" :key="n">
            <div class="upload-imgs">
              <div v-if="!item.imageUrl">
                <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'bottom')">
                <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                <p class="img-tips">图片规格：368*204</p>
                <div>
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable>
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                </div>
              </div>
              <div class="img">
                <img v-if="item.imageUrl" :src="item.imageUrl">
                <div v-if="item.imageUrl" class="text">
                  <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="imgUpload($event,n,'bottom')">
                  <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
                </div>
                <p class="img-tips">图片规格：368*204</p>
                <div v-if="item.imageUrl">
                  <div class="sub-info">
                    <span>跳转网址：</span>
                    <el-input v-model="item.linkUrl" />
                  </div>
                  <div class="sub-info">
                    <span>跳转班型：</span>
                    <el-select v-model="item.classTypeId" filterable @change="getClassTypeId">
                      <el-option v-for="items in classList" :key="items.id" :label="items.title" :value="items.id" />
                    </el-select>
                  </div>
                </div>

              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="terminal-footer">
      <el-button type="default" @click="cancelTerminal">取消</el-button>
      <el-button type="primary" @click="confirmTerminal">确定</el-button>
    </div>
  </div>
</template>
<script>
import { obsClient, uuid } from '@/utils/index'
import { uploadSuccess } from '@/api/common'
import { terminalList, terminalSet, getCode } from '@/api/poster'
export default {
  name: '',
  data() {
    return {
      showIndex: 0,
      terminalTitle: [{
        clientCode: 102,
        name: '普高终端新版',
        type: 'top'
      },
      {
        clientCode: 102,
        name: '普高终端旧版',
        type: 'old_top'
      },
      {
        clientCode: 202,
        name: '烨晨终端',
        type: 'old_top'
      },
      {
        clientCode: 302,
        name: '艺考终端',
        type: 'old_top'
      }],
      imgList: [
        {
          resourcesId: null,
          imageUrl: '',
          linkUrl: '',
          classTypeId: null
        }
      ],
      imgListOld: [
        {
          resourcesId: null,
          imageUrl: '',
          linkUrl: '',
          classTypeId: null
        }
      ],
      imgListBottom: [
        {
          resourcesId: null,
          imageUrl: '',
          linkUrl: '',
          classTypeId: null
        },
        {
          resourcesId: null,
          imageUrl: '',
          linkUrl: '',
          classTypeId: null
        },
        {
          resourcesId: null,
          imageUrl: '',
          linkUrl: '',
          classTypeId: null
        }
      ],
      classList: [],
      bannerArea: 'top',
      clientCode: 102
    }
  },
  mounted() {
    this.getTerminalBanner(102)
    this.getCode(102)
  },
  methods: {
    getTerminal(item, i) {

      this.showIndex = this.showIndex === i ? -1 : i
      this.bannerArea = item.type
      this.clientCode = item.clientCode
      this.getTerminalBanner(item.clientCode)
      this.getCode(item.clientCode)
    },
    imgUpload(e, i, type) {
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '请上传1M以内的图片'
        })
        return false
      }
      this.posterUpload(file, i, type)
    },
    posterUpload(file, i, type) {
      const that = this
      const tempName = file.name.split('.')
      const ids = uuid()
      const fileName = `santao_stip/crm/terminal/${ids}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {
          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/terminal/${ids}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {

              if (type === 'top') {
                that.imgList[i].resourcesId = res.data.id
                that.imgList[i].imageUrl = res.data.url
              } else if (type === 'bottom') {
                that.imgListBottom[i].resourcesId = res.data.id
                that.imgListBottom[i].imageUrl = res.data.url
              } else if (type === 'old') {
                that.imgListOld[i].resourcesId = res.data.id
                that.imgListOld[i].imageUrl = res.data.url
              }
            }
          }).catch(() => {

          })
        }
      })
    },
    delModuleTop(index) {
      if (this.imgList.length > 1) {
        this.imgList.splice(index, 1)
      } else {
        this.$message({
          message: '至少保留一个栏位',
          type: 'warning'
        })
      }
    },
    delModuleOld(index) {
      if (this.imgListOld.length > 1) {
        this.imgListOld.splice(index, 1)
      } else {
        this.$message({
          message: '至少保留一个栏位',
          type: 'warning'
        })
      }
    },
    uploadTerminal() {
      const objs = {
        resourcesId: null,
        imageUrl: '',
        linkUrl: '',
        classTypeId: ''
      }
      if (this.clientCode === 102 && this.bannerArea === 'top') this.imgList.push(objs)
      else if (this.clientCode === 102 && this.bannerArea === 'old_top') this.imgListOld.push(objs)
      else if (this.clientCode === 202 && this.bannerArea === 'old_top') this.imgList.push(objs)
      else this.imgList.push(objs)
    },
    getTerminalBanner(code) {
      terminalList(code).then(res => {
        if (res.code === '000000') {
          if (code === 102) {

            this.imgList = res.data.length > 0 ? res.data.filter(item => item.bannerArea === 'top')[0].records : []
            this.imgListBottom = res.data.length > 0 ? res.data.filter(item => item.bannerArea === 'bottom')[0].records : []
            this.imgListOld = res.data.length > 0 ? res.data.filter(item => item.bannerArea === 'old_top')[0].records : []
          } else if (code === 202) {
            this.imgList = res.data.length > 0 ? res.data.filter(item => item.bannerArea === 'old_top')[0].records : []
          } else if (code === 302) {
            this.imgList = res.data.length > 0 ? res.data.filter(item => item.bannerArea === 'old_top')[0].records : []
          }
          this.getCode(this.clientCode)
        }
      }).catch((error) => {

      })
    },
    confirmTerminal() {

      let subParams = []


      const subList = []
      this.imgList.length > 0 ? this.imgList.forEach((item, index) => {
        const objs = {}
        objs['resourcesId'] = item.resourcesId ? item.resourcesId : null
        objs['imageUrl'] = item.imageUrl ? item.imageUrl : null
        objs['linkUrl'] = item.linkUrl ? item.linkUrl : null
        objs['classTypeId'] = item.classTypeId ? item.classTypeId : null
        objs['sort'] = `${index + 1}`
        subList.push(objs)
      }) : []

      const subListOld = []
      this.imgListOld.length > 0 ? this.imgListOld.forEach((item, index) => {
        const objs = {}
        objs['resourcesId'] = item.resourcesId ? item.resourcesId : null
        objs['imageUrl'] = item.imageUrl ? item.imageUrl : null
        objs['linkUrl'] = item.linkUrl ? item.linkUrl : null
        objs['classTypeId'] = item.classTypeId ? item.classTypeId : null
        objs['sort'] = `${index + 1}`
        subListOld.push(objs)
      }) : []

      const subListBottom = []
      this.imgListBottom.length > 0 ? this.imgListBottom.forEach((item, index) => {
        const objs = {}
        objs['resourcesId'] = item.resourcesId ? item.resourcesId : null
        objs['imageUrl'] = item.imageUrl ? item.imageUrl : null
        objs['linkUrl'] = item.linkUrl ? item.linkUrl : null
        objs['classTypeId'] = item.classTypeId ? item.classTypeId : null
        objs['sort'] = `${index + 1}`
        subListBottom.push(objs)
      }) : []

      if (this.clientCode === 102) {
        if (this.bannerArea === 'top') {
          const topObj = {
            bannerArea: 'top',
            clientCode: this.clientCode,
            records: subList
          }
          const bottomObj = {
            bannerArea: 'bottom',
            clientCode: this.clientCode,
            records: subListBottom
          }
          subParams = [
            topObj,
            bottomObj
          ]
        } else if (this.bannerArea === 'old_top') {
          subParams = [{
            bannerArea: 'old_top',
            clientCode: this.clientCode,
            records: subListOld
          }]
        }
      } else {
        subParams = [{
          bannerArea: 'old_top',
          clientCode: this.clientCode,
          records: subList
        }]
      }
      terminalSet(subParams).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '添加成功',
            type: 'success'
          })
          this.getTerminalBanner(this.clientCode)
        }
      }).catch((error) => {

      })
    },
    getCode(code) {
      getCode(code).then(res => {
        if (res.code === '000000') {
          this.classList = res.data || []
        }
      }).catch((error) => {

      })
    },
    cancelTerminal() {
      this.getTerminalBanner(this.clientCode)
    },
    getClassTypeId(val) {

    },
    getClassed(val) {

    }
  }

}
</script>

<style lang="scss" scoped>
.terminal-title{
    ul{
        display: flex;
        li{
            margin-right: 10px;
            padding: 7px 10px;
            border-radius: 5px;
            border:1px #eaeaea solid;
            color: #666;
            cursor: pointer;
            &.default{
                background: #1890ff;
                color: #fff;
                border-color: #1890ff;
            }
        }
    }
}
.terminal-list{
    margin: 15px 0;
}
.common-title{
    font-weight:normal;
    color: #666;
    padding: 20px 0;
}
.terminal-info{
    margin: 20px 0;
    display: flex;
    .terminal-info-list{
        width: 95%;
        clear: both;
    }
}
.upload-btn{
    float: left;
    width: 120px;
    height: 120px;
    border-radius: 4px;
    border:1px #1890ff dashed;
    background: #1890ff;
    text-align: center;
    margin-right: 20px;
    cursor: pointer;
    p{
        color: #fff;
        &.big{
            font-size: 22px;
            padding-bottom: 10px;
            margin-top: 25%;
        }
        &.small{
            font-size: 14px;
        }
    }
  }
.upload-imgs{
    position: relative;
    float: left;
    width: 320px;
    height: 340px;
    font-size: 14px;
    margin-right: 25px;
    margin-bottom: 20px;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0 3px 3px #eaeaea;
    border-radius: 3px;
  }
  .upload-imgs .add{
    display: block;
    width: 320px;
    background-color: #ccc;
    color: #ffffff;
    height: 200px;
    line-height: 200px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    margin-bottom: 10px;
  }
  .upload-imgs .add .el-icon-picture{
    font-size: 35px;
  }
  .upload-imgs .upload{
    position: absolute;
    bottom: 130px;
    left: 0;
    width: 320px;
    height: 200px;
    opacity: 0;
    cursor: pointer;
    z-index: 999;
  }
  .upload-imgs .img{
    width: 320px;
    height: 200px;
    margin-bottom: 10px;
  }
  .upload-imgs .img img{
    position: absolute;
    left: 0;
    bottom: 130px;
    vertical-align: middle;
    width: 320px;
    height: 200px;
    margin-bottom: 10px;
    z-index: 111;
  }
  .img-tips{
    position: absolute;
    bottom: 140px;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba($color: #333, $alpha: 0.6);
    color: #fff;
    font-size: 14px;
    padding: 5px 0;
    z-index: 999;
  }
  .upload-imgs .img .close{
    display: none;
    span{
        &:first-child{
            padding-right: 10px;
        }
    }
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:0px;
    left: 0px;
    width:320px;
    height:300px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 300px;
    font-size: 16px;
    color: #333;
    cursor: pointer;
    z-index: 9999999;
  }
  .btn-terminal{
      margin-top: 10px;
      span{
          cursor: pointer;
          &:last-child{
              color: red;
              margin-left: 10px;
          }
      }
  }
  .img-upload{
    padding-right: 8px;
  }
  .upload-tips{
    position: absolute;
    bottom: 0;
    left: 0;
    right:0;
    width:100%;
    text-align: center;
    font-size: 12px;
    color: #666;
    z-index: 9999;
  }
  .sub-info{
      display: flex;
      margin-bottom: 8px;
      padding:0 10px;
      line-height: 36px;
      span{
          font-size: 12px;
          color: #666;
          display: inline-block;
          width: 80px;
      }
  }
  .terminal-footer{
    display: flex;
    justify-content: center;
    margin: 20px 0;
    .cancel-terminal{
      margin-right: 15px;
    }
  }
  .img-tips{

  }
</style>
