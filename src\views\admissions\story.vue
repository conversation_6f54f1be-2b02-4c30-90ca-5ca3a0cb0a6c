<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="故事编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.title"
        placeholder="故事标题"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select filterable v-model="listQuery.subject" placeholder="选择科目" clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select filterable v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in admissionStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select filterable v-model="listQuery.questionType" placeholder="题目类型" clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in questions" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['admissions:storys:creat']" class="filter-item" type="primary" size="mini" @click="handleCreat">
        新增
      </el-button>
    </div>
    <el-table v-loading="listLoading" :data="list" border fit stripe highlight-current-row>
      <af-table-column label="编号" prop="id" width="50" align="center" />
      <af-table-column label="故事标题" prop="title" />
      <af-table-column label="科目" prop="subjectName" show-overflow-tooltip />
      <af-table-column label="类型" prop="questionType" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.questionType===1">选择题</span>
          <span v-if="scope.row.questionType===2">背诵题</span>
        </template>
      </af-table-column>
      <af-table-column label="状态" prop="status" :formatter="getStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" min-width="230" fixed="right">
        <template slot-scope="scope">
          <el-button v-permission="['admissions:storys:edit']" type="primary" size="mini" @click="editDetailPop(scope.row)">
            编辑
          </el-button>
          <el-button v-permission="['admissions:storys:able']" type="primary" size="mini" @click="disableOpera(scope.row)">{{ scope.row.status===99?'启用':'禁用' }}</el-button>
          <el-button v-permission="['admissions:storys:del']" type="primary" size="mini" @click="deleteSignle(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { storyList, getSubjects, enableStory, removeStory } from '@/api/admissions'
import { operatorsStatus, converseEnToCn } from '@/utils/field-conver'
export default {
  name: 'Story',
  components: {
    Pagination
  },
  data() {
    return {
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      listLoading: false,
      list: [],
      total: 0,
      subjectsList: [],
      questions: [
        {
          id: 1,
          title: '选择题'
        },
        {
          id: 2,
          title: '背诵题'
        }
      ],
      admissionStatus: operatorsStatus
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getSubjects()
    })
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleCreat() {
      localStorage.setItem('storyName', '新增')
      this.$router.push({
        name: 'AddStory',
        params: {
          isEdit: false
        }
      })
    },
    getList() {
      const that = this
      const params = Object.assign({}, that.listQuery)
      storyList(params).then(res => {
        if (res.code === '000000') {
          that.list = res.data.records
          that.listLoading = false
          that.total = res.data.total
        }
      }).catch(() => {

      })
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getStatus(row) {
      return converseEnToCn(this.admissionStatus, row.status)
    },
    editDetailPop(row) {
      localStorage.setItem('storyName', '修改')
      this.$router.push({
        name: 'AddStory',
        params: {
          isEdit: true,
          id: row.id
        }
      })
    },
    disableOpera(row) {
      const tips = row.status === 0 ? '确定要启用该试题吗?' : '确定要禁用该试题吗?'
      const result = row.status === 0 ? '启用成功' : '禁用成功'
      this.$confirm(`${tips}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        enableStory(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: `${result}`,
              type: 'success'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    deleteSignle(row) {
      this.$confirm(`确定要删除此试题吗?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        removeStory(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    }
  }
}
</script>
