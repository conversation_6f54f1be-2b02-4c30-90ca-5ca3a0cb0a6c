<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.productName" placeholder="产品名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.common" placeholder="共有的" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in isCommon"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.needDelivery" placeholder="是否需要物流" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in needDelivery"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.productType" placeholder="产品类别" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in productType"
          :key="item.itemValue"
          :label="item.itemName"
          :value="item.itemValue"
        />
      </el-select>
      <el-select v-model="listQuery.projectId" placeholder="项目" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in project"
          :key="item.id"
          :label="item.projectName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.valid" placeholder="是否可用" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in menuValidList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="产品名称">
        <template slot-scope="scope">
          <span class="link-type" @click="handleQuery(scope.row)">{{ scope.row.productName }}</span>
        </template>
      </af-table-column>
      <af-table-column label="项目" prop="projectName" width="100" />
      <af-table-column label="产品类别" prop="productType" :formatter="getProjectType" />
      <af-table-column label="共有的" prop="common" :formatter="getCommon" width="80" />
      <af-table-column label="是否需要物流" prop="needDelivery" :formatter="getDelivery" width="120" />
      <af-table-column label="单位" prop="productUnit" :formatter="getProductUnit" />
      <af-table-column label="是否可用" prop="valid" :formatter="getMenuValidList" width="100" />
      <af-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right" width="250">
        <template slot-scope="{row}">

          <el-button type="primary" size="mini" @click="editP=true,handleUpdate(row)">
            修改
          </el-button>
          <el-button type="primary" size="mini" @click="handleDelete(row)">
            删除
          </el-button>
          <el-button :type="buttonColor(row.valid)" size="mini" @click="handleStatus(row)">
            {{ getButtonText(row.valid) }}
          </el-button>
        </template>
      </af-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <product-dialog ref="productDialog" :is-edit="isEdit" @refresh="getList" @handleAdd="handleAdd" />

    <el-dialog title="修改产品" :visible.sync="editP" :close-on-click-modal="!editP">
      <el-form
        ref="editForm"
        :model="detail"
        label-width="130px"
        :rules="baseInfoRules"
        :disabled="isEdit"
      >
        <el-row>
          <el-col :xs="24" :sm="24">
            <el-form-item label="产品名称：" prop="productName"><el-input v-model="detail.productName" placeholder="产品名称" /></el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12">
            <el-form-item label="项目类型：" prop="projectId">
              <el-select v-model="detail.projectId" placeholder="项目类型" filterable clearable style="width: 100%" @change="getProjects">
                <el-option
                  v-for="item in project"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="产品类型：" prop="productType">
              <el-select v-model="detail.productType" placeholder="产品类型" filterable clearable style="width: 100%" @change="getPros">
                <el-option
                  v-for="item in productType"
                  :key="item.itemValue"
                  :label="item.itemName"
                  :value="item.itemValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12">
            <el-form-item label="是否共有：" prop="common">
              <el-select v-model="detail.common" placeholder="是否共有" filterable clearable style="width: 100%">
                <el-option
                  v-for="item in isCommon"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="单位：" prop="productUnit">
              <el-select v-model="detail.productUnit" placeholder="单位" filterable clearable style="width: 100%">
                <el-option
                  v-for="item in productUnit"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12">
            <el-form-item label="是否需要物流：" prop="needDelivery">
              <el-select v-model="detail.needDelivery" placeholder="是否需要物流" filterable clearable style="width: 100%">
                <el-option
                  v-for="item in needDelivery"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="是否可用：" prop="valid">
              <el-select v-model="detail.valid" placeholder="是否可用" filterable clearable style="width: 100%">
                <el-option
                  v-for="item in menuValidList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="(projectFlag==1||projectFlag==2||projectFlag==3)&&(proFlag==4)" :xs="24" :sm="24">
            <el-form-item label="播课产品：" prop="podcastProductId">
              <el-select ref="selectCh" v-model="detail.podcastProductId" placeholder="播课产品" filterable clearable style="width: 100%" @change="getBoke">
                <el-option
                  v-for="item in bokes"
                  :key="item.podcastProductId"
                  :label="item.podcastProductName"
                  :value="item.podcastProductId"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="detail.podcastProductName" style="display:none;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editDetail">确 定</el-button>
        <el-button @click="editP=false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { editProductStatus, getProductList, deleteProduct, getDetailPro, editProduct } from '@/api/system-setting'
import { getAllProject, getbo, getProductType } from '@/api/common'
import { menuValidList, converseEnToCn, productTypeList, isCommon, needDelivery, productUnit } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import productDialog from './dialog'
export default {
  name: 'Express',
  components: { Pagination, productDialog },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      menuValidList: menuValidList,
      isCommon: isCommon,
      needDelivery: needDelivery,
      productType: [],
      buttonText: '',
      project: [],
      editP: false,

      detail: {},
      baseInfoRules: {
        productName: { required: true, message: '产品名称必填', trigger: 'blur' },
        common: { required: true, message: '是否共有必选', trigger: 'blur' },
        needDelivery: { required: true, message: '是否需要物流必选', trigger: 'blur' },
        productType: { required: true, message: '产品类别必选', trigger: 'blur' },
        productUnit: { required: true, message: '单位必选', trigger: 'blur' },
        valid: { required: true, message: '是否可用必选', trigger: 'blur' },
        projectId: { required: true, message: '所属项目必选', trigger: 'blur' },
        productTypeName: { required: true, message: '产品类型名称必填', trigger: 'blur' }
      },
      productUnit: productUnit,
      projectList: [],
      bokes: [],
      projectFlag: 0,
      proFlag: 0,
      projectIds: 0
    }
  },
  created() {
    this.listLoading = false
    this.getList()
    this.getProject()
    this.getProductType()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增产品
     * */
    handleAdd() {
      this.$refs.productDialog.productDialog = true
      this.$refs.productDialog.detail = {}
      this.isEdit = false
    },

    getList() {
      const that = this
      getProductList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getProject() {
      const that = this
      getAllProject().then(res => {
        if (res.code === '000000') {
          that.project = res.data
        }
      })
    },
    getProductType() {
      const that = this
      getProductType('PRODUCT_TYPE').then(res => {
        if (res.code === '000000') {
          that.productType = res.data
        }
      })
    },
    /**
     * 查看详情
     * */
    handleQuery(row) {
      this.isEdit = true
      this.$refs.productDialog.getDetail(row.id)
    },
    /**
     * 修改产品
     * */
    handleUpdate(row) {
      this.isEdit = false
      getDetailPro(row.id).then(res => {
        this.detail = res.data
        this.projectFlag = res.data.projectId
        this.proFlag = res.data.productType
        this.getBokeList()
      })
    },
    /**
     * 更改状态
     * */
    handleStatus(row) {
      if (row.valid === 1) {
        this.$confirm('确定停用 ' + row.productName + '？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          editProductStatus({ id: row.id, valid: 0 }).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '停用成功!'
              })
              this.getList()
            }
          }).catch(res => {})
        }).catch(action => {

        })
      } else {
        this.$confirm('确定启用 ' + row.productName + '？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          editProductStatus({ id: row.id, valid: 1 }).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '启用成功!'
              })
              this.getList()
            }
          }).catch(res => {})
        }).catch(action => {

        })
      }
    },
    /**
     * 删除
     * */
    handleDelete(row) {
      this.$confirm('是否确认删除' + row.productName + '？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteProduct({ id: row.id }).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(action => {

      })
    },
    getMenuValidList(row) {
      return converseEnToCn(menuValidList, row.valid)
    },
    getProjectType(row) {
      return converseEnToCn(productTypeList, row.productType)
    },
    getCommon(row) {
      return converseEnToCn(isCommon, row.common)
    },
    getDelivery(row) {
      return converseEnToCn(needDelivery, row.needDelivery)
    },
    getProductUnit(row) {
      return converseEnToCn(productUnit, row.productUnit)
    },
    getAllProject(row) {
      return converseEnToCn(this.project, row.projectId)
    },
    getButtonText(valid) {
      return valid === 1 ? '停用' : '启用'
    },
    buttonColor(valid) {
      return valid === 1 ? 'danger' : 'primary'
    },
    editDetail() {
      const that = this
      editProduct(that.detail).then(res => {
        if (res.code === '000000') {
          that.$message({
            message: '修改成功！',
            type: 'success'
          })
          that.getList()
          that.editP = false
        } else {
          that.editP = false
        }
      })
    },
    getProjects(val) {
      const that = this
      that.projectIds = val
      that.projectFlag = val
      getbo(val).then(res => {
        if (res.code === '000000') {
          this.bokes = res.data
        }
      })
    },
    getBokeList() {
      if (this.projectFlag) {
        getbo(this.projectFlag).then(res => {
          if (res.code === '000000') {
            this.bokes = res.data
          }
        })
      }
    },
    getPros(val) {
      this.proFlag = val
    },
    getBoke(val) {
      this.$nextTick(() => {
        this.detail.podcastProductName = this.$refs.selectCh.selectedLabel
      })
    }
  }
}
</script>

<style scoped>

</style>
