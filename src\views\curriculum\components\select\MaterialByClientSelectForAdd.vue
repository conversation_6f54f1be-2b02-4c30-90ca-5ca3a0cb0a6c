<template>
  <el-select v-model="tmpId" style="width: 220px;" filterable clearable placeholder="教材版本（新）" v-bind="$attrs" no-data-text="请先选择产品线">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="`${item.title}`"
      :value="`${item.id}`">
      <div style="min-width: 200px;">
        <span class="fl">{{ item.title }}</span>
        <span class="subTitle">{{ item.id }}</span>
      </div>
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getMaterialListByClassTypeId, getMaterialListByClientCode } from '@/api/classType'

/**
 * 教材版本选择框
 */
export default {
  name: 'MaterialByClientSelectForAdd',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    clientCode: {
      type: [String, Number],
      required: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  watch: {
    clientCode(val) {
      this.handleChange(null)
      this.dataList=[]
      if (val) {
        this.getList()
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.title : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      if (!this.clientCode) {
        return
      }
      this.loading = true
      getMaterialListByClientCode(this.clientCode).then(res => {
        if (res.code === SUCCESS) {
          this.loading = false
          this.dataList= res.data
        }
        else {
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.subTitle {
  float: right;
  color: #8492a6;
  font-size: 13px
}
</style>
