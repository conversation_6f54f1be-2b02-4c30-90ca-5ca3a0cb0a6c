<template>
  <el-dialog
    :visible.sync="materialPop"
    :append-to-body="true"
    :title="materialTitle"
    :close-on-click-modal="!materialPop"
    width="60%"
    @open="open"
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="materialForm"
        :model="listQuery"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          label="子版本名称"
          :prop="data.textType ? 'editionId' : 'title'"
        >
          <el-select v-show="!!data.textType" v-model="listQuery.bookId" filterable>
            <el-option
              v-for="item in subVersionList"
              :key="item.index"
              :label="item.name"
              :value="item.bookId"
            ></el-option>
          </el-select>
          <el-input
            v-if="!data.textType"
            v-model="listQuery.title"
            placeholder="请输入教材版本"
            maxlength="20"
            :disabled="isEdit"
          />
        </el-form-item>
        <!--        <el-form-item label="适用产品线" prop="clientCode">-->
        <!--          <el-select v-model="listQuery.clientCode" placeholder="请选择产品线" clearable class="filter-item" :disabled="isEdit">-->
        <!--            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="listQuery.status"
            placeholder="请选择状态"
            clearable
            class="filter-item"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in enableList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input
            v-model.number="listQuery.sort"
            placeholder="请输入排序"
            :disabled="isEdit"
          />
        </el-form-item>
        <!-- <el-form-item label="v3" prop="bookId">
          <el-select v-model="listQuery.bookId">
            <el-option
              v-for="item in subVersionList"
              :key="item.index"
              :label="item.name"
              :value="item.bookId"
            ></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(materialPop = false), cancelClass()"
        >取消</el-button
      >
      <el-button type="primary" size="mini" @click="custormClass"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  clientCode,
  getSubMaterial,
  addSubMaterial,
  getv3SubVersionList,
  getCoursesMaterials
} from "@/api/classType";
import { enableList } from "@/utils/field-conver";
export default {
  name: "AddSubMaterial",
  data() {
    return {
      materialTitle: "",
      materialPop: false,
      listQuery: {
        status: 1,
        bookId: "",
        title: "",
        sort: ""
      },
      rules: {
        title: {
          required: true,
          trigger: "blur",
          message: "请输入教材子版本名称"
        },
        status: { required: true, trigger: "blur", message: "请选择状态" },
        bookId: { required: true, trigger: "change", message: "请选择v3版本" }
      },
      clientCode: [],
      enableList: enableList,
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false,
      subVersionList: [], //教材下拉列表
      data: {
        textType:''
      }
    };
  },
  props: {
    materialId: {
      type: [String, Number],
      default: "",
      required: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode();
    });
  },
  methods: {
    async open() {
      const { data } = await getCoursesMaterials(this.materialId);
      if(data){
        this.data = data;
        this.getVersionList();
      }
    },
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || [];
        this.clientCode = clientCodes.filter(item => item.level === 1);
      });
    },
    cancelClass() {
      this.listQuery = {};
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    },
    custormClass() {
      this.enableFlagsCreate = true;
      this.$refs.materialForm.validate(valid => {
        if (valid) {
          const params = Object.assign(
            {},
            this.listQuery,
            { id: this.listQuery.id ? this.listQuery.id : "" },
            { materialId: this.materialId },
          );
          if (this.data.textType) {
            params.title = this.subVersionList.find(
              item => item.bookId === params.bookId
            ).name;
          }
          addSubMaterial(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "操作成功"
                });
                this.$emit("addMaterialList");
                this.materialPop = false;
                this.listQuery = {
                  status: 1,
                  bookId: "",
                  title: "",
                  sort: ""
                };
                this.enableFlagsCreate = false;
                this.$refs.materialForm.clearValidate();
              }
            })
            .catch(() => {
              this.enableFlagsCreate = false;
            });
        } else {
          this.enableFlagsCreate = false;
          return false;
        }
      });
    },
    getMaterialList(row) {
      getSubMaterial(row.id).then(res => {
        if (res.code === "000000") {
          this.listQuery = res.data;
        }
      });
    },
    getVersionList() {
      const { subjectSid, editionId } = this.data;
      getv3SubVersionList({ subjectSid: subjectSid, editionId: editionId }).then(res => {
        if (res.code === "000000") {
          this.subVersionList = res.data;
        }
      });
    },
    changeInit() {
      this.listQuery = {
        status: 1,
        bookId: "",
        title: "",
        sort: ""
      };
      if (this.$refs.materialForm) {
        this.$refs.materialForm.clearValidate();
      }
    }
  }
};
</script>

<style scoped>
.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}
</style>
