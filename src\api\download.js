import request from '@/utils/request'
/**
 * 查询树形数据
 * @param data
 */
export function treeData(clientCode) {
  return request({
    url: `download/menu/getResourceDownloadMenuTree?clientCode=${clientCode}`,
    method: 'GET'
  })
}

/**
 * 查询下载中心关联的班型列表选项
 * @param clientCode
 * @returns {AxiosPromise}
 */
export function getClassData(clientCode) {
  return request({
    url: `download/menu/getResourceDownloadClass?clientCode=${clientCode}`,
    method: 'GET'
  })
}

/**
 * 查询下载中心关联的班型列表选项
 * @param menuId
 * @param classId
 */
export function saveMenuClass(menuId, classId) {
  return request({
    url: `download/menu/saveMenuClass?menuId=${menuId}&classId=${classId}`,
    method: 'GET'
  })
}
/**
 * 修改菜单排序
 * @param data
 */
export function treeSort(data) {
  return request({
    url: `download/menu/editResourceDownloadMenuSort`,
    method: 'GET',
    params: data
  })
}
/**
 * 修改当前菜单的父菜单
 * @param data
 */
export function editParentMenu(data) {
  return request({
    url: `download/menu/editResourceDownloadMenuParentMenuId`,
    method: 'GET',
    params: data
  })
}
/**
 * 删除菜单
 * @param data
 */
export function delMenu(data) {
  return request({
    url: `download/menu/deleteResourceDownloadMenu`,
    method: 'GET',
    params: data
  })
}
/**
 * 修改菜单名称
 * @param data
 */
export function editMenu(data) {
  return request({
    url: `download/menu/editResourceDownloadMenuName`,
    method: 'GET',
    params: data
  })
}
/**
 * 添加菜单
 * @param data
 */
export function addMenu(data) {
  return request({
    url: `download/menu/addResourceDownloadData`,
    method: 'GET',
    params: data
  })
}
/**
 * 点击tree查询文件列表
 * @param data
 */
export function fileList(data) {
  return request({
    url: `download/data/listResourceDownloadData`,
    method: 'GET',
    params: data
  })
}
/**
 * 编辑文件列表
 * @param data
 */
export function editFileList(data) {
  return request({
    url: `download/data/editResourceDownloadDataNameAndSortAndRemark`,
    method: 'GET',
    params: data
  })
}
/**
 * 编辑文件列表
 * @param data
 */
export function delFileList(data) {
  return request({
    url: `download/data/deleteResourceDownloadData`,
    method: 'GET',
    params: data
  })
}
/**
 * 添加文件
 * @param data
 */
export function addFileList(data) {
  return request({
    url: `download/data/addResourceDownloadData`,
    method: 'POST',
    data: data
  })
}
