<template>
  <el-dialog :visible.sync="storyFlag" :title="storyTitle" :close-on-click-modal="!storyFlag" width="60%" @close="changeInit">
    <h3 v-if="savePoster" class="upload-img-title">请点击保存按钮上传图片</h3>
    <div class="hello">
      <div class="upload">
        <div v-if="showDel" class="upload_warp">
          <div class="upload_warp_left" @click="fileClick">
            <img src="../../../assets/img/upload.png">
          </div>
          <div v-if="imgFlag!==2" class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
            或者将文件拖到此处
          </div>
        </div>
        <div class="upload_warp_text">
          <i v-if="imgList.length>0">选中{{ imgList.length }}张文件，</i><i v-if="size">共{{ bytesToSize(size) }}</i>
        </div>
        <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
        <div v-show="imgList.length!=0" class="upload_warp_img">
          <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
            <div class="upload_warp_img_div_top">
              <div class="upload_warp_img_div_text">
                {{ item.file.name }}
              </div>
              <img src="../../../assets/img/del.png" class="upload_warp_img_div_del dels" @click.stop.prevent="fileDel($event,index,item.file.src)">
            </div>
            <img :src="item.file.src">
          </div>
        </div>
      </div>
      <div class="upload-opera">
        <el-button v-if="showUpload" size="mini" type="primary" :disabled="imgList.length===0" @click="imgUpload">提交图片</el-button>
        <el-button v-if="savePoster" size="mini" type="primary" @click="confirmUpload">保存</el-button>
        <el-button size="mini" type="primary" @click="storyFlag=false,changeInit()">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { uuid } from '@/utils/index'
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { uploadSuccess } from '@/api/common'
export default {
  name: 'UploadPoster',
  data() {
    return {
      storyFlag: false,
      storyTitle: '批量上传图片',
      imgList: [],
      size: 0,
      listQuery: {},
      uploadImg: [],
      savePoster: false,
      showUpload: true,
      imgFlag: null,
      currentImgs: null,
      errorImgs: null,
      storyImgs: null,
      len: 0,
      showDel: true
    }
  },
  watch: {
    'imgList': {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.showUpload = true
          this.showDel = true
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    changeInit() {
      this.listQuery = {}
      this.imgList = []
      this.storyFlag = false
      this.savePoster = false
      this.size = 0
    },
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {
      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        // 判断是否为文件夹
        if (files[i].type !== '') {
          this.fileAdd(files[i])
        } else {
          // 文件夹处理
          this.folders(fileList.items[i])
        }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = 'wenjian.png'
        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(e, index, url) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      e.stopPropagation() // 表示阻止向父元素冒泡
      e.preventDefault()
      e.target.click = null
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.imgList.splice(index, 1)
        this.savePoster = false
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    },
    imgUpload() {
      const validateImg = this.imgList.every(item => {
        return (item.file.size / 1024 / 1024).toFixed(3) < 5
      })
      if (!validateImg) {
        this.$message({
          type: 'warning',
          message: '请上传5M以内的图片'
        })
        return false
      }



      const storyImgsLength = this.imgFlag === 1 && this.storyImgs && this.storyImgs.length > 0 ? this.storyImgs.length + this.imgList.length : this.imgList.length
      const imgLength = this.imgFlag === 3 && this.currentImgs && this.currentImgs.length > 0 ? this.currentImgs.length + this.imgList.length : this.imgList.length
      const imgLengths = this.imgFlag === 4 && this.errorImgs && this.errorImgs.length > 0 ? this.errorImgs.length + this.imgList.length : this.imgList.length
      let tips
      if (this.imgFlag === 3) {
        tips = '正确答案的图片只能显示5张，请先删去已经存在的图片再上传'
      } else if (this.imgFlag === 4) {
        tips = '错误答案的图片只能显示5张，请先删去已经存在的图片再上传'
      } else if (this.imgFlag === 1) {
        tips = '故事情节的图片只能显示20张，请先删去已经存的图片再上传'
      }
      if (storyImgsLength > 20 && this.imgFlag === 1) {
        this.$message({
          type: 'error',
          message: `${tips}`
        })
        return false
      }
      if (imgLength > 5 && this.imgFlag === 3) {
        this.$message({
          type: 'error',
          message: `${tips}`
        })
        return false
      }
      if (imgLengths > 5 && this.imgFlag === 4) {
        this.$message({
          type: 'error',
          message: `${tips}`
        })
        return false
      }
      this.posterUpload(this.imgList, 0)
    },
    confirmUpload() { // listQuery.clientCode
      const that = this

      if (that.imgList.length > 0) {
        if (that.imgFlag === 1) { // 故事情节
          that.$emit('getPic', that.imgList)
        } else if (that.imgFlag === 2) { // 题目
          that.$emit('getTitle', that.imgList[0])
        } else if (that.imgFlag === 3) { // 正确答案
          that.$emit('getCorrect', that.imgList)
        } else if (that.imgFlag === 4) {
          that.$emit('getError', that.imgList)
        }
        that.storyFlag = false
      } else {
        that.$message({
          type: 'warning',
          message: '请先上传图片'
        })
      }
    },
    posterUpload(files, i) {
      const that = this
      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const tempName = files[i].file.name.split('.')
      const ids = uuid()
      const fileName = `santao_stip/crm/story/${ids}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: files[i].file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {
          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/story/${ids}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {

              that.imgList[i].id = res.data.id
              that.imgList[i].src = res.data.url
              if (++i < that.imgList.length) {
                that.posterUpload(that.imgList, i)
              } else {
                setTimeout(() => {
                  loading.close()
                  that.savePoster = true
                  that.showUpload = false
                  that.showDel = false
                }, 2000)
              }
            }
          }).catch(() => {
            that.showUpload = true
            that.showDel = true
          })
        }
      })

    }
  }
}
</script>

<style scoped>
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 130px;
    color: #999;
  }

  .upload_warp_left img {
    margin-top: 32px;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
  }

  .upload_warp {
    margin: 14px;
    height: 130px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
  .upload-img-title{
    color: red;
    padding-bottom: 10px;
  }
</style>
<style>
  .el-dialog__body{
    padding-top: 10px !important;
  }
</style>
