<template>
  <el-dialog :title="receiveTitle" :visible.sync="receivePop" :close-on-click-modal="!receivePop" width="40%">
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="接收人" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.partnerName">{{ scope.row.partnerName }}</span>
          <span v-if="scope.row.partnerAccount">({{ scope.row.partnerAccount }})</span>
        </template>
      </el-table-column>
      <af-table-column label="状态" prop="isRead">
        <template slot-scope="scope">
          <span v-if="scope.row.isRead===1">已读</span>
          <span v-else>未读</span>
        </template>
      </af-table-column>
      <af-table-column label="已读时间" show-overflow-tooltip prop="readTime" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>
<script>
import Pagination from '@/components/Pagination'
import { receiveMessage } from '@/api/message'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      receivePop: false,
      receiveTitle: ''
    }
  },
  methods: {
    getList(messageId) {
      const params = Object.assign({}, this.listQuery)
      receiveMessage(messageId, params).then(res => {

        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {

      })
    }
  }
}
</script>
