<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.searchField"
        placeholder="姓名/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.company"
        placeholder="公司名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.isAllotted" placeholder="分配状态" filterable clearable class="filter-item" style="width: 120px;">
        <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.userId" filterable placeholder="跟进人员" clearable class="filter-item" style="width: 160px;">
        <el-option v-for="item in followLists" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="提交开始日期"
        end-placeholder="提交结束日期"
        style="width:270px"
      />
      <el-button class="filter-item" size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button class="filter-item" size="mini" type="primary" @click="handleReset">重置</el-button>
      <el-button v-permission="['website:cooperation:export']" class="filter-item" size="mini" type="primary" @click="handleExport">导出</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" />
      <af-table-column label="姓名" prop="name" />
      <af-table-column label="手机号/微信" prop="phone" />
      <af-table-column label="邮箱" prop="email" />
      <af-table-column label="公司名称" prop="company" />
      <af-table-column label="咨询内容" prop="content" />
      <af-table-column label="分配状态" prop="isAllotted" width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.isAllotted===1">已分配</span>
          <span v-if="scope.row.isAllotted===0">未分配</span>
        </template>
      </af-table-column>
      <af-table-column label="跟进人员" prop="realName" />
      <af-table-column label="提交时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" align="center" width="180" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['website:cooperation:distribution']" type="primary" size="mini" @click="distributionShow=true,distributionPlan(row)">分配</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!--分配人员-->
    <el-dialog :visible.sync="distributionShow" :title="distributionTitle" :close-on-click-modal="!distributionShow" width="40%" @close="cancelFollow">
      <el-form>
        <el-form-item>
          <span style="padding-right:10px">
            <i class="red">*</i>
            <em>分配员工</em>
          </span>
          <el-select v-model="userId" filterable placeholder="请分配跟进人员" clearable class="filter-item" style="width:70%">
            <el-option v-for="item in followLists" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item class="opera-btns">
          <el-button type="primary" size="mini" @click="confirmFollow">确定</el-button>
          <el-button type="default" size="mini" @click="cancelFollow">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--分配人员-->
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { cooperationList, followList, allott } from '@/api/website'
export default {
  name: 'CooperationPlan',
  components: {
    Pagination
  },
  data() {
    return {
      followDate: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 15
      },
      listLoading: true,
      list: [],
      total: 0,
      typeList: [
        {
          id: 1,
          title: '已分配'
        }, {
          id: 0,
          title: '未分配'
        }
      ],
      followLists: [],
      distributionShow: false,
      distributionTitle: '分配',
      userId: null,
      cooperationId: null,
      followRules: [
        {
          userId: { required: true, trigger: 'blur', message: '请分配跟进人员' }
        }
      ]
    }
  },
  mounted() {
    this.getList()
    this.getFollow()
  },
  methods: {
    getList() {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery, {
        beginTime: this.followDate !== null && this.followDate[0] ? this.followDate[0] : '',
        endTime: this.followDate !== null && this.followDate[1] ? this.followDate[1] : ''
      })
      cooperationList(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records || []
          this.total = res.data.total
          setTimeout(() => {
            this.listLoading = false
          }, 500)
        }
      }).catch(error => {

      })
    },
    handleSearch() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    getFollow() {
      followList().then(res => {
        const arrs = []
        if (res.code === '000000') {
          res.data.length > 0 ? res.data.forEach(item => {
            const objs = {
              id: item.id,
              title: `${item.realName}(${item.mobile})`
            }
            arrs.push(objs)
          }) : []
          this.followLists = arrs
        }
      }).catch(error => {

      })
    },
    distributionPlan(row) {
      this.cooperationId = row.id
      this.userId = row.userId
      this.getFollow()
    },
    confirmFollow() {
      if (!this.userId) {
        this.$message({
          message: '请选择分配人员',
          type: 'error'
        })
        return false
      }
      allott(this.cooperationId, this.userId).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '分配成功',
            type: 'success'
          })
          this.userId = null
          this.cooperationId = null
          this.distributionShow = false
          this.getList()
        }
      }).catch(error => {

      })
    },
    cancelFollow() {
      this.userId = null
      this.cooperationId = null
      this.distributionShow = false
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'website/cooperations/export?pageIndex=' + that.listQuery.pageIndex + '&pageSize=' + that.listQuery.pageSize
      url = url + '&company=' + `${that.listQuery.company ? that.listQuery.company : ''}` + '&isAllotted=' + `${that.listQuery.isAllotted ? that.listQuery.isAllotted : ''}` + '&searchField=' + `${that.listQuery.searchField ? that.listQuery.searchField : ''}` + '&beginTime=' + `${that.followDate.length > 0 && that.followDate[0] ? that.followDate[0] : ''}` + '&endTime=' + `${that.followDate.length > 0 && that.followDate[1] ? that.followDate[1] : ''}` + '&userId=' + `${that.listQuery.userId ? that.listQuery.userId : ''}`
      that.$confirm('确定导出数据?', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        that.listLoading = true
        const params = Object.assign(that.listQuery, {
          beginTime: that.followDate[0] || '',
          endTime: that.followDate[1] || ''
        })
        cooperationList(params).then(res => {
          that.list = res.data.records || []
          that.total = res.data.total || 0
          that.listLoading = false
          if (url && that.list.length > 0) {
            a.href = url
            a.target = '_blank'
            document.body.appendChild(a)
            a.click()
          } else {
            setTimeout(() => {
              that.$message({
                type: 'warning',
                message: '没有可以导出的数据'
              })
            }, 500)
          }
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '没有可以导出的数据'
          })
        })
      }).catch(() => {
        that.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.red{
  color: red;
}
.opera-btns{
  display: flex;
  justify-content: center;
}
</style>
