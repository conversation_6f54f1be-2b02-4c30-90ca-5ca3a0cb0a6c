import request from '@/utils/request'
/**
 * 获取校区项目列表（机构）
 * @param data
 */
export function getShoolProjectList(data) {
  return request({
    url: 'institutions/page',
    method: 'GET',
    params: data
  })
}

/**
 * 获取启动期校区列表
 * @param data
 */
export function pageByStart(data) {
  return request({
    url: 'institutions/pageByStart',
    method: 'GET',
    params: data
  })
}
/**
 * 获取启动期校区列表总家数和启动家数
 * @param data
 */
export function pageByStartTj(data) {
  return request({
    url: 'institutions/pageByStartTj',
    method: 'GET',
    params: data
  })
}
/**
 * 获取校区项目列表详情（机构）
 * @param data
 */
export function getShoolProjectDetail(data) {
  return request({
    url: 'institutions/' + data,
    method: 'GET'
  })
}
/**
 * 删除校区项目列表（机构）
 * @param data
 */
export function deleteShoolProjectList(data) {
  return request({
    url: 'institutions/' + data,
    method: 'DELETE'
  })
}
/**
 * 修改校区项目
 * @param data
 */
export function updateShoolProjectList(data) {
  return request({
    url: 'institutions',
    method: 'PUT',
    data: data
  })
}
/**
 * 解约校区项目
 * @param data
 */
export function rescissionShoolProjectList(data) {
  return request({
    url: 'institutions/rescission/' + data,
    method: 'PUT'
  })
}
/**
 * 延期合同
 * @param data
 */
export function delay(data) {
  return request({
    url: 'institutions/delay/' + data,
    method: 'PUT'
  })
}
/**
 * 续约提醒
 * @param data
 */
export function remind(schoolId) {
  return request({
    url: `/stip/schoolManage/remind/${schoolId}`,
    method: 'POST'
  })
}
/**
 * 开通机构账号
 * @param data
 */
export function openAccount(id) {
  return request({
    url: `institutions/openAccount/${id}`,
    method: 'POST'
  })
}

/**
 * 直接开通机构账号
 * @param id
 * @returns {*}
 */
export function openAdminAccount(id) {
  return request({
    url: `institutions/admin/openAccount/${id}`,
    method: 'POST'
  })
}
/**
 * 查询机构到期数量
 * @param data
 */
export function getExpireInstitutionNums() {
  return request({
    url: `institutions/getExpireInstitutionNums`,
    method: 'GET'
  })
}
/**
 * 查询机构到期数列表
 * @param data
 */
export function getInstitutionList(data) {
  return request({
    url: `institutions/page/expires`,
    method: 'GET',
    params: data
  })
}

export function institutions(data) {
  return request({
    url: 'institutions/editCustomerLevel',
    method: 'put',
    data: data
  })
}
