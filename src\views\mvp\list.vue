<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.customer"
        placeholder="家长姓名/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.assignAgency"
        placeholder="校区名称/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-select v-model="listQuery.assignSatuts" placeholder="指派状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in assignSatuts" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.paymentStatus" placeholder="付费状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in paymentStatus" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="times"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="width: 300px"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleCreate">添加家长线索</el-button>
      <el-button v-waves v-permission="['mvp:list:assing']" class="filter-item" type="primary" size="mini" @click="getTransferDialog">
        批量指派
      </el-button>
      <el-button v-waves v-permission="['mvp:list:export']" class="filter-item" type="primary" size="mini" @click="handleExport">
        导出报表
      </el-button>
      <span class="assign-user">
        <em>未指派用户</em>
        <el-tag>{{ allWaitAssign }}</el-tag>
      </span>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" fixed="left" />
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="添加日期" show-overflow-tooltip prop="createTime" />
      <af-table-column label="客户名称" show-overflow-tooltip prop="customerName" />
      <af-table-column label="客户手机号" prop="customerPhone" show-overflow-tooltip />
      <af-table-column label="所在区域" prop="location" show-overflow-tooltip />
      <af-table-column label="信息来源" prop="customerSource" :formatter="customerSources" />
      <af-table-column label="付费状态" prop="paymentStatus" :formatter="paymentStatusList" />
      <af-table-column label="指派校区" prop="assignAgencyName" />
      <af-table-column label="指派状态" prop="assignSatuts" :formatter="assignSatutsList" />
      <af-table-column label="操作人" prop="assignName" show-overflow-tooltip />
      <af-table-column label="操作时间" prop="assignTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-if="row.assignSatuts!==1" v-permission="['mvp:list:SingleAssings']" type="primary" size="mini" @click="handleUpdate(row)">指派</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!-- 指派弹框 -->
    <assing-pop ref="assing" :clue-ids="clueIds" @updateList="getList" />
    <!-- 指派弹框 -->
    <!-- 添加家长线索弹框 -->
    <creat-clue ref="cluesParents" @updateClue="getList" />
    <!-- 添加家长线索弹框 -->
  </div>
</template>

<script>
import {
  getCustomerList
  // exportCustorm
} from '@/api/mvp'
import {
  parseTime
} from '@/utils'
import {
  customerSource,
  paymentStatus,
  assignSatuts,
  converseEnToCn
} from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import AssingPop from './components/assingPop'
import CreatClue from './components/creatClue'
export default {
  name: 'CustomerAssigned',
  components: {
    Pagination,
    AreaPicker,
    AssingPop,
    CreatClue
  },
  directives: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      assignSatuts: assignSatuts,
      paymentStatus: paymentStatus,
      editCustomer: true,
      times: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        customer: '',
        assignAgency: '',
        assignSatuts: '',
        paymentStatus: ''
      },
      dialogTransfer: false, // 批量转让的弹窗
      multipleSelection: [], // 多选框被选中的row
      multipleSelectCustomerId: [], // 多选框选中的客户id
      beTransferCustomer: '',
      exportSetting: { // 导出按钮
        downloadLoading: false
      },
      createDialogTitle: '新增客户',
      testDialog: false,
      allWaitAssign: '',
      customerSource: customerSource,
      clueIds: [] // 指派时需要传递的客户资源id
    }
  },
  computed: {},
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    async getList() {

      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, that.areaList, {
        startTime: this.times !== null ? this.times[0] : '',
        endTime: this.times !== null ? this.times[1] : ''
      })

      await getCustomerList(params).then(response => {
        that.list = response.data.page.records
        that.total = response.data.page.total
        that.allWaitAssign = response.data.allWaitAssign || 0
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
       * 获取省市区的地址
       * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
       * 格式化时间
       */
    formatterTime(row) {
      return parseTime(row.createTime)
    },
    // 重置
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.times = []
      this.getList()
    },
    handleCreate() {
      this.$refs.cluesParents.cluePop = true
      this.$refs.cluesParents.clueParent = {}
      this.$refs.cluesParents.areaList = {}
    },
    handleUpdate(row) {

      const ids = []
      ids.push(row.id)
      this.clueIds = ids
      this.$refs.assing.assingPop = true
    },
    handleFollow(row) {
      this.$refs.followRef.getLists(row.id)
    },
    /**
       * 批量转让的弹窗
       * */
    getTransferDialog(row) {
      if (row.id) {
        this.multipleSelectCustomerId = [row.id]
      }
      if (this.multipleSelectCustomerId && this.multipleSelectCustomerId.length > 0) {
        // this.clueIds = this.multipleSelectCustomerId
        this.$refs.assing.assingPop = true
      } else {
        this.$message({
          message: '请先选择指派人',
          type: 'warning'
        })
      }
    },
    /**
       * 获取搜索筛选之后的用户id
       **/
    getEmployeeId(id) {
      this.beTransferCustomer = id
    },
    /**
       * 多选框的返回值
       */
    handleSelectionChange(val) {
      let assignFlag = false
      this.multipleSelection = val
      this.multipleSelectCustomerId = this.multipleSelection.map(item => {
        return item.id
      })
      this.clueIds = this.multipleSelection.map(item => {
        if (item.assignSatuts === 1) {
          assignFlag = true
          this.$refs.assignTab.clearSelection()
        } else {
          return item.id
        }
      })
      if (assignFlag) {
        this.$message({
          type: 'warning',
          message: '请选择未指派的数据'
        })
      }

    },
    /* 导出数据 */
    handleExport() {
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'customerClues/export?pageIndex=' + this.listQuery.pageIndex + '&pageSize=' + this.listQuery.pageSize
      const startTime = this.times[0]
      const endTime = this.times[1]
      const provinceId = this.areaList.provinceId
      const cityId = this.areaList.cityId
      const areaId = this.areaList.areaId
      const assignAgency = this.listQuery.assignAgency
      const assignSatuts = this.listQuery.assignSatuts
      const paymentStatus = this.listQuery.paymentStatus
      if (startTime !== undefined && startTime !== '' && startTime !== null) {
        url = url + '&startTime=' + startTime
      }
      if (endTime !== undefined && endTime !== '' && endTime !== null) {
        url = url + '&endTime=' + endTime
      }
      url = url + '&provinceId=' + provinceId + '&cityId=' + cityId + '&areaId=' +
          areaId + '&assignAgency=' + assignAgency + '&assignSatuts=' + assignSatuts +
          '&paymentStatus=' + paymentStatus
      if (this.list.length > 0) {
        this.$confirm('确定导出数据?', {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          if (url) {
            a.href = url
            a.target = '_blank'
            document.body.appendChild(a)
            a.click()
          }
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      } else {
        this.$message({
          type: 'warning',
          message: '无导出的数据'
        })
      }
    },
    cancelTransfer() {
      this.dialogTransfer = false
      this.$refs.transfer.transferForm.employeeSearchField = ''
    },
    // 转化信息来源
    customerSources(row) {
      return converseEnToCn(this.customerSource, row.customerSource)
    },
    // 转化付费状态
    paymentStatusList(row) {
      return converseEnToCn(this.paymentStatus, row.paymentStatus)
    },
    assignSatutsList(row) {
      return converseEnToCn(this.assignSatuts, row.assignSatuts)
    },
    changeTime(val) {

    }
  }
}
</script>
<style scoped="scoped" lang="scss">
  em {
    font-style: normal;
  }
  .filter-container .filter-item{
    margin-top: 6px !important;
  }
  .assign-user{
    padding-left: 10px;
  }
  @media screen and (max-width: 1366px){
    .assign-user{
      display: block;
      padding-left: 0;
    }
  }
</style>
