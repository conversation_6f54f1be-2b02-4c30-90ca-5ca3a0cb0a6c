<template>
  <el-dialog
    :visible.sync="coursePop"
    :title="courseTitle"
    top="0"
    :close-on-click-modal="!coursePop"
    width="60%"
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="courseForms"
        :model="listQuery"
        size="mini"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="课程名称" prop="title">
              <el-input
                v-model="listQuery.title"
                placeholder="请输入课程名称"
                maxlength="30"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="所属产品线" prop="clientCode">
              <ProductLineSelect
                v-model="listQuery.clientCode"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属班型" prop="classTypeId">
              <!--              <el-select v-model="listQuery.classTypeId" filterable placeholder="请选择所属班型" :disabled="isEdit">-->
              <!--                <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />-->
              <!--              </el-select>-->
              <ClassTypeByClientSelect
                v-model="listQuery.classTypeId"
                :clientCode="listQuery.clientCode"
              ></ClassTypeByClientSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属科目" prop="subjectId">
              <el-select
                v-model="listQuery.subjectId"
                filterable
                placeholder="请选择所属科目"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="changeSubject"
              >
                <el-option
                  v-for="item in subjectsAll"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="授课教师" prop="teacherId">
              <el-select
                v-model="listQuery.teacherId"
                filterable
                placeholder="请选择授课教师"
                no-data-text="请先选择班型和科目"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="getTeacher"
              >
                <el-option
                  v-for="item in teacherList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="right" v-if="showStyle === STYLE_BOTH || showStyle === STYLE_MATERIAL">新版设置项</el-divider>
        <el-row :gutter="10">
          <el-col
            :span="8"
            v-if="showStyle === STYLE_BOTH || showStyle === STYLE_MATERIAL"
          >
            <el-form-item
              label="教材版本（新）"
              prop="materialNewId"
              :rules="[
                { required: true, message: '请选择教材版本', trigger: 'blur' }
              ]"
            >
              <el-select v-model="listQuery.materialNewId" filterable>
                <el-option
                  v-for="item in materialNewList"
                  :key="item.index"
                  :label="item.title"
                  :value="item.id"
                ></el-option>
              </el-select>
              <!-- <MaterialByClientSelectForAdd
                v-model="listQuery.materialNewId"
                :clientCode="listQuery.clientCode"
                :disabled="isEdit"
              ></MaterialByClientSelectForAdd> -->
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="showStyle === STYLE_BOTH">
            <el-form-item
              label="教材子版本"
              prop="versionId"
              :rules="[
                { required: true, message: '请选择教材子版本', trigger: 'blur' }
              ]"
            >
              <SubMaterialSelect
                v-model="listQuery.versionId"
                :main-id="listQuery.materialNewId"
                :disabled="isEdit"
              ></SubMaterialSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="catalogFlag" label="知识模块" prop="catalogId">
              <!-- <KnowledgeByClassAndSubjectSelectForAdd
                v-model="listQuery.catalogId"
                :class-id="listQuery.classTypeId"
                :subject-id="listQuery.subjectId"
                :disabled="isEdit"
              ></KnowledgeByClassAndSubjectSelectForAdd> -->
              <el-select v-model="listQuery.catalogId" filterable>
                <el-option
                  v-for="item in subjectCatalogList"
                  :key="item.index"
                  :label="item.title"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="right">旧版设置项</el-divider>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="教材版本（旧）" prop="materialId">
              <el-select
                v-model="listQuery.materialId"
                filterable
                placeholder="请选择教材版本（旧）"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="changeMaterial"
              >
                <el-option
                  v-for="item in materialList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="大纲/章" prop="outlineId">
              <el-select
                v-model="listQuery.outlineId"
                filterable
                placeholder="请选择大纲/章"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="getOutLine"
              >
                <el-option
                  v-for="item in outLines"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="考点/节" prop="keynoteId">
              <el-select
                v-model="listQuery.keynoteId"
                placeholder="请选择考点/节"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="changeKeynote"
              >
                <el-option
                  v-for="item in keynoteList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="年级" prop="gradeId">
              <el-select
                v-model="listQuery.gradeId"
                placeholder="请选择年级"
                clearable
                class="filter-item"
                :disabled="isEdit"
              >
                <el-option
                  v-for="item in gradesList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="计时规则" prop="billingType">
              <el-radio-group
                v-model="listQuery.billingType"
                placeholder="请选择计时规则"
                clearable
                class="filter-item"
                :disabled="isEdit"
              >
                <el-radio
                  v-for="item in billingType"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12" v-show="false">-->
          <!--            <el-form-item label="课程渠道">-->
          <!--              <el-radio-group v-model="listQuery.courseSource" @change="resetValidate">-->
          <!--                <el-radio :label="1">三陶</el-radio>-->
          <!--                <el-radio :label="2">奇速</el-radio>-->
          <!--              </el-radio-group>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              v-if="listQuery.courseSource === 1"
              label="阿里链接"
              prop="vid"
              required
            >
              <el-input
                v-model="listQuery.vid"
                placeholder="请输入阿里链接"
                maxlength="32"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="listQuery.courseSource === 1" label="华为链接">
              <el-input
                v-model="listQuery.assetId"
                placeholder="请输入华为链接"
                maxlength="32"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="listQuery.courseSource === 2" label="课程URL">
              <el-input
                v-model="listQuery.visitUrl"
                placeholder="请输入课程URL"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="课程时长" required>
              <div class="course-duration">
                <el-input
                  v-model="listQuery.videoMinute"
                  placeholder="请输入课程时长"
                  maxlength="20"
                  :disabled="isEdit"
                />
                <em>分</em>
                <el-input
                  v-model="listQuery.videoSecond"
                  placeholder="请输入课程时长"
                  maxlength="20"
                  :disabled="isEdit"
                />
                <em>秒</em>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="课程排序">
              <el-input
                v-model="listQuery.sort"
                placeholder="请输入排序"
                maxlength="20"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-form-item label="显示状态" prop="isShow">
            <el-radio-group v-model="listQuery.isShow" :disabled="isEdit">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="课程状态" prop="status">
            <el-radio-group v-model="listQuery.status" :disabled="isEdit">
              <el-radio :label="0">未上架</el-radio>
              <el-radio :label="1">已上架</el-radio>
              <el-radio :label="2">已下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="课程封面" required>
            <div class="upload-imgs">
              <div v-if="!resourcesImg && !isEdit">
                <input
                  ref="inputerA"
                  type="file"
                  class="upload"
                  multiple
                  accept="image/png,image/jpeg,image/gif,image/jpg"
                  @change="upload($event)"
                />
                <a class="add"
                  ><i class="iconfont icon-plus" />
                  <p>点击上传</p></a
                >
              </div>
              <p class="img">
                <img v-if="resourcesImg" :src="resourcesImg" />
                <a
                  v-if="resourcesImg && !isEdit"
                  class="close"
                  @click="delImgA"
                >
                  <i class="el-icon-delete" />
                </a>
              </p>
            </div>
          </el-form-item>
          <el-form-item label="上线时间" prop="onlineDate">
            <el-date-picker
              v-model="listQuery.onlineDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="filter-item"
              placeholder="上线时间"
            />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(coursePop = false), changeInit()"
        >取消</el-button
      >
      <!-- 新增课程-->
      <el-button
        v-if="flags === 1"
        :disabled="disableSubmit"
        type="primary"
        size="mini"
        @click="addCourse"
        >确定</el-button
      >
      <!-- 修改课程-->
      <el-button
        v-if="flags === 0"
        :disabled="disableSubmit"
        type="primary"
        size="mini"
        @click="editCourse"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
// eslint-disable-next-line no-undef
import MaterialByClientSelect from "@/views/curriculum/components/select/MaterialByClientSelectForAdd.vue";

const STYLE_BOTH = 1; // 教材和子版本
const STYLE_MATERIAL = 2; //仅教材
const STYLE_NONE = 3; // 都没有
var obsClient = new ObsClient({
  access_key_id: "CSMHAP6XJZ3Q9NTLYX7W",
  secret_access_key: "o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC",
  server: "obs.cn-north-1.myhuaweicloud.com",
  timeout: 60 * 5
});
import {
  getAllClassType,
  getAllSubjects,
  classAndSubject,
  materialsList,
  outLines,
  getKeynote,
  grades,
  addCourse,
  courseDetail,
  editCourse,
  getCourseMaterialByParam,
  getStyleModelByClassAndSubject
} from "@/api/classType";
import { billingType } from "@/utils/field-conver";
import { uploadSuccess } from "@/api/common";
import SubMaterialSelect from "@/components/Select/SubMaterialSelect.vue";
import ProductLineSelect from "@/components/Select/ProductLineSelect.vue";
import MaterialByClientSelectForAdd from "@/views/curriculum/components/select/MaterialByClientSelectForAdd.vue";
import KnowledgeByClassAndSubjectSelectForAdd from "@/views/curriculum/components/select/KnowledgeByClassAndSubjectSelectForAdd.vue";
import ClassTypeByClientSelect from "@/components/Select/ClassTypeByClientSelect.vue";
import { SUCCESS } from "@/utils/http-status-code";

const initListQuery = {
  subjectId: null,
  classTypeId: null,
  courseSource: 1,
  materialNewId: null,
  title: ''
}
export default {
  name: "AddClassPop",
  components: {
    ClassTypeByClientSelect,
    KnowledgeByClassAndSubjectSelectForAdd,
    MaterialByClientSelectForAdd,
    ProductLineSelect,
    SubMaterialSelect,
    MaterialByClientSelect
  },
  data() {
    return {
      courseTitle: "",
      coursePop: false,
      disableSubmit: false,
      listQuery: {
        subjectId: null,
        classTypeId: null,
        courseSource: 1,
        materialNewId: null,
        title: ''
      },
      STYLE_BOTH: STYLE_BOTH,
      STYLE_MATERIAL: STYLE_MATERIAL,
      STYLE_NONE: STYLE_NONE,
      showStyle: null,
      rules: {
        title: { required: true, trigger: "blur", message: "请输入课程名称" },
        classTypeId: { required: true, trigger: "blur", message: "请选择班型" },
        subjectId: {
          required: true,
          trigger: "blur",
          message: "请选择所属科目"
        },
        teacherId: {
          required: true,
          trigger: "blur",
          message: "请选择授课教师"
        },
        materialId: {
          required: true,
          trigger: "blur",
          message: "请选择教材版本"
        },
        materialNewId: {
          required: true,
          trigger: "blur",
          message: "请选择教材版本(新)"
        },
        outlineId: {
          required: true,
          trigger: "blur",
          message: "请选择大纲/章"
        },
        keynoteId: {
          required: true,
          trigger: "blur",
          message: "请选择考点/节"
        },
        courseSource: {
          required: true,
          trigger: "blur",
          message: "请选择课程渠道"
        },
        isShow: { required: true, trigger: "blur", message: "请选择显示状态" },
        status: { required: true, trigger: "blur", message: "请选择课程状态" },
        videoMinute: {
          required: true,
          trigger: "blur",
          message: "请输入课程时长"
        },
        videoSecond: {
          required: true,
          trigger: "blur",
          message: "请输入课程时长"
        },
        onlineDate: {
          required: true,
          trigger: "blur",
          message: "请选择课程上线时间"
        }
      },
      assignSatuts: [],
      host: "https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/",
      customerUpdate: false,
      allClassType: [],
      subjectsAll: [],
      billingType: billingType,
      teacherList: [],
      materialList: [],
      outLines: [],
      keynoteList: [],
      gradesList: [],
      isEdit: false,
      flags: -1,
      resourcesImg: "", // 课程封面
      resourcesImgId: "", // 课程封面id
      uuid: "",
      catalogFlag: 0,
      subjectCatalogList: [],
      materialNewList: [] //教材版本列表
    };
  },
  watch: {
    teacherList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.teacherId = "";
        }
      },
      deep: true
    },
    outLines: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.outlineId = "";
          this.listQuery.keynoteId = "";
          this.keynoteList = [];
        }
      },
      deep: true
    },
    keynoteList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.keynoteId = "";
        }
      },
      deep: true
    },
    "listQuery.classTypeId": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getStyleBySubjectAndClass();
        }
      }
    },
    "listQuery.subjectId": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getStyleBySubjectAndClass();
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // this.getAllClassType()
      this.getAllSubjects();
      this.getMaterial();
      this.getGrades();
    });
  },
  methods: {
    getStyleBySubjectAndClass() {
      if (!this.listQuery.classTypeId || !this.listQuery.subjectId) return;
      const params = {
        classTypeId: this.listQuery.classTypeId,
        subjectId: this.listQuery.subjectId
      };
      getStyleModelByClassAndSubject(params)
        .then(res => {
          if (res.code === SUCCESS) {
            this.disableSubmit = false;
            this.showStyle = res.data.style || STYLE_NONE;
            this.catalogFlag = res.data.catalogFlag;
            this.subjectCatalogList = res.data.subjectCatalogList;
          }
        })
        .catch(res => {
          this.showStyle = STYLE_NONE;

          // 如果没有数据重置掉新版设置项
          this.listQuery.materialNewId = '';
          this.disableSubmit = true;
        });
    },
    getAllClassType() {
      // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data;
      });
    },
    getAllSubjects() {
      // 有效科目
      getAllSubjects().then(res => {
        if (res.code === "000000") {
          this.subjectsAll = res.data;
        }
      });
    },
    getTeacher() {
      if (this.listQuery.classTypeId && this.listQuery.subjectId) {
        const params = {
          classId: this.listQuery.classTypeId,
          subjectId: this.listQuery.subjectId
        };
        classAndSubject(params).then(res => {
          if (res.code === "000000") {
            this.teacherList = res.data;
          }
        });
      } else {
        this.$message({
          type: "warning",
          message: "请先选择科目和班型"
        });
      }
    },
    getMaterial() {
      materialsList().then(res => {
        if (res.code === "000000") {
          this.materialList = res.data;
        }
      });
    },
    changeSubject(val) {
      if (this.listQuery.classTypeId && val) {
        this.getTeacher();
        this.getMaterialNew()
        // 重置掉新版设置项
        this.listQuery.materialNewId = ''
      }
    },
    getOutLine() {
      // outLines
      if (
        this.listQuery.classTypeId &&
        this.listQuery.subjectId &&
        this.listQuery.materialId
      ) {
        const params = {
          classId: this.listQuery.classTypeId,
          materialId: this.listQuery.materialId,
          subjectId: this.listQuery.subjectId
        };
        outLines(params).then(res => {
          if (res.code === "000000") {
            this.outLines = res.data;
            if (this.listQuery.outlineId) {
              this.getKeynote(this.listQuery.outlineId);
            } else {
              this.keynoteList = [];
            }
          }
        });
      } else {
        this.$message({
          type: "warning",
          message: "请先选择科目和班型和教材"
        });
      }
    },
    getKeynote(outlineId) {
      getKeynote(outlineId).then(res => {
        if (res.code === "000000") {
          this.keynoteList = res.data;
        }
      });
    },
    changeMaterial(val) {
      if (this.listQuery.classTypeId && this.listQuery.subjectId && val) {
        this.getOutLine();
      }
    },
    getGrades() {
      grades().then(res => {
        if (res.code === "000000") {
          this.gradesList = res.data;
        }
      });
    },
    addCourse() {
      this.$refs.courseForms.validate(valid => {
        if (
          valid &&
          (this.listQuery.videoMinute || this.listQuery.videoSecond) &&
          this.resourcesImgId &&
          this.resourcesImg
        ) {
          const params = Object.assign({}, this.listQuery, {
            resourcesImgId: this.resourcesImgId || ""
          });
          addCourse(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "添加成功"
                });
                this.$emit("addCourseList");
                this.coursePop = false;
                this.listQuery = {
                  materialNewId: '',
                  title: ''
                };
                this.resourcesImg = "";
                this.resourcesImgId = "";
                this.$refs.courseForms.clearValidate();
              }
            })
            .catch(() => {});
        } else if (!this.listQuery.videoMinute && !this.listQuery.videoSecond) {
          this.$message({
            type: "warning",
            message: "请输入课程时长"
          });
        } else if (!(this.resourcesImgId && this.resourcesImg)) {
          this.$message({
            type: "warning",
            message: "请上传课程封面"
          });
        } else if (
          this.listQuery.courseSource === 2 &&
          !this.listQuery.visitUrl
        ) {
          this.$message({
            type: "warning",
            message: "请填写课程URL"
          });
        } else if (this.listQuery.courseSource === 1 && !this.listQuery.vid) {
          this.$message({
            type: "warning",
            message: "请输入阿里链接"
          });
        } else {
        }
      });
    },
    editCourse() {
      // 修改课程
      this.$refs.courseForms.validate(valid => {
        if (
          valid &&
          (this.listQuery.videoMinute || this.listQuery.videoSecond) &&
          this.resourcesImgId &&
          this.resourcesImg
        ) {
          if (this.listQuery.id) {
            const params = Object.assign({}, this.listQuery, {
              id: this.listQuery.id,
              resourcesImgId: this.resourcesImgId || ""
            });
            editCourse(params)
              .then(res => {
                if (res.code === "000000") {
                  this.$message({
                    type: "success",
                    message: "修改成功"
                  });
                  this.$emit("addCourseList");
                  this.coursePop = false;
                  this.listQuery = {};
                  this.resourcesImg = "";
                  this.resourcesImgId = "";
                  this.$refs.courseForms.clearValidate();
                }
              })
              .catch(() => {});
          }
        } else if (!this.listQuery.videoMinute && !this.listQuery.videoSecond) {
          this.$message({
            type: "warning",
            message: "请输入课程时长"
          });
        } else if (!(this.resourcesImgId && this.resourcesImg)) {
          this.$message({
            type: "warning",
            message: "请上传课程封面"
          });
        } else if (
          this.listQuery.courseSource === 2 &&
          !this.listQuery.visitUrl
        ) {
          this.$message({
            type: "warning",
            message: "请填写课程URL"
          });
        } else if (this.listQuery.courseSource === 1 && !this.listQuery.vid) {
          this.$message({
            type: "warning",
            message: "请输入阿里链接"
          });
        } else {
        }
      });
    },
    courseDetail(ids) {
      // 获取课程详情
      courseDetail(ids).then(res => {
        if (res.code === "000000") {
          this.listQuery = res.data;
          this.resourcesImg = res.data.resourcesImgUrl || "";
          this.resourcesImgId = res.data.resourcesImgId || "";
          this.getTeacher(); // 获取教师
          this.getOutLine(); // 获取大纲
          this.getKeynote(res.data.outlineId); // 获取考点
          this.getMaterialNew();
        }
      });
    },
    // 获取教材版本
    async getMaterialNew() {
      try {
        const params = {
          subjectId: this.listQuery.subjectId,
          clientCode: this.listQuery.clientCode
        };
        const { data, code } = await getCourseMaterialByParam(params);
        if (code === SUCCESS) {
          this.materialNewList = data;
        } else {
          this.materialNewList = [];
          this.listQuery.materialNewId = "";
        }
      } catch (error) {}
    },
    changeInit() {
      this.listQuery = { ...initListQuery }
      this.resourcesImg = "";
      this.resourcesImgId = "";
      if (this.$refs.courseForms) {
        this.$refs.courseForms.clearValidate();
      }
    },

    upload(e) {
      // 课程图片
      const that = this;
      const file = e.target.files[0];
      const size = (file.size / 1024 / 1024).toFixed(3);
      that.uuid = that.get_uuid();
      const tempName = file.name.split(".");
      const fileName = `santao_stip/crm/course/${that.uuid}.${
        tempName[tempName.length - 1]
      }`;
      if (size > 1) {
        this.$message({
          type: "warning",
          message: "上传的课程图片不能大于1M"
        });
      } else {
        obsClient.putObject(
          {
            Bucket: "obs-d812",
            Key: `${fileName}`, // 文件名
            SourceFile: file // 文件路径
          },
          function(err, result) {
            if (err) {
              console.error("Error-->" + err);
            } else {
              const paramsUpload = Object.assign(
                {},
                {
                  imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${
                    that.uuid
                  }.${tempName[tempName.length - 1]}`,
                  resourceType: "image"
                }
              );
              uploadSuccess(paramsUpload).then(res => {
                if (res.code === "000000") {
                  that.resourcesImgId = res.data.id;
                  that.resourcesImg = res.data.url;
                }
              });
            }
          }
        );
      }
    },
    delImgA() {
      this.resourcesImgId = "";
      this.resourcesImg = "";
    },
    get_uuid() {
      // 获取uuid
      var s = [];
      var hexDigits = "0123456789abcdef";
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      }
      s[14] = "4";
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
      s[8] = s[13] = s[18] = s[23] = "-";
      var uuid = s.join("");
      return uuid;
    },
    changeKeynote() {
      this.$forceUpdate();
    },
    resetValidate() {
      this.$refs.courseForms.clearValidate();
    }
  }
};
</script>
<style scoped lang="scss">
em {
  font-style: normal;
}

.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}

.course-duration {
  display: flex;
  justify-content: space-around;

  em {
    padding-left: 8px;
    padding-right: 8px;
  }
}

.upload-imgs {
  position: relative;
  width: 118px;
  height: 118px;
  font-size: 14px;
  display: inline-block;
  padding: 10px;
  margin-right: 25px;
  border: 2px dashed #ccc;
  text-align: center;
  vertical-align: middle;
}

.upload-imgs .add {
  display: block;
  background-color: #ccc;
  color: #ffffff;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .add .iconfont {
  padding: 10px 0;
  font-size: 40px;
}

.upload-imgs .upload {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 118px;
  height: 118px;
  opacity: 0;
  cursor: pointer;
}

.upload-imgs .img {
  position: relative;
  width: 94px;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .img img {
  vertical-align: middle;
  width: 94px;
  height: 94px;
}

.upload-imgs .img .close {
  display: none;
}

.upload-imgs:hover .img .close {
  display: block;
  position: absolute;
  top: -10px;
  left: -10px;
  width: 118px;
  height: 118px;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 118px;
  font-size: 24px;
  color: #fff;
}

.img-upload {
  padding-right: 8px;
}

/deep/ .el-divider--horizontal {
  margin: 10px 0;
}

/deep/ .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 10px;
}
</style>
