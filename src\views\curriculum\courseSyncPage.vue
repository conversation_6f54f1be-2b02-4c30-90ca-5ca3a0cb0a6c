<template>
  <div class="app-container">
    <div class="list-nav">
      <ul>
        <li v-for="(item,index) in subjectCourseNums" :key="item.subjectId" :class="[current===index?'actived':'']" @click="changeTab(index,item)">
          <span>{{ item.syncNums }} / {{ item.allNums }}</span>
          <em>{{ item.subjectName }}</em>
          <i v-show="current===index" class="actived-line" />
        </li>
      </ul>
    </div>
    <div class="filter-container">
      <el-input
        v-model="listQuery.courseMgrName"
        placeholder="课程名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.teacherId" placeholder="主讲老师" filterable clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in members" :key="item.memberId" :label="item.memberName" :value="item.memberId" />
      </el-select>

      <el-select v-model="listQuery.gradeId" placeholder="年级" filterable clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.isSync" placeholder="同步状态" filterable clearable class="filter-item" style="width: 200px;">
        <el-option v-for="item in syncStatus" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.lastOperatorId"
        size="small"
        filterable
        clearable
        remote
        reserve-keyword
        placeholder="最后操作人"
        :loading="recommendLoading"
        :remote-method="getOriginUserList"
        style="width: 200px;"
      >
        <el-option
          v-for="item in operaList"
          :key="item.id"
          :label="item.realName"
          :value="item.id"
        />
      </el-select>
      <el-date-picker
        v-model="datePicker"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="同步开始时间"
        end-placeholder="同步结束时间"
        style="width: 300px;"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">查询</el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">重置</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      class="table"
    >
      <af-table-column label="草稿id" show-overflow-tooltip prop="id" width="120px">
        <template slot-scope="scope">
          <a class="codes">{{ scope.row.courseMgrId }}</a>
        </template>
      </af-table-column>
      <el-table-column label="课程名称" prop="courseMgrName" show-overflow-tooltip />
      <el-table-column label="主讲老师" prop="teacherName" show-overflow-tooltip />
      <el-table-column label="年级" prop="gradeName" show-overflow-tooltip />
      <el-table-column label="课程时长" prop="videoLength">
        <template slot-scope="scope">
          <span v-if="scope.row.videoLength">
            <em>{{ parseInt(scope.row.videoLength/3600) }}小时</em>
            <em>{{ parseInt(scope.row.videoLength/60 % 60) }}分钟</em>
            <em>{{ parseInt(scope.row.videoLength% 60) }}秒</em>
          </span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" prop="isSync" :formatter="formatSyncStatus" />
      <el-table-column label="最后操作人" prop="lastOperator" show-overflow-tooltip />
      <el-table-column label="最后同步时间" prop="lastSyncTime" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230" fixed="right">
        <template slot-scope="scope">
          <div class="sync-opera" @click="toSyncPage(scope.row)">
            <em>同步</em>
            <i v-show="scope.row.isShow" class="circle" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import { getSyncCourse, listSubjectCourseSyncNums, listSubjectMembers } from '@/api/courseMgr'
import { grades } from '@/api/classType'
import { getOperaList } from '@/api/common'
import { converseEnToCn, courseMgrSyncStatus } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
export default {
  inject: ['reload'],
  name: 'CourseSyncPage',
  components: {
    Pagination
  },
  data() {
    return {
      classTypeRelationId: 0,
      courseMgrClassTypeId: 0,
      subjectId: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      subjectCourseNums: [],
      members: [],
      grades: [],
      datePicker: [],
      syncStatus: courseMgrSyncStatus,
      current: 0,
      recommendLoading: false,
      operaList: []
    }
  },
  watch: {

  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {

      this.classTypeRelationId = localStorage.getItem('classTypeRelationId')

      this.getGrades()
      if (this.classTypeRelationId) this.getSubjectCourseSyncNums(this.classTypeRelationId)
    })
  },
  methods: {
    getOriginUserList(query) {

      this.recommendLoading = true
      getOperaList(query).then(res => {
        this.recommendLoading = false
        this.operaList = res.data
      })
    },
    async getSubjectCourseSyncNums(classTypeRelationId) {
      const that = this
      await listSubjectCourseSyncNums(classTypeRelationId).then(res => {
        if (res.code === '000000') {
          this.subjectCourseNums = res.data
          if (res.data[0]) {
            that.courseMgrClassTypeId = res.data[0].courseMgrClassTypeId
            that.classTypeRelationId = res.data[0].classTypeRelationId
            that.subjectId = res.data[0].subjectId
            that.getList(res.data[0].subjectId, res.data[0].classTypeRelationId)
            that.getMembers(that.courseMgrClassTypeId, that.subjectId)
          }
        }
      })
    },
    async getList(subjectId, classTypeRelationId) {
      const that = this
      that.listLoading = true

      const params = Object.assign(that.listQuery, { beginTime: that.datePicker !== null && that.datePicker.length > 0 && that.datePicker[0] ? that.datePicker[0] : '', endTime: that.datePicker !== null && that.datePicker.length > 0 && that.datePicker[1] ? that.datePicker[1] : '', subjectId: subjectId, classTypeRelationId: classTypeRelationId })
      await getSyncCourse(params).then(response => {


        that.listLoading = false
        const arr = []

        response.data.records.forEach(item => {

          let objs = {}
          objs = Object.assign({}, item, {
            isShow: !!((item.lastUpdateTime && item.lastSyncTime) && ((new Date(Date.parse(item.lastUpdateTime.replace('-', '/'))) > (new Date(Date.parse(item.lastSyncTime.replace('-', '/')))))))
          })
          arr.push(objs)
        })
        that.list = arr.length > 0 ? arr : []

        that.total = that.list.length
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList(this.subjectId, this.classTypeRelationId)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.datePicker = []
      this.getList(this.subjectId, this.classTypeRelationId)
      this.getMembers(this.courseMgrClassTypeId, this.subjectId)
    },
    getGrades() {
      grades().then(res => {
        if (res.code === '000000') {
          this.grades = res.data
        }
      })
    },
    getMembers(courseMgrClassTypeId, subjectId) {
      listSubjectMembers(courseMgrClassTypeId, subjectId).then(res => {
        if (res.code === '000000') {
          this.members = res.data
        }
      })
    },
    formatSyncStatus(row) {

      return converseEnToCn(this.syncStatus, row.isSync, 'key', 'value')
    },
    changeTab(index, item) {
      this.current = index
      this.subjectId = item.subjectId
      this.classTypeRelationId = item.classTypeRelationId
      this.courseMgrClassTypeId = item.courseMgrClassTypeId
      this.getList(this.subjectId, this.classTypeRelationId)
      this.getMembers(this.courseMgrClassTypeId, this.subjectId)
    },
    toSyncPage(row) { // 跳到同步页面
      localStorage.setItem('courseMgrId', row.courseMgrId)
      localStorage.setItem('clientCode', row.clientCode)
      localStorage.setItem('courseId', row.courseId)
      this.$router.push({
        name: 'SyncPage',
        params: {
          courseId: row.courseId, // 播课课程id
          courseMgrId: row.courseMgrId, // 审课课程Id
          syncLogId: row.syncLogId, // 同步记录id
          clientCode: row.clientCode,
          lastSyncTime: row.lastSyncTime,
          lastUpdateTime: row.lastUpdateTime,
          courseClassTypeId: row.courseClassTypeId// 班型id
        }
      })
    }
  }
}

</script>
<style lang="scss" scoped>
.sync-opera{
    position: relative;
    color: #fff;
    background-color: #1890ff;
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    .circle{
      position: absolute;
      top: 0px;
      right: 1px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: red;
    }
}
.list-nav{
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px #f1f1f1 solid;
  margin-bottom: 10px;
  ul{
     display: flex;
    flex-wrap: wrap;
    li{
      position: relative;
      display: flex;
      flex-direction: column;
      width: 95px;
      text-align: center;
      margin-right: 10px;
      cursor: pointer;
      span{
        font-size: 12px;
        color: #666;
      }
      em{
        font-size: 14px;
        color: #333;
      }
      .actived-line{
        position: absolute;
        bottom: -2px;
        left: 35%;
        width: 30%;
        height: 3px;
        background: #539fff;
        content: '';
      }
      &.actived{
        span{
          font-weight: 550;
        }
        em{
          font-size: 16px;
          color: #539fff;
          font-weight: 550;
        }
      }
    }
  }
}
</style>
