<template>
  <div class="app-container">
    <div class="filter-container">
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item"
                   :get-area-name="true" @getAreaNames="setCityAreaNames" @getAreaList="getAreaList" />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <label class="ml-10 mt10" type="success" v-if="allAreaNames&&showAlert">
        共计 {{ allAreaNames.length || 0 }} 个区域
      </label>
    </div>
    <el-row :gutter="10" v-if="allAreaNames&&showAlert">
      <el-col :span="24">
        <el-alert type="success" class="alert" center :closable="false" effect="dark">以下区域没有任何代理项目</el-alert>
        <div>
          <el-tag type="success" class="ml-10 mt10" effect="plain" size="mini" v-for="item in getEmptyAreaNames">{{ item }}</el-tag>
        </div>
        <el-alert type="error" class="mt10 alert" center :closable="false" effect="dark">已有代理区域，请自行确认单点、独家！
        </el-alert>
        <el-row class="mt10">
          <el-col :span="12" :xs="24">
            <el-tag type="primary" effect="plain" size="mini" class="ml-10 mb10">
              普高共 {{ getPuGao.length || 0 }} 家，
              独家 {{ getPuGao | getDjJoinTypeNum }}，
              单点 {{ getPuGao | getDdJoinTypeNum }}
            </el-tag>
            <el-tag type="warning" effect="plain" size="mini" class="ml-10 ">
              烨晨共 {{ getYeChen.length || 0 }} 家，
              独家 {{ getYeChen | getDjJoinTypeNum }}，
              单点 {{ getYeChen | getDdJoinTypeNum }}
            </el-tag>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;margin-top: 10px;"
    >
<!--      <el-table-column label="" type="index" width="20" align="center" />-->
      <af-table-column label="省份"  prop="provinceName" width="70"/>
      <af-table-column label="城市"  prop="cityName" />
      <af-table-column label="区县"  prop="areaName"  sortable width="90"/>
      <el-table-column label="项目" width="80" sortable prop="projectName">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.projectName == '100'" type="primary" size="mini" effect="plain">三陶教育</el-tag>
          <el-tag v-if="scope.row.projectName == '1'" type="primary"  size="mini" effect="plain">三陶教育</el-tag>
          <el-tag v-if="scope.row.projectName == '200'" type="warning" size="mini" effect="plain">烨晨中学</el-tag>
          <el-tag v-if="scope.row.projectName == '3'" type="warning" size="mini" effect="plain">烨晨中学</el-tag>
        </template>
      </el-table-column>
      <af-table-column label="加盟类型" prop="areaJoinType" width="100">
        <template slot-scope="scope">
        <!--  0区县单点  1 区县独家      2 乡镇独家  3乡镇单点-->
          <el-tag type="primary" size="mini" effect="dark" v-if="scope.row.areaJoinType == 0|| scope.row.areaJoinType == 1">区县</el-tag>
          <el-tag type="danger" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 2 || scope.row.areaJoinType == 3">乡镇</el-tag>
          <el-tag type="warning" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 0 || scope.row.areaJoinType == 3">单点</el-tag>
          <el-tag type="danger" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 1 || scope.row.areaJoinType == 2">独家</el-tag>
          <el-tag type="danger" size="mini"  v-if="scope.row.areaJoinType == 2 || scope.row.areaJoinType == 3">{{ scope.row.countyName || '未知乡镇' }}</el-tag>
        </template>
      </af-table-column>
      <af-table-column label="到期时间" prop="schoolEndDate">
        <template slot-scope="scope">
          <!--          1. 已加盟  2. 签约中(已完款)  3.未完款  -->
          <span v-if="scope.row.expiration == 1">{{ scope.row.schoolEndDate }}</span>
          <span v-if="scope.row.expiration == 0" style="color: red">{{ scope.row.schoolEndDate }} ( 已到期 )</span>
          <span v-if="scope.row.expiration == 2" style="color: red">

            <span v-if="scope.row.type== 1"> 已加盟</span>
            <span v-if="scope.row.type== 2"> 签约中(已完款)</span>
            <span v-if="scope.row.type== 3"> 未完款</span>
            <span>  交接单创建时间:{{ scope.row.schoolEndDate }}</span>
          </span>
        </template>
      </af-table-column>
<!--   半径(米)   -->
      <af-table-column label="半径(米)" prop="radius">
        <template slot-scope="scope">
          <div v-if="scope.row.areaJoinType == 0 || scope.row.areaJoinType == 3">
            <span>{{ scope.row.locationRadius?scope.row.locationRadius:'' }}</span>
          </div>
        </template>
      </af-table-column>
      <af-table-column label="操作" prop="locationAddress">
        <template slot-scope="scope">
          <el-button  v-permission="['customer:empty:address']" class="filter-item" type="text" size="mini" @click="handleDetail(scope.row)">
            详细地址
          </el-button>
        </template>
      </af-table-column>
<!--      <el-table-column-->
<!--              header-align="center"-->
<!--              align="center"-->
<!--              prop="columnProp"-->
<!--              label="加盟情况"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <el-card class="box-card">-->
<!--            <div slot="header" class="clearfix">-->
<!--              <span>-->
<!--                  <el-tag v-if="scope.row.projectName == '100'" type="primary" size="mini" effect="plain">三陶教育</el-tag>-->
<!--          <el-tag v-if="scope.row.projectName == '1'" type="primary"  size="mini" effect="plain">三陶教育</el-tag>-->
<!--          <el-tag v-if="scope.row.projectName == '200'" type="warning" size="mini" effect="plain">烨晨中学</el-tag>-->
<!--          <el-tag v-if="scope.row.projectName == '3'" type="warning" size="mini" effect="plain">烨晨中学</el-tag>-->
<!--              </span>-->
<!--              <span style="float: right; padding: 3px 0" type="text">-->
<!--                   <el-tag type="primary" size="mini" effect="dark" v-if="scope.row.areaJoinType == 0|| scope.row.areaJoinType == 1">区县</el-tag>-->
<!--          <el-tag type="danger" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 2 || scope.row.areaJoinType == 3">乡镇</el-tag>-->
<!--          <el-tag type="warning" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 0 || scope.row.areaJoinType == 3">单点</el-tag>-->
<!--          <el-tag type="danger" size="mini" effect="dark"  v-if="scope.row.areaJoinType == 1 || scope.row.areaJoinType == 2">独家</el-tag>-->
<!--          <el-tag type="danger" size="mini"  v-if="scope.row.areaJoinType == 2 || scope.row.areaJoinType == 3">{{ scope.row.countyName || '未知乡镇' }}</el-tag>-->
<!--              </span>-->
<!--            </div>-->
<!--            <div  class="text item">-->
<!--             <div> {{ scope.row.provinceName }}  {{ scope.row.cityName }} {{ scope.row.areaName }}</div>-->

<!--              <div>-->
<!--                <span v-if="scope.row.expiration == 1">{{ scope.row.schoolEndDate }}</span>-->
<!--                <span v-if="scope.row.expiration == 0" style="color: red">{{ scope.row.schoolEndDate }} ( 已到期 )</span>-->
<!--                <span v-if="scope.row.expiration == 2" style="color: green">交接单创建时间:{{ scope.row.schoolEndDate }} ( 未完款 )</span>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-card>-->
<!--        </template>-->
<!--      </el-table-column>-->

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page-sizes="[50]"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { institutionsPayType } from '@/utils/field-conver'
import AreaPicker from '@/components/area-picker/index.vue'
import { emtpyList } from '@/api/customer'

export default {
  name: 'emptyList',
  components: {
    AreaPicker,
    Pagination
  },
  data() {
    return {
      list: [],
      pgList: [],
      ycList: [],
      total: 0,
      showAlert: false,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 50
      },
      clientCode: [],
      institutionsPayType: institutionsPayType,
      followDate: [],
      // 当前城市下的区县名称
      allAreaNames: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
    }
  },
  mounted() {
    this.listLoading = false
  },
  watch: {
    areaList: {
      handler: function (val, oldval) {
        if (val.provinceId && val.cityId && !val.areaId) {
          this.showAlert = true
        }
        else {
          this.showAlert = false
        }
        if (val.areaId) {
          this.showAlert = false
        }

        if (val.provinceId && val.cityId)
          this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    getEmptyAreaNames() {
      // let existAreaNames = [...new Set(this.list.map(e => e.areaName))]
      return this.allAreaNames.filter(e => !this.getExistAreaNames.has(e))
    },
    getExistAreaNames() {
      return new Set(this.list.map(e => {
        // if (e.areaJoinType === 1) {
        return e.areaName
        // }
      }))
    },
    getPuGao() {
      return this.list.filter(e => e.projectName === '1' || e.projectName === '100')
    },
    getYeChen() {
      return this.list.filter(e => e.projectName === '3' || e.projectName === '200')
    },
  },
  filters: {
    getDdJoinTypeNum(list) {
      if (!list)
        return 0
      return list.filter(e => e.areaJoinType === 0||e.areaJoinType === 3).length
    },
    getDjJoinTypeNum(list) {
      if (!list)
        return 0
      return list.filter(e => e.areaJoinType === 1||e.areaJoinType === 2).length
    }
  },
  methods: {
    handleDetail(row) {
      this.$alert(row.address, '地址详情',)
    },
    setCityAreaNames(list) {
      this.allAreaNames = list.map(e => e.name)
    },
    async getList() {
      const that = this
      if (that.areaList.provinceId && !that.areaList.cityId) {
        this.$message({
          type: 'warning',
          message: '请至少选择到市级别'
        })
        this.list = []
        return
      }
      that.listLoading = true
      const params = Object.assign(that.listQuery, that.areaList)
      await emtpyList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
      }
      this.areaList.provinceId = ''
      this.areaList.cityId = ''
      this.areaList.areaId = ''
      this.showAlert = false
      this.list = []
    },
    getStatus(row) {
    },
    getDetail(row) {

    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
  }
}
</script>
<style scoped>
.codes {
  font-weight: bold;
  color: #0a76a4;
}

.alert {
  height: 24px;
  line-height: 24px;
}
/deep/.el-table .cell{
    text-overflow: clip;
    padding:0 5px !important;
}

</style>
