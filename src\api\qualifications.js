import request from '@/utils/request'

/**
 * 获取个人资质
 * @param data
 */
export function getCustomerQualfication(data) {
  return request({
    url: 'qualification/getSignatory/' + data,
    method: 'get'
  })
}
/**
 * 获取校区资质
 * @param data
 */
export function getSchoolQualfication(data) {
  return request({
    url: 'qualification/getSchoolInfo/' + data,
    method: 'get'
  })
}
/**
 * 获取企业资质
 * @param data
 */
export function getEnterpriseQualfication(data) {
  return request({
    url: 'qualification/getEnterprise/' + data,
    method: 'get'
  })
}
/**
 * 获取资质的详情
 * @param data
 */
export function getQualficationDetail(orderId) {
  return request({
    url: `qualification/order/${orderId}`,
    method: 'GET'
  })
}

/**
 * 审核个人资质
 * @param data
 */
export function editCustomerQualfication(data) {
  return request({// qualification/auditSignatory
    url: 'qualification/auditSignatory',
    method: 'get',
    params: data
  })
}
/**
 * 审核校区资质
 * @param data
 */
export function editSchoolQualfication(data) {
  return request({
    url: 'qualification/auditSchool',
    method: 'get',
    params: data
  })
}
/**
 * 审核企业资质
 * @param data
 */
export function editEnterpriseQualfication(data) {
  return request({
    url: 'qualification/auditEnterprise',
    method: 'get',
    params: data
  })
}
/**
 * 编辑个人资质
 * @param data
 */
export function editCustomerInfo(data) {
  return request({
    url: 'qualification/modifySignatory',
    method: 'put',
    data: data
  })
}
/**
 * 编辑校区资质
 * @param data
 */
export function editSchoolInfo(data) {
  return request({
    url: 'qualification/modifySchool',
    method: 'put',
    data: data
  })
}
/**
 * 编辑企业资质
 * @param data
 */
export function editEnterpriseInfo(data) {
  return request({
    url: 'qualification/modifyEnterprise',
    method: 'put',
    data: data
  })
}

/**
 * 审核资质查询订单列表
 * @param data
 */
export function orderListQualification(data) {
  return request({
    url: 'order/list/qualification',
    method: 'get',
    params: data
  })
}

/**
 * 上传本地合同
 * @param data
 */
export function uploadLocalContract(data) {
  return request({
    url: `contracts/upOfflineContract?contractId=${data.id}&orderId=${data.orderId}`,
    method: 'POST',
    data: data.list
  })
}
