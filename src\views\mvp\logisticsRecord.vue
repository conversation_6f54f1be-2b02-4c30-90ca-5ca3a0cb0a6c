<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.orderNo"
        placeholder="订单号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.customer"
        placeholder="客户姓名/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.agency"
        placeholder="指派校区名称/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.postType" placeholder="邮寄状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in postType" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.joinType" placeholder="请选择加盟类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in joinTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="times"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="用户开始购买的开始时间"
        end-placeholder="用户开始购买的结束时间"
      />
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-permission="['mvp:logisticsRecord:sign']" v-waves class="filter-item" type="primary" size="mini" @click="getTransferDialog">
        批量标记
      </el-button>
      <el-button v-permission="['mvp:logisticsRecord:export']" v-waves class="filter-item" type="primary" size="mini" @click="handleExport">
        导出明细报表
      </el-button>
      <span class="assign-user">
        <em>待发货订单</em>
        <el-tag>{{ waitPostNum }}</el-tag>
      </span>
    </div>
    <el-table
      ref="logisticsTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="50" align="center" fixed="left" />
      <af-table-column label="订单号" show-overflow-tooltip prop="orderNo" />
      <af-table-column label="买家姓名/买家手机号" show-overflow-tooltip>
        <template slot-scope="scope">
          <p v-if="scope.row.userName">{{ scope.row.userName }}</p>
          <p v-if="scope.row.userPhone">{{ scope.row.userPhone }}</p>
        </template>
      </af-table-column>
      <af-table-column label="收货校区" prop="agencyName" show-overflow-tooltip />
      <el-table-column label="收货地址" width="200px" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            <span>{{ scope.row.deliveryName }}</span>
            <span>{{ scope.row.deliveryPhone }}</span>
          </p>
          <p>
            <span>{{ scope.row.postProvince }}</span>
            <span>{{ scope.row.postCity }}</span>
            <span>{{ scope.row.postArea }}</span>
            <span>{{ scope.row.postAddress }}</span>
          </p>
        </template>
      </el-table-column>
      <af-table-column label="货物信息" prop="goodsName" />
      <af-table-column label="加盟类型" prop="joinType" :formatter="getJoinType" />
      <af-table-column label="邮寄单号" prop="postNo" />
      <af-table-column label="邮寄状态" prop="postType" :formatter="postTypeStatue" />
      <af-table-column label="邮寄操作人" prop="postName" />
      <af-table-column label="邮寄操作时间" prop="postTime" />
      <af-table-column label="备注" prop="remark" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.postType!==1" v-permission="['mvp:logisticsRecord:SingleSign']" type="primary" size="mini" @click="handleUpdate(row)">标记</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 标记弹框 -->
    <sign-pop ref="signlogistics" :user-order-ids="userOrderIds" :is-batch="isBatch" @updeteLogisticsList="getList" />
    <!-- 标记弹框 -->
  </div>
</template>

<script>
import {
  getransactionList
} from '@/api/mvp'
import {
  parseTime
} from '@/utils'
import {
  customerSource,
  assignSatuts,
  postType,
  joinTypes,
  converseEnToCn
} from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import signPop from './components/signPop.vue'
export default {
  name: 'LogisticsRecord',
  components: {
    Pagination,
    AreaPicker,
    signPop
  },
  directives: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      assignSatuts: assignSatuts,
      postType: postType,
      editCustomer: true,
      times: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        customer: '',
        agency: '',
        postType: '',
        orderNo: '',
        joinType: ''
      },
      dialogTransfer: false, // 批量转让的弹窗
      multipleSelection: [], // 多选框被选中的row
      multipleSelectCustomerId: [], // 多选框选中的客户id
      beTransferCustomer: '',
      exportSetting: { // 导出按钮
        downloadLoading: false
      },
      testDialog: false,
      waitPostNum: '',
      customerSource: customerSource,
      clueIds: [], // 指派时需要传递的客户资源id
      payment: {}, // 结算需要传递的id
      // sign: {},
      userOrderIds: [],
      isBatch: false, // 判断是否为批量
      goods: [], // 批量标记的时候货物信息
      agencyIdList: [], // 收货校区集合
      unshipped: [], // 未发货的id集合
      joinTypes: joinTypes
    }
  },
  computed: {},
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, that.areaList, {
        startTime: this.times !== null ? this.times[0] : '',
        endTime: this.times !== null ? this.times[1] : ''
      })

      await getransactionList(params).then(response => {
        that.list = response.data.page.records || []
        that.total = response.data.page.total || 0
        that.listLoading = false
        that.waitPostNum = response.data.waitPostNum || 0
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
       * 获取省市区的地址
       * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
       * 格式化时间
       */
    formatterTime(row) {
      return parseTime(row.createTime)
    },
    // 重置
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.times = []
      this.getList()
    },
    sortChange(data) {},
    handleUpdate(row) {
      this.$refs.signlogistics.signShow = true
      // this.sign = row
      const ids = []
      ids.push(row.id)
      this.userOrderIds = ids
      this.isBatch = false
      this.$refs.signlogistics.getInit(row)
      this.$refs.signlogistics.getBatchGoods(this.userOrderIds)
    },
    handleFollow(row) {
      this.$refs.followRef.getLists(row.id)
    },
    /**
       * 批量标记的弹窗
       * */
    getTransferDialog(row) { // 校区id，相同的可以批量标记
      if ((new Set(this.agencyIdList).size !== this.agencyIdList.length && this.multipleSelectCustomerId && this.multipleSelectCustomerId.length > 0) || (new Set(this.agencyIdList).size === 1 && this.agencyIdList.length === 1)) {
        this.$refs.signlogistics.signShow = true
        this.isBatch = true
        this.$refs.signlogistics.getBatchGoods(this.multipleSelectCustomerId)
      } else if (new Set(this.agencyIdList).size === this.agencyIdList.length && this.agencyIdList.length > 1) { // size===length,说明没有重复
        this.$message({
          message: '非同一收货校区，无法批量操作',
          type: 'warning'
        })
      } else {
        this.$message({
          message: '请先选择被标记的数据',
          type: 'warning'
        })
      }
    },
    /**
       * 获取搜索筛选之后的用户id
       **/
    getEmployeeId(id) {
      this.beTransferCustomer = id
    },
    /**
       * 多选框的返回值
       */
    handleSelectionChange(val) {
      let assignFlag = false
      this.multipleSelection = val
      this.multipleSelectCustomerId = this.multipleSelection.map(item => {
        return item.id
      })
      this.goods = this.multipleSelection.map(item => { // 批量标记的货物信息
        return item.goodsName
      })
      this.agencyIdList = this.multipleSelection.map(item => { // 批量标记货物校区的集合
        return item.agencyId || []
      })

      this.userOrderIds = this.multipleSelection.map(item => {
        if (item.postType === 1) {
          assignFlag = true
          this.$refs.logisticsTab.clearSelection()
        } else {
          return item.id
        }
      })
      if (assignFlag) {
        this.$message({
          type: 'warning',
          message: '请选择未发货的订单'
        })
      }
      this.$refs.signlogistics.getInit(this.multipleSelection[0])
      // this.$refs.signlogistics.getGoods(this.goods)
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'userOrders/export/deliveries?pageIndex=' + that.listQuery.pageIndex + '&pageSize=' + that.listQuery.pageSize
      if (that.times.length !== 2) {
        that.$message({
          type: 'warning',
          message: '请先选择导出开始时间和结束时间'
        })
      } else {
        url = url + '&provinceId=' + that.areaList.provinceId + '&cityId=' + that.areaList.cityId + '&areaId=' + that.areaList.areaId + '&agency=' + that.listQuery.agency + '&customer=' + that.listQuery.customer + '&orderNo=' + that.listQuery.orderNo + '&startTime=' + that.times[0] + '&endTime=' + that.times[1] + '&postType=' + that.listQuery.postType + '&joinType=' + that.listQuery.joinType
        that.$confirm('确定导出数据?', {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.listLoading = true
          const params = Object.assign(that.listQuery, that.areaList, {
            startTime: that.times[0] || '',
            endTime: that.times[1] || ''
          })
          getransactionList(params).then(response => {
            that.list = response.data.page.records || []
            that.total = response.data.page.total || 0
            that.listLoading = false
            if (url && that.list.length > 0) {
              a.href = url
              a.target = '_blank'
              document.body.appendChild(a)
              a.click()
            } else {
              setTimeout(() => {
                that.$message({
                  type: 'warning',
                  message: '没有可以导出的数据'
                })
              }, 500)
            }
          }).catch(() => {
            that.$message({
              type: 'warning',
              message: '没有可以导出的数据'
            })
          })
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      }
    },
    // 转化信息来源
    customerSources(row) {
      return converseEnToCn(this.customerSource, row.customerSource)
    },
    assignSatutsList(row) {
      return converseEnToCn(this.assignSatuts, row.assignSatuts)
    },
    // 邮寄状态
    postTypeStatue(row) {
      return converseEnToCn(this.postType, row.postType)
    },
    getJoinType(row) {
      return converseEnToCn(this.joinTypes, row.joinType)
    }
  }
}
</script>
<style scoped="scoped" lang="scss">
  em {
    font-style: normal;
  }
  .filter-container .filter-item{
    margin-top: 6px !important;
  }
  .assign-btns {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
</style>
