<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.projectName" placeholder="项目名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.valid" placeholder="是否可用" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in menuValidList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <af-table-column label="项目名称" align="center" prop="projectName" />
      <af-table-column label="是否需要产品" align="center" prop="productFlag" :formatter="getProductFlag" />
      <af-table-column label="是否需要开通机构" align="center" prop="agencyFlag">
        <template slot-scope="scope">
          <span v-if="scope.row.agencyFlag===0">不需要</span>
          <span v-if="scope.row.agencyFlag===1">需要</span>
        </template>
      </af-table-column>
      <af-table-column label="必须企业资质" align="center" prop="requiredEnterprise" :formatter="getRequiredEnterprise" />
      <af-table-column label="是否可用" prop="valid" align="center" :formatter="getMenuValidList" />
      <af-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button :type="buttonColor(row.valid)" size="mini" @click="handleStatus(row)">
            {{ getButtonText(row.valid) }}
          </el-button>
        </template>
      </af-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <project-dialog ref="projectDialog" :is-edit="isEdit" @refresh="getList" />
  </div>
</template>

<script>
import { editProjectStatus, getProjectList } from '@/api/system-setting'
import { menuValidList, productFlag, requiredEnterprise, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import projectDialog from './dialog'
export default {
  name: 'Project',
  components: { Pagination, projectDialog },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      menuValidList: menuValidList,
      buttonText: ''
    }
  },
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增项目
     * */
    handleAdd() {
      this.$refs.projectDialog.getDetail()
      this.isEdit = false
    },

    getList() {
      const that = this
      getProjectList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    /**
     * 修改项目
     * */
    handleUpdate(row) {
      this.isEdit = true
      const form = JSON.parse(JSON.stringify(row))
      this.$refs.projectDialog.editDetail(form)
    },
    /**
     * 更改状态
     * */
    handleStatus(row) {
      if (row.valid === 1) {
        this.$confirm('', '确定停用 ' + row.projectName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editProjectStatus(row.id).then(res => {
            if (res.msg === 'success') {
              this.$message({
                type: 'success',
                message: '停用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      } else {
        this.$confirm('', '确定启用 ' + row.projectName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editProjectStatus(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '启用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      }
    },
    getMenuValidList(row) {
      return converseEnToCn(menuValidList, row.valid)
    },
    getProductFlag(row) {
      return converseEnToCn(productFlag, row.productFlag)
    },
    getRequiredEnterprise(row) {
      return converseEnToCn(requiredEnterprise, row.requiredEnterprise)
    },
    getButtonText(valid) {
      return valid === 1 ? '停用' : '启用'
    },
    buttonColor(valid) {
      return valid === 1 ? 'danger' : 'primary'
    }
  }
}
</script>

<style scoped>

</style>
