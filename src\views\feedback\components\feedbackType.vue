<template>
  <el-dialog :visible.sync="feedbackPop" :title="feedbackTitle" :close-on-click-modal="!feedbackPop" width="60%" @close="changeInit">
    <el-form ref="feedbackTypes" :model="listQuery" :rules="rules" label-width="100px">
      <el-form-item label="问题类型" prop="category">
        <el-select v-model="listQuery.category" placeholder="请选择问题类型" filterable clearable class="filter-item">
          <el-option v-for="item in questionCategory" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="listQuery.remark" type="textarea" placeholder="请写明修改问题类型的原因" show-word-limit maxlength="100" />
      </el-form-item>
      <div class="assign-operas">
        <el-button type="infor" size="mini" @click="feedbackPop=false,changeInit()">取消</el-button>
        <el-button type="primary" size="mini" :disabled="enableFlagsCreate" @click="confirmFeedback">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import { questionCategories, editQuestionCategories } from '@/api/feedback'
export default {
  name: 'FeedbackType',
  data() {
    return {
      feedbackPop: false,
      feedbackTitle: '反馈类型',
      listQuery: {},
      enableList: [],
      enableFlagsCreate: false,
      questionCategory: [],
      rules: {
        category: { required: true, trigger: 'blur', message: '请选择问题类型' },
        remark: { required: true, trigger: 'blur', message: '请写明修改问题类型的原因' }
      },
      feedbackId: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCategories()
    })
  },
  methods: {
    getCategories() {
      questionCategories().then(res => {
        if (res.code === '000000') {
          this.questionCategory = res.data || []
        }
      }).catch(() => {

      })
    },
    changeInit() {
      this.feedbackPop = false
      this.listQuery = {}
      if (this.$refs.feedbackTypes) {
        this.$refs.feedbackTypes.clearValidate()
      }
    },
    confirmFeedback() {
      this.$refs.feedbackTypes.validate((valid) => {
        if (valid && this.feedbackId) {
          const data = Object.assign({}, this.listQuery, { feedbackId: this.feedbackId })
          editQuestionCategories(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '转交成功'
              })
              this.feedbackPop = false
              this.listQuery = {}
              this.$emit('refreshList')
            }
          })
        } else {

          return false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
