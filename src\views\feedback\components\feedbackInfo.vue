<template>
  <div class="feedack-info">
    <h2>
      <!--反馈类型,0:反馈,1:建议,2:投诉-问题类型category1:运营,2:市场,3,课程,4:系统,5:智能终端,6:三陶AI,7:其他-->
      <em>反馈详情</em>
      <span class="feedack-info-type">
        <i v-if="replyInfo.feedbackType===0">反馈-</i>
        <i v-if="replyInfo.feedbackType===1">建议-</i>
        <i v-if="replyInfo.feedbackType===2">投诉-</i>
        <i>{{ replyInfo.categoryName }}</i>
      </span>
    </h2>
    <div v-if="replyInfo.contents&&replyInfo.contents.length>0" class="reply-info">
      <ul>
        <li v-for="item in replyInfo.contents" :key="item.id">
          <div class="feedback-sub reply-top">
            <h2>
              <span>
                <i :class="[item.replyType===3?'el-icon-user':'el-icon-user-solid']" />
                <i v-if="item.replyType===1||item.replyType===2">{{ item.partnerName }}</i>
                <i v-if="item.replyType&&item.replyType===3">{{ item.sysUserName }}</i>
                <i v-if="item.replyType===3&&item.sysUserName===null">客服</i>
              </span>
              <span>{{ item.replyTime }}</span>
            </h2>
            <p>{{ item.content }}</p>
            <div v-if="item.attachments&&item.attachments.length>0">
              <el-image v-for="(replyImg,i) in item.attachments" :key="i" :src="replyImg" class="small-img" :preview-src-list="item.attachments" />
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="reply-opera">
      <el-form v-if="replyInfo.status===1||replyInfo.status===0">
        <el-form-item label="内容">
          <el-input v-model="replay.content" type="textarea" maxlength="500" show-word-limit :autosize="{minRows: 4}" />
        </el-form-item>
        <el-form-item label="上传图片">
          <div class="control-form">
            <p class="help-block">(建议图片格式为：JPEG/BMP/PNG/GIF，大小不超过3M，最多可上传5张)</p>
            <ul class="upload-imgs">
              <li v-for="(item,index) in replay.uploadImg" :key="index">
                <div v-if="!item.resourceUrl">
                  <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="addImgA($event,index)">
                  <a class="add"><i class="iconfont icon-plus" /><p>点击上传</p></a>
                </div>
                <p v-if="item.resourceUrl" class="img">
                  <img :src="item.resourceUrl">
                  <a class="close" @click="delImgA(index)">
                    <i class="el-icon-delete" />
                  </a>
                </p>
              </li>
            </ul>
          </div>
        </el-form-item>
        <el-form-item class="replay-btns">
          <el-button type="infor" size="mini" @click="cancelSubmit">取消</el-button>
          <el-button v-if="replyInfo.status===1||replyInfo.status===0" type="primary" size="mini" @click="subReply">回复</el-button>
          <el-button v-if="replyInfo.status===1||replyInfo.status===0" type="danger" size="mini" @click="refused">驳回</el-button>
        </el-form-item>
      </el-form>
      <p v-if="assessStar===null&&(replyInfo.status===2||replyInfo.status===3)" class="rate">未评分</p>
      <div class="rates-info">
        <div v-if="assessStar!==null&&(replyInfo.status===2||replyInfo.status===3)" class="rate-info">
          <em class="rate-tips">评分:</em>
          <el-rate
            v-model="assessStar"
            disabled
            text-color="#ff9900"
            class="rate-items"
          />
        </div>
        <p v-if="assessStar===1&&(replyInfo.status===2||replyInfo.status===3)" class="reason">不满意原因:<i>{{ assessReason }}</i></p>
      </div>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})

import { uploadSuccess } from '@/api/common'
import { feedbackIn, replayInfo, replayRefused } from '@/api/feedback'
export default {
  name: 'FeedbackInfo',
  inject: ['reload'],
  data() {
    return {
      types: '',
      replyInfo: {},
      formData: new FormData(),
      feedackId: null,
      replay: {
        content: '',
        uploadImg: [
          {
            resourceUrl: '',
            resourceId: ''
          }
        ]
      },
      imgLength: 1,
      assessStar: null,
      assessReason: null,
      uuid: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.types = this.$route.params.type
      this.feedackId = this.$route.params.id ? this.$route.params.id : localStorage.getItem('feedbackIn')
      this.getFeedbackIn(this.feedackId)
    })
  },
  methods: {
    getFeedbackIn(ids) {
      feedbackIn(ids).then(res => {
        if (res.code === '000000') {
          this.feedackId = res.data.id
          this.assessStar = res.data.assessStar
          this.assessReason = res.data.assessReason !== null ? res.data.assessReason : ''
          const contentList = res.data.contents
          const arr = []
          for (let i = 0; i < contentList.length; i++) {
            const obj = {}
            obj['content'] = contentList[i].content
            obj['replyType'] = contentList[i].replyType
            obj['replyTime'] = contentList[i].replyTime
            obj['sysUserName'] = contentList[i].sysUserName
            obj['partnerName'] = contentList[i].partnerName
            obj['id'] = contentList[i].id
            obj['attachments'] = contentList[i].attachments && contentList[i].attachments.length > 0 ? contentList[i].attachments.map(item => item.resourceUrl) : []
            arr.push(obj)
          }
          this.replyInfo = {
            id: res.data.id,
            status: res.data.status,
            accountType: res.data.accountType,
            assessReason: res.data.assessReason,
            category: res.data.category,
            feedbackType: res.data.feedbackType,
            categoryName: res.data.categoryName,
            contents: arr
          }
        }
      }).catch(() => {

      })
    },
    addImgA(e, index) {

      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      if (size > 3) {
        this.$message({
          type: 'warning',
          message: '请上传3M以内的图片'
        })
      } else {
        this.uploadA(file, index)
        this.imgLength++
        if (this.imgLength < 6) {
          setTimeout(() => {
            this.replay.uploadImg.push({
              resourceUrl: '',
              resourceId: ''
            })
          }, 1000)
        }
      }
    },
    uploadA(file, i) {
      const that = this
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/feedback/${that.uuid}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {

          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/feedback/${that.uuid}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              that.replay.uploadImg[i].resourceUrl = res.data.url
              that.replay.uploadImg[i].resourceId = res.data.id
            }
          }).catch(() => {

          })
        }
      })
    },
    delImgA(index) {
      if (this.replay.uploadImg.length === 1) {
        this.replay.uploadImg[0].resourceUrl = ''
        this.imgLength = 1
      } else {
        this.replay.uploadImg.splice(index, 1)
        this.imgLength--
      }
    },
    subReply() {
      if (this.replyInfo.id && this.replay.content) {
        const imgList = this.replay.uploadImg && this.replay.uploadImg.length > 0 ? this.replay.uploadImg.map(item => item.resourceId) : []
        const data = Object.assign({}, {
          feedbackId: this.replyInfo.id,
          content: this.replay.content,
          attachments: imgList.filter(Boolean)
        })
        replayInfo(data).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '回复成功'
            })
            this.getFeedbackIn(this.replyInfo.id)
            this.replay = {
              content: '',
              uploadImg: [
                {
                  resourceUrl: '',
                  resourceId: ''
                }
              ]
            }
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请输入回复内容'
        })
      }
    },
    refused() {
      this.$confirm('确定要驳回吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.replyInfo.id && this.replay.content) {
          const imgList = this.replay.uploadImg && this.replay.uploadImg.length > 0 ? this.replay.uploadImg.map(item => item.resourceId) : []
          const data = Object.assign({}, {
            feedbackId: this.replyInfo.id,
            content: this.replay.content,
            attachments: imgList.filter(Boolean)
          })
          replayRefused(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '反馈已驳回'
              })
              this.getFeedbackIn(this.replyInfo.id)
              this.replay = {
                content: '',
                uploadImg: [
                  {
                    resourceUrl: '',
                    resourceId: ''
                  }
                ]
              }
            }
          }).catch(() => {

          })
        } else {
          this.$message({
            type: 'warning',
            message: '请输入驳回原因'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消驳回操作'
        })
      })
    },
    cancelSubmit() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
      })
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog{
    height: 650px;
    overflow-y: auto;
  }
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
    min-height: 580px;
    overflow-y: auto;
  }
  /deep/ .el-rate__icon{
    font-size: 35px;
  }
  .upload-imgs{
    margin: 10px 0 30px 0;
    overflow: hidden;
    font-size: 0;
  }
  .upload-imgs li{
    position: relative;
    width: 118px;
    height: 118px;
    font-size: 14px;
    display: inline-block;
    padding: 10px;
    margin-right: 25px;
    border: 2px dashed #ccc;
    text-align: center;
    vertical-align: middle;
  }
  .upload-imgs .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 94px;
    line-height: 94px
  }
  .upload-imgs .add .iconfont{
    padding: 10px 0;
    font-size: 40px;
  }
  .upload-imgs li .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 118px;
    height: 118px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    position: relative;
    width: 94px;
    height: 94px;
    line-height: 94px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs li:hover .img .close{
    display: block;
    position: absolute;
    top:-10px;
    left: -10px;
    width:118px;
    height:118px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 118px;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
</style>
