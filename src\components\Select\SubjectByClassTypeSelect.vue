<template>
  <el-select v-model="tmpId" :style="{ width: inputWidth + 'px' }" filterable clearable placeholder="科目" no-data-text="请先选择班型">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="item.title"
      :value="`${item.id}`">
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getSubjectsByClassType } from '@/api/classType'

/**
 * 科目选择框
 */
export default {
  name: 'SubjectByClassTypeSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    inputWidth: {
      type: Number,
      default: 140
    },
    classTypeId: {
      type: [String, Number],
      required: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  watch: {
    classTypeId(val) {
      this.handleChange(null)
      this.getList()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.title : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      if (!this.classTypeId)
        return
      this.loading = true
      getSubjectsByClassType(this.classTypeId).then(res => {
        this.loading = false
        if (res.code === SUCCESS) {
          this.dataList = res.data
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
</style>
