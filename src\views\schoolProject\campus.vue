<template>
  <div class="app-container bgGrey campus-info">
    <el-form ref="form" size="small" label-width="120px">
      <el-card class="box-card campus-list" shadow="hover">
        <div slot="header" class="clearfix">
          <span>客户信息</span>
        </div>
        <div class="item">
          <el-row>
            <el-col :sm="24" :md="8">
              <el-form-item label="客户编号：">
                <div v-if="clueBase.clueCode">{{ clueBase.clueCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="客户名称：">
                <div>{{ clueBase.customer }}</div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="手机号码：">
                <div>{{ clueBase.mobile }}</div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="机构区域：">
                <div>
                  <span v-if="clueBase.provinceName">{{ clueBase.provinceName }}-</span>
                  <span v-if="clueBase.cityName">{{ clueBase.cityName }}-</span>
                  <span v-if="clueBase.areaName">{{ clueBase.areaName }}</span>
                  <span v-if="clueBase.countyName">-{{ clueBase.countyName }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="机构地址：">
                <div>{{ clueBase.address }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card class="box-card campus-list" shadow="hover">
        <div slot="header" class="clearfix">
          <div class="flex-between">
            <span>校区信息 <el-tag type="success">ID: {{ schoolManage.agencyId }}</el-tag></span>
            <el-button v-if="schoolManage.status" v-permission="['customer:schoolProject:institutionsOpera']"
              size="mini" type="danger" @click="institutionsOpera(schoolManage.status)">
              {{ schoolManage.status === 1 ? '关闭机构' : '开启机构' }}
            </el-button>
          </div>
        </div>
        <div class="item">
          <el-row>
            <el-col :sm="24" :md="5">
              <el-form-item label="校区项目编号：">
                <div>{{ schoolManage.institutionCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="校区名称：">
                <span>{{ schoolManage.title }}</span>
                <el-button type="text" @click="openName"
                  v-permission="['customer:schoolProject:resetAccount']">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="4">
              <el-form-item label="校区联系人：">
                <span>{{ schoolManage.realName }}</span>
                <el-button type="text" @click="openRealName"
                  v-permission="['customer:schoolProject:resetAccount']">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="6">
              <el-form-item label="校区账号：">
                <span>{{ schoolManage.schoolAccount }}</span>
                <el-button type="text" v-permission="['customer:schoolProject:resetAccount']"
                  @click="openAccountChangeDialog">修改账号</el-button>
                <changeSchoolAccount @ok="loadData" ref="accountDialog" :school-id="schoolManage.agencyId">
                </changeSchoolAccount>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :sm="24" :md="5">
              <el-form-item label="签约区域：">
                <div>
                  <span v-if="schoolManage.provinceName">{{ schoolManage.provinceName }}-</span>
                  <span v-if="schoolManage.cityName">{{ schoolManage.cityName }}-</span>
                  <span v-if="schoolManage.areaName">{{ schoolManage.areaName }}</span>
                  <span v-if="schoolManage.countyName">-{{ schoolManage.countyName }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="11">
              <el-form-item label="详细地址：">
                <span>{{ schoolManage.address }}</span>
                <router-link v-permission="['customer:list:detail:school:update']"
                  :to="{ path: '/customer/detail/' + Number(clueBase.clueCode && clueBase.clueCode.substr(1,)), query: { title: '客户-' + clueBase.customer } }"
                  class="link-type">
                  <el-button type="text" style="margin-left: 15px;">修改地址</el-button>
                </router-link>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="校区经度/纬度：">
                <div>
                  <span v-if="schoolManage.longitude">{{ schoolManage.longitude }}/</span>
                  <span v-if="schoolManage.latitude">{{ schoolManage.latitude }}</span>
                  <el-button v-permission="['customer:schoolProject:areaSet']" type="text" style="margin-left: 25px;"
                    @click="areaPop = true, getAreaSetDetail()">区域类型设置
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :sm="24" :md="5">
              <el-form-item label="学生数：">
                <el-tag v-if="schoolManage.studentNum > 0" @click="studentsList"><a>{{ schoolManage.studentNum
                    }}</a></el-tag>
                <el-tag v-else>0</el-tag>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="11">
              <el-form-item label="智能终端数量：">
                <el-tag v-if="schoolManage.terminalNum > 0" @click="IntelligentTerminal"><a>{{ schoolManage.terminalNum
                    }}</a>
                </el-tag>
                <el-tag v-else @click="IntelligentTerminal"><a>0</a></el-tag>
                <el-button v-permission="['customer:schoolProject:locationSchool']" size="mini" type="primary"
                  style="margin: 0 25px" @click="boxLocation">智能终端定位
                </el-button>
                <el-switch v-model="openRegion" v-permission="['customer:schoolProject:openRegion']" active-value="1"
                  inactive-value="0" :width="widthLabel" @change="openInterregional(schoolManage.openRegion)" />
                <span class="active-text">{{ activeText }}</span>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="终端开放项目：" prop="macUpdate">
                <el-select :disabled="disabledProjectSelect" v-model="schoolManage.macUpdate" filterable style="width: 80%;">
                  <el-option :key="0" label="三陶普高、烨晨中学" :value="0"> </el-option>
                  <el-option :key="1" label="三陶普高" :value="1"> </el-option>
                  <el-option :key="2" label="烨晨中学" :value="2"> </el-option>
                </el-select>
                <el-button type="primary" size="mini" v-permission="['customer:schoolProject:updateProject']"
                  v-if="showSaveMacUpdateBtn" plain @click="updateOpenProject">保存</el-button>
              </el-form-item>

            </el-col>
            <el-col :sm="24" :md="8">
              <el-form-item label="校区到期时间：">
                <em style="padding-right: 20px">{{ schoolManage.expirationDate }}</em>
                <el-button v-permission="['customer:schoolProject:equipment']" type="text"
                  @click="classPop = true">播课设备设置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!-- 分校信息 -->
      <el-card class="box-card campus-list" shadow="hover">
        <div slot="header" class="clearfix">
          <div class="flex-between">
            <span>分校信息</span>
          </div>
        </div>
        <div class="item">
          <el-table
            v-loading="branchListLoading"
            :data="branchSchoolList"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column type="index" label="序号" align="center" width="50" />
            <af-table-column label="分校名称" prop="schoolName" min-width="230" />
            <af-table-column label="分校地址" prop="address" show-overflow-tooltip />
            <af-table-column label="经纬度" width="180" >
              <template slot-scope="scope" v-if="scope.row.netLatitude && scope.row.netLongitude">
                <span>{{ scope.row.netLongitude }},{{ scope.row.netLatitude}}</span>
              </template>
            </af-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="editBranchSchool(scope.row)">设置</el-button>
                <el-button size="mini" type="danger" @click="clearBranchLocation(scope.row)">清除定位</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <!-- 独家-单点 -->
      <el-card class="box-card campus-list" shadow="hover">
        <div slot="header" class="clearfix">
          <span>独家-单点</span>
          <el-button size="mini" type="primary" @click="addExclusivePoint">新增</el-button>
        </div>
        <div class="item">
          <el-table
            v-loading="exclusiveListLoading"
            :data="exclusivePointList"
            border
            fit
            stripe
            highlight-current-row
            style="width: 100%;"
          >
            <!-- 序号 -->
            <el-table-column type="index" label="序号" align="center" width="50" />
            <af-table-column label="校区" prop="schoolName"/>
            <af-table-column label="单点校区ID" prop="singlePointSchoolId"/>
            <af-table-column label="经纬度" width="180" >
              <template slot-scope="scope" v-if="scope.row.netLatitude && scope.row.netLongitude">
                <span>{{ scope.row.netLongitude }},{{ scope.row.netLatitude}}</span>
              </template>
            </af-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="deleteExclusivePoint(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card class="box-card campus-list" shadow="hover">
        <div slot="header" class="clearfix">
          <span>班型课时信息</span>
        </div>
        <div class="item">
          <div class="campus-btns">
            <div>
              <el-row :gutter="10">
                <el-col :span="8" :xs="24">
                  <el-button v-permission="['customer:schoolProject:setPackage']" size="mini" type="primary" plain
                    @click="setDiagnoseDialog">
                    学业诊断设置
                  </el-button>
                  <Diagnose :school-id="schoolManage.agencyId" v-if="showDiagnoseDialog" @close="hideDiagnoseDialog">
                  </Diagnose>
                  <el-button v-permission="['customer:schoolProject:setPackage']" size="mini" type="primary" plain
                    @click="setSYGHPackage">
                    生涯规划设置
                  </el-button>
                  <div class="campus-btns-ai">
                    AI题库：<span v-if="!aiQuestionBank.openQuestionBank">未开通</span>
<!--                    <el-switch v-if="!aiQuestionBank.openQuestionBank" :value="false"
                      @change="openAiQuestionBank"></el-switch>-->
                    <span v-else class="ai-question-bank">
                      {{ aiQuestionBank.questionBankExpiryDate }}
                    </span>
                    <el-button type="text" v-permission="['customer:schoolProject:setPackage']" size="mini" @click="openAiQuestionBank">设置</el-button>
                  </div>
                  <AiQuestionBankDialog ref="aiQuestionBankDialog" @refresh="Object.assign(aiQuestionBank, $event)" />
                  <SYGXDialog :school-id="schoolManage.agencyId" v-if="showSYGHDialog" @close="hideSYGHDialog">
                  </SYGXDialog>
                </el-col>
                <el-col :span="16" :xs="24">
                  <div class="campus-btns-list">
                    <el-button v-permission="['customer:schoolProject:setPackage']" size="mini" type="primary"
                      @click="setPackage">
                      设置流量包
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:conversionPackage']" size="mini" type="primary"
                      @click="conversionPackage">流量包转换
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:sourceSet']" size="mini" type="primary"
                      @click="sets">播放源设置
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:transferStudents']" size="mini" type="primary"
                      @click="transferStudents">转移学生到基地校
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:resetPassword']" size="mini" type="danger"
                      @click="resetPassword">
                      重置密码
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:openLesson']" size="mini" type="warning"
                      @click="trialLesson">
                      {{ schoolManage.auditRole === 0 ? '开通审课权限' : '关闭审课权限' }}
                    </el-button>
                    <el-button v-permission="['customer:schoolProject:delayPackage']" size="mini" type="warning"
                      @click="delay">延期
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <el-table ref="assignTab" v-loading="listLoading" :data="list" border fit stripe highlight-current-row
            style="width: 100%;">
            <af-table-column label="流量包" show-overflow-tooltip prop="productName" width="150px">
              <template slot-scope="scope">
                <div>
                  <span>{{ scope.row.productName }}</span>
                  <div>
                    <el-tag size="medium" effect="dark" v-if="Number(scope.row.openSubject || 0) === 1" type="success">
                      已启用科目数计费
                    </el-tag>
                  </div>
                </div>
              </template>
            </af-table-column>
            <el-table-column label="适用班型/科目" width="350px">
              <template slot-scope="scope">
                <div
                  v-if="scope.row.rangeType === 1 && scope.row.applyClassTypes && scope.row.applyClassTypes.length > 0">
                  <span v-for="(item, index) in scope.row.applyClassTypes" :key="index" class="subjects">
                    <span>
                      {{
                        item.title
                      }}
                    </span>
                    <el-button plain size="mini" v-permission="['customer:schoolProject:updatePackage']"
                      v-if="Number(scope.row.openSubject || 0) === 1" @click="handleOpen(scope.row, item)">
                      <span class="subjectNum">{{ item.subjectNum || 0 }}</span>
                      充值</el-button>
                  </span>
                </div>
                <div
                  v-if="scope.row.rangeType === 2 && scope.row.applySubjectList && scope.row.applySubjectList.length > 0">
                  <span v-for="(item, index) in scope.row.applySubjectList" :key="index" class="subjects">{{ item.title
                    }}</span>
                </div>
              </template>
            </el-table-column>
            <af-table-column label="剩余总数/小时" show-overflow-tooltip width="150px">
              <template slot-scope="scope">
                <span><em>{{ scope.row.remainHour }}</em><em>{{ scope.row.remainMinPackage }}</em></span>
              </template>
            </af-table-column>
            <af-table-column label="充值总数" show-overflow-tooltip width="150px">
              <template slot-scope="scope">
                <span><em>{{ scope.row.sumnumberHour }}</em><em>{{ scope.row.remainSumnumberPackage }}</em></span>
              </template>
            </af-table-column>
            <af-table-column label="扣减总数" width="150px">
              <template slot-scope="scope">
                <span><em>{{ scope.row.decreaseHour }}</em><em>{{ scope.row.remainDecreasePackage }}</em></span>
              </template>
            </af-table-column>
            <af-table-column label="消费量" width="150px">
              <template slot-scope="scope">
                <span><em>{{ scope.row.consumptionHour }}</em><em>{{ scope.row.consumptionPackage }}</em></span>
              </template>
            </af-table-column>
            <af-table-column label="状态" prop="valid">
              <template slot-scope="scope">
                <span v-if="scope.row.valid === 1">已生效</span>
                <span v-if="scope.row.valid === 0">已作废</span>
              </template>
            </af-table-column>
            <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" fixed="right" width="450px">
              <template slot-scope="{row}">
                <el-button v-permission="['customer:schoolProject:up']" type="success" size="mini"
                  @click="chargeFlowPackagePop = true, chargeFlowPackage(row, 1)">充值
                </el-button>
                <el-button v-permission="['customer:schoolProject:reduce']"  size="mini"
                           @click="openPackageManagement(row)">套餐
                </el-button>
                <el-button v-permission="['customer:schoolProject:reduce']"  size="mini"
                           @click="chargeFlowPackagePop=true,chargeFlowPackage(row,2)">减扣
                </el-button>
                <el-button v-permission="['customer:schoolProject:editPackage']" size="mini"
                  @click="handleUpdate(row)">修改
                </el-button>
                <el-button v-permission="['customer:schoolProject:enableOpera']" size="mini"
                  @click="handleEnable(row)">{{ row.valid === 1 ? '作废' : '生效' }}
                </el-button>
                <el-button v-permission="['customer:schoolProject:delPackage']" size="mini"
                  @click="delFlowPackage(row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-form>
    <!--    延期弹框-->
    <delay-pop ref="delay" />
    <reset-pop ref="reset" />
    <set-pop ref="set" @updatePlayer="getDetail(orderId)" />
    <intelligent-pop ref="intelligent" @intelligentResh="getDetail(orderId)" />
    <students-pop ref="students" @refreshSchools="getDetail" />
    <!-- 设置流量包-->
    <set-package ref="packages" @updatePackage="getDetail(orderId)" />
    <!-- 减扣/充值弹框-->
    <el-dialog :visible.sync="chargeFlowPackagePop" :title="chargeFlowPackageTitle"
      :close-on-click-modal="!chargeFlowPackagePop" width="30%" @close="chargeFlowPackageCancel">
      <el-form :model="classForm" label-width="90px">
        <el-form-item :label="setPackageTitle" required>
          <div class="charge-flow">
            <el-input v-model.number="classForm.hour" style="width: 150px" maxlength="7" />
            <em>时</em>
            <el-input v-model.number="classForm.minute" style="width: 150px" maxlength="7" />
            <em>分</em>
          </div>
        </el-form-item>
        <el-form-item v-if="flagsNum === 1" label="充值金额" prop="payAmount">
          <el-input v-model.number="classForm.payAmount" />
        </el-form-item>
        <el-form-item v-if="flagsNum === 1" label="备注" prop="tips">
          <el-input v-model="classForm.tips" placeholder="请输入备注" type="textarea" maxlength="255" show-word-limit />
        </el-form-item>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="chargeFlowPackageCancel()">取消</el-button>
          <el-button type="primary" size="mini" @click="chargeFlowPackageConfirm">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--区域类型设置弹框-->
    <el-dialog title="区域类型设置" :visible.sync="areaPop" :close-on-click-modal="!areaPop" width="30%"
      @close="areaSetCancel">
      <el-form label-width="120px">
        <el-form-item label="加盟区域类型" required>
          <el-radio-group v-model="franchiseAreaType">
            <el-radio v-for="(item, i) in areaList" :key="i" :label="item.itemValue">{{ item.itemName }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定位区域类型" required>
          <el-radio-group v-model="areaJoinType">
            <el-radio v-for="(item, i) in areaList" :key="i" :label="item.itemValue">{{ item.itemName }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定位方式" required>
          <el-radio-group v-model="modelType">
            <el-radio label="2">行政区域</el-radio>
            <el-radio label="1">校区半径</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="校区半径/米" required>
          <el-input v-model.number="locationRadius" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" @click="areaSetType">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--播课设备弹框-->
    <el-dialog title="播课设备设置" :visible.sync="classPop" :close-on-click-modal="!classPop" width="25%"
      @close="areaSetCancel">
      <div class="set-info">
        <el-form label-width="80px">
          <el-form-item label="播课设备">
            <el-checkbox-group v-model="playDeviceTypes">
              <el-checkbox label="1">智能终端</el-checkbox>
              <el-checkbox label="2">APK</el-checkbox>
              <el-checkbox label="3">平板</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div style="text-align: center;">
            <el-button size="mini" type="primary" @click="setDeviceTypes">确定</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!--播课设备弹框-->
    <package-transfer ref="packageTransfer" />
    <package-management :school-id="schoolManage.agencyId" ref="packageManagement" :product-id="productId" @refresh="getDetail(orderId)" />
    <el-dialog
            :title='dialogTitle'
            :visible.sync="dialogVisible"
            width="30%"
            v-if="currentPackage&&currentPackage.productId"
            :before-close="handleClose">
      <div>
        <!--        <h3>{{ currentPackage && currentPackage.productName }}</h3>-->
        <el-form ref="form" label-width="120px">
          <el-form-item label="当前剩余科目数">
            <el-input v-model="currentPackage.num" readonly disabled />
          </el-form-item>
          <el-form-item label="操作类型">
            <el-radio-group v-model="currentPackage.type">
              <el-radio label="add">充值</el-radio>
              <el-radio label="reduce">扣减</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="数量">
            <el-input-number :min="0" :max="999" v-model="currentPackage.newNum" placeholder="请输入充值数量" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubject">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 分校设置dialog -->
    <el-dialog
      title="分校设置"
      :visible.sync="branchSettingDialog"
      width="400px"
      :close-on-click-modal="false"
      @close="handleBranchSettingCancel">
      <el-form :model="branchSettingForm" label-width="100px">
        <el-form-item label="校区编号：" required>
          <el-input
            v-model="branchSettingForm.branchCode"
            placeholder="请输入校区编号"
            maxlength="10"
            show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleBranchSettingCancel">取 消</el-button>
        <el-button type="primary" @click="handleBranchSettingConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 新增独家单点dialog -->
    <el-dialog
      title="新增独家单点"
      :visible.sync="exclusivePointDialog"
      width="400px"
      :close-on-click-modal="false"
      @close="handleExclusivePointCancel">
      <el-form :model="exclusivePointForm" label-width="140px">
        <el-form-item label="单点校区机构ID：" required>
          <el-input
            v-model="exclusivePointForm.pointCode"
            placeholder="请输入关联机构ID"
            maxlength="10"
            show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleExclusivePointCancel">取 消</el-button>
        <el-button type="primary" @click="handleExclusivePointConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import DelayPop from './components/delayPop'
import ResetPop from './components/resetPop'
import SetPop from './components/setPop'
import IntelligentPop from './components/IntelligentPop'
import StudentsPop from './components/studentsPop'
import SetPackage from './components/setPackage'
import PackageTransfer from './components/packageTransfer'
import PackageManagement from './components/packageManagement'
import AiQuestionBankDialog from './components/AiQuestionBankDialog'
import {
  getDetail,
  removeFlowPackage,
  setFlowPackage,
  openAuditRole,
  transferBaseSchool,
  setLocationModelType,
  getLocationModelType,
  openRegion,
  getSchoolPlayDeviceTypes,
  setSchoolPlayDeviceTypes,
  enableSchool,
  enablePackage,
  updateSubjectNum, setOpenProject, resetSchoolName, resetSchoolPartnerName,
  getExclusivePointList, addExclusivePoint, deleteExclusivePoint, clearLatAndLon, listBranchSchoolByAgencyId, updateLatAndLon
} from '@/api/schoolCampus'
import {
  getPayType
} from '@/api/common'
import ChangeSchoolAccount from '@/views/schoolProject/components/changeSchoolAccount.vue'
import { checkBtnPermission } from '@/utils/permission'
import SYGXDialog from '@/views/schoolProject/components/SYGXDialog.vue'
import Diagnose from '@/views/schoolProject/components/Diagnose.vue'
import { deepClone } from '@/utils'

export default {
  name: 'Campusmanagement',
  components: {
    SYGXDialog,
    ChangeSchoolAccount,
    DelayPop,
    ResetPop,
    SetPop,
    IntelligentPop,
    StudentsPop,
    SetPackage,
    PackageTransfer,
    PackageManagement,
    Diagnose,
    AiQuestionBankDialog
  },
  data() {
    return {
      clueBase: {},
      dialogTitle: '',
      schoolManage: {},
      originSchoolManage: {},//原始数据
      list: [],
      dialogVisible: false,
      showSYGHDialog: false, //生涯规划弹窗
      currentPackage: {
        id: '',
        type: 'add',
        classTypeId: '',
        schoolId: '',
        productName: '',
        num: 0,
        newNum: 0
      },
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 10
      },
      orderId: 0,
      chargeFlowPackagePop: false,
      chargeFlowPackageTitle: '',
      flagsNum: -1, // 操作类型 1-充值，2-扣减
      PackageId: '', // 流量id
      tips: '',
      package: {},
      classForm: {
        hour: 0,
        minute: 0
      },
      userSchoolDelayLogs: [], // 延期记录
      setPackageTitle: '',
      areaPop: false,
      areaList: [],
      areaSets: 1,
      radiusCampus: null,
      areaJoinType: '',
      modelType: '',
      locationRadius: '',
      classPop: false,
      playDeviceTypes: [],
      openRegion: true,
      activeText: '',
      widthLabel: 80,
      showAppend: false,
      showDiagnoseDialog: false,
      aiQuestionBank: '',
      productId: null,
      franchiseAreaType: '',
      // 分校信息相关数据
      branchSchoolList: [],
      branchListLoading: false,
      // 独家-单点相关数据
      exclusivePointList: [],
      exclusiveListLoading: false,
      // 分校设置dialog相关数据
      branchSettingDialog: false,
      branchSettingForm: {
        branchCode: '',
        branchId: null
      },
      currentBranchRow: null,
      // 独家单点dialog相关数据
      exclusivePointDialog: false,
      exclusivePointForm: {
        pointCode: ''
      }
    }
  },
  computed: {
    showSaveMacUpdateBtn() {
      return this.schoolManage && this.schoolManage.macUpdate !== this.originSchoolManage.macUpdate;
    },
    disabledProjectSelect() {
      return !checkBtnPermission(['customer:schoolProject:updateProject'])
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.orderId = this.$route.query.id
      this.getDetail(this.$route.query.id)
      this.getAreaType('area_join_type')
    })
  },
  methods: {

    openName() {
      const id = this.schoolManage.agencyId
      this.$prompt('请输入新的校区名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.schoolManage.title,
        inputPattern: /^[a-zA-Z0-9\u4e00-\u9fa5()（）\-]+$/,
        inputErrorMessage: '校区名称不能为空，可以使用中文、数字、英文'
      }).then(({ value }) => {
        resetSchoolName({ schoolId: id, resetSchoolName: value }).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '修改成功'
            })
            this.loadData()
          }
        })
      }).catch(() => {

      });
    },
    openRealName() {
      const id = this.schoolManage.agencyId
      this.$prompt('请输入新的校区联系人名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.schoolManage.realName,
        inputPattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
        inputErrorMessage: '联系人名称不能为空，可以使用中文、数字、英文'
      }).then(({ value }) => {
        resetSchoolPartnerName({ schoolId: id, partnerName: value }).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '修改成功'
            })
            this.loadData()
          }
        })
      }).catch(() => {

      });
    },
    loadData() {
      this.orderId = this.$route.query.id
      this.getDetail(this.$route.query.id)
      this.getAreaType('area_join_type')
    },
    handleOpen(row, item) {
      this.currentPackage = {
        productId: row.productId,
        schoolId: this.schoolManage.agencyId,
        classTypeId: item.id,
        productName: row.productName,
        num: item.subjectNum || 0,
        type: 'add',
        newNum: 0
      }
      this.dialogVisible = true
      this.dialogTitle = `【${row.productName}-${item.title}】科目数调整`
    },
    handleClose() {
      this.dialogVisible = false
      this.currentPackage = {}
      this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    },
    handleSubject() {
      if (this.currentPackage.newNum === 0) {
        this.$message({
          message: '充值或扣减数为0，无法提交',
          type: 'warning'
        })
        return
      }
      if (this.currentPackage.type === 'reduce' && this.currentPackage.num < this.currentPackage.newNum) {
        this.$message({
          message: '扣减数量过多，请重新操作',
          type: 'warning'
        })
        return
      }
      updateSubjectNum(this.currentPackage).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.getDetail(this.$route.query.id)
          this.handleClose()
        }
      })
    },
    updateOpenProject() {
      const data = { schoolId: this.schoolManage.agencyId, type: this.schoolManage.macUpdate }
      setOpenProject(data).then(
        res => {
          if (res.code === '000000') {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.getDetail(this.$route.query.id)
            this.handleClose()
          }
        }
      )
    },
    getDetail(ids) {
      getDetail(ids).then(res => {
        if (res.code === '000000') {
          this.clueBase = res.data.clueBase || {}
          this.schoolManage = res.data.schoolManage || {}
          this.originSchoolManage = Object.assign({}, res.data.schoolManage)
          this.openRegion = res.data.schoolManage.openRegion ? (res.data.schoolManage.openRegion).toString() : ''
          this.listLoading = false
          this.activeText = this.openRegion === '1' ? '允许校区跨区' : '禁止校区跨区'
          this.total = res.data.flowPackages !== null ? res.data.flowPackages.length : 0
          this.userSchoolDelayLogs = res.data.userSchoolDelayLogs || []
          if (this.schoolManage.agencyId) {
            this.getPlayDeviceTypes(this.schoolManage.agencyId)
          }
          // 流量列表
          const packageLists = res.data.flowPackages !== null ? res.data.flowPackages : []
          const packArr = []
          for (let i = 0; i < packageLists.length; i++) {
            const objs = {}
            objs['id'] = packageLists[i].id
            objs['productId'] = packageLists[i].productId
            objs['productName'] = packageLists[i].productName
            objs['rangeType'] = packageLists[i].rangeType
            objs['valid'] = packageLists[i].valid
            objs['openSubject'] = packageLists[i].openSubject
            objs['applyClassTypeList'] = packageLists[i].applyClassTypeList || []
            objs['applyClassTypes'] = packageLists[i].applyClassTypes || []
            objs['applySubjectList'] = packageLists[i].applySubjectList || []
            objs['paymentStatus'] = packageLists[i].paymentStatus
            if (packageLists[i].balance) { // 剩余流量
              objs['remainHour'] = `${parseInt(packageLists[i].balance / 60)}小时`
              const remainMin = (packageLists[i].balance / 60).toString()
              const remainMins = remainMin.includes('.') ? (remainMin - parseInt(remainMin)) : ''
              objs['remainMinPackage'] = `${Math.round(Number(remainMins) * 60)}分钟`
            }
            else {
              objs['remainHour'] = ''
              objs['remainMinPackage'] = ''
            }
            if (packageLists[i].sumnumber) { // 剩余流量
              objs['sumnumberHour'] = `${parseInt(packageLists[i].sumnumber / 60)}小时`
              const remainSumnumberMin = (packageLists[i].sumnumber / 60).toString()
              const remainSumnumberMins = remainSumnumberMin.includes('.') ? (remainSumnumberMin - parseInt(remainSumnumberMin)) : ''
              objs['remainSumnumberPackage'] = `${Math.round(Number(remainSumnumberMins) * 60)}分钟`
            }
            else {
              objs['sumnumberHour'] = ''
              objs['remainSumnumberPackage'] = ''
            }

            if (packageLists[i].decrease) { // 剩余流量
              objs['decreaseHour'] = `${parseInt(packageLists[i].decrease / 60)}小时`
              const remainDecreaseMin = (packageLists[i].decrease / 60).toString()
              const remainDecreaseMins = remainDecreaseMin.includes('.') ? (remainDecreaseMin - parseInt(remainDecreaseMin)) : ''
              objs['remainDecreasePackage'] = `${Math.round(Number(remainDecreaseMins) * 60)}分钟`
            }
            else {
              objs['decreaseHour'] = ''
              objs['remainDecreasePackage'] = ''
            }
            if (packageLists[i].consumption) { // 消耗量
              objs['consumptionHour'] = `${parseInt(packageLists[i].consumption / 60)}小时`
              const consumptionMin = (packageLists[i].consumption / 60).toString()
              const consumptionMins = consumptionMin.includes('.') ? (consumptionMin - parseInt(consumptionMin)) : ''
              objs['consumptionPackage'] = `${Math.round(Number(consumptionMins) * 60)}分钟`
            }
            else {
              objs['consumptionHour'] = '0小时'
              objs['consumptionPackage'] = '0分钟'
            }
            packArr.push(objs)
          }
          this.list = packArr

          // ai题库

          this.aiQuestionBank = {
            agencyId: res.data.schoolManage.agencyId,
            openQuestionBank: res.data.schoolManage.openQuestionBank,
            questionBankExpiryDate: res.data.schoolManage.questionBankExpiryDate
          }
          console.log(this.aiQuestionBank);

          // 获取分校信息和独家-单点信息
          this.getBranchSchoolList()
          this.getExclusivePointList()
        }
      }).catch(() => {

      })
    },
    getPlayDeviceTypes(schoolId) {
      getSchoolPlayDeviceTypes(schoolId).then(res => {
        if (res.code === '000000') {
          this.playDeviceTypes = res.data || []
        }
      }).catch((err) => {

      })
    },
    delFlowPackage(row) { // 删除流量包
      if (Number(row.remainHour) !== 0 && Number(row.remainMinPackage) !== 0) {
        this.$confirm('还有剩余流量，确定删除吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          removeFlowPackage(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.getDetail(this.orderId)
            }
          }).catch(() => {

          })
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      }
      else {
        this.$confirm('删除流量包后，学生的剩余课时将无法扣减至校区流量包中，请先联系校区处理好流量包内的剩余课时?', {
          confirmButtonText: '已处理好,继续删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          removeFlowPackage(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.getDetail(this.orderId)
            }
          }).catch(() => {

          })
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消操作'
          })
        })
      }
    },
    openAccountChangeDialog() {
      this.$refs.accountDialog.dialogVisible = true;
    },
    chargeFlowPackage(row, num) { // 充值-1/减扣-2
      this.chargeFlowPackageTitle = num === 1 ? '充值' : '减扣'
      this.setPackageTitle = num === 1 ? '充值流量' : '减扣流量'
      this.flagsNum = num
      this.PackageId = row.id
      this.$set(this.classForm, 'hour', 0)
      this.$set(this.classForm, 'minute', 0)
    },
    chargeFlowPackageCancel() {
      this.chargeFlowPackagePop = false
      this.classForm = {}
    },
    chargeFlowPackageConfirm() {
      const tipsTitle = this.flagsNum === 1 ? '请输入充值流量' : '请输入减扣流量'
      if (this.PackageId && this.flagsNum && !(this.classForm.hour === 0 && this.classForm.minute === 0) && !(!this.classForm.hour && !this.classForm.minute)) {
        const data = Object.assign({}, {
          hour: this.classForm.hour,
          minute: this.classForm.minute,
          id: this.PackageId,
          operateType: this.flagsNum,
          payAmount: this.flagsNum === 1 ? this.classForm.payAmount : 0,
          tips: this.flagsNum === 1 ? this.classForm.tips : ''
        })
        setFlowPackage(data).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getDetail(this.orderId)
            this.chargeFlowPackagePop = false
            this.classForm.payAmount = ''
            this.classForm.tips = ''
          }
        }).catch(() => {

        })
      }
      else if ((this.classForm.hour === 0 && this.classForm.minute === 0) || (!this.classForm.hour && !this.classForm.minute)) {
        this.$message({
          type: 'warning',
          message: `${tipsTitle}`
        })
      }
    },
    handleUpdate(row) {
      this.$refs.packages.setPackagePop = true
      this.$refs.packages.setPackageTitle = '修改流量包'
      this.$refs.packages.packageDetail(row.id)
      this.$refs.packages.schoolId = this.schoolManage.agencyId
      this.$refs.packages.flags = 0 // 修改
      this.$refs.packages.isEdit = true // 修改
      this.$refs.packages.packageId = row.id
    },
    delay() { // showAppend
      this.$refs.delay.showAppend = false
      this.$refs.delay.delayPop = true
      this.$refs.delay.delayTitle = '延期履历'
      this.$refs.delay.getDetail(this.orderId)
      this.$refs.delay.orderDelay = this.orderId
      this.$refs.delay.schoolId = this.schoolManage.agencyId
      this.$refs.delay.institutionId = this.orderId
      this.$refs.delay.expirationDate = this.schoolManage.expirationDate
    },
    boxLocation() {
      this.$router.push({
        name: 'BoxLocation',
        params: {
          mainSchoolId: this.schoolManage.agencyId
        },
        query: {
          title: '盒子-' + this.schoolManage.institutionName
        }
      })
    },
    resetPassword() {
      this.$refs.reset.resetPop = true
      this.$refs.reset.schoolId = this.schoolManage.agencyId
    },
    sets() {
      this.$refs.set.setPop = true
      this.$refs.set.schoolId = this.schoolManage.agencyId
      this.$refs.set.playSource = this.schoolManage.playSource
    },
    IntelligentTerminal() {
      this.$refs.intelligent.IntelligentPop = true
      this.$refs.intelligent.mainSchoolId = this.schoolManage.agencyId
      this.$refs.intelligent.getList()
      this.$refs.intelligent.getSource('device_source_type')
    },
    studentsList() {
      this.$refs.students.studentsPop = true
      this.$refs.students.mainSchoolId = this.schoolManage.agencyId
      this.$refs.students.getList()
      this.$refs.students.partnerSchool(this.schoolManage.agencyId)
      this.$refs.students.schoolList(this.schoolManage.agencyId)
      this.$refs.students.orderId = this.orderId
    },
    setPackage() {
      this.$refs.packages.setPackagePop = true
      this.$refs.packages.setPackageTitle = '设置流量包'
      this.$refs.packages.optionalPackage(this.schoolManage.agencyId)
      this.$refs.packages.schoolId = this.schoolManage.agencyId
      this.$refs.packages.flags = 1 // 新增
      this.$refs.packages.isEdit = false // 新增
    },
    setSYGHPackage() {
      this.showSYGHDialog = true
    },
    hideSYGHDialog() {
      this.showSYGHDialog = false
    },
    trialLesson() { // 开通审课权限
      const title = this.schoolManage.auditRole === 1 ? '确定要关闭审课权限' : '确定要开通审课权限'
      this.$confirm(`${title}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.schoolManage.agencyId) {
          openAuditRole(this.schoolManage.agencyId).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
            }
            this.getDetail(this.orderId)
          })
        }
        else {
          return false
        }
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    transferStudents() {
      this.$confirm('确认将该校区所有学生转移到基地校', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.schoolManage.agencyId) {
          transferBaseSchool(this.schoolManage.agencyId).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '该校区学生全部转移到基地校'
              })
            }
            this.getDetail(this.orderId)
          }).catch(() => {

          })
        }
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '学生在原校区'
        })
      })
    },
    areaSetCancel() {

    },
    getAreaType(str) {
      const that = this
      getPayType(str).then(res => {

        that.areaList = res.data
      })
    },
    getAreaSetDetail() {
      getLocationModelType(this.orderId, this.schoolManage.agencyId).then(res => {
        if (res.code === '000000') {
          this.areaJoinType = res.data.areaJoinType
          this.locationRadius = res.data.locationRadius
          this.franchiseAreaType = res.data.franchiseAreaType
          this.modelType = JSON.stringify(res.data.modelType)
        }
      })
    },
    areaSetType() {
      if (this.orderId && this.schoolManage.agencyId) {

        if (!this.areaJoinType && this.areaJoinType !== 0) {
          this.$message({
            type: 'warning',
            message: '区域类型必选'
          })
          return
        }
        if (!this.modelType) {
          this.$message({
            type: 'warning',
            message: '定位方式必选'
          })
          return
        }
        if (!this.locationRadius) {
          this.$message({
            type: 'warning',
            message: '校区半径必填'
          })
          return
        }
        if (!Number.isInteger(this.franchiseAreaType)) {
          this.$message({
            type: 'warning',
            message: '加盟区域类型必选'
          })
          return
        }
        const params = Object.assign({}, {
          areaJoinType: this.areaJoinType,
          franchiseAreaType: this.franchiseAreaType,
          locationRadius: this.locationRadius ? this.locationRadius : '',
          modelType: this.modelType,
          institutionId: this.orderId,
          mainSchoolId: this.schoolManage.agencyId
        })
        setLocationModelType(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '设置成功'
            })
            this.getDetail(this.orderId)
            this.areaPop = false
          }
        }).catch(() => {

        })
      }
    },
    openInterregional(status) { // 开启校区跨区
      const tips = status === 1 ? '确认该校区关闭跨区?' : '确认该校区开启跨区?'
      const codeTips = status === 1 ? '该校区关闭跨区' : '该校区开启跨区'
      const schoolId = this.schoolManage.agencyId
      this.$confirm(`${tips}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        openRegion(schoolId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${codeTips}`
            })
            this.getDetail(this.orderId)
          }
        }).catch(() => {

          this.getDetail(this.orderId)
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
        this.getDetail(this.orderId)
      })
    },
    setDeviceTypes() {
      const that = this
      if (that.playDeviceTypes && that.playDeviceTypes.length > 0) {
        setSchoolPlayDeviceTypes(that.playDeviceTypes, that.schoolManage.agencyId).then(res => {
          if (res.code === '000000') {
            that.$message({
              type: 'success',
              message: '操作成功'
            })
            that.classPop = false
            that.getDetail(that.orderId)
          }
        }).catch(() => {

        })
      }
      else {
        that.$message({
          type: 'warning',
          message: '请选择播课设备'
        })
      }
    },
    conversionPackage() { // 流量包转化
      this.$refs.packageTransfer.showTranser = true
      this.$refs.packageTransfer.getList(this.schoolManage.agencyId)
      this.$refs.packageTransfer.getlistSchoolHasFlows(this.schoolManage.agencyId)
      this.$refs.packageTransfer.schoolId = this.schoolManage.agencyId
    },
    institutionsOpera(status) { // 开启关闭机构
      const tips = status === 1 ? '确认关闭机构?' : '确认开启机构?'
      const resTips = status === 1 ? '关闭成功' : '开启成功'
      this.$confirm(`${tips}`, {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableSchool(this.schoolManage.agencyId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${resTips}`
            })
            this.getDetail(this.orderId)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    handleEnable(row) { //
      const that = this
      that.$confirm(`${row.valid === 1 ? '流量包作废后，机构后台不能再给该流量包购买课时、不能使用该流量包给学生充值，只能进行流量包转出或扣减课时' : '确定重新生效该流量包?'}`, {
        confirmButtonText: `${row.valid === 1 ? '已知晓,确认作废' : '确认'}`,
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enablePackage(row.id).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: '设置成功',
              type: 'success'
            })
            that.getDetail(this.orderId)
          }
        }).catch(error => {

        })
      }).catch(() => {
        that.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },

    setDiagnoseDialog() {
      this.showDiagnoseDialog = true
    },

    hideDiagnoseDialog() {
      this.showDiagnoseDialog = false
    },
    openPackageManagement(row) {
      this.$refs.packageManagement.open()
      this.productId = row.productId
    },
    openAiQuestionBank() {
      this.$refs.aiQuestionBankDialog.open(this.aiQuestionBank)
    },
    getBranchSchoolList() {
      this.branchListLoading = true
      listBranchSchoolByAgencyId({
        pageIndex: 1,
        pageSize: 100,
        schoolId: this.schoolManage.agencyId,
      }).then(res => {
        if (res.code === '000000') {
          this.branchSchoolList = res.data.records || []
          console.log(this.branchSchoolList)
        }
        this.branchListLoading = false
      }).catch(() => {
        this.branchListLoading = false
      })
    },
   
    editBranchSchool(row) {
      // 打开分校设置dialog
      this.currentBranchRow = row
      this.branchSettingForm.branchCode = row.schoolCode || ''
      this.branchSettingForm.branchId = row.id
      this.branchSettingDialog = true
    },
    clearBranchLocation(row) {
      // 实现清除定位功能
      this.$confirm('确定要清除该分校的定位信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        clearLatAndLon(row.schoolId).then(res => {
          if (res.code === '000000') {
            this.$message.success('清除定位成功')
            // 刷新分校列表
            this.getBranchSchoolList()
          } else {
            this.$message.error(res.msg || '清除定位失败')
          }
        }).catch(err => {
          this.$message.error('清除定位失败')
          console.error('清除定位错误:', err)
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    // 独家-单点相关方法
    getExclusivePointList() {
      this.exclusiveListLoading = true
      getExclusivePointList({
        pageIndex: 1,
        pageSize: 100,
        schoolId: this.schoolManage.agencyId,
        schoolName: this.schoolManage.title
      }).then(res => {
        if (res.code === '000000') {
          this.exclusivePointList = res.data.records || []
        }
        this.exclusiveListLoading = false
      }).catch(() => {
        this.exclusiveListLoading = false
      })
    },
    addExclusivePoint() {
      this.exclusivePointDialog = true
      this.exclusivePointForm.pointCode = ''
    },
    deleteExclusivePoint(row) {
      this.$confirm('确定要删除该独家单点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteExclusivePoint(row.id).then(res => {
          if (res.code === '000000') {
            this.$message.success('删除成功')
            this.getExclusivePointList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 分校设置相关方法
    handleBranchSettingCancel() {
      this.branchSettingDialog = false
      this.branchSettingForm = {
        branchCode: '',
        branchId: null
      }
      this.currentBranchRow = null
    },
    handleBranchSettingConfirm() {
      if (!this.branchSettingForm.branchCode.trim()) {
        this.$message.warning('请输入校区编号')
        return
      }

      // TODO: 这里可以调用API保存校区编号
      console.log('保存校区编号:', this.branchSettingForm.branchCode)
      updateLatAndLon({
        schoolCode: this.branchSettingForm.branchCode,
        schoolId: this.currentBranchRow.schoolId,
      }).then(res => {
        if (res.code === '000000') {
          this.$message.success('更新成功')
          this.handleBranchSettingCancel()
          this.getBranchSchoolList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 独家单点对话框相关方法
    handleExclusivePointCancel() {
      this.exclusivePointDialog = false
      this.exclusivePointForm = {
        pointCode: ''
      }
    },
    handleExclusivePointConfirm() {
      if (!this.exclusivePointForm.pointCode.trim()) {
        this.$message.warning('请输入单点校区ID')
        return
      }

      const data = {
        schoolId: this.schoolManage.agencyId,
        singlePointSchoolIds: [this.exclusivePointForm.pointCode]
      }

      addExclusivePoint(data).then(res => {
        if (res.code === '000000') {
          this.$message.success('绑定成功')
          this.handleExclusivePointCancel()
          this.getExclusivePointList()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.campus-list {
  margin-bottom: 10px;
}

.campus-btns {
  margin-bottom: 15px;
}

.campus-btns-list {
  display: flex;
  justify-content: flex-end;
  margin-right: 15px;
  flex-wrap: wrap;
}

.interregional-types {
  display: flex;
}

.set-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.interregional-tips {
  color: #606266;
  padding-left: 15px;
}

.active-text {
  color: #1890ff;
}

.campus-info>>>.el-switch__core {
  height: 30px;
  width: 80px;
  border-radius: 15px;
}

.campus-info>>>.el-switch__core:after {
  top: 3px;
  height: 20px;
  width: 20px;
}

.campus-info>>>.el-switch.is-checked .el-switch__core::after {
  margin-left: -25px;
}

.subjectNum {
  font-weight: bolder;
  color: #1890ff;
}

@media screen and (max-width: 768px) {
  .campus-btns {
    display: block;
  }

  .campus-btns-list {
    width: 100%;
    margin-right: 0 !important;
    gap: 10px;
    justify-content: flex-start;

    &:last-child {
      margin-top: 15px;
    }
  }
}
</style>

<style>
.campus-info .el-switch__core {
  height: 30px;
  width: 80px;
  border-radius: 15px;
}

.campus-info .el-switch__core:after {
  top: 3px;
  height: 20px;
  width: 20px;
}

.campus-info .el-switch.is-checked .el-switch__core::after {
  margin-left: -25px;
}

.campus-btns-ai {
  display: inline-block;
  margin-left: 10px;

  >>>.el-switch {
    font-size: 14px;

    .el-switch__core {
      height: 20px;
      width: 40px;
    }

    .el-switch__core::after {
      top: 1px;
      width: 16px;
      height: 16px;
    }
  }

  >>>.el-switch.is-checked .el-switch__core::after {
    margin-left: -17px;
  }

  .ai-question-bank {
    font-weight: bolder;

    .el-button {
      min-width: auto;
      padding-left: 5px;
    }
  }
}
</style>
