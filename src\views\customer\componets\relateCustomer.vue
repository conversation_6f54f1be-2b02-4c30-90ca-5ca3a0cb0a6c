<template>
  <el-dialog v-el-drag-dialog width="50%" :title="title" :visible.sync="dialogRelate" :close-on-click-modal="!dialogRelate">
    <el-row>
      <div>
        <el-col :span="24" class="search-wrap">
          <el-col :span="19" class="search-wrap-inner">
            <el-input
              v-model="listQuery.searchField"
              prefix-icon="el-icon-search"
              placeholder="手机号/客户名/校区名称"
              class="filter-item"
              style="width: 100%;"
              @input="resetKey"
            />
          </el-col>
          <el-col :span="5" class="custorm-serch">
            <el-button type="primary" size="small" round @click="getLists">搜索</el-button>
          </el-col>
        </el-col>
      </div>
      <el-col :span="24">
        <el-table v-loading="relateListLoading" :data="relateList" border fit stripe highlight-current-row style="width: 100%;">
          <el-table-column label="#" type="index" width="50" />
          <el-table-column label="客户名称" prop="customer" width="90" show-overflow-tooltip />
          <el-table-column label="客户手机号" prop="mobile" :formatter="encryptionPhone" />
          <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
            <template slot-scope="{row}">
              <el-button v-if="!isChoose" type="primary" size="mini" @click="handleRelate(row)">
                关联客户
              </el-button>
              <el-button v-if="isChoose" type="primary" size="mini" @click="handleChoose(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getLists" />
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-row>
        <el-col :span="24">
          <div class="add-customer" @click="handleCreate"><i class="el-icon-plus" style="font-size: 12px" />&nbsp;新增客户</div>
        </el-col>
      </el-row>
    </div>
    <el-dialog title="新增客户" :visible.sync="innerVisible" append-to-body>
      <el-form ref="addRelate" :model="customerAdd" :rules="customerRules" class="demo-form-inline" label-width="100px">
        <el-row>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="客户名称" prop="customer">
              <el-input v-model="customerAdd.customer" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="customerAdd.mobile" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="校区名称" prop="institution">
              <el-input v-model="customerAdd.institution" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构区域">
              <area-picker
                :area-list="areaList"
                :level="'3'"
                :area-style="'width:100%'"
                class="filter-item"
                @getAreaList="getAreaList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构地址" prop="address">
              <el-input v-model="customerAdd.address" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="信息来源" prop="originName">
              <el-input v-model="customerAdd.originName" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="意向度" prop="clueType">
              <el-select v-model="customerAdd.clueType" placeholder="信息来源" clearable class="filter-item" style="width: 100%;" filterable>
                <el-option v-for="item in intentionList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="微信" prop="weixin">
              <el-input v-model="customerAdd.weixin" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="QQ" prop="qq">
              <el-input v-model="customerAdd.qq" />
            </el-form-item>
          </el-col>
          <el-col :xs="{span:24}" :sm="{span:12}">
            <el-form-item label="邮箱" prop="mail">
              <el-input v-model="customerAdd.mail" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="customerAdd.remark" type="textarea" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="relate-foot">
          <el-button @click="cancelRelate">取 消</el-button>
          <el-button type="primary" @click="confirmCreate">确 定</el-button>
        </el-row>
      </el-form>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  getCustomerList,
  relateCustomer
} from '@/api/customer'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import { createCustomer } from '@/api/customer'
import { intentionList, originList } from '@/utils/field-conver'
import { validPhone } from '@/utils/validate.js'
export default {
  name: 'RelateCustomer',
  directives: {
    elDragDialog
  },
  components: {
    AreaPicker,
    Pagination
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    currentId: {
      type: String,
      default: ''
    },
    isChoose: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogRelate: false,
      relateListLoading: false,
      relateList: [],
      listQuery: {
        page: 1,
        pageSize: 5,
        searchField: ''
      },
      customerModule: {},
      total: 0,
      innerVisible: false,
      customerAdd: {},
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      intentionList: intentionList,
      originList: originList,
      customerRules: {
        customer: {
          required: true,
          message: '客户姓名必填',
          trigger: 'blur'
        },
        mobile: { required: true, validator: validPhone, trigger: 'blur' },
        institution: {
          required: true,
          message: '校区名称必填',
          trigger: 'blur'
        },
        clueType: {
          required: true,
          message: '信息来源必填',
          trigger: 'blur'
        },
        originName: {
          required: true,
          message: '信息来源必填',
          trigger: 'blur'
        },
        area: {
          required: true,
          message: '区域必填',
          trigger: 'blur'
        }
      }
    }
  },
  methods: {
    /**
       * 获取客户列表
       * */
    getLists() {
      const that = this
      const params = this.listQuery
      getCustomerList(params).then(res => {
        that.relateList = res.data.records
        that.total = res.data.total
        that.dialogRelate = true
      })
    },
    resetKey() {
      this.getLists()
    },
    /**
       * 关联客户
       */
    handleRelate(row) {
      const params = {
        clueId: this.currentId,
        relateClueIds: [row.id]
      }
      relateCustomer(params).then(res => {
        this.$message({
          message: '关联成功',
          type: 'success'
        })
        this.$emit('success')
        this.dialogRelate = false
      }).catch(() => {

      })
    },
    /**
       * 选择客户
       */
    handleChoose(row) {
      this.dialogRelate = false
      this.$emit('chooseOk', row)
    },
    /**
       * 客户手机号加密
       */
    encryptionPhone(row) {
      const tel = '' + row.mobile
      const tel1 = tel.replace(tel.substring(3, 7), '****')
      return tel1
    },
    handleCreate() {
      this.innerVisible = true
      this.customerAdd = {}
      this.areaList = {}
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    confirmCreate() {
      const params = Object.assign(this.customerAdd, this.areaList)
      if (!this.areaList.provinceId || !this.areaList.cityId || !this.areaList.areaId) {
        this.$message({
          message: '机构区域填写有误',
          type: 'warning'
        })
        return
      }
      this.$refs['addRelate'].validate(valid => {
        if (valid) {
          createCustomer(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增客户成功',
                type: 'success'
              })
              this.customerAdd = {}
              this.innerVisible = false
              this.getLists()
            }
          })
        }
      })
    },
    cancelRelate() {
      this.customerAdd = {}
      this.areaList = {}
      this.innerVisible = false
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
  }

  /deep/ .search-wrap-inner .el-input__inner {
    border-radius: 18px;
  }

  .search-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .add-customer {
    border-radius: 18px;
    height: 36px;
    background-color: #F8F8F8;
    color: #539FFF;
    font-size: 15px;
    line-height: 36px;
    text-align: center;
    font-weight: 500;
  }

  .custorm-serch {
    display: flex;
    justify-content: flex-end;
  }
  .relate-foot{
    display: flex;
    justify-content: center;
  }
</style>
