<template>
  <el-dialog
    top="2vh"
    width="60%"
    :title="dialogTitle[status]"
    :visible.sync="dialogVisible"
    :close-on-click-modal="!dialogVisible"
  >
    <div class="dispose-title">
      <span class="dispose-title-span"> 处理反馈：</span>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="textarea"
        :rows="5"
        maxlength="300"
        show-word-limit
      >
      </el-input>
    </div>
    <div class="dispose-button">
      <el-button type="primary" @click="handleSubmit">
        {{ status === "completeSchedule" ? "完成处理" : "确认" }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { requirementSave } from "@/api/feedback";
export default {
  name: "RequirementsDialog",
  data() {
    return {
      dialogTitle: ["", "排期处理", "不处理", "完成处理"],
      id: "",
      textarea: "",
      // schedule: 排期处理
      status: "",
      dialogVisible: false
    };
  },
  mounted() {},
  methods: {
    // 初始化
    init(status, id) {
      this.id = id;
      this.status = status;
      this.dialogVisible = true;
    },
    // 提交
    handleSubmit() {
      if (!this.textarea) {
        this.$message({
          type: "warning",
          message: "请输入反馈内容"
        });
        return;
      }
      let params = {
        id: this.id,
        type: "0", // 0 产品需求 1 教研需求 暂时只做产品需求
        operationType: this.status,
        content: this.textarea
      };
      requirementSave(params)
        .then(res => {
          this.$message({
            type: "success",
            message: "提交成功"
          });
          this.dialogVisible = false;
          this.textarea = "";
          this.$emit("refresh");
          this.$router.push({
            name: "Requirements",
            params: { refresh: true }
          });
        })
        .catch();
    }
  }
};
</script>
<style scoped lang="scss">
.dispose-title {
  display: flex;
  .dispose-title-span {
    width: 90px;
    margin-right: 10px;
  }
}
.dispose-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
