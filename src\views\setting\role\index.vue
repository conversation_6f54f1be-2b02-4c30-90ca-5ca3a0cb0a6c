<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.roleName" placeholder="角色名称" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves v-permission="['setting:role:create']" class="filter-item" type="primary" @click="showCreate">
        新增角色
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="角色名称" prop="roleName" />
      <af-table-column label="角色编码" prop="code" />
      <af-table-column label="创建时间" prop="createTime" :formatter="formatterTime" />
      <af-table-column label="使用状态" prop="valid" :formatter="getRoleStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}" width="230">
          <el-button v-permission="['setting:role:authorize']" type="primary" size="mini" @click="showPermissionDialog(row)">
            授权
          </el-button>
          <el-button v-permission="['setting:role:update']" type="primary" size="mini" @click="editRole(row)">
            修改角色
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <!--    角色弹窗-->
    <el-dialog title="角色管理" :visible.sync="dialogCreateRole" width="30%" :close-on-click-modal="!dialogCreateRole">
      <el-form :model="createRoleForm" :rules="createRoleFormRules">
        <el-form-item label="角色名称" label-width="120px" prop="roleName">
          <el-input v-model="createRoleForm.roleName" autocomplete="off" />
        </el-form-item>
        <el-form-item label="角色编号" label-width="120px" prop="code">
          <el-input v-model="createRoleForm.code" autocomplete="off" />
        </el-form-item>
        <el-form-item label="角色权限" label-width="120px" prop="permissionLevel">
          <el-select v-model="createRoleForm.permissionLevel" filterable>
            <el-option label="查看自己" value="1" />
            <el-option label="指定省份（需要到授权配置）" value="2" />
            <el-option label="指定城市（需要到授权配置）" value="4" />
            <el-option label="指定校区（需要到授权配置）" value="5" />
            <el-option label="所有" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可用" label-width="120px" prop="valid">
          <el-radio-group v-model="createRoleForm.valid">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="dialogCreateRole = false">取 消</el-button>
        <el-button type="primary" @click="confirmCreate">确 定</el-button>
      </div>
    </el-dialog>
    <permission-dialog ref="permissionDialog" :type="'role'" @refresh="getList" />
  </div>
</template>

<script>
import { getRoleList, createRole, editRole } from '@/api/system-setting'
import Pagination from '@/components/Pagination'
import PermissionDialog from '@/components/set-permission'
import { parseTime } from '@/utils'
import { roleStatuList, converseEnToCn } from '@/utils/field-conver'
export default {
  name: 'Role',
  components: { Pagination, 'permission-dialog': PermissionDialog },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [], // 角色列表
      curRoleId: '', // 当前角色的id
      listQuery: {
        pageIndex: 1,
        pageSize: 20,
        roleName: ''
      },
      dialogCreateRole: false, // 创建角色
      createRoleForm: {
        roleName: '',
        code: '',
        permissionLevel: '3',
        valid: 1
      },
      createRoleFormRules: {
        roleName: [{ required: true, trigger: 'blur', message: '角色名称必填' }],
        code: [{ required: true, trigger: 'blur', message: '角色编号必填' }],
        permissionLevel: [{ required: true, trigger: 'blur', message: '角色权限必选' }],
        valid: [{ required: true, trigger: 'blur', message: '是否停用必选' }]
      },
      total: 0 // 分页总数
    }
  },
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      const that = this
      getRoleList(this.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    // 打开新增角色弹窗
    showCreate() {
      this.dialogCreateRole = true
      this.createRoleForm = {
        roleName: '',
        code: '',
        permissionLevel: '3',
        valid: 1
      }
    },
    /**
     * 创建角色
     */
    confirmCreate() {
      if (this.createRoleForm.id) {
        editRole(this.createRoleForm).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '修改角色成功',
              type: 'success'
            })
            this.getList()
            this.dialogCreateRole = false
          }
        })
      } else {
        createRole(this.createRoleForm).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '新增角色成功',
              type: 'success'
            })
            this.getList()
            this.dialogCreateRole = false
          }
        })
      }
    },
    /**
     * 打开权限弹窗
     */
    showPermissionDialog(row) {
      this.curRoleId = row.id
      this.$refs.permissionDialog.getPermissionList(this.curRoleId)
    },
    /**
     * 修改角色
     */
    editRole(row) {
      this.dialogCreateRole = true
      this.createRoleForm = row
      this.createRoleForm.permissionLevel = typeof row.permissionLevel === 'string' ? row.permissionLevel : JSON.stringify(row.permissionLevel)
    },
    handleQuery() {

    },
    handleUpdate() {

    },
    // 格式化时间
    formatterTime(row) {
      return parseTime(row.createTime)
    },
    getRoleStatus(row) {
      return converseEnToCn(roleStatuList, row.valid)
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    padding: 0 20px;
  }
  .permission-title {
    padding: 10px 0;
  }
  .permission-item-body {
    padding: 0 10px;
  }
</style>
