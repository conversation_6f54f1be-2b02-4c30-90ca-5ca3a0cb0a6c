<template>
  <el-dialog :visible.sync="showUpload" :title="uploadTitle" :close-on-click-modal="!showUpload" width="60%" @close="changeInit">
    <div class="upload-inner">
      <div class="upload-list">
        <span style="padding-top:2px">上传文件:</span>
        <div class="upload">
          <div v-if="showUploadOpera" class="upload_warp">
            <div class="upload_warp_left" @click="fileClick">
              <span class="el-icon-upload" />
            </div>
            <div class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
              或者将文件拖到此处
            </div>
          </div>
          <div class="upload_warp_text">
            <i v-if="imgList.length>0">选中{{ imgList.length }}张文件</i>
          </div>
          <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
          <div v-show="imgList.length!=0" class="upload_warp_img">
            <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
              <div class="upload_warp_img_div_top">
                <div class="upload_warp_img_div_text">
                  {{ item.file.name }}
                </div>
                <img v-if="showUploadFiles" src="../../../assets/img/del.png" class="upload_warp_img_div_del" @click="fileDel(index)">
              </div>
              <img :src="item.file.src">
            </div>
          </div>
        </div>
      </div>
      <div class="upload-list btn-list">
        <el-button v-if="showUploadFiles&&imgList.length>0" size="mini" type="primary" :disabled="imgList.length===0" @click="imgUpload">上传资料</el-button>
      </div>
      <div v-if="showUploadFiles&&imgList.length>0" class="upload-list">
        <span style="padding-top:2px">上传进度:</span>
        <el-progress :percentage="percentUpload" :text-inside="true" :stroke-width="20" style="width:92%" />
      </div>
      <div v-if="uploadResult&&imgList.length>0" class="upload-list">
        <span>上传结果:</span>
        <el-table
          ref="multipleTable"
          :data="list"
          row-key="index"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column type="index" width="55" label="序号" />
          <el-table-column label="名称" prop="name" width="200" />
          <el-table-column label="类型" prop="type" />
          <el-table-column prop="size" label="大小(单位:MB)" />
          <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="200">
            <template slot-scope="scope">
              <i class="el-icon-sort sort-table" />
              <el-button type="primary" size="mini" @click="delFile(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="imgList.length>0" class="upload-list btn-list">
        <el-button v-if="!showUploadFiles" type="primary" size="mini" @click="addFiles">提交</el-button>
        <el-button v-if="!showUploadFiles" type="default" size="mini" @click="changeInit">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { uuid } from '@/utils/index'
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})

import { GetRound } from '@/utils/field-conver'
import { uploadSuccess } from '@/api/common'
import { editCourseNotes, editCourseExercises } from '@/api/admissions'
import Sortable from 'sortablejs'
export default {
  components: {
  },
  data() {
    return {
      showUpload: false,
      uploadTitle: '上传资料',
      imgList: [],
      size: 0,
      list: [],
      uploadResult: false,
      showUploadOpera: true,
      showUploadFiles: true,
      percentUpload: 0,
      uploadFileName: '',
      notesResourceId: null,
      notesResourceUrl: '',
      courseId: '',
      uploadType: ''
    }
  },
  watch: {
    'imgList': {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.showUploadFiles = true
          this.list = []
          this.uploadResult = false
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    changeInit() {
      this.list = []
      this.imgList = []
      this.percentUpload = 0
      this.uploadFileName = ''
      this.showUpload = false
      this.uploadResult = false
      this.showUploadOpera = true
      this.showUploadFiles = true
    },
    // 拖拽上传文档
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      // if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {

      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        this.fileAdd(files[i])
        // 判断是否为文件夹
        // if (files[i].type !== '') {
        //   this.fileAdd(files[i])
        // } else {
        //   // 文件夹处理
        //   this.folders(fileList.items[i])
        // }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = require('./word.png')

        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(index) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        if (this.percentUpload && (this.percentUpload < 100 && this.percentUpload > 0)) {
          this.$message({
            message: '该文件正在上传中，请勿删除',
            type: 'error'
          })
        } else {
          this.imgList.splice(index, 1)
        }
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    },
    imgUpload() {
      if (this.imgList.length >= 2) {
        this.$message({
          message: '只能上传一个文件',
          type: 'error'
        })
        return false
      }
      if (Array.from(new Set(this.imgList)).length !== this.imgList.length) {
        this.$message({
          message: '请勿上传重复的文件',
          type: 'error'
        })
        return false
      }

      this.multipartUpload(this.imgList, 0)
    },
    multipartUpload(files, i) { // 分段上传
      const that = this

      const ids = uuid()
      const fileName = `santao_stip/crm/course/${ids}.${files[i].file.name}`
      const fileSize = (`${files[i].file.size}`) / 1024 / 1024
      const types = `${files[i].file.name.split('.')[files[i].file.name.split('.').length - 1]}`

      if (types === 'png' || types === 'jpg' || types === 'jpge') {
        that.$message({
          message: '请上传文件格式',
          type: 'error'
        })
        return false
      }
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: files[i].file, // 文件路径
        ProgressCallback: function(transferredAmount, totalAmount, totalSeconds) {
          // 获取上传进度百分比
          that.percentUpload = Math.floor(transferredAmount * 100.0 / totalAmount)
          const arrsName = `${files[i].file.name}`.split('.').concat()
          arrsName.pop()

          that.uploadFileName = `${arrsName.join('.')}.${types}`
          that.imgList[i].src = `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${ids}.${files[i].file.name}`
          that.imgList[i].name = arrsName.join('.')
          that.imgList[i].size = GetRound(fileSize, 2) < 0.01 ? 0.01 : GetRound(fileSize, 2)
          that.imgList[i].type = `${files[i].file.name.split('.')[files[i].file.name.split('.').length - 1]}`
          if (transferredAmount * 100.0 / totalAmount === 100) {
            that.uploadFileName = '文件上传完毕'
            that.uploadResult = true
            that.list.push(that.imgList[i])
            setTimeout(() => {
              that.rowDrop()
            }, 300)
          }
        }
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {
          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${ids}.${files[i].file.name}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              that.notesResourceId = res.data.id
              that.notesResourceUrl = res.data.url
              setTimeout(() => {
                that.showUploadOpera = false
                that.showUploadFiles = false
              }, 2000)
            }
          })
        }
      })
    },
    addFiles() { // editCourseExercises
      const that = this




      if (that.list.length) {
        if (that.uploadType === 'notes') { // 上传讲义
          const paramsList = {
            notesResourceId: that.notesResourceId,
            courseId: that.courseId
          }
          editCourseNotes(paramsList).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '添加成功',
                type: 'success'
              })
              that.changeInit()
              that.percentUpload = 0
              that.uploadFileName = ''
              that.notesResourceId = ''
              that.$emit('refreshList')
            }
          }).catch(() => {

          })
        } else if (that.uploadType === 'exercises') {
          const params = {
            exercisesResourceId: that.notesResourceId,
            courseId: that.courseId
          }
          editCourseExercises(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '添加成功',
                type: 'success'
              })
              that.changeInit()
              that.percentUpload = 0
              that.uploadFileName = ''
              that.notesResourceId = ''
              that.$emit('refreshList')
            }
          }).catch(err => {

          })
        }
      } else {
        that.$message({
          message: '请先上传文件',
          type: 'warning'
        })
      }
    },
    delFile(index) {
      this.$confirm('确定要删除此文件?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.list.splice(index, 1)
        this.imgList = []
        this.percentUpload = 0
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    // 拖动排序
    rowDrop() {
      const tbody = document.querySelector('.el-dialog .el-table__body-wrapper tbody')

      const that = this
      Sortable.create(tbody, {
        sort: true,
        animation: 300,
        handle: '.el-icon-sort',
        onEnd: function(evt) { // 拖拽结束发生该事件
          that.list.splice(evt.newIndex, 0, that.list.splice(evt.oldIndex, 1)[0])
          var newArray = that.list.slice(0)
          that.list = []
          that.$nextTick(function() {
            that.list = newArray
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
 .upload-inner{
    position: relative;
  }
  .upload-list{
      display: flex;
      margin-bottom: 10px;
      span{
          font-size: 16px;
          color: #687182;
          padding-right: 8px;
      }
  }
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 80px;
    color: #687182;
    text-align: center;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    line-height: 80px;
    span{
        font-size: 32px;
        color: #687182;
    }
  }

  .upload_warp {
    margin: 14px;
    height: 80px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
    width: 92%;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
  .btn-upload{
      margin-left: 77px;
      margin-top: 15px;
  }
  .btn-list{
      justify-content: center;
      margin-top:15px;
  }
  .upload-file-name{
    color: red;
    font-size: 20px;
    font-weight: bold;
  }
  .sort-table{
    cursor: pointer;
    font-size:18px;
    color:#A5A5A5;
    margin:0 20px 0 20px;
  }
  @media screen and (max-width: 1441px) {
    .upload{
      width: 90%;
    }
  }
</style>
