<template>
  <div class="q-box" :class="{'selected':selected}" @click="clickHandler">
    <div class="q-content">
      <span class="label title">题目：</span>
      <span v-html="question.question"></span>
    </div>
    <div class="answer">
      <span class="label title">答案：</span>
      <span style="display: inline-block;" v-html="question.answer"></span>
    </div>
  </div>
</template>
<script>
export default {
  name: 'QuestionView',
  props: {
    question: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    clickHandler(){
      this.$emit('tap', this.question)
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .el-dialog__body {
  padding-top: 0;
}
.selected{
  position: relative;
  background: rgba(59, 145, 255, 0.1) !important;
  border: 1px solid #3b91ff !important;
  border-radius: 5px;
  box-sizing: border-box;
}
.selected::after{
  content: '';
  position: absolute;
  top: 0;
  right: 10px;
  width: 10px;
  height: 20px;
  border-right: 3px solid #3b91ff;
  border-bottom: 3px solid #3b91ff;
  transform: rotate(45deg);
}
.q-box {
  padding: 5px 15px;
  margin-bottom: 20px;
  background: #f7f7f7;
  width: 100%;
  border: 1px solid #e5e5e5;
  box-shadow: 5px 5px 5px #e5e5e5;
  .q-content {
    display: flex;

  }

  .answer {
    border-top: 1px solid #ccc;
  }

  .title {
    width: 50px;
  }
}
</style>
