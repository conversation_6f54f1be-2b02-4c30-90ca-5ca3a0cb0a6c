<template>
  <div class="app-container bgGrey">

    <el-row :gutter="10">
      <el-col :span="24">
        <el-form ref="deliveryForm" :model="deliveryAddress" :rules="deliveryRules" size="small" label-width="100px">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>发货单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="收货人：">
                    <div>{{ deliveryAddress.userName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="手机号：">
                    <!-- <el-input v-model="deliveryAddress.mobile" /> -->
                    <template>{{ deliveryAddress.mobile }}</template>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="收货地址：">
                    <div>{{ deliveryAddress.provinceName }}{{ deliveryAddress.cityName }}{{ deliveryAddress.areaName }}{{ deliveryAddress.countyName }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="详细地址：">
                    <!-- <el-input v-model="deliveryAddress.address" /> -->
                    <template>{{ deliveryAddress.address }}</template>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="发货类型：">
                    <template>
                      {{ setShipType(newShipType) }}
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="发货时间：" prop="deliveryTime">
                    <el-date-picker
                      v-model="deliveryAddress.deliveryTime"
                      type="datetime"
                      placeholder="选择发货时间"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="物流公司：" prop="shipCode">
                    <el-select v-model="deliveryAddress.shipCode" filterable style="width: 100%" placeholder="">
                      <el-option v-for="item in expressList" :key="item.expressCode" :label="item.expressName" :value="item.expressCode" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="快递单号：" prop="shipNo">
                    <el-input v-model="deliveryAddress.shipNo" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="快递费用：" prop="shipAmount">
                    <el-input v-model="deliveryAddress.shipAmount" />
                  </el-form-item>
                </el-col>

              </el-row>
            </div>
          </el-card>
        </el-form>
      </el-col>
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>发货单商品信息</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :xs="{span:24}" :sm="{span:24}">
                <el-table :data="orderProducts" border fit stripe highlight-current-row style="width: 100%;">
                  <el-table-column type="index" label="#" width="40" />
                  <el-table-column label="产品名称" width="120" show-overflow-tooltip>
                    <template slot-scope="{row}">
                      {{ row.productName }}
                    </template>
                  </el-table-column>
                  <el-table-column label="发货数">
                    <template slot-scope="{row}">
                      <div>
                        <span class="el-form--inline box text-right">
                          <el-input v-model="row.productNum" disabled />
                        </span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="序列号">
                    <template slot-scope="{row}">
                      <el-input v-model="row.serialNumber" :disabled="row.productType !== 6" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6" :offset="9" class="text-center">
        <el-button v-if="canCreate" type="primary" @click="createDelivery">确&nbsp;认</el-button>
        <el-button v-if="!canCreate" @click="closeTabView">关&nbsp;闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getDeliveryDetail,
  createDeliveryOrder,
  getExpress
} from '@/api/handover'
import {
  converseEnToCn,
  getShipType
} from '@/utils/field-conver'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'Invoice',
  directives: {
    elDragDialog
  },
  components: {},
  props: {},
  data() {
    return {
      canCreate: false,
      title: '合伙人发货单',
      shipTypeList: getShipType,
      newShipType: -1,
      expressList: [], // 物流公司code
      boxList: [], // 智能盒子
      deliveryAddress: {}, // 收货人地址
      orderProducts: [], // 可发货的商品详情
      deliveryRules: {
        shipNo: {
          required: true,
          message: ' ',
          trigger: 'blur'
        },
        shipCode: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        shipAmount: {
          required: true,
          message: ' ',
          trigger: 'blur'
        },
        deliveryTime: {
          required: true,
          message: ' ',
          trigger: 'blur'
        }
      }
    }
  },
  computed: {},
  created() {
    const type = Number(this.$route.query.type || '')
    const orderId = this.$route.query.orderId || ''
    const name = this.$route.query.name || '' // 客户名称
    this.title = type === 2 ? '推荐人发货单' : '合伙人发货单'
    this.newShipType = type
    this.getDeliveryExpress()
    this.getOrderDeliveryDetail(orderId, type)
    this.setTagsViewTitle(name)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${this.title}-${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    /**
     * 关闭当前页面
     * */
    closeTabView() {
      this.$store.dispatch('tagsView/delView', this.$route).then(res => {
        this.$router.go(-1)
      })
    },
    /**
       * 获取发货单信息
       * */
    getOrderDeliveryDetail(orderId, type) {
      const that = this
      getDeliveryDetail(orderId, type).then(res => {
        if (res.code === '000000') {
          const arrType = []
          let Products = []
          that.deliveryAddress = res.data.deliveryAddress // 收货人地址
          Products = res.data.orderProducts || [] // 可发货的商品详情
          Products.forEach((item, index, arr) => {
            if (item.productType === 6) {
              for (let i = 0; i < item.productNum; i++) {
                arrType.push({
                  serialNumber: item.serialNumber,
                  productNum: 1,
                  productId: item.productId,
                  productType: item.productType,
                  productName: item.productName
                })
              }
            } else {
              arrType.push({
                serialNumber: item.serialNumber,
                productNum: item.productNum,
                productId: item.productId,
                productType: item.productType,
                productName: item.productName
              })
            }
          })
          that.orderProducts = arrType

          that.canCreate = true
        }
      }).catch(res => {

      })
    },
    getDeliveryExpress() {
      getExpress().then(res => {
        if (res.code === '000000') {
          this.expressList = res.data
        }
      })
    },
    createDelivery() {
      this.$refs['deliveryForm'].validate(valid => {
        if (valid) {
          const tmpList = JSON.parse(JSON.stringify(this.orderProducts))
          const tmpValid = this.checkValid()
          const repeatValid = this.showRepet()
          if (!tmpValid.flag) {
            this.$message({
              message: tmpValid.msg,
              type: 'warning'
            })
            return
          }
          if (!repeatValid.flag) {
            this.$message({
              message: repeatValid.msg,
              type: 'warning'
            })
            return
          }
          // 获取物流公司名称
          const shipName = this.expressList.filter(item => {
            return item.expressCode === this.deliveryAddress.shipCode
          })
          const baseInfo = {
            orderId: this.deliveryAddress.orderId,
            shipAmount: this.deliveryAddress.shipAmount,
            shipCode: this.deliveryAddress.shipCode,
            shipName: shipName[0]['expressName'] || '',
            shipNo: this.deliveryAddress.shipNo,
            shipType: this.newShipType,
            deliveryTime: this.deliveryAddress.deliveryTime
          }
          const params = Object.assign({
            deliveryDetails: tmpList
          }, baseInfo)
          createDeliveryOrder(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增' + this.title + '成功',
                type: 'success'
              })
              this.closeTabView()
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    checkValid() {
      let res = {
        flag: true
      }
      const tmpList = JSON.parse(JSON.stringify(this.orderProducts))
      tmpList.forEach(item => {
        if (!!Number(item.productNum) === false) {
          res = {
            flag: false,
            msg: '请输入实发数'
          }
          return res
        }
        if (item.productType === 6 && !item.serialNumber) {
          res = {
            flag: false,
            msg: '请输入序列号'
          }
          return res
        }
      })
      return res
    },
    showRepet() {
      let repeatFlag = {
        flag: true
      }
      const tmpLists = JSON.parse(JSON.stringify(this.orderProducts))
      const serialList = tmpLists.filter(item => item.productType === 6)

      var arrids = []
      arrids = serialList.map(item => {
        return item.serialNumber
      })

      if (new Set(arrids).size !== arrids.length) {

        repeatFlag = {
          flag: false,
          msg: '发货单商品信息的序列号不允许重复'
        }
        return repeatFlag
      }
      // var nary = arrids.sort()
      // for (var i = 0; i < arrids.length; i++) {
      //   if (nary[i] === nary[i + 1]) {
      //
      //     repeatFlag = {
      //       flag: false,
      //       msg: '发货单商品信息的序列号不允许重复'
      //     }
      //     return repeatFlag
      //   }
      // }
      return repeatFlag
    },
    /**
       * 数据转换
       */
    setShipType(data) {
      return converseEnToCn(getShipType, data)
    }
  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }

  /*.add-customer {*/
  /*  border-radius: 18px;*/
  /*  height: 36px;*/
  /*  color: #539FFF;*/
  /*  font-size: 15px;*/
  /*  line-height: 36px;*/
  /*  text-align: center;*/
  /*  font-weight: 500;*/
  /*  border: 1px dashed #DCDCDC;*/
  /*  margin: 10px auto;*/
  /*  cursor: pointer;*/
  /*}*/

  /*.add-customer.submit {*/
  /*  background-color: #46a6ff;*/
  /*  color: #ffffff;*/
  /*  border: 1px solid #1890ff*/
  /*}*/

  /deep/ .el-table__expanded-cell {
    padding: 20px 20px;
  }

  .box {
    display: inline-block;
    width: 100px;
  }

  .text-right {
    text-align: right;
  }
</style>
