<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.expressName" placeholder="快递名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.valid" placeholder="是否可用" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in menuValidList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="快递公司名称" align="center" prop="expressName" width="300" />
      <el-table-column label="快递公司编号" align="center" prop="expressCode" width="300" />
      <el-table-column label="是否可用" prop="valid" width="300" align="center" :formatter="getMenuValidList" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button :type="buttonColor(row.valid)" size="mini" @click="handleStatus(row)">
            {{ getButtonText(row.valid) }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <express-dialog ref="expressDialog" :is-edit="isEdit" @refresh="getList" />
  </div>
</template>

<script>
import { editExpressStatus, getExpressList } from '@/api/system-setting'
import { menuValidList, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import expressDialog from './dialog'
export default {
  name: 'Express',
  components: { Pagination, expressDialog },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      menuValidList: menuValidList,
      buttonText: ''
    }
  },
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增快递
     * */
    handleAdd() {
      this.$refs.expressDialog.getDetail()
      this.isEdit = false
    },

    getList() {
      const that = this
      getExpressList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    /**
     * 修改快递
     * */
    handleUpdate(row) {
      this.isEdit = true
      const form = JSON.parse(JSON.stringify(row))
      this.$refs.expressDialog.editDetail(form)
    },
    /**
     * 更改状态
     * */
    handleStatus(row) {
      if (row.valid === 1) {
        this.$confirm('', '确定停用 ' + row.expressName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editExpressStatus(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '停用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      } else {
        this.$confirm('', '确定启用 ' + row.expressName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editExpressStatus(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '启用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      }
    },
    getMenuValidList(row) {
      return converseEnToCn(menuValidList, row.valid)
    },
    getButtonText(valid) {
      return valid === 1 ? '停用' : '启用'
    },
    buttonColor(valid) {
      return valid === 1 ? 'danger' : 'primary'
    }
  }
}
</script>

<style scoped>

</style>
