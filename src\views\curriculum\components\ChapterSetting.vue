<template>
  <el-dialog
          :title="'【'+ title+'】切片设置' "
          width="40%"
          :visible="true"
          :before-close="handleClose">
    <div>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-steps direction="vertical">
            <el-step v-for="(item,index) in courseChapter" :key="index">
              <div slot="title" class="title">
                {{ item.tag }}
                <el-link type="danger" size="mini" @click="delTag(index)">删除</el-link>
              </div>
              <div slot="description">
                <el-tag size="mini" type="info" class="time"><i class="el-icon-time"></i> {{ item.time | convertSecondsToTime }}</el-tag>
              </div>
            </el-step>
          </el-steps>
        </el-col>
        <el-col :span="24" style="margin-top: 20px;">
          <el-form label-width="80px" :inline="true" size="mini">
            <el-form-item>
              <el-form-item label="节点名称">
                <el-input v-model="tag" placeholder="请输入节点名称"></el-input>
              </el-form-item>
              <el-form-item label="节点时间">
                <el-time-picker
                        arrow-control
                        v-model="time"
                        value-format="HH:mm:ss"
                        format="HH:mm:ss"
                        :picker-options="{ selectableRange: '00:00:00 - 03:00:00'}"
                        placeholder="选择节点时间">
                </el-time-picker>
              </el-form-item>
              <el-button type="primary" plain @click="addTag">添加节点</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getCourseChapter, saveCourseChapter } from '@/api/courseMgr'

export default {
  name: 'ChapterSetting',
  data() {
    return {
      courseChapter: [],
      tag: '',
      time: new Date(0, 0, 0, 0, 0, 0)
    }
  },
  props: {
    resourceId: {
      type: Number,
      default: '',
      required: true
    },
    title: {
      type: String,
      default: '',
      required: false
    }
  },
  created() {
    getCourseChapter(this.resourceId).then(res => {
      this.courseChapter = res.data.nodes || []
      // 通过moment 设置0时0分0秒
      this.time = this.$moment(this.time).format('HH:mm:ss')
    })
  },
  filters:{
    convertSecondsToTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      } else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      }
    },
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    addTag() {
      if(!this.tag){
        this.$message({
          title: '请输入节点名称',
          message: '请输入节点名称',
          type: 'warning'
        })
        return
      }
      this.courseChapter.push({
        tag: this.tag,
        time:this.getTimeSeconds()
      })
      this.courseChapter.sort((a, b) =>  a.time-b.time)
      this.$notify({
        title: '已添加，保存后生效',
        message: '已按节点出现时间点进行排序',
        type: 'success'
      })
      this.tag = ''
      this.time = new Date(0, 0, 0, 0, 0, 0)
    },
    delTag(index) {
      this.courseChapter.splice(index, 1)
    },
    submit() {
      const data = {
        resourceId: this.resourceId,
        nodes: this.courseChapter
      }
      saveCourseChapter(data).then(res => {
        if (res.code === '000000') {
          this.$message({
            title: '保存成功',
            message: '保存成功',
            type: 'success'
          })
          this.handleClose()
        }
      })
    },
    getTimeSeconds(){
      return this.timeStringToSeconds(this.time)
    },
    timeStringToSeconds(timeString) {
      const [hours, minutes, seconds] = timeString.split(':').map(Number);
      return (hours * 3600) + (minutes * 60) + seconds;
    }
  }
}
</script>
<style scoped>
.title{
    color:#333;
    font-weight: bolder;
}
.time{
    margin-bottom: 20px;
}
</style>
