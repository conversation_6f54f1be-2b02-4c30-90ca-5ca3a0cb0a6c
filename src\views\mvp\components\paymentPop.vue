<template>
  <el-dialog title="结算" :visible.sync="paymentPop" :close-on-click-modal="!paymentPop" width="40%">
    <div class="payment-list">
      <ul>
        <li>
          <em>订单号:</em>
          <el-input v-model="payment.orderNo" placeholder="请输入订单号" disabled />
        </li>
        <li>
          <em>客户付款:</em>
          <el-input v-model="payment.paymentAmount" placeholder="请输入客户付款" disabled />
        </li>
        <li>
          <em>成本费:</em>
          <el-input v-model="payment.costAmount" placeholder="" disabled />
        </li>
        <li>
          <em>应结算:</em>
          <el-input v-model="payment.settlementAmount" placeholder="请输入应结算" disabled />
        </li>
        <li>
          <em>实际结算:</em>
          <el-input v-model="payment.actualSettlementAmount" placeholder="请输入实际结算" />
        </li>
        <li id="payment-btns">
          <el-button type="primary" size="mini" @click="settlement">确定</el-button>
          <el-button type="infor" size="mini" @click="paymentPop=false">取消</el-button>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
import { settlement } from '@/api/mvp.js'
export default {//
  props: {

  },
  data() {
    return {
      paymentPop: false,
      payment: {}
    }
  },
  created() {
    this.getInitVal()
  },
  methods: {
    getInitVal(obj) {
      if (obj) {
        this.payment = obj
      } else {
        this.payment = {}
      }
    },
    settlement() {
      const that = this
      if (!that.payment.actualSettlementAmount) {
        this.$message({
          type: 'warning',
          message: '请输入实际结算'
        })
      } else if (that.payment.actualSettlementAmount && that.payment.id) {
        const params = Object.assign({}, { actualSettlementAmount: Number(that.payment.actualSettlementAmount), userOrderId: that.payment.id })

        if (Number(that.payment.settlementAmount) !== Number(that.payment.actualSettlementAmount)) {
          that.$confirm('应结算金额与实际结算金额不同，是否确认?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            settlement(params).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '结算成功!'
                })
                that.paymentPop = false
                that.$emit('updetePaymentList')
                that.payment = {}
              }
            }).catch(() => {

            })
          }).catch(() => {
            that.$message({
              type: 'warning',
              message: '取消操作'
            })
          })
        } else {
          settlement(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '结算成功!'
              })
              that.paymentPop = false
              that.$emit('updetePaymentList')
              that.payment = {}
            }
          }).catch(() => {

          })
        }
      }
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
  em,i{
    font-style: normal;
  }
  .payment-list{
    ul{
      padding:0;
      margin: 0;
      li{
        display: flex;
        list-style: none;
        margin-bottom: 10px;
        &#payment-btns{
          display: flex;
          justify-content: center;
          align-items: center;
          padding-top: 15px;
        }
        em{
          display: inline-block;
          &:first-child{
            width:18%;
            font-weight:bold;
            text-align: right;
            padding-right: 8px;
          }
        }
      }
    }
  }
</style>
