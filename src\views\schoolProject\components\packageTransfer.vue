<template>
  <el-dialog :visible.sync="showTranser" :close-on-click-modal="!showTranser" width="70%" @close="delayClose">
    <h3 slot="title" class="transfer-title">
      <span>流量包转换</span>
      <em>请注意，部分流量包为赠送，免费流量包，请勿私下转换，违规必究。</em>
    </h3>
    <el-form :model="transfer" label-width="95px">
      <el-row>
        <el-col :sm="{span:24}" :md="{span:12}">
          <el-form-item label="转出流量包" required>
            <el-select v-model="transfer.outputBalanceId" placeholder="--请选择转出流量包--" filterable @change="getOutputBalance">
              <el-option v-for="item in packages" :key="item.id" :label="item.productName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:12}">
          <el-form-item label="转入流量包" required>
            <el-select v-model="transfer.inputBalanceId" placeholder="--请选择转入流量包--" filterable @change="getInputBalanceId">
              <el-option v-for="item in packagesIn" :key="item.id" :label="item.productName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:12}">
          <el-form-item label="转换课时" required>
            <el-input v-model.number="transfer.hour" style="width:100px" />
            <em>小时</em>
            <el-input v-model.number="transfer.minute" style="width:100px" />
            <em>分钟</em>
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:12}">
          <el-form-item label="转换说明" required>
            <el-input v-model="transfer.remark" type="textarea" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :sm="{span:24}" :md="{span:24}" class="transfer-btns">
          <el-button type="primary" size="small" @click="transfers">转换</el-button>
        </el-col>
      </el-row>
    </el-form>
    <h3 class="opera-title">操作记录</h3>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="转出流量包" show-overflow-tooltip prop="outputProductName" width="200" />
      <af-table-column label="转出课时" prop="outputConvertNums">
        <template slot-scope="scope">
          <span v-if="scope.row.outputConvertNums">
            <em>{{ Math.floor(scope.row.outputConvertNums/60) }}小时</em>
            <em>{{ Math.floor(scope.row.outputConvertNums%60) }}分钟</em>
          </span>
          <span v-else>--</span>
        </template>
      </af-table-column>
      <af-table-column label="转入流量包" show-overflow-tooltip prop="inputProductName" width="200" />
      <af-table-column label="转入课时" prop="inputConvertNums">
        <template slot-scope="scope">
          <span v-if="scope.row.inputConvertNums">
            <em>{{ Math.floor(scope.row.inputConvertNums/60) }}小时</em>
            <em>{{ Math.floor(scope.row.inputConvertNums%60) }}分钟</em>
          </span>
          <span v-else>--</span>
        </template>
      </af-table-column>
      <el-table-column label="转换说明" prop="remark" width="300" />
      <af-table-column label="操作时间" prop="createTime" />
      <af-table-column label="操作人" prop="createUser" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList(schoolId)"
    />
  </el-dialog>
</template>
<script>
import Pagination from '@/components/Pagination'
import { transferPackages, convert, listSchoolHasFlows } from '@/api/schoolCampus'
export default {
  name: 'PackageTransfer',
  components: {
    Pagination
  },
  data() {
    return {
      showTranser: false,
      transferTitle: '',
      packages: [],
      out: null,
      inflows: null,
      transfer: {
        hour: 0,
        minute: 0
      },
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      outPackage: {},
      inPackage: {},
      schoolId: null,
      packagesIn: []
    }
  },
  methods: {
    delayClose() {
      this.transfer = {}
      this.showTranser = false
    },
    getList(schoolId) {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery)
      transferPackages(schoolId, params).then(res => {

        if (res.code === '000000') {
          this.list = res.data.records || []
          this.listLoading = false
          this.total = res.data.total
        }
      }).catch(() => {

        this.listLoading = false
      })
    },
    transfers() {
      const that = this




      if (!that.transfer.outputBalanceId) {
        that.$message({
          type: 'warning',
          message: '请选择转出流量包'
        })
        return false
      }
      if (!that.transfer.inputBalanceId) {
        that.$message({
          type: 'warning',
          message: '请选择转入流量包'
        })
        return false
      }

      if (!that.transfer.hour && that.transfer.hour !== 0) {
        that.$message({
          type: 'warning',
          message: '请输入转换小时数'
        })
        return false
      }
      if (!that.transfer.minute && that.transfer.minute !== 0) {
        that.$message({
          type: 'warning',
          message: '请输入转换分钟数'
        })
        return false
      }
      if (!that.transfer.remark) {
        that.$message({
          type: 'warning',
          message: '请输入转换原因'
        })
        return false
      }
      const subPackage = Number(`${that.transfer.hour * 60}`) + Number(`${that.transfer.minute}`)

      if (subPackage > that.outPackage.balance) {
        that.$message({
          type: 'warning',
          message: '转换课时不能大于【转出流量包】的剩余课时'
        })
        return false
      }
      if (that.transfer.outputBalanceId && that.transfer.inputBalanceId && that.transfer.outputBalanceId === that.transfer.inputBalanceId) {
        that.$message({
          type: 'warning',
          message: '转入和转出流量包不能为同一个流量包'
        })
        return false
      }




      // const totalPackage = `${subPackage}` * `${that.outPackage.productPrice}` / `${that.inPackage.productPrice}`
      const totalPackages = `${subPackage}` * `${that.outPackage.productPrice}`
      const finallyTotal = (`${totalPackages}` * 100) / (`${that.inPackage.productPrice}` * 100).toFixed(2)

      const hourIn = Math.floor(`${finallyTotal / 60}`)
      const minuteIn = Math.floor(`${finallyTotal % 60}`)



      const params = Object.assign({}, that.transfer, { schoolId: that.schoolId })
      this.$confirm(`<p style="padding-bottom:10px">转出 <i style="font-weight: bold">${that.outPackage.productName}:</i><em>${that.transfer.hour}小时</em><em>${that.transfer.minute}分钟</em></p> <p class="package-tips">转入 <i style="font-weight: bold">${that.inPackage.productName}:</i><em style="font-size:18px;font-weight:bold;color:red;">${hourIn}小时</em><em style="font-size:18px;font-weight:bold;color:red;">${minuteIn}分钟</em></p>`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true,
        dangerouslyUseHTMLString: true
      }).then(() => {
        convert(params).then(res => {
          if (res.code === '000000') {
            that.transfer.outputBalanceId = ''
            that.transfer.inputBalanceId = ''
            that.transfer.hour = 0
            that.transfer.minute = 0
            that.transfer.remark = ''
            that.getList(that.schoolId)
          }
        }).catch(() => {
          that.transfer.outputBalanceId = ''
          that.transfer.inputBalanceId = ''
          that.transfer.hour = 0
          that.transfer.minute = 0
          that.transfer.remark = ''

        })
      }).catch(() => {

      })
    },
    getOutputBalance(val) {
      this.outPackage = this.packages.filter(item => item.id === val)[0]
      this.packagesIn = this.packages.filter(item => item.id !== val && item.valid !== 0)
    },
    getInputBalanceId(val) {
      this.inPackage = this.packages.filter(item => item.id === val)[0]
    },
    getlistSchoolHasFlows(schoolId) {
      listSchoolHasFlows(schoolId).then(res => {
        if (res.code === '000000') {
          const lists = res.data && res.data.length > 0 ? res.data : []
          const arrs = []
          lists.forEach(item => {
            const hoursPackage = Math.floor(`${item.balance / 60}`) + '小时' + Math.floor(`${item.balance % 60}`) + '分钟'
            const packageObj = {}
            packageObj['id'] = item.id
            packageObj['valid'] = item.valid
            packageObj['productName'] = `${item.productName}-${hoursPackage}`
            packageObj['productPrice'] = item.productPrice
            arrs.push(packageObj)
          })
          this.packages = arrs
          this.packagesIn = arrs.filter(item => item.valid !== 0)
        }
      }).catch(() => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
    .opera-title{
        font-size: 16px;
        color: #666;
        padding: 15px 0;
        font-weight: normal;
    }
    .transfer-btns{
        margin-left: 95px;
    }
/deep/ .transfer-btns .el-button--small{
    padding: 9px 40px;
}
.transfer-title{
    font-weight: normal;
    span{
        font-size: 18px;
        color: #666;
    }
    em{
        font-size: 12px;
        color: red;
    }
}
/deep/ .transfer-btns .package-tips{
    padding-bottom: 10px;
    font-size: 14px;
    color: #666;
}
</style>
<style>
.el-dialog__body{
        padding: 10px 20px 30px 20px;
    }
    .el-message-box--center .el-message-box__content{
        padding-left: 0px !important;
    padding-right: 0px !important;
    }
</style>
