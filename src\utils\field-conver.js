/**
 * 跟进模板
 * @type {*[]}
 */
export const followTemplateList = [
  {
    value: '0',
    label: '上门拜访',
    desc: '1.拜访的结果:\n2.下一步的跟进动作:\n3.需要的支持:'
  },
  {
    value: '1',
    label: '电话拜访',
    desc: '1.沟通的结果:\n2.下一步的跟进动作:\n3.需要的支持:'
  },
  {
    value: '2',
    label: '企微/微信聊天',
    desc: '1.沟通的结果:\n2.下一步的跟进动作:\n3.需要的支持:'
  },
  {
    value: '3',
    label: '会议邀约',
    desc: '1.邀约的结果:\n2.下一步的跟进动作:\n3.需要的支持:'
  },
  {
    value: '4',
    label: '特殊政策',
    desc: '1.客户的要求:\n2.政策的内容:\n3.需要的支持:'
  }
]
/**
 * 信息来源
 *
 * @type {*[]}
 */
export const originList = [{
  value: 1,
  label: '第三方购买'
},
{
  value: 2,
  label: '官网采集'
},
{
  value: 3,
  label: '地推采集'
},
{
  value: 4,
  label: '合伙人介绍'
},
{
  value: 5,
  label: '省代介绍'
},
{
  value: 6,
  label: '员工介绍'
},
{
  value: 7,
  label: '熟人介绍'
},
{
  value: 8,
  label: '互联网'
},
{
  value: 9,
  label: '微信公众号'
},
{
  value: 10,
  label: '其他'
}, {
  value: 11,
  label: '新媒体部门'
}
]
/**
 * 加盟状态
 * @type {*[]}
 */
export const joinStatusList = [{
  value: 0,
  label: '未加盟',
  type: 'info'
},
{
  value: 1,
  label: '加盟中',
  type: 'warning'
},
{
  value: 2,
  label: '已加盟',
  type: 'success'
},
{
  value: 3,
  label: '已到期',
  type: 'info'
},
{
  value: 4,
  label: '已解约',
  type: 'info'
},
{
  value: 5,
  label: '解约中',
  type: 'info'
}
]

/**
 * 意向度列表
 * @type {*[]}
 */
export const intentionList = [{
  value: 1,
  label: '有意向'
},
{
  value: 2,
  label: '感兴趣'
},
{
  value: 3,
  label: '了解情况'
},
{
  value: 4,
  label: '想代理'
}
]
/**
 * 性别列表
 * @type {*[]}
 */
export const genderList = [{
  value: 0,
  label: '未知'
},
{
  value: 1,
  label: '男'
},
{
  value: 2,
  label: '女'
}
]
/**
 * 当前角色使用状态
 * @type {*[]}
 */
export const getMarriedStatus = [{
  value: 0,
  label: '未婚'
},
{
  value: 1,
  label: '已婚'
}
]
/**
 * 当前角色是否离职
 * @type {*[]}
 */
export const getDimissionStatus = [{
  value: 1,
  label: '离职'
},
{
  value: 0,
  label: '在职'
}
]
/**
 * 当前角色使用状态
 * @type {*[]}
 */
export const roleStatuList = [{
  value: '1',
  label: '可用'
},
{
  value: '0',
  label: '已停用'
}
]
/**
 * 课程习题类型
 * 讲义例题=2、习题作业=1
 * @type {[{label: string, value: string},{label: string, value: string}]}
 */
export const homeworkTestType = [{
  value: '1',
  label: '课后作业'
},
{
  value: '2',
  label: '讲义例题'
}
]

/**
 * 当前菜单的类型 菜单/权限
 * @type {*[]}
 */
export const menuCategoryList = [{
  value: '1',
  label: '菜单'
},
{
  value: '2',
  label: '按钮'
}
]
/**
 * 当前菜单的状态 启用/禁用
 * @type {*[]}
 */
export const menuValidList = [{
  value: 1,
  label: '可用'
},
{
  value: 0,
  label: '禁用'
}
]
/**
 * 校区关联状态
 * @type {*[]}
 */
export const relationList = [{
  value: 1,
  label: '已关联'
},
{
  value: 0,
  label: '未关联'
}
]
/**
 * 校区加盟状态
 * @type {*[]}
 */
export const schoolJoinStatusList = [{
  value: 0,
  label: '未加盟',
  type: 'info'
},
{
  value: 1,
  label: '签约中',
  type: 'warning'
},
{
  value: 2,
  label: '已加盟',
  type: 'success'
},
{
  value: 3,
  label: '已到期',
  type: 'info'
},
{
  value: 4,
  label: '已解约',
  type: 'info'
},
{
  value: 5,
  label: '解约中',
  type: 'info'
},
{
  value: 6,
  label: '已撤销',
  type: 'info'
}
]
/**
 * 打款方式
 * @type {*[]}
 */
export const payMethod = [{
  value: 30,
  label: '其他'
},
{
  value: 27,
  label: '三陶支付宝'
},
{
  value: 28,
  label: '三陶微信'
},
{
  value: 29,
  label: '三陶基本户(527)'
},
{
  value: 66,
  label: '萨赫基本户(619)'
},
{
  value: 65,
  label: '萨赫微信'
},
{
  value: 64,
  label: '萨赫支付宝'
},
{
  value: 63,
  label: '恒提基本户(651)'
},
{
  value: 62,
  label: '恒提微信'
},
{
  value: 61,
  label: '恒提支付宝'
},
{
  value: 60,
  label: '汇付POS机'
},
{
  value: 59,
  label: '富有POS机'
}
]
/**
 * 财务审核状态
 * @type {*[]} 财务未操作，20:待财务确认
 */
export const auditStatus = [{
  value: 10,
  label: '待提交打款记录'
},
{
  value: 20,
  label: '待财务审核'
},
{
  value: 30,
  label: '已到账'
},
{
  value: 40,
  label: '未到账'
},
{
  value: 50,
  label: '已撤销'
}
]
/**
 * 业务类型列表
 * @type {*[]}
 */
export const businessTypeList = [{
  value: 1,
  label: '加盟'
},
{
  value: 2,
  label: '续约'
},
{
  value: 3,
  label: '加盟升级'
},
{
  value: 4,
  label: '复购'
},
{
  value: '5',
  label: '退款'
},
{
  value: '6',
  label: '冲减'
}
]
/**
 * 跟进方式列表 1-电话，2-qq，3-微信聊天，4-微信视频，5-陌拜，6-当面沟通
 * @type {*[]}
 */
export const followStyleList = [{
  value: 1,
  label: '电话'
},
{
  value: 2,
  label: 'qq'
},
{
  value: 3,
  label: '微信聊天'
},
{
  value: 4,
  label: '微信视频'
},
{
  value: 5,
  label: '陌拜'
},
{
  value: 6,
  label: '当面沟通'
}
]
/**
 * 是否独家
 * @type {*[]}
 */
export const getAreaSingle = [{
  value: 0,
  label: '区县单点'
},
{
  value: 1,
  label: '区县独家'
},
{
  value: 2,
  label: '乡镇独家'
},
{
  value: 3,
  label: '乡镇单点'
}
]
/**
 * 合同状态
 * @type {*[]}
 */

export const getContractStatus = [{
  value: 0,
  label: '创建'
},
{
  value: 1,
  label: '保存'
},
{
  value: 2,
  label: '核准&待签署'
},
{
  value: 3,
  label: '已签署'
},
{
  value: 4,
  label: '复编&作废&重新签署'
},
{
  value: 5,
  label: '生效'
},
{
  value: 6,
  label: '解约中'
},
{
  value: 7,
  label: '已解约'
},
{
  value: 8,
  label: '预览'
},
{
  value: 9,
  label: '已撤销'
}
]
/**
 * 客户产品列表
 */
export const productTypeList = [{
  value: 4,
  label: '班型'
},
{
  value: 5,
  label: '设备'
},
{
  value: 6,
  label: '智能终端'
},
{
  value: 7,
  label: '学生账号'
},
{
  value: 8,
  label: '课时'
},
{
  value: 9,
  label: '其他'
}
]
/**
 * 客户产品列表
 */
export const productTypeName = [{
  value: 4,
  label: '课程班型'
},
{
  value: 5,
  label: '硬件设备'
},
{
  value: 6,
  label: '智能终端'
}
]
/**
 * 推荐人渠道
 */
export const channelList = [{
  value: 1,
  label: '三陶员工'
},
{
  value: 4,
  label: '渠道客户'
}

]
/**
 * 政治面貌
 */
export const politicsStatus = [{
  value: 1,
  label: '群众'
},
{
  value: 2,
  label: '党员'
},
{
  value: 3,
  label: '团员'
}
]
/**
 * 健康状况
 */
export const health = [{
  value: 1,
  label: '良好'
},
{
  value: 2,
  label: '欠佳'
}
]
/**
 * 工作性质
 */
export const jobType = [{
  value: 1,
  label: '全职'
},
{
  value: 2,
  label: '兼职'
}
]
/**
 * 户口类型
 */
export const residenceType = [{
  value: 1,
  label: '农村'
},
{
  value: 2,
  label: '城镇'
}
]
/**
 * 订单状态
 * 1 待专员打款 2 财务审核 3 财务审核不通过 4 市场审核 5 市场审核不通过 6 合伙人提交资质
 * 7 运营审批资质 8 运营编辑合同 9 资质不通过 10 待签署 11 已签署 12 已作废
 * 13 已生效 14 解约中 15 已解约 16 解约中 17 已解约 18 已退款 19 待运营专员审批
 * 20 异常 21 创建合同模版，确认合同类型
 */
export const orderStatusList = [{
  value: 1,
  label: '待专员打款',
  type: 'info'
},
{
  value: 2,
  label: '财务审核',
  type: 'info'
},
{
  value: 3,
  label: '财务审核不通过',
  type: 'warning'
},
{
  value: 4,
  label: '市场审核',
  type: 'info'
},
{
  value: 5,
  label: '市场审核不通过',
  type: 'warning'
},
{
  value: 6,
  label: '上传&确认资质',
  type: 'info'
},
{
  value: 7,
  label: '运营确认资质',
  type: 'info'
},
{
  value: 8,
  label: '运营编辑合同',
  type: 'info'
},
{
  value: 9,
  label: '资质不通过',
  type: 'warning'
},
{
  value: 10,
  label: '待签署',
  type: 'info'
},
{
  value: 11,
  label: '已签署',
  type: 'info'
},
{
  value: 12,
  label: '已作废',
  type: 'info'
},
{
  value: 13,
  label: '已生效',
  type: 'success'
},
{
  value: 14,
  label: '错签解约中',
  type: 'info'
},
{
  value: 15,
  label: '错签已解约',
  type: 'info'
},
{
  value: 16,
  label: '校区项目解约中',
  type: 'info'
},
{
  value: 17,
  label: '校区项目已解约',
  type: 'info'
},
{
  value: 18,
  label: '已退款',
  type: 'info'
},
{
  value: 19,
  label: '待运营专员审批',
  type: 'info'
},
{
  value: 20,
  label: '异常',
  type: 'warning'
},
{
  value: 21,
  label: '创建合同模版',
  type: 'info'
},
{
  value: 22,
  label: '已撤销',
  type: 'info'
}
]
/**
 * 订单状态
 */
export const orderStatusActiveList = [{
  value: 1,
  label: '待专员打款'
},
{
  value: 2,
  label: '财务审核'
}, {
  label: '创建合同模版',
  flag: 4

},
{
  value: 6,
  label: '合伙人提交资质'
},
{
  value: 7,
  label: '运营确认资质'
},
{
  value: 8,
  label: '运营编辑合同'
},
{
  value: 10,
  label: '待签署'
},
{
  value: 11,
  label: '已签署'
},
{
  value: 12,
  label: '已作废'
},
{
  value: 13,
  label: '已生效'
},
{
  flag: 14,
  label: '解约状态'
},
{
  value: 18,
  label: '已退款'
},
{
  value: 19,
  label: '待运营专员审批'
}
]
/**
 * 合同类型
 */
export const contractType = [{
  value: 1,
  label: '个人合同'
},
{
  value: 2,
  label: '企业合同'
}
]
/**
 * 合同类别
 */
export const contractClass = [{
  value: 1,
  label: '普通合同'
},
{
  value: 2,
  label: '解约合同'
}
]
/**
 * 代理方式
 */
export const cooperationType = [{
  value: 0,
  label: '请选择区域类型'
},
{
  value: 1,
  label: '独家'
},
{
  value: 2,
  label: '非独家'
}
]
/**
 * 资质审核状态
 */
export const qualificationStatus = [{
  value: -1,
  label: '已填写'
},
{
  value: -2,
  label: '待填写'
},
{
  value: 0,
  label: '待审核'
},
{
  value: 1,
  label: '审核通过'
},
{
  value: 2,
  label: '审核未通过'
}
]
/**
 * 发货类型
 */
export const getShipType = [{
  value: 1,
  label: '合伙人发货'
},
{
  value: 2,
  label: '推荐人发货'
}
]
/**
 * 发货单状态
 */
export const getShipStatus = [{
  value: 1,
  label: '保存'
},
{
  value: 2,
  label: '发货'
},
{
  value: 3,
  label: '修改'
},
{
  value: 4,
  label: '同步播客'
}
]
/**
 * 地址类型
 */
export const getDeliveryAddressType = [{
  value: 1,
  label: '客户'
},
{
  value: 2,
  label: '推荐人'
}
]
/**
 * 合伙人发货状态
 */
export const getDeliverType = [{
  value: -1,
  label: '无需发货'
},
{
  value: 0,
  label: '未发货'
},
{
  value: 1,
  label: '部分发货'
},
{
  value: 2,
  label: '发货完成'
},
{
  value: 3,
  label: '线下发货'
},
{
  value: 4,
  label: '撤回'
}
]
/**
 * 推荐人发货状态
 */
export const getRecDeliveryType = [{
  value: -1,
  label: '无需发货'
},
{
  value: 0,
  label: '未发货'
},
{
  value: 1,
  label: '部分发货'
},
{
  value: 2,
  label: '发货完成'
},
{
  value: 3,
  label: '线下发货'
}
]
/**
 * 是否需要产品
 */
export const productFlag = [{
  value: 1,
  label: '需要'
},
{
  value: 0,
  label: '不需要'
}
]
/**
 * 是否需要企业资质
 */
export const requiredEnterprise = [{
  value: 1,
  label: '需要'
},
{
  value: 0,
  label: '不需要'
}
]
/**
 * 是否共有
 */
export const isCommon = [{
  value: 1,
  label: '是',
  type: 'success'
},
{
  value: 0,
  label: '不是',
  type: 'info'
}
]
export const yesOrNo = [{
  value: 1,
  label: '是',
  type: 'success'
},
{
  value: 0,
  label: '否',
  type: 'info'
}
]
export const styleTags = [
  {
    value: 1,
    label: '教材及子版本',
    type: 'primary'
  },
  {
    value: 2,
    label: '仅教材',
    type: 'warning'

  },
  {
    value: 3,
    label: '无',
    type: 'danger'
  }
]
/**
 * 是否需要物流
 */
export const needDelivery = [{
  value: 1,
  label: '需要'
},
{
  value: 0,
  label: '不需要'
}
]
/**
 * 单位
 */
export const productUnit = [{
  value: 1,
  label: '小时'
},
{
  value: 0,
  label: '个数'
}
]

/**
 * mvp信息来源
 */
export const customerSource = [{
  value: 0,
  label: '官方推广'
}, {
  value: 1,
  label: '校区分享'
}]

/**
 * mvp付费状态
 */
export const paymentStatus = [{
  value: 1,
  label: '已付费'
}, {
  value: 0,
  label: '未付费'
}]
/**
 * mvp指派状态
 */
export const assignSatuts = [{
  value: 1,
  label: '是'
}, {
  value: 0,
  label: '否'
}]
/**
 * mvp结算状态
 */
export const settlements = [{//
  value: 1,
  label: '已结算'
}, {
  value: 0,
  label: '待结算'
}]
/**
 * mvp邮寄状态
 */
export const postType = [{
  value: 1,
  label: '已发货'
}, {
  value: 0,
  label: '未发货'
}]
/**
 * 区域等级
 */
export const grades = [
  {
    value: 0,
    label: '--'
  }, {
    value: 1,
    label: '省'
  },
  {
    value: 2,
    label: '市'
  }, {
    value: 3,
    label: '区县'
  }, {
    value: 4,
    label: '乡镇'
  }
]
/**
 *班型状态
 */
export const classType = [{
  label: '待审核',
  value: 0
},
{
  label: '已上架',
  value: 1
},
{
  label: '已下架',
  value: 2
}]
/**
 *是否跨区校验
 */
export const checkAddress = [{
  label: '否',
  value: 0
},
{
  label: '是',
  value: 1
}]
/**
 *通用状态
 */
export const enableList = [{
  label: '启用',
  value: 1,
  type: 'success'
},
{
  label: '禁用',
  value: 99,
  type: 'info'
}]
/**
 *计时规则，默认计时 0:免费，1:计时
 */
export const billingType = [{
  label: '计时',
  value: 1
},
{
  label: '免费',
  value: 0
}]
/**
 *是否同步至华为云(0：否，1.是)
 */
export const isSynHuawei = [{
  label: '已同步',
  value: '1'
},
{
  label: '未同步',
  value: '0'
}]
/**
 *显示状态(0：否，1.是)
 */
export const isShow = [{
  label: '显示',
  value: 1
},
{
  label: '隐藏',
  value: 0
}]
/**
 *状态 0:未上架，1:已上架，2：已下架
 */
export const statusList = [{
  label: '已上架',
  value: 1
},
{
  label: '待审核',
  value: 0
}, {
  label: '已下架',
  value: 2
}]
/**
 *状态  1创建 2修改 3 同步华为云 4停用 5启用 6 推荐 7取消推荐
 */
export const operateTypes = [{
  label: '创建',
  value: 1
},
{
  label: '修改',
  value: 2
}, {
  label: '同步华为云',
  value: 3
}, {
  label: '停用',
  value: 4
}, {
  label: '启用',
  value: 5
},
{
  label: '推荐',
  value: 6
}, {
  label: '取消推荐',
  value: 7
}]
/**
 *app更新状态： 1非强制性，2强制性
 */
export const updateTypes = [{
  label: '非强制性',
  value: 1
},
{
  label: '强制性',
  value: 2
}]
/**
 *机构充值： 支付类型1 支付宝 2 微信 3手动 4 0元订单
 */
export const institutionsPayType = [{
  label: '支付宝',
  value: 1
},
{
  label: '微信',
  value: 2
}, {
  label: '手动',
  value: 3
}, {
  label: '0元订单',
  value: 4
}]

/**
 *盒子总览状态 1正常，2禁用，3临时启用，99停用
 */
export const statusLists = [{
  label: '正常',
  value: 1
},
{
  label: '禁用',
  value: 2
}]
/**
 *盒子跨区状态 是否开放[0:不开放,关闭跨区域，1:开放,开启跨区域],(判断是否需要扫码）
 */
export const regionalStatusLists = [{
  label: '禁止跨区',
  value: 0
},
{
  label: '允许跨区',
  value: 1
}]
/**
 *消息是否显示
 */
export const isShows = [{
  label: '是',
  value: 1
},
{
  label: '否',
  value: 0
}]
/**
 *播放源设置 0：默认，1：华为云，2：阿里云高清，3：阿里云标清
 */
export const playSources = [
  {
    label: '默认',
    value: 0
  },
  {
    label: '华为云',
    value: 1
  }, {
    label: '阿里云高清',
    value: 2
  }, {
    label: '阿里云标清',
    value: 3
  }]
/**
 *校区管理智能终端状态 1正常，2跨区域禁用，3临时启用，99停用
 */
export const intelligentStatus = [
  {
    label: '正常',
    value: 1
  }, {
    label: '跨区域禁用',
    value: 2
  }]
export const admissionStatus = [
  {
    label: '禁用',
    value: 0
  }, {
    label: '启用',
    value: 1
  }
]
/**
 *机构意见反馈问题类型,1:运营,2:市场,3,课程,4:系统,5:智能终端,6:三陶AI,7:其他
 */
export const questionCategory = [
  {
    label: '运营',
    value: 1
  }, {
    label: '市场',
    value: 2
  },
  {
    label: '课程',
    value: 3
  },
  {
    label: '系统',
    value: 4
  },
  {
    label: '智能终端',
    value: 5
  },
  {
    label: '三陶AI',
    value: 6
  },
  {
    label: '其他',
    value: 7
  }
]
/**
 *机构意见反馈类型;0:反馈,1:建议,2:投诉
 */
export const feedbackList = [
  {
    label: '反馈',
    value: 0
  }, {
    label: '建议',
    value: 1
  },
  {
    label: '投诉',
    value: 2
  }
]
/**
 *机构意见反馈状态 0待处理 1,已处理,2:已解决,3,已关闭
 */
export const feedbackStatus = [
  {
    label: '待处理',
    value: 0
  }, {
    label: '已处理',
    value: 1
  },
  {
    label: '已解决',
    value: 2
  },
  {
    label: '已关闭',
    value: 3
  }
]
/**
 * 加盟类型 joinType  0-A类全科 1-A类文科 2-A类理科 3-B类单科 4-C类单科
 */
export const joinTypes = [{
  value: 0,
  label: 'A类全科'
},
{
  value: 1,
  label: 'A类文科'
}, {
  value: 2,
  label: 'A类理科'
}, {
  value: 3,
  label: 'B类单科'
},
{
  value: 4,
  label: 'C类单科'
}
]
/**
 * 合同类型
 */
export const certType = [// 0:身份证 1:护照号 B 港澳居民来往内地通行证号 C 台湾居民来往大陆通行证号
  {
    value: '0',
    label: '身份证'
  },
  {
    value: '1',
    label: '护照号'
  },
  {
    value: '2',
    label: '港澳居民来往内地通行证号'
  },
  {
    value: '4',
    label: '台湾居民来往大陆通行证号'
  }
]
export const principalType = [// 0:身份证 1:护照号 B 港澳居民来往内地通行证号 C 台湾居民来往大陆通行证号
  {
    value: '1',
    label: '法人'
  },
  {
    value: '2',
    label: '代理人'
  }
]
export const businessList = [ // 业务类型[1:加盟,2:续费, 3:加盟升级, 4:班型复购]
  {
    value: '1',
    label: '加盟'
  },
  {
    value: '2',
    label: '续费'
  },
  {
    value: '3',
    label: '加盟升级'
  },
  {
    value: '4',
    label: '复购'
  },
  {
    value: '5',
    label: '退款'
  },
  {
    value: '6',
    label: '冲减'
  }
]
export const institutionTypes = [ // 机构类型[1:加盟,2:续费, 3:加盟升级, 4:班型复购]
  {
    value: '0',
    label: '培训机构'
  },
  {
    value: '1',
    label: '公办校'
  },
  {
    value: '3',
    label: '特区'
  },
  {
    value: '4',
    label: '省代'
  },
  {
    value: '7',
    label: '市代'
  },
  {
    value: '5',
    label: '赠送'
  },
  {
    value: '6',
    label: '测试'
  },
  {
    value: '8',
    label: '复购账号'
  }
]
/**
 * 运营经理的状态 启用/禁用
 * @type {*[]}
 */
export const operatorsStatus = [{
  value: 1,
  label: '可用'
},
{
  value: 99,
  label: '禁用'
}
]
/**
 * 课程渠道
 * @type {*[]}
 */
export const courseSource = [{
  value: 1,
  label: '三陶'
},
{
  value: 2,
  label: '奇速'
}
]
export const versionTypes = [{
  value: 1,
  label: '正式版'
},
{
  value: 2,
  label: '预签版'
},
{
  value: 3,
  label: '抢分'
},
{
  value: 4,
  label: '特色班型'
},
{
  value: 5,
  label: '烨晨市代'
},
{
  value: 6,
  label: '烨晨渠道'
},
{
  value: 7,
  label: '高考绝招班'
},
{
  value: 8,
  label: '招生服务合同'
},
{
  value: 9,
  label: '特色班型合同'
},
{
  value: 10,
  label: '变更合同'
}
]

// 普高经营班型列表
export const pgBusinessClassTypeList = [{
  key: 1,
  value: '命题猜想班'
},
{
  key: 2,
  value: '高考绝招班'
}
]

// 烨晨双师经营班型列表
export const ycBusinessClassTypeList = [{
  key: 1,
  value: '中考抢分班'
},
{
  key: 2,
  value: '中考绝招班'
},
{
  key: 3,
  value: '中考命题猜想班'
}
]
export const courseMgrSyncStatus = [{
  key: 0,
  value: '未同步'
},
{
  key: 1,
  value: '已同步'
}]

export const flatStatus = [{// 平板状态 1正常，2跨区域禁用，3临时启用，99停用
  key: 1,
  value: '正常'
},
{
  key: 2,
  value: '跨区域禁用'
},
{
  key: 3,
  value: '临时启用'
},
{
  key: 99,
  value: '停用'
}
]
export const customerTypeList = [{
  key: 1,
  value: '意向客户'
},
{
  key: 2,
  value: '续存客户'
},
{
  key: 3,
  value: '到期客户'
}
]
export const customerLevelList = [{
  key: 1,
  value: 'S级'
},
{
  key: 2,
  value: 'A级'
},
{
  key: 3,
  value: 'B级'
}
]
// 内部需求状态
export const requirementsStatusList = [{
  key: '0',
  value: '待处理'
},
{
  key: '1',
  value: '处理中'
},
{
  key: '2',
  value: '已上线'
},
{
  key: '3',
  value: '不处理'
}
]
/**
 * 生命周期
 * @type {[{}]}
 */
export const lifeCycleList = [
  {
    value: 'A',
    label: '筹备期',
    children: [
      {
        value: 'A1',
        label: '选址阶段'
      },
      {
        value: 'A2',
        label: '装修阶段'
      },
      {
        value: 'A3',
        label: '招聘阶段'
      },
      {
        value: 'A4',
        label: '人员培训阶段'
      }
    ]
  },
  {
    value: 'B',
    label: '启动期',
    children: [
      {
        value: 'B1',
        label: '试营业阶段'
      },
      {
        value: 'B2',
        label: '开业阶段'
      },
      {
        value: 'B3',
        label: '启动情况-耗时衡量(100h)'
      }
    ]
  },
  {
    value: 'C',
    label: '稳定期'
  },
  {
    value: 'D',
    label: '裂变期'
  }
]
export function GetRound(num, len) { // 四舍五入
  return Math.round(num * Math.pow(10, len)) / Math.pow(10, len)
}
/**
 * 用来将key：，value：，形式的中英文字段（主要是筛选框）进行转化
 * @param list 用来比较的数据源
 * @param compareValue 需要备转换的值， 如传入1，则输出：已加盟
 * @param key 被比较的字段，如： joinStatusList中， 默认为value 即：item[value]
 * @param printLabel 输出的字段，如：输出已加盟 对应的是 label
 * @returns {*} 如果这个数组里的对象被取出来，则输出label  如果没有取出来，则输出这个key
 */
export function converseEnToCn(list, compareValue, key, printLabel) {
  const beCompare = (compareValue === null || compareValue === undefined) ? '--' : compareValue + ''
  // 获取 list对象数组里 含有 compareValue 的那个数组 res[0] 取出这个对象
  const res = list.filter(item => (item[key || 'value'] + '') === beCompare)
  return res[0] ? res[0][printLabel || 'label'] : beCompare
}

export function converseEnToCnList(list, compareValue, key, printLabel) {
  const beCompare = (compareValue === null || compareValue === undefined) ? '--' : compareValue + ''
  // 获取 list对象数组里 含有 compareValue 的那个数组 res[0] 取出这个对象
  const res = list.filter(item => (item[key || 'id'] + '') === beCompare)
  return res[0] ? res[0][printLabel || 'itemName'] : beCompare
}

export function converseEnToCode(list, compareValue, key, printLabel) {
  const beCompare = (compareValue === null || compareValue === undefined) ? '--' : compareValue + ''
  // 获取 list对象数组里 含有 compareValue 的那个数组 res[0] 取出这个对象
  const res = list.filter(item => (item[key || 'code'] + '') === beCompare)
  return res[0] ? res[0][printLabel || 'name'] : beCompare
}
