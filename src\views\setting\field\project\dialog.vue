<template>
  <el-dialog v-el-drag-dialog title="项目详情" :visible.sync="projectDialog" :close-on-click-modal="!projectDialog" width="50%">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="140px"
      :rules="baseInfoRules"
    >
      <el-row>
        <el-col :xs="24" :sm="12">
          <el-form-item label="项目名称：" prop="projectName">
            <el-input v-model="detail.projectName" placeholder="项目名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="是否需要产品：" prop="productFlag">
            <el-select v-model="detail.productFlag" placeholder="是否需要产品" filterable clearable style="width: 100%">
              <el-option
                v-for="item in productFlag"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="必须企业资质：" prop="requiredEnterprise">
            <el-select v-model="detail.requiredEnterprise" placeholder="必须企业资质" filterable clearable style="width: 100%">
              <el-option
                v-for="item in requiredEnterprise"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="是否需要开通机构：" prop="agencyFlag">
            <el-select v-model="detail.agencyFlag" placeholder="是否需要开通机构" filterable clearable style="width: 100%">
              <el-option
                v-for="item in isOpenAccount"
                :key="item.id"
                :label="item.val"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24">
          <el-form-item label="备注："><el-input v-model="detail.remark" placeholder="备注：" /></el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmProjectDetail">确 定</el-button>
      <el-button @click="closeProjectDetail">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addProject, editProject } from '@/api/system-setting'
import { productFlag, requiredEnterprise } from '@/utils/field-conver'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'ProjectDialog',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      projectDialog: false,
      detail: {},
      baseInfoRules: {
        projectName: { required: true, message: '项目名称必填', trigger: 'blur' },
        productFlag: { required: true, message: '是否需要产品必选', trigger: 'blur' },
        requiredEnterprise: { required: true, message: '必须企业资质必选', trigger: 'blur' }
      },
      productFlag: productFlag,
      requiredEnterprise: requiredEnterprise,
      isOpenAccount: [
        {
          id: 0,
          val: '不需要'
        },
        {
          id: 1,
          val: '需要'
        }
      ]
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      const that = this
      that.detail = {}
      that.projectDialog = true
    },
    editDetail(data) {
      const that = this
      that.detail = data
      that.projectDialog = true
    },
    /**
     * 确认修改信息
     */
    confirmProjectDetail() {
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!that.isEdit) {
            addProject(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '新增成功！',
                  type: 'success'
                })
              }
              that.projectDialog = false
              that.$emit('refresh')
            })
          } else {
            editProject(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              }
              that.projectDialog = false
              that.$emit('refresh')
            })
          }
        }
      })
    },
    closeProjectDetail() {
      this.projectDialog = false
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
</style>
