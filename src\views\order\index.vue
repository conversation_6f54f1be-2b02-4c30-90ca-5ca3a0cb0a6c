<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.orderId"
        placeholder="充值订单编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.schoolId"
        placeholder="机构Id"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.queryInfo"
        placeholder="合伙人/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.schoolName"
        placeholder="校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.productTypeName"
        placeholder="流量包名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.productId" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.payType" placeholder="支付类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in institutionsPayType" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="充值开始时间"
        end-placeholder="充值结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="充值订单编号" show-overflow-tooltip prop="orderId" width="250px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.orderId }}</a>
        </template>
      </el-table-column>
      <af-table-column label="机构Id" prop="schoolId" show-overflow-tooltip />
      <af-table-column label="合伙人(机构账号)" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.partnerName&&scope.row.partnerAccount">{{ scope.row.partnerName }}({{ scope.row.partnerAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="schoolName" show-overflow-tooltip />
      <af-table-column label="产品线" prop="productName" />
      <af-table-column label="流量包名称" prop="productTypeName" />
      <af-table-column label="充值课时/分钟" prop="chargeNumber">
        <template slot-scope="scope">
          <span v-if="scope.row.chargeNumber!==null">{{ scope.row.chargeNumber }}分钟</span>
        </template>
      </af-table-column>
      <af-table-column label="支付类型" prop="payTypeName" />
      <af-table-column label="支付金额/元" prop="orderPrice" />
      <af-table-column label="备注" prop="remark" />
      <af-table-column label="充值时间" prop="chargeTime" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { clientCode } from '@/api/classType'
import { getCharge } from '@/api/charge'
import { institutionsPayType } from '@/utils/field-conver'
export default {
  name: 'OrderList',
  components: {
    Pagination
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      institutionsPayType: institutionsPayType,
      followDate: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 1) || []
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { createBeginTime: this.followDate[0] ? this.followDate[0] : '', createEndTime: this.followDate[1] ? this.followDate[1] : '' })
      await getCharge(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    getStatus(row) {
      // return converseEnToCn(this.statusList, row.status)
    },
    getDetail(row) { // 获取列表详情

    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
