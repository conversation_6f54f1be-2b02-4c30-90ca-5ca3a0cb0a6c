<template>
  <div class="questions">
    <div class="questions-head">
      <span>
        <em v-if="questionList&&questionList.length>0" class="questions-title">{{ name }}</em>
        <em v-else class="questions-title">试题</em>
      </span>
      <el-tag v-if="subjectName" type="success" class="subject-name">{{ subjectName }}</el-tag>
      <el-tag type="danger">{{ clientName }}</el-tag>
      <el-button v-permission="['admissions:subjects:addQuestion']" style="float: right;" size="mini" type="primary" @click="addQuestions">
        添加试题
      </el-button>
    </div>
    <div>
      <draggable v-model="questionList" :animation="500" @update="moveQuestions" @start="moveStart" @end="moveEnd">
        <transition-group name="list">
          <!-- 过度效果 -->
          <div v-for="(item,index) in questionList" id="questions-choice" :key="item.id" class="questions-list">
            <div class="questions-list-title">
              <div>
                <span class="questions-title-left">
                  <em>第</em>
                  <em>{{ index+1 }}</em>
                  <em>题</em>
                </span>
                <el-tag type="warning">选择题-{{ item.viewType===1?'批量导入':'普通新增' }}</el-tag>
              </div>
              <div class="questions-btns">
                <el-button v-permission="['admissions:subjects:sort']" type="default" size="mini">拖动排序</el-button>
                <el-button v-permission="['admissions:subjects:remove']" type="danger" size="mini" @click="remove(item)">移除</el-button>
              </div>
            </div>
            <div v-show="item.isShow" class="questions-knowledge">
              <h2 v-if="item.question&&item.viewType!==1">{{ item.question }}</h2>
              <h2 v-if="item.question&&item.viewType===1" v-html="item.question" />
              <img v-if="item.questionImage&&item.questionImage!==null" :src="item.questionImage">
              <div class="questions-knowledge-list"><i>知识点:</i><el-tag v-for="(itemKnowledge,i) in item.keynoteList" :key="i" type="info" size="mini">{{ itemKnowledge }}</el-tag></div>
            </div>
          </div>
        </transition-group>
      </draggable>
    </div>
    <p v-show="questionList.length===0" class="no-data">暂无试题</p>
    <question-pop ref="addQuestion" @getQuestions="getQuestionDetail" />
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import QuestionPop from './questionPop'
import { getExamPaperIncludeQuestions, removeQuestion, sortQuestion } from '@/api/admissions'
import { converseEnToCode } from '@/utils/field-conver'
export default {
  name: 'QuestionsDetail',
  components: {
    draggable,
    QuestionPop
  },
  data() {
    return {
      name: '',
      ids: '',
      subjectName: '',
      questionList: [],
      subjectId: '',
      clientCode: null,
      productCodeList: [],
      clientName: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.name = this.$route.params.name
      this.productCodeList = this.$route.params.productCodeList
      this.setTagsViewTitle(localStorage.getItem('paperName'))
      this.ids = this.$route.params.id
      this.getQuestionDetail(localStorage.getItem('ids'))
    })
  },
  methods: {
    setTagsViewTitle(name) {
      const cRoute = Object.assign({}, this.$route)
      const title = '试卷试题'
      const route = Object.assign({}, cRoute, { title: `${title}-${name}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getQuestionDetail(ids) { // 获取试卷详情 包括关联的试题
      getExamPaperIncludeQuestions(ids).then(res => {
        if (res.code === '000000') {
          const arrs = res.data.questionList
          const arrList = []
          for (let i = 0; i < arrs.length; i++) {
            const objs = {}
            objs['id'] = arrs[i].id
            objs['question'] = arrs[i].question
            objs['questionImage'] = arrs[i].questionImage != null ? arrs[i].questionImage : ''
            objs['keynoteList'] = arrs[i].keynoteList.length > 0 ? arrs[i].keynoteList : []
            objs['isShow'] = true
            objs['viewType'] = arrs[i].viewType
            arrList.push(objs)
          }
          this.questionList = arrList
          this.name = res.data.name
          this.ids = res.data.id
          this.clientCode = res.data.clientCode
          this.subjectId = res.data.subjectId
          this.subjectName = `${res.data.subjectName}试卷`
          this.clientName = this.getClientCode(this.clientCode)
          this.$nextTick(() => {
            if (this.commonsVariable.isMathjaxConfig) {
              this.commonsVariable.initMathjaxConfig()
            }
            this.commonsVariable.MathQueue('questions-choice')
          })
        }
      }).catch(() => {

      })
    },
    remove(row) { // 移除
      this.$confirm('确定要移除此条数据吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.ids && row.id) {
          removeQuestion(this.ids, row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '移除成功'
              })
              this.getQuestionDetail(this.ids)
            }
          }).catch(() => {

          })
        }
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    moveQuestions(ele) {
      const index = ele.newIndex
      this.questionList[index].isShow = false
    },
    moveStart(ele) {
      const index = ele.oldIndex
      this.questionList[index].isShow = false
    },
    moveEnd(ele) {
      const index = ele.newIndex
      this.questionList[index].isShow = true
      const questionIdList = this.questionList.map(item => item.id)

      if (this.ids && questionIdList.length > 0) {
        const params = Object.assign({}, { paperId: this.ids, questionIdList: questionIdList })
        sortQuestion(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '排序成功'
            })
            this.getQuestionDetail(this.ids)
          }
        }).catch(() => {

        })
      }
    },
    addQuestions() {
      this.$refs.addQuestion.questionIshow = true
      this.$refs.addQuestion.getQuestionOutline(this.subjectId)
      this.$refs.addQuestion.addQuestionTitle = `${this.name}-${this.subjectName}`
      if (this.ids && this.subjectId) {
        this.$refs.addQuestion.paperId = this.ids
        this.$refs.addQuestion.subjectId = this.subjectId
        this.$refs.addQuestion.clientCode = this.clientCode
        this.$refs.addQuestion.getList(this.ids, this.subjectId, this.clientCode)
      }
    },
    getClientCode(code) {
      return converseEnToCode(this.productCodeList, code)
    }
  }
}
</script>

<style scoped lang="scss">
  .subject-name{
    margin-left: 10px;
  }
  .list-item {
    display: inline-block;
    margin-right: 10px;
  }
  .list-enter-active, .list-leave-active {
    transition: all 1s;
  }
  .list-enter, .list-leave-to
    /* .list-leave-active for below version 2.1.8 */ {
    opacity: 0;
    transform: translateY(30px);
  }
</style>
