<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" :rules="comRules" label-width="130px" :disabled="!isEdit">
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" />
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约期限：" label-width="110px">
                    <span v-if="detail.contractOrder.signStartTime">{{ detail.contractOrder.signStartTime }}</span>
                    <span v-if="detail.contractOrder.signEndTime">-{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="22">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="合同编号：" label-width="130px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:13}">
                  <el-form-item label="合同名称:" label-width="160px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="加盟项目：" label-width="130px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:13}">
                  <el-form-item label="区域类型:" required label-width="160px">
                    <el-radio-group v-model="detail.cooperationType" disabled>
                      <el-radio :label="1">区县独家</el-radio>
                      <el-radio :label="0">区县单点</el-radio>
                      <el-radio :label="2">乡镇独家</el-radio>
                      <el-radio :label="3">乡镇单点</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="130px">
                    <el-radio-group v-model="detail.contractType" class="radios" :disabled="detail.orderStatus!==21">
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本:" label-width="160px">
                    <div>{{ versionTypeStatus }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}" :sm-offset="12">
                  <el-form-item label="我方签约主体：">
                    <div>{{ detail.contractOrder.signPartyName || '暂无'}}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="经营班型：" prop="businessClassType">
                    <el-select v-model="detail.specialClassTypeContract.classTypeName" clearable placeholder="请选择班型" class="filter-item" style="width: 100%" :disabled="!isEdit" filterable>
                      <el-option
                        v-for="item in businessClassTypeList"
                        :key="item.id"
                        :label="item.title"
                        :value="item.title"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:13}">
                  <el-form-item label="合同期限:" label-width="160px" required>
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="合作费/元：" prop="specialClassTypeContract.cooperationFee">
                    <el-input v-model="detail.specialClassTypeContract.cooperationFee" :placeholder="comRules.specialClassTypeContract.cooperationFee.message" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:13}">
                  <el-form-item label="流量费(元/小时):" label-width="160px" prop="specialClassTypeContract.trafficFee">
                    <el-input v-model="detail.specialClassTypeContract.trafficFee" :placeholder="comRules.specialClassTypeContract.trafficFee.message" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 15px">
                <el-col :xs="{span:24}" :sm="{span:11}">
                  <el-form-item label="下线日期：" required>
                    <el-date-picker
                      v-model="detail.specialClassTypeContract.offlineDate"
                      style="width: 100%"
                      type="date"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd"
                      :disabled="!isEdit"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:13}">
                  <el-form-item label="智能终端数:" label-width="160px" prop="specialClassTypeContract.deviceNumber">
                    <el-input v-model="detail.specialClassTypeContract.deviceNumber" :placeholder="comRules.specialClassTypeContract.deviceNumber.message" type="number" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:13}" :offset="11">
                  <el-form-item label="智能终端价格(元/台):" label-width="160px" prop="specialClassTypeContract.devicePrice">
                    <el-input v-model="detail.specialClassTypeContract.devicePrice" :placeholder="comRules.specialClassTypeContract.devicePrice.message" type="number" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :xs="{span:24}" :sm="{span:24}">
                <el-form-item label="补充条款：">
                  <!--补充条款内容-->
                  <el-input v-model="detail.specialClassTypeContract.supplementInfo" type="textarea" placeholder="请输入补充条款内容" maxlength="300" show-word-limit />
                </el-form-item>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>
  </div>
</template>

<script>
import {
  getContractDetail,
  modifyContractDetail,
  getCreatContract,
  getClassTypes
} from '@/api/contract'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  orderStatusList,
  contractClass,
  getContractStatus,
  versionTypes
  // pgBusinessClassTypeList,
  // ycBusinessClassTypeList
} from '@/utils/field-conver'

export default {
  name: 'Features',
  data() {
    const businessClassTypeValid = (rule, value, callback) => {
      if (!this.detail.specialClassTypeContract.classTypeName) {
        return callback(new Error('经营班型是必填项'))
      } else {
        callback()
      }
    }
    return {
      detail: {
        specialClassTypeContract: {}
      },
      // 经营班型列表
      businessClassTypeList: [],
      // 当前选择的经营班型名称
      classTypeName: '',
      comRules: {
        // 合同类型
        contractType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        // 区域类型
        cooperationType: {
          required: true,
          message: ' ',
          trigger: 'change'
        },
        // 经营班型
        businessClassType: {
          required: true,
          validator: businessClassTypeValid,
          message: ' ',
          trigger: 'change'
        },
        // 特色班型合同 其他必填信息
        specialClassTypeContract: {
          cooperationFee: {
            required: true,
            message: '请输入合作费/元',
            trigger: 'blur'
          },
          trafficFee: {
            required: true,
            message: '请输入流量费（元/小时）',
            trigger: 'blur'
          },
          deviceNumber: {
            required: true,
            message: '请输入赠送智能终端数',
            trigger: 'blur'
          },
          devicePrice: {
            required: true,
            message: '请输入智能终端价格',
            trigger: 'blur'
          }
        }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      projectId: 0,
      timeArr: [],
      versionTypeStatus: null
    }
  },
  watch: {
    $route(to, from) {
      this.$router.go(0)
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    this.projectId = this.$route.query.projectId
    // this.businessClassTypeList = this.projectId === 1 ? pgBusinessClassTypeList : ycBusinessClassTypeList
    if (this.isEdit) {
      // 编辑合同  根据项目ID获取班型信息
      this.getClassTypes()
    }
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const title = this.projectId === 1 ? '三陶普高特色班型' : '烨晨双师特色班型'
    const tagsName = this.isEdit ? '编辑合同' : '合同详情'
    this.setTagsViewTitle(title + '-' + tagsName)
  },
  mounted() {

  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    // 根据项目ID获取班型信息
    getClassTypes() {
      const that = this
      const data = this.projectId
      getClassTypes(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.businessClassTypeList = that.detail
      })
    },
    getDetail() { // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.specialClassTypeContract !== null) {
          this.detail.specialClassTypeContract = res.data.specialClassTypeContract
        } else {
          this.detail.specialClassTypeContract = {
            cooperationFee: 10000,
            trafficFee: 1.5,
            deviceNumber: 3,
            devicePrice: 820,
            offlineDate: '', // 下线日期字段
            classTypeName: '', // 经营班型字段
            supplementInfo: ''// 补充条款字段
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = that.id
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
        that.versionTypeStatus = that.detail.versionType ? converseEnToCn(versionTypes, that.detail.versionType) : null
      })
    },
    getCreatContract() { // 从合同列表点进到编辑合同页面
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        this.detail.aiContract = res.data.aiContract || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        if (res.data.specialClassTypeContract !== null) {
          this.detail.specialClassTypeContract = res.data.specialClassTypeContract
        } else {
          this.detail.specialClassTypeContract = {
            cooperationFee: 10000,
            trafficFee: 1.5,
            deviceNumber: 3,
            devicePrice: 820,
            offlineDate: '', // 下线日期字段
            classTypeName: '', // 经营班型字段
            supplementInfo: ''// 补充条款字段
          }
        }
        this.detail.revokeContract = res.data.revokeContract || {}
        this.detail.txtContract = res.data.txtContract || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = res.data.contractId
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
        that.versionTypeStatus = that.detail.versionType ? converseEnToCn(versionTypes, that.detail.versionType) : null
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
       * 确认修改信息
       */
    confirmEdit(num) {
      const that = this
      if (!that.detail.contractType) {
        that.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!that.timeArr || (that.timeArr && that.timeArr.length < 2)) {
        that.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.classTypeName) {
        that.$message({
          type: 'warning',
          message: '经营班型必选!'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.cooperationFee) {
        that.$message({
          type: 'warning',
          message: '合作费不能为空!'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.trafficFee) {
        that.$message({
          type: 'warning',
          message: '流量费不能为空!'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.offlineDate) {
        that.$message({
          type: 'warning',
          message: '下线日期必选!'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.deviceNumber) {
        that.$message({
          type: 'warning',
          message: '智能终端数不能为空!'
        })
        return
      }
      if (!that.detail.specialClassTypeContract.devicePrice) {
        that.$message({
          type: 'warning',
          message: '智能终端价格不能为空!'
        })
        return
      }
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          const data = Object.assign(that.detail, {
            operateType: num,
            startTime: that.timeArr[0],
            endTime: that.timeArr[1],
            specialClassTypeContractDTO: that.detail.specialClassTypeContract
          })
          that.$confirm('提交后合同类型不能修改，确认提交吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            modifyContractDetail(data).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '编辑成功!'
                })
                this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                  this.$router.go(-1)
                })
              }
            })
          }).catch(() => {
            that.$message({
              type: 'info',
              message: '已取消提交'
            })
          })
        }
      })
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }

  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
</style>
