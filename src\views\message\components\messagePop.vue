<template>
  <el-dialog :visible.sync="messagePop" :title="messageTitle" :close-on-click-modal="!messagePop" width="60%" @close="cancelClass">
    <div class="assing-info">
      <el-form ref="messageForm" :model="listQuery" :rules="rules" label-width="100px">
        <el-form-item label="消息标题" prop="title">
          <el-input v-model="listQuery.title" placeholder="请输入标题" maxlength="100" />
        </el-form-item>
        <el-form-item label="客户端" prop="clientCode">
          <el-select filterable v-model="listQuery.clientCode" placeholder="请选择客户端" clearable class="filter-item" @change="getClientCode">
            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息类型" prop="messageType">
          <el-select filterable v-model="listQuery.messageType" placeholder="请选择消息类型" clearable class="filter-item" >
            <el-option v-for="item in messageTypes" :key="item.code" :label="item.name" :value="Number(item.code)" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="rangeShow()" label="显示范围" prop="messageScope" required>
          <el-radio-group v-model="listQuery.messageScope" @change="refreshScope">
            <el-radio :label="3">全部校区</el-radio>
            <el-radio :label="4">总校</el-radio>
            <el-radio :label="5">分校</el-radio>
          </el-radio-group>
        </el-form-item>
       
        <el-form-item label="输入链接" prop="detailsPath">
          <el-input v-model="listQuery.detailsPath" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="显示状态" prop="isShow">
          <el-radio-group v-model="listQuery.isShow">
            <el-radio :label="0">隐藏</el-radio>
            <el-radio :label="1">显示</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 是否必读 -->
        <el-form-item label="是否必读" prop="mustRead" required>
          <el-switch v-model="listQuery.mustRead" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="有效时间">
          <el-date-picker
            v-model="followDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="有效开始时间"
            end-placeholder="有效结束时间"
          />
        </el-form-item>

      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="messagePop=false,cancelClass()">取消</el-button>
      <!--新增消息-->
      <el-button v-if="flags===1" type="primary" size="mini" @click="confirmMessage">确定</el-button>
      <!--修改消息-->
      <el-button v-if="flags===0" type="primary" size="mini" @click="editMessage">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// var ObsClient = require('esdk-obs-nodejs')
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { clientCode } from '@/api/classType'
import { messType, messageDetail, addMessage, updateMessage } from '@/api/message'
import { uploadSuccess } from '@/api/common'
import { getClientList } from '../common'
export default {
  name: 'MessagePop',
  data() {
    return {
      messagePop: false,
      messageTitle: '',
      listQuery: {
        detailsType: '2',
        messageScope: 3,
        mustRead: 1,
        messageType: ''
      },
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入消息标题' },
        clientCode: { required: true, trigger: 'blur', message: '请选择消息展示客户端' },
        message: { required: true, trigger: 'blur', message: '请输入消息简介' },
        detailsType: { required: true, trigger: 'blur', message: '请选择详情类型' },
        messageType: { required: true, trigger: 'blur', message: '请选择消息类型' },
        isShow: { required: true, trigger: 'blur', message: '请选择显示状态' },
        detailsPath: { required: true, trigger: 'blur', message: '请输入外链' }
      },
      clientCode: [],
      messageTypes: [],
      attachmentQOList: [],
      host: 'https://obs-d812.obs.cn-north-1.myhuaweicloud.com/',
      uploadImg: '',
      uploadImgId: '',
      flags: -1,
      isEdit: false,
      resourcesImg: '', // 课程封面
      imageResource: '', // 图片id
      messId: '',
      uuid: '',
      followDate: [],
      noticeTypes: {}
    }
  },
  watch: {
    'listQuery.detailsType': {
      deep: true,
      handler(newVal, oldVal) {

        if (newVal === 1) {
          this.listQuery.detailsPath = ''
        } else if (newVal === 2) {
          this.listQuery.details = ''
        }
      }
    },
    'noticeTypes.code': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal !== 'alert') {
          this.listQuery.messageScope = null
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode()
      this.getMessageType()
    })
  },
  methods: {
    messageDetail(ids) { // 获取信息详情
      if (ids) {
        messageDetail(ids).then(res => {
          if (res.code === '000000') {
            this.listQuery = res.data
            this.listQuery.detailsType = res.data.detailsType ? JSON.stringify(res.data.detailsType) : ''
            this.listQuery.messageType = res.data.messageType ? Number(res.data.messageType) : ''
            this.listQuery.messageScope = (res.data.messageScope !== 3 && res.data.messageScope !== 4 && res.data.messageScope !== 5) ? 3 : res.data.messageScope
            this.followDate = this.listQuery.beginTime && this.listQuery.endTime ? [this.listQuery.beginTime, this.listQuery.endTime] : []
            this.resourcesImg = res.data.imgUrl || ''
            this.imageResource = res.data.imageResource || ''
            this.noticeTypes = this.messageTypes.length > 0 ? this.messageTypes.filter(item => item.id === this.listQuery.messageType)[0] : {}
          }
        })
      }
    },
    getCode() {
      getClientList().then(res => {
        this.clientCode = res
      })
    },
    getMessageType() { // 消息类型
      messType().then(res => {
        this.messageTypes = res.data && res.data.length > 0 ? res.data.filter(item => item.name !== '续约提醒') : []
      })
    },
    cancelClass() {
      this.listQuery = this.$options.data().listQuery
      this.imageResource = ''
      this.resourcesImg = ''
      this.followDate = []
      this.noticeTypes = {}
      if (this.$refs.messageForm) {
        this.$refs.messageForm.clearValidate()
      }
    },
    confirmMessage() {
      this.$refs.messageForm.validate((valid) => {
        if (valid) {
          const detailsInfo = this.listQuery.detailsType === '1' ? this.listQuery.details : ''
          // const detailsPathInfo = this.listQuery.detailsType === '2' ? this.listQuery.detailsPath : ''
          const params = Object.assign({}, this.listQuery, { imageResource: this.imageResource || '', details: detailsInfo, beginTime: this.followDate !== null && this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate !== null && this.followDate[1] ? this.followDate[1] : '' })
          if (this.listQuery.detailsType === '2' && !this.listQuery.detailsPath) {
            this.$message({
              type: 'warning',
              message: '请输入外链'
            })
          } else if (this.listQuery.detailsType === '1' && !this.listQuery.details) {
            this.$message({
              type: 'warning',
              message: '请输入消息详情'
            })
          } else
          if (this.followDate !== null && (Date.parse(this.followDate[0]) > Date.parse(this.followDate[1]))) {
            this.$message({
              type: 'warning',
              message: '开始时间不能大于结束时间'
            })
          } else if (((this.listQuery.clientCode === 101 || this.listQuery.clientCode === 201 || this.listQuery.clientCode === 301) &&  this.noticeTypes && this.noticeTypes.code === 'alert') && !this.listQuery.messageScope) {
            this.$message({
              type: 'warning',
              message: '请选择显示范围'
            })
          } else {
            addMessage(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '添加成功'
                })
                this.$emit('addMessageList')
                this.messagePop = false
                this.listQuery = {
                  detailsType: '2',
                  messageScope: 3,
                  mustRead: 1,
                  messageType: ''
                }
                this.imageResource = ''
                this.resourcesImg = ''
                this.followDate = []
                this.noticeTypes = {}
                this.$refs.messageForm.clearValidate()
              }
            }).catch(() => {

            })
          }
        } else {
          return false
        }
      })
    },
    upload(e) {
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/message/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 10) {
        this.$message({
          type: 'warning',
          message: '上传的图片不能大于10M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/message/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.imageResource = res.data.id
                that.resourcesImg = res.data.url
              }
            })
          }
        })
      }
    },
    editMessage() {

      if (this.messId) {
        this.$refs.messageForm.validate((valid) => {
          if (valid) {
            const params = Object.assign({}, this.listQuery, { imageResource: this.imageResource || '', id: this.messId, beginTime: this.followDate !== null && this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate !== null && this.followDate[1] ? this.followDate[1] : '' })
            if (this.listQuery.detailsType === '2' && !this.listQuery.detailsPath) {
              this.$message({
                type: 'warning',
                message: '请输入外链'
              })
            } else if (this.listQuery.detailsType === '1' && !this.listQuery.details) {
              this.$message({
                type: 'warning',
                message: '请输入消息详情'
              })
            } else
            if (this.followDate !== null && (Date.parse(this.followDate[0]) > Date.parse(this.followDate[1]))) {
              this.$message({
                type: 'warning',
                message: '开始时间不能大于结束时间'
              })
            } else if (((this.listQuery.clientCode === 101 || this.listQuery.clientCode === 201 || this.listQuery.clientCode === 301) &&  this.noticeTypes && this.noticeTypes.code === 'alert') && !this.listQuery.messageScope) {
              this.$message({
                type: 'warning',
                message: '请选择显示范围'
              })
            } else {
              updateMessage(params).then(res => {
                if (res.code === '000000') {
                  this.$message({
                    type: 'success',
                    message: '修改成功'
                  })
                  this.$emit('addMessageList')
                  this.messagePop = false
                  this.listQuery = {}
                  this.imageResource = ''
                  this.resourcesImg = ''
                  this.followDate = []
                  this.noticeTypes = {}
                  this.$refs.messageForm.clearValidate()
                }
              }).catch(() => {

              })
            }
          } else {
            return false
          }
        })
      }
    },
    delImgA() {
      this.imageResource = ''
      this.resourcesImg = ''
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    },
    getClientCode(val) {


    },
    refreshScope() {
      this.$forceUpdate()
    },

    rangeShow() {
      const listQuery = this.listQuery
      const noticeTypes = this.noticeTypes
      if (listQuery.clientCode === 101 || listQuery.clientCode === 201 || listQuery.clientCode === 301) {
        if (noticeTypes && noticeTypes.code === 'alert') {
          return true
        }
      }
      return false
    }
  }
}
</script>

<style scoped lang="scss">
  .upload-imgs{
    position: relative;
    width: 118px;
    height: 118px;
    font-size: 14px;
    display: inline-block;
    padding: 10px;
    margin-right: 25px;
    border: 2px dashed #ccc;
    text-align: center;
    vertical-align: middle;
  }
  .upload-imgs .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 94px;
    line-height: 94px
  }
  .upload-imgs .add .iconfont{
    padding: 10px 0;
    font-size: 40px;
  }
  .upload-imgs .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 118px;
    height: 118px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    position: relative;
    width: 94px;
    height: 94px;
    line-height: 94px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
    width: 94px;
    height: 94px;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:-10px;
    left: -10px;
    width:118px;
    height:118px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 118px;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
  .require-tips{
    font-size: 12px;
    color: red;
  }
</style>
