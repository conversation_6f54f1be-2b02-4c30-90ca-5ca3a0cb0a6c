<template>
  <el-dialog :destroy-on-close="true" top="50px" v-el-drag-dialog :title="institutionsTitle" :visible.sync="institutionsCreateMenu" :close-on-click-modal="false" width="30%" @close="changeInit">
    <el-form ref="institutionsForm" :model="createMenuForm" :rules="createMenuFormRules" label-width="120px">
      <el-form-item label="项目类型" prop="clientCode" required>
        <el-select v-model="createMenuForm.clientCode" placeholder="请选择项目类型" filterable @change="getMenuTreeList('change')" clearable class="filter-item">
          <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="createMenuForm.name" autocomplete="off"  placeholder="请输入菜单名称"/>
      </el-form-item>
      <el-form-item label="菜单层级" prop="menuType">
        <el-select v-model="createMenuForm.menuType" placeholder="请选择菜单层级" filterable clearable class="filter-item">
          <el-option label="菜单" :value="1" />
          <el-option label="按钮" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="前端CODE" prop="code" required>
        <el-input v-model="createMenuForm.code" autocomplete="off" placeholder="请输入前端CODE" />
      </el-form-item>
      <el-form-item label="适用客户端" prop="suitType" required>
        <el-radio-group v-model="createMenuForm.suitType" @change="getMenuTreeList('change')">
          <el-radio :label="1">PC</el-radio>
          <el-radio :label="2">校区助手小程序</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="父级菜单" prop="parentId">
        <el-select-tree
          v-model="createMenuForm.parentId"
          placeholder="请选择父级菜单"
          :data="treeList"
          :props="defaultProps"
          :check-strictly="checkAnyLevel"
        />
      </el-form-item>
      <el-form-item label="分校是否适用" prop="allowBranch">
        <el-radio-group v-model="createMenuForm.allowBranch">
          <el-radio :label="1">适用</el-radio>
          <el-radio :label="0">不适用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开通" prop="status" required>
        <el-radio-group v-model="createMenuForm.status">
          <el-radio :label="1">开通</el-radio>
          <el-radio :label="99">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="员工是否可见" prop="allowEmployee" required>
        <el-radio-group v-model="createMenuForm.allowEmployee">
          <el-radio :label="0">不可见</el-radio>
          <el-radio :label="1">可见</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="系统版本" prop="menuVersion" required>
        <el-radio-group v-model="createMenuForm.menuVersion">
          <el-radio :label="0">共用</el-radio>
          <el-radio :label="1">2.0系统</el-radio>
          <el-radio :label="2">3.0系统</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer text-center">
      <el-button @click="changeInit">取消</el-button>
      <el-button type="primary"  @click="confirmCreate">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { institutionParent, institutionsAdd, institutionsEdit, institutionsDetail } from '@/api/system-setting'
import ElSelectTree from '@/components/el-select-tree'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { clientCode } from '@/api/classType'
export default {
  name: 'MenuDetail',
  components: { ElSelectTree },
  directives: { elDragDialog },
  props: {
    institutionsTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      institutionsCreateMenu: false, // 增删查菜单
      isCloseSelect: false,
      currentMenuId: '',
      createMenuForm: {
        clientCode:'',
        name: '',
        menuType: '',
        code: '',
        suitType: 1,
        parentId: '',
        allowBranch: '',
        status: '',
        allowEmployee: '',
        menuVersion: ''
      },
      createMenuFormRules: {
        name: {
          required: true,
          message: '请填写菜单名称',
          trigger: 'blur'
        },
        menuType: {
          required: true,
          message: '请填写菜单层级',
          trigger: 'blur'
        },
        code: {
          required: true,
          message: '请填写前端CODE',
          trigger: 'blur'
        },
        clientCode: {
          required: true,
          message: '请选择项目类型',
          trigger: 'change'
        },
        allowBranch: {
          required: true,
          message: '请选择分校是否适用',
          trigger: 'change'
        },
        status: {
          required: true,
          message: '请选择是否开通',
          trigger: 'change'
        },
        suitType:{
          required: true,
          message: '请选择适用客户端',
          trigger: 'change'
        },
        allowEmployee:{
          required: true,
          message: '请选择员工是否可见',
          trigger: 'change'
        },
        menuVersion:{
          required: true,
          message: '请选择系统版本',
          trigger: 'change'
        }
      },
      treeList: [],
      defaultProps: {
        value: 'id', label: 'name', children: 'subMenus'
      },
      checkAnyLevel: true,
      clientCode: [],
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        this.clientCode =  (res.data || []).filter(item => (item.code === 100 || item.code === 200));
      })
    },
    async getMenuTreeList(type) {
      if(type){
        this.treeList = [];
        this.createMenuForm.parentId = null;
      }
      const resp = await institutionParent({
        clientCode: this.createMenuForm.clientCode,
        suitType: this.createMenuForm.suitType
      })
      this.treeList = resp.data
    },
    /**
     * 新增菜单
     **/
    confirmCreate() {
      this.$refs.institutionsForm.validate(valid => {
        if (valid) {
          if(this.createMenuForm.hasOwnProperty('id')){
            this.confirmUpdate()
          }else{
            institutionsAdd(this.createMenuForm).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '新增菜单成功',
                  type: 'success'
                })
                this.institutionsCreateMenu = false
                this.$refs.institutionsForm.resetFields();
                /**
                 * 通知父组件更新
                 */
                this.$emit('refreh')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    /**
     * 修改
     * */
    confirmUpdate() {
      institutionsEdit(this.createMenuForm).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$refs.institutionsForm.resetFields();
          this.institutionsCreateMenu = false
          /**
           * 通知父组件更新
           */
          this.$emit('refreh')
        }
      })
    },
    /**
     * 获取菜单详情
     */
   async getMenusDetail(id) {
      if(id){
        institutionsDetail(id).then(async res => {
          this.institutionsCreateMenu = true
          this.createMenuForm.clientCode = res.data.clientCode;
          this.createMenuForm.suitType = res.data.suitType;
          await this.getMenuTreeList()
          Object.assign(this.createMenuForm ,res.data)
        })
      }else{
        if(this.createMenuForm.hasOwnProperty('id')){
          delete this.createMenuForm.id
        }
        this.treeList = []
      }
    },
    changeInit() {
      this.$refs.institutionsForm.resetFields();
      this.institutionsCreateMenu = false
    }
  }
}
</script>

<style scoped>

</style>
