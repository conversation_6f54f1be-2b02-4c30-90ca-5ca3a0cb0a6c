<template>
  <el-dialog v-el-drag-dialog :title="title" :visible.sync="dialogPolicy" width="40%" :close-on-click-modal="!dialogPolicy">
    <el-form ref="policyDialogForm" :model="detail" label-width="100px" :disabled="!isEdit" :rules="policyRules">
      <el-row :gutter="18">
        <el-col :xs="{span:24}" :sm="{span:12}">
          <el-form-item label="套餐名称：" prop="policyName">
            <el-input v-model="detail.policyName" />
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:12}">
          <el-form-item label="套餐价格：" prop="price">
            <el-input v-model="detail.price" />
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:12}">
          <el-form-item label="套餐类型：" prop="businessType">
            <el-select v-model="detail.businessType" clearable filterable style="width: 100%">
              <el-option
                v-for="item in businessTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:12}">
          <el-form-item label="项目名称：" prop="projectId">
            <el-select v-model="detail.projectId" class="filter-item" filterable style="width: 100%;" clearable @change="getProductOfProject">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="isEdit" :xs="{span:24}" :sm="{span:24}" class="productInfo">
          <el-form-item label="关联产品：">
            <el-col :span="24">
              <el-table
                :data="detail.policyProductList"
                border
                fit
                stripe
                highlight-current-row
                style="width: 100%;"
              >
                <el-table-column label="#" type="index" width="50" align="center" />
                <af-table-column label="产品名称">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" size="small" filterable style="width: 100%">
                      <el-option
                        v-for="item in productProjects"
                        :key="item.id"
                        :value="item.id"
                        :label="item.productName"
                      />
                    </el-select>
                    <!--                  <el-input v-model="row.productName"></el-input>-->
                  </template>
                </af-table-column>
                <af-table-column label="产品数量/课时">
                  <template slot-scope="{ row }">
                    <el-input v-model="row.productNum" />
                  </template>
                </af-table-column>
                <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="deleteCustomerProject(scope.$index, 'policyProductList')">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="6" :offset="9" class="text-center">
              <div class="add-customer" @click="addProductProject('policyProductList')"><i class="el-icon-plus" style="font-size: 12px" />添加</div>
            </el-col>
          </el-form-item>
        </el-col>
        <el-col v-if="!isEdit" :xs="{span:24}" :sm="{span:24}">
          <el-form-item label="关联产品：">
            <el-table
              :data="detail.policyProductList"
              border
              fit
              stripe
              highlight-current-row
              style="width: 100%;"
            >
              <el-table-column label="#" type="index" width="50" align="center" />
              <af-table-column label="产品名称" prop="productName" />
              <af-table-column label="产品数量/课时" prop="productNum" />
              <!-- <af-table-column label="产品单价" prop="productPrice" /> -->
              <!--              <af-table-column label="单位" prop="productUnit" />-->
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :xs="{span:24}" :sm="{span:24}">
          <el-form-item label="套餐备注：">
            <el-input v-model="detail.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="isEdit === 'create'" type="primary" @click="confirmAdd">确定</el-button>
      <el-button v-if="isEdit === 'update'" type="primary" @click="confirmEdit">确定</el-button>
      <el-button @click="dialogPolicy = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { getPolicyDetail, updatePolicy, createPolicy } from '@/api/policy'
import { getAllProject, getProjectProduct } from '@/api/common'
import { businessTypeList } from '@/utils/field-conver'
export default {
  name: 'PolicyDetail',
  directives: { elDragDialog },
  data() {
    return {
      detail: {
        policyProductList: []
      },
      productPriceList: [],
      isEdit: false,
      dialogPolicy: false,
      title: '套餐详情',
      projectList: [],
      productProjects: [],
      businessTypeList: businessTypeList,
      policyRules: {
        businessType: { required: true, message: '业务类型必选' },
        policyName: { required: true, message: '套餐名称必填', trigger: 'blur' },
        price: { required: true, message: '套餐价格必填', trigger: 'blur' },
        projectId: { required: true, message: '关联项目必选' }
      }
    }
  },
  watch: {
  },
  created() {
    this.getProject()
  },
  methods: {
    getDetail(id, edit) {
      const that = this
      that.title = edit ? '修改套餐' : '套餐详情'
      that.isEdit = edit
      getPolicyDetail(id).then(res => {
        that.detail = res.data
        that.dialogPolicy = true
        that.getProductOfProject(that.detail.projectId, true)
      })
    },
    /**
     * 确认修改信息
     */
    confirmEdit() {
      const that = this
      const params = JSON.parse(JSON.stringify(that.detail))
      that.$refs['policyDialogForm'].validate(valid => {
        if (valid) {
          updatePolicy(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '修改成功',
                type: 'success'
              })
              that.dialogPolicy = false
              /**
               * 通知父组件更新
               */
              that.$emit('refresh')
            }
          })
        }
      })
    },
    // 新增
    confirmAdd() {
      const that = this
      const params = JSON.parse(JSON.stringify(that.detail))
      that.$refs['policyDialogForm'].validate(valid => {
        if (valid) {
          if (!that.checkProductList()) {
            this.$message({
              message: '产品信息填写错误',
              type: 'error'
            })
            return
          }
          createPolicy(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '新增成功',
                type: 'success'
              })
              that.dialogPolicy = false
              /**
               * 通知父组件更新
               */
              that.$emit('refresh')
            }
          })
        }
      })
    },
    /**
     * 新增套餐
     * */
    createNewPolicy(edit) {
      this.detail = {
        policyProductList: []
      }
      this.title = '新增套餐'
      this.isEdit = edit
      this.dialogPolicy = true
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    /**
     * 获取项目下的产品
     */
    getProductOfProject(val, isRender) {
      if (!val) {
        this.$message({
          message: '请先选择项目',
          type: 'warning'
        })
        return
      }
      if (!isRender) {
        this.detail.policyProductList = [
          {
            productNum: 1,
            productId: '',
            priceId: ''
          }
        ]
      }
      const params = {
        projectId: val
      }
      getProjectProduct(params).then(res => {
        this.productProjects = res.data
      })
    },
    /**
     * 添加产品列表
     * prop : referrerProductList 推荐人产品列表
     * prop: projectInfo 当前用户产品列表
     * */
    addProductProject(prop) {
      if (this.productProjects && this.productProjects.length < 1) {
        this.$message({
          message: '该项目下没有产品，请重新选择项目',
          type: 'warning'
        })
        return
      }
      this['detail'][prop].push({
        productNum: 1,
        productId: '',
        priceId: ''
      })
    },
    /**
     *  删除已选产品
     * */
    deleteCustomerProject(index, prop) {
      this['detail'][prop].splice(index, 1)
    },
    /**
     * 检查产品列表
     */
    checkProductList() {
      let res = true
      this.detail.policyProductList.forEach(item => {
        if (!item.productNum || item.productNum < 0 || !item.productId) {
          res = false
        }
      })
      return res
    }
    /**
     * 切换产品获取播客价格列表
     */
    // chooseProduct(val, index) {
    //   getProductPriceList(val).then(res => {
    //     if (res.code === '000000') {
    //       this.$set(this.detail.policyProductList[index], 'podcastPrices', res.data)
    //     }
    //   })
    // }
  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .add-customer {
    border-radius: 18px;
    height: 36px;
    color: #539FFF;
    font-size: 15px;
    line-height: 36px;
    text-align: center;
    font-weight: 500;
    border: 1px dashed #DCDCDC;
    margin: 10px auto;
    cursor: pointer;
  }
  /deep/ .productInfo td {
    padding: 0;
  }
</style>
