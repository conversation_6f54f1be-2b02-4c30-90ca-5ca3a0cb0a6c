<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.deptName" placeholder="部门" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.parentId" placeholder="上级部门" filterable class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter">
        <el-option
          v-for="item in parentDepartmentList"
          :key="item.id"
          :label="item.departmentName"
          :value="item.id"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:department:create']" class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <af-table-column label="部门编号" prop="id" align="center" />
      <af-table-column label="部门名称" prop="departmentName" />
      <af-table-column label="上级部门" prop="parentName" />
      <af-table-column label="操作" class-name="small-padding fixed-width action-warp">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:department:update']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['setting:department:delete']" type="primary" size="mini" @click="handleDelete(row.id)">
            删除
          </el-button>
        </template>
      </af-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <department-detail ref="departmentDetail" :is-edit="isEdit" @refresh="getList" />
  </div>
</template>

<script>
import { getDepartmentList, deleteDepartMent } from '@/api/system-setting'
import Pagination from '@/components/Pagination'
import departmentDetail from './detail'
export default {
  name: 'Department',
  components: { Pagination, departmentDetail },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        deptName: ''
      },
      total: 0,
      dialogRolePermission: false,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      parentDepartmentList: []
    }
  },
  created() {
    this.listLoading = false
    this.getList()
    this.getDetail()
  },
  methods: {
    getDetail() {
      const that = this
      getDepartmentList({ 'pageSize': 9999 }).then(res => {
        that.parentDepartmentList = res.data.records
      })
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增员工
     * */
    handleAdd() {
      this.$refs.departmentDetail.getDetail()
      this.isEdit = false
    },

    getList() {
      const that = this
      getDepartmentList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    /**
     * 修改员工信息
     * */
    handleUpdate(row) {

      this.isEdit = true
      this.$refs.departmentDetail.editDetail(row)
    },
    /**
     * 删除员工
     * */
    handleDelete(row) {
      this.$confirm('', '确定删除？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }).then(() => {
        deleteDepartMent(row).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(action => {

      })
    }
  }
}
</script>

<style scoped>

</style>
