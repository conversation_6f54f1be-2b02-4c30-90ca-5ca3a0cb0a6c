<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="100px" :disabled="!isEdit" :rules="aiRules">
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 15px;">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约期限：" label-width="110px">
                    <span v-if="detail.contractOrder.signStartTime">{{ detail.contractOrder.signStartTime }}-</span>
                    <span v-if="detail.contractOrder.signEndTime">{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin:15px 0">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="22">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同编号：" label-width="120px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同名称：" label-width="120px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：" label-width="120px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本：" label-width="120px">
                    <div>{{ versionTypeStatus }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="120px" required>
                    <el-radio-group v-model="detail.contractType" class="radios">
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="区域类型：" label-width="130px" required>
                    <el-radio-group v-model="detail.cooperationType" disabled>
                      <el-radio :label="1">区县独家</el-radio>
                      <el-radio :label="0">区县单点</el-radio>
                      <el-radio :label="2">乡镇独家</el-radio>
                      <el-radio :label="3">乡镇单点</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="套餐：" label-width="120px" required>
                    <el-input v-model="uniqueSkillsContract.servicePlan" maxlength="12" placeholder="填入A文科和理科;B理科;C文科" @input="refresh" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="充值价格：" label-width="120px" required>
                    <el-input v-model="uniqueSkillsContract.rechargePrice" type="number" placeholder="填入充值价格" maxlength="4" @input="refresh" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="服务费/元：" label-width="120px" required>
                    <el-input v-model="uniqueSkillsContract.serviceAmount" placeholder="填入服务费" type="number" maxlength="7" @input="refresh" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区地址：" label-width="120px">
                    <el-input v-model="schoolAddress" placeholder="填入校区地址" maxlength="25" @input="refresh" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                  <el-form-item label="备注：" label-width="120px">
                    <el-input v-model="detail.remark" type="textarea" maxlength="255" show-word-limit />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>
  </div>
</template>
<script>
import { getContractDetail, modifyContractDetail, getCreatContract } from '@/api/contract'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  versionTypes
} from '@/utils/field-conver'
export default {
  name: 'Entrance',
  data() {
    return {
      detail: {},
      aiRules: {
        cooperationType: { required: true, message: ' ', trigger: 'change' },
        contractType: { required: true, message: ' ', trigger: 'change' },
        cooperationFee: { required: true, message: ' ', trigger: 'blur' },
        courseConsumption: { required: true, message: ' ', trigger: 'blur' }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      title: null,
      versionTypeStatus: null,
      uniqueSkillsContract: {},
      schoolAddress: ''
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? `高考绝招班-编辑合同` : `高考绝招班-合同详情`
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.schoolAddress = that.detail.schoolAddress
        that.remark = that.detail.remark
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        that.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = that.id
        that.uniqueSkillsContract = that.detail.uniqueSkillsContract ? that.detail.uniqueSkillsContract : {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.versionTypeStatus = that.detail.versionType ? converseEnToCn(versionTypes, that.detail.versionType) : null
      })
    },
    getCreatContract() {
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.schoolAddress = that.detail.schoolAddress
        that.remark = that.detail.remark
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        that.detail.clueSchool = res.data.clueSchool || {}
        that.detail.id = that.id
        that.uniqueSkillsContract = that.detail.uniqueSkillsContract ? that.detail.uniqueSkillsContract : {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.versionTypeStatus = that.detail.versionType ? converseEnToCn(versionTypes, that.detail.versionType) : null
      })
    },
    /**
     * 确认修改信息
     */
    confirmEdit(num) {
      const that = this
      if (!that.detail.contractType) {
        that.$message({
          type: 'warning',
          message: '请选择合同类型'
        })
        return
      }
      if (!that.uniqueSkillsContract.servicePlan) {
        that.$message({
          message: '请输入套餐类型',
          type: 'warning'
        })
        return
      }
      if (!that.uniqueSkillsContract.rechargePrice) {
        that.$message({
          message: '请输入充值价格',
          type: 'warning'
        })
        return
      }
      if (!that.uniqueSkillsContract.serviceAmount) {
        that.$message({
          message: '请输入服务费',
          type: 'warning'
        })
        return
      }

      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          const uniqueSkillsContractDTO = that.uniqueSkillsContract
          const params = Object.assign(that.detail, {
            operateType: num, id: that.detail.contractId,
            uniqueSkillsContractDTO: uniqueSkillsContractDTO,
            schoolAddress: that.schoolAddress
          })
          modifyContractDetail(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '保存成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    refresh() {
      this.$forceUpdate()
    }
  }

}
</script>

<style scoped>

</style>
