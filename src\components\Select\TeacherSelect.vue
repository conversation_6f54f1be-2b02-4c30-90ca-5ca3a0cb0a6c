<template>
  <el-select v-model="tmpId" style="width: 140px;" filterable clearable placeholder="教师">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="item.name"
      :value="`${item.id}`">
      <div style="min-width: 200px;">
        <span class="fl">{{ item.name }}</span>
        <span class="subTitle">{{ item.id }}</span>
      </div>
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import {  getTeachers } from '@/api/classType'

/**
 * 科目选择框
 */
export default {
  name: 'TeacherSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      this.loading = true
      getTeachers().then(res => {
        this.loading = false
        if (res.code === SUCCESS) {
          this.dataList = res.data
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.subTitle {
  float: right;
  color: #8492a6;
  font-size: 13px
}
</style>
