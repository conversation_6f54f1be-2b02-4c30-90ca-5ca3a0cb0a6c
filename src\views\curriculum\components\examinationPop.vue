<template>
  <el-dialog :visible.sync="outlinePop" :title="outlineTitle" :close-on-click-modal="!outlinePop" width="80%" @close="examinationValid">
    <div class="examinationOutline">
      <el-form ref="addExamination" :model="examination" :rules="examinationRules" class="examinationForms">
        <el-form-item prop="title" class="examinationForms-item">
          <el-input
            v-model="examination.title"
            placeholder="请输入考点名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-form-item>
        <el-form-item prop="status" class="examinationForms-item">
          <el-select
            v-model="examination.status"
            placeholder="请选择考点状态"
            clearable
            class="filter-item"
          >
            <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="examinationForms-item">
          <el-input
            v-model="examination.sort"
            placeholder="请输入考点排序"
            class="filter-item"
            maxlength="6"
            @keyup.enter.native="handleFilter"
          />
        </el-form-item>
        <el-form-item class="examinationForms-item">
          <el-button v-waves class="filter-item" type="primary" size="mini" :disabled="enableFlag" @click="handleFilter">
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="考点编号" show-overflow-tooltip prop="id">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
      <af-table-column label="考点名称" show-overflow-tooltip prop="title" />
      <af-table-column label="考点状态" prop="status" show-overflow-tooltip :formatter="getJoinStatusCN" />
      <af-table-column label="考点排序" prop="sort" show-overflow-tooltip />
      <af-table-column label="更新时间" prop="updateTime" />
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp auto-fixed"
        min-width="230"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="outlineAddPop=true,handleUpdate(row)">修改</el-button>
          <el-button type="primary" size="mini" @click="enable(row)">{{ row.status===1?'禁用':'启用' }}</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :visible.sync="outlineAddPop"
      :title="outlineAddTitle"
      :close-on-click-modal="!outlineAddPop"
      width="60%"
      append-to-body
      @close="cancelClass"
    >
      <div class="assing-info">
        <el-form ref="outlineForms" :model="outline" :rules="rules" label-width="100px">
          <el-form-item label="大纲名称" prop="outlineId">
            <el-input v-model="title" disabled />
            <el-input v-model="outline.outlineId" placeholder="" disabled style="display: none" />
          </el-form-item>
          <el-form-item label="考点名称" prop="title">
            <el-input v-model="outline.title" placeholder="请输入考点名称" maxlength="30" />
          </el-form-item>
          <el-form-item label="考点状态" prop="status">
            <el-select v-model="outline.status" placeholder="请选择适用科目" clearable class="filter-item" filterable>
              <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="考点排序">
            <el-input v-model.number="outline.sort" placeholder="请输入考点排序" maxlength="7" />
          </el-form-item>
        </el-form>
      </div>
      <div class="assign-operas">
        <el-button type="infor" size="mini" @click="outlineAddPop=false,cancelClass()">取消</el-button>
        <el-button type="primary" size="mini" @click="custormClass">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import { examinationList, addExamination, examinationEnable, examinationDetail } from '@/api/classType'
import { enableList, converseEnToCn } from '@/utils/field-conver'
export default {
  name: 'AddClassPop',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      outlinePop: false,
      outlineTitle: '考点管理',
      examinationTitle: '',
      examinationPop: false,
      outlineAddPop: false,
      outlineAddTitle: '',
      rules: {
        outlineId: { required: true, trigger: 'blur', message: '请输入考点名称' },
        title: { required: true, trigger: 'blur', message: '请输入考点名称' },
        status: { required: true, trigger: 'blur', message: '请选择考点状态' }
      },
      examinationRules: {
        title: { required: true, trigger: 'blur', message: '请输入考点名称' },
        status: { required: true, trigger: 'blur', message: '请选择考点状态' }
      },
      assignSatuts: [],
      outline: {},
      enableList: enableList,
      outlineId: '',
      examination: {},
      title: '', // 考点名称
      ids: '', // 考点列表id
      enableFlag: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { outlineId: that.outlineId })
      await examinationList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    handleFilter() {
      this.enableFlag = true
      this.$refs.addExamination.validate((valid) => {
        if (valid && this.outlineId) {
          const data = Object.assign({}, this.examination, { outlineId: this.outlineId })
          addExamination(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.enableFlag = false
              this.examination = {}
              this.getList(this.outlineId)
            }
          }).catch(() => {
            this.enableFlag = false

          })
        } else {
          this.enableFlag = false
          return false
        }
      })
    },
    cancelClass() {
      this.outline = {}
      if (this.$refs.outlineForms) {
        this.$refs.outlineForms.clearValidate()
      }
    },
    examinationValid() {
      this.examination = {}
      if (this.$refs.addExamination) {
        this.$refs.addExamination.clearValidate()
      }
    },
    custormClass() { // 修改考点
      this.$refs.outlineForms.validate((valid) => {
        if (valid) {
          if (this.outlineId && this.ids) {
            const params = Object.assign({}, this.outline, { outlineId: this.outlineId, id: this.ids })
            addExamination(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.outline = {}
                this.getList(this.outlineId)
                this.ids = ''
                this.outlineAddPop = false
              }
            }).catch(() => {

            })
          }
        } else {
          return false
        }
      })
    },
    handleUpdate(row) {
      this.outlineAddTitle = '修改考点'
      examinationDetail(row.id).then(res => {
        if (res.code === '000000') {
          this.outline = res.data
        }
      })
      this.ids = row.id
    },
    enable(row) { // 启用/禁用
      const title = row.status === 1 ? '禁用' : '启用'
      this.$confirm(`确定要${title}吗?`, {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        examinationEnable(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList(this.outlineId)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .assign-operas {
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .examinationOutline{
    display: flex;
    .examinationForms{
      width:70%;
      display: flex;
      .examinationForms-item{
        padding-right:15px;
      }
    }
  }
</style>
