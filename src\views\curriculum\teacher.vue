<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="教师名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />

      <el-select v-model="listQuery.subjectId" placeholder="任教科目" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="教师状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="更新开始时间"
        end-placeholder="更新结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:teacher:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="教师编号" show-overflow-tooltip prop="id" width="120px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
<!--      <el-table-column label="教师头像" show-overflow-tooltip prop="imageUrl" width="120px">-->
<!--        <template slot-scope="scope">-->
<!--          <img v-if="scope.row.imageUrl" :src="scope.row.imageUrl" class="cover-img">-->
<!--        </template>-->
<!--      </el-table-column>-->
      <af-table-column label="教师名称" prop="name" show-overflow-tooltip />
      <af-table-column label="任教科目" prop="subjectName" show-overflow-tooltip width="120px" />
      <af-table-column label="适用产品线" prop="clientName" />
      <af-table-column label="教师状态" prop="status" :formatter="getJoinStatusCN" />
      <el-table-column label="教师简介" prop="introduction" width="450px" />
      <af-table-column label="教师排序" prop="sort" />
      <el-table-column label="更新时间" prop="updateTime" width="160px" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:teacher:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['curriculum:teacher:opera']" type="primary" size="mini" @click="enable(row)">{{ row.status===1?'禁用':'启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改教师弹框-->
    <teacher-pop ref="teacher" @addTeacherList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import TeacherPop from './components/teacherPop'
import { clientCode, getAllSubjects, getAllTeacher, enableTeacher } from '@/api/classType'
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
export default {
  name: 'Teacher',
  components: {
    Pagination,
    TeacherPop
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      subjectsAll: [],
      clientCode: [],
      enableList: enableList,
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getAllSubjects()
      this.getCode()
      this.getList()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 1)
      })
    },
    getAllSubjects() {
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data
        }
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })

      await getAllTeacher(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = '新增教师'
      this.$refs.teacher.isEdit = false
      this.$refs.teacher.flags = 1
      this.$refs.teacher.listQuery.status = 1
    },
    handleUpdate(row) {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = `修改${row.name}教师`
      this.$refs.teacher.getTeacherDetail(row.id)
      this.$refs.teacher.isEdit = false
      this.$refs.teacher.flags = 0
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    getDetail(row) {
      this.$refs.teacher.teacherPop = true
      this.$refs.teacher.teacherTitle = `${row.name}详情`
      this.$refs.teacher.getTeacherDetail(row.id)
      this.$refs.teacher.isEdit = true
      this.$refs.grade.flags = -1
    },
    enable(row) { // 启用/禁用
      const title = row.status === 1 ? '禁用此项会造成引用此项的数据显示异常，确认删除?' : '确定要启用吗?'
      const tips = row.status === 1 ? '禁用成功' : '启用成功'
      this.$confirm(`${title}`, {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        enableTeacher(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${tips}`
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
