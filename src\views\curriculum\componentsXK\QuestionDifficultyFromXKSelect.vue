<template>
  <el-select  v-model="tmpId" multiple clearable placeholder="请选择试题难度" :disabled="disabled" filterable>
    <el-option
            v-for="item in optionList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
    </el-option>
  </el-select>
</template>
<script>

export default {
  name: 'QuestionDifficultyFromXKSelect',
  data: function () {
    return {
      //17 容易 18 较易 19 一般 20 较难 21 困难
      optionList: [
        {id: 17, name: '容易'},
        {id: 18, name: '较易'},
        {id: 19, name: '一般'},
        {id: 20, name: '较难'},
        {id: 21, name: '困难'}
      ]
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number,Array],
      required: false
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? this.targetId : []
      },
      set(val) {
        this.handleChange(val)
      }
    }
  },
  created() {
  },
  methods: {
    handleChange(value) {
      // const selectedOption = this.optionList.find(option => option.id == value)
      return this.$emit('change', value)
    },
    getList() {

    },
  }
}
</script>
<style scoped>
</style>
