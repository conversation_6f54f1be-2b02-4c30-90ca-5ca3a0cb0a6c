<template>
  <el-dialog v-el-drag-dialog title="打款记录详情" :visible.sync="collectFlag" :close-on-click-modal="!collectFlag" class="departmentDialog" width="40%">
    <el-form ref="detailForm" :model="collect" label-width="140px">
      <el-row>
        <el-col :lg="{span:12}">
          <el-form-item label="当前打款编号：">
            <div>{{ collect.payRecordCode }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="订单编号：">
            <div>{{ collect.orderCode }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="应付金额：">
            <div>{{ collect.orderPayAmount }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="已到账金额：">
            <div>{{ collect.orderRealAmount }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:24}">
          <el-form-item label="已打款编号：">
            <div v-show="collect.relationPayRecords&&collect.relationPayRecords.length>0">
              <a v-for="item in collect.relationPayRecords" :key="item.id" class="codes">{{ item.payRecordCode }}</a>
            </div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="财务审核状态：">
            <div>{{ getAuditStatus(collect.auditStatus) }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="打款金额：">
            <div>{{ collect.payAmount }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="打款方式：">
            <div>{{ collect.payTypeName }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="打款类型：" prop="tradeType">
            <el-radio-group v-model="collect.tradeType" disabled>
              <el-radio v-for="(item,i) in trades" :key="i" :label="item.id">{{ item.itemName }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="打款时间：">
            <div>{{ collect.payTime }}</div>
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="交易流水号：">
            <div>{{ collect.transactionNo }}</div>
          </el-form-item>
        </el-col>
        <!--<el-col :lg="{span:12}">-->
        <!--<el-form-item label="打款状态：" prop="status">-->
        <!--<el-radio-group v-model="collect.auditStatus" disabled>-->
        <!--<el-radio :label="30">已到账</el-radio>-->
        <!--<el-radio :label="40">未到账</el-radio>-->
        <!--</el-radio-group>-->
        <!--</el-form-item>-->
        <!--</el-col>-->
        <el-col :lg="{span:12}">
          <el-form-item label="开票时间：" prop="invoiceTime">
            <el-date-picker
              v-model="collect.invoiceTime"
              type="datetime"
              placeholder="开票时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 220px"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="{span:12}">
          <el-form-item label="转款人姓名：" prop="tradeCustomer">
            <el-input v-model="collect.tradeCustomer" type="text" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="{span:24}">
          <el-form-item label="备注：" prop="remark">
            <el-input v-model="collect.remark" type="textarea" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { payDetail } from '@/api/payment'
import {
  getPayType
} from '@/api/common'
import { converseEnToCn, auditStatus } from '@/utils/field-conver'
export default {
  name: 'CollectDetail',
  directives: {
    elDragDialog
  },
  data() {
    return {
      collectFlag: false,
      collect: {},
      trades: []
    }
  },
  mounted() {
    this.tradeList('trade_type')
  },
  methods: {
    getDetail(id) {
      payDetail(id).then(res => {
        if (res.code === '000000') {
          this.collect = res.data
        }
      })
    },
    tradeList(str) { // 打款类型
      const that = this
      getPayType(str).then(res => {
        that.trades = res.data && res.data.length > 0 ? res.data.filter(item => item.itemValue !== -1) : []
      })
    },
    getAuditStatus(status) {
      return converseEnToCn(auditStatus, status)
    }
  }
}
</script>

<style scoped>
.codes{
  padding-right: 8px;
}
</style>
