<template>
  <div v-show="faqTitlePop" class="edit-one-bg">
    <div class="edit-one-title">
      <div class="assing-info">
        <h3>
          <span>{{ faqParentTitle }}</span>
          <em class="el-icon-close" @click="dataInit()" />
        </h3>
        <el-form ref="oneTitleForms" :model="listQuery" :rules="rules" label-width="120px">
          <el-form-item label="标题" prop="title">
            <el-input v-model="listQuery.title" placeholder="请输入标题" maxlength="30" />
          </el-form-item>
          <el-form-item label="一级标题" prop="levelOneId">
            <el-select v-model="listQuery.levelOneId" placeholder="一级标题" filterable clearable class="filter-item" @change="getTwo">
              <el-option v-for="item in oneList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="二级标题" prop="levelTwoId">
            <el-select v-model="listQuery.levelTwoId" placeholder="二级标题" filterable clearable class="filter-item" @change="refresh">
              <el-option v-for="item in twoList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="常见问题内容" required>
            <div id="editor2" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="listQuery.sort" placeholder="请输入排序" maxlength="1000" />
          </el-form-item>
        </el-form>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="dataInit()">取消</el-button>
          <el-button type="primary" size="mini" @click="confirmOneTitle">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { levelOneList, levelTwo, addQuestion, questionIn, editQuestionIn } from '@/api/faq'
import { uploadSuccess } from '@/api/common'
import Editor from 'wangeditor'
export default {
  name: 'AddOneTitle',
  data() {
    return {
      faqTitlePop: false,
      faqParentTitle: '',
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入问题标题' },
        levelOneId: { required: true, trigger: 'blur', message: '请选择一级标题' },
        levelTwoId: { required: true, trigger: 'blur', message: '请选择二级标题' },
        sort: { required: true, trigger: 'blur', message: '请输入排序' }
      },
      listQuery: {},
      oneList: [],
      twoList: [],
      oneTitleInfo: '',
      editFlag: '',
      ids: null,
      showAddFaq: false,
      content: '',
      uuid: ''
    }
  },
  watch: {
    twoList: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.levelTwoId = ''
        }
      }
    },
    'listQuery.levelOneId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.levelTwoId = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      const that = this
      that.levelOneList()
      that.oneTitleInfo = new Editor('#editor2')

      that.oneTitleInfo.customConfig.uploadImgShowBase64 = false // 只能上传base64图片
      that.oneTitleInfo.customConfig.showLinkImg = true // 隐藏网络图片只能上传本地图片
      that.oneTitleInfo.customConfig.uploadImgMaxSize = 1 * 1024 * 1024 // 限制图片上传的大小
      that.oneTitleInfo.customConfig.customUploadImg = function(file, insert) { // 调用华为云的api，把编辑框的图片上传到华为云上
        that.upload(file, insert)
      }
      that.oneTitleInfo.create()
      if (that.content) that.oneTitleInfo.txt.html(that.content)
    })
  },
  methods: {
    levelOneList() {
      levelOneList().then(res => {
        if (res.code === '000000') {
          this.oneList = res.data
        }
      }).catch(() => {

      })
    },
    getTwo(val) {
      if (val) {
        levelTwo(val).then(res => {
          if (res.code === '000000') {
            this.twoList = res.data
          }
        }).catch(() => {

        })
      }
    },
    refresh() {
      this.$forceUpdate()
    },
    dataInit() {
      this.$emit('refreshList')
      this.faqTitlePop = false
      this.listQuery = {}
      this.oneTitleInfo.txt.html('')
      if (this.$refs.oneTitleForms) {
        this.$refs.oneTitleForms.clearValidate()
      }
    },
    confirmOneTitle() {
      const that = this

      this.$refs.oneTitleForms.validate((valid) => {
        const questionIn = that.oneTitleInfo.txt.html()

        if (valid && questionIn) {
          const data = Object.assign({}, that.listQuery, { content: questionIn, id: that.listQuery.id ? that.listQuery.id : '' })
          if (that.editFlag === 'create') {
            addQuestion(data).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '添加成功'
                })
                that.listQuery = {}
                that.oneTitleInfo.txt.html('')
                this.faqTitlePop = false
                that.$emit('refreshList')
              }
            }).catch(() => {

            })
          } else {
            editQuestionIn(data).then(res => {
              if (res.code === '000000') {
                that.$message({
                  type: 'success',
                  message: '修改成功'
                })
                that.listQuery = {}
                that.oneTitleInfo.txt.html('')
                this.faqTitlePop = false
                that.$emit('refreshList')
              }
            }).catch(() => {

            })
          }
        } else {
          that.$message({
            type: 'warning',
            message: '请输入必填项'
          })
        }
      })
    },
    questionIn(ids) {

      questionIn(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
          this.oneTitleInfo.txt.html(res.data.content)
          this.getTwo(res.data.levelOneId)
        }
      }).catch(() => {

      })
    },
    upload(file, insert) {

      const that = this
      that.uuid = that.get_uuid()
      const tempName = file[0].name.split('.')
      const fileName = `santao_stip/crm/faq/${that.uuid}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file[0]// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {

          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/faq/${that.uuid}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') { // insert 图片插入编辑框显示
              insert(res.data.url)
            }
          })
        }
      })
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    },
    changeInit() {
      this.listQuery = {}
      this.oneTitleInfo.txt.html('')
    }
  }
}
</script>

<style scoped>

</style>
