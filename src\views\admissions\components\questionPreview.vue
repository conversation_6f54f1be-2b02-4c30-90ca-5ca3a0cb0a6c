<template>
  <el-dialog :visible.sync="questionPreviewPop" :title="questionPreviewTitle" :close-on-click-modal="!questionPreviewPop" width="50%" class="preview-pop" @close="changeInit">
    <div id="preview" class="preview-info">
      <h3 class="preview-info-title">
        <span>
          <em>题号:</em>
          <em v-if="previewObj.questionCode">{{ previewObj.questionCode }}</em>
        </span>
        <span>
          <i>正确答案</i>
          <span v-for="(item,index) in previewObj.questionAnswerList" :key="item.id">
            <em v-if="item.correctFlag===1">
              <i v-show="index===0">A:</i>
              <i v-show="index===1">B:</i>
              <i v-show="index===2">C:</i>
              <i v-show="index===3">D:</i>
            </em>
          </span>
        </span>
      </h3>
      <div class="preview-infos">
        <h3 v-if="previewObj.question&&previewObj.viewType!==1">{{ previewObj.question }}</h3>
        <h3 v-if="previewObj.viewType===1&&previewObj.question" v-html="previewObj.question" />
        <img v-if="previewObj.questionImage&&previewObj.questionImage!==null" :src="previewObj.questionImage" class="title-big">
      </div>
      <div class="answer-list">
        <el-row>
          <el-col v-for="(item,index) in previewObj.questionAnswerList" :key="item.id" :sm="{span:24}" :md="{span:24}" class="answer-in">
            <em v-show="index===0">A:</em>
            <em v-show="index===1">B:</em>
            <em v-show="index===2">C:</em>
            <em v-show="index===3">D:</em>
            <p :class="{actived:item.correctFlag===1}" class="line">
              <em v-if="item.answer&&previewObj.viewType!==1">{{ item.answer }}</em>
              <em v-if="item.answer&&previewObj.viewType===1" v-html="item.answer" />
              <img v-if="item.answerImage&&item.answerImage!=='false'" :src="item.answerImage" class="title-img">
              <i v-show="item.correctFlag===1" class="el-icon-success checked" />
            </p>
          </el-col>
        </el-row>
      </div>
      <div class="preview-infos parse">
        <h3>答案解析</h3>
        <p v-if="previewObj.analysis&&previewObj.viewType!==1">{{ previewObj.analysis }}</p>
        <p v-if="previewObj.analysis&&previewObj.viewType===1" v-html="previewObj.analysis" />
        <img v-if="previewObj.analysisImage&&previewObj.analysisImage!==null" :src="previewObj.analysisImage" class="title-big">
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { questionInfo } from '@/api/admissions'
export default {
  name: 'QuestionPreview',
  data() {
    return {
      questionPreviewPop: false,
      questionPreviewTitle: '试题预览',
      previewObj: {}
    }
  },
  methods: {
    questionInfo(ids) {
      questionInfo(ids).then(res => {
        if (res.code === '000000') {
          this.previewObj = res.data
          this.$nextTick(() => {
            if (this.commonsVariable.isMathjaxConfig) {
              this.commonsVariable.initMathjaxConfig()
            }
            this.commonsVariable.MathQueue('preview')
          })
        }
      })
    },
    changeInit() {

    }
  }
}
</script>

<style scoped>
  .preview-pop>> .el-dialog__body{
    padding: 0 !important;
  }
</style>
