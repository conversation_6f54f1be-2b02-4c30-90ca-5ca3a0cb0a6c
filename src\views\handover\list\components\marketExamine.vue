<template>
  <div>
    <el-dialog v-el-drag-dialog title="市场审核" :visible.sync="marketExamineDialog" :close-on-click-modal="!marketExamineDialog" class="departmentDialog" width="40%">
      <el-form ref="detailForm" :model="detail" :rules="marketExamineFormRules" label-width="140px">
        <el-row>
          <el-col :lg="{span:12}">
            <el-form-item label="合伙人：">
              <div>{{ detail.customer }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="加盟项目：">
              <div>{{ detail.projectName }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="加盟区域：">
              {{ detail.provinceName }}{{ detail.cityName }}{{ detail.areaName }}{{ detail.countyName }}
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="项目金额：">
              <div>{{ detail.payAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="审核类型：" prop="type">
              <el-radio-group v-model="detail.type">
                <el-radio :label="1">通过</el-radio>
                <el-radio :label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="备注：" prop="remark">
              <el-input v-model="detail.remark" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="marketExamineOrder">确 认</el-button>
        <el-button @click="marketExamineDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { marketExamine } from '@/api/handover'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'MarketExamineOrder',
  directives: {
    elDragDialog
  },
  props: {},
  data() {
    return {
      marketExamineDialog: false,
      detail: {},
      marketExamineFormRules: {
        type: [{ required: true, trigger: 'blur', message: '审核类型必选' }],
        remark: [{ required: true, trigger: 'blur', message: '审核备注必填' }]
      }
    }
  },
  created() {
  },
  methods: {
    /**
     * 市场审核交接单
     * */
    getDetail(data) {
      this.detail = data
      this.marketExamineDialog = true
    },
    marketExamineOrder() {
      const that = this
      const msg = that.detail.type === 1 ? '通过' : '拒绝'
      that.$refs['detailForm'].validate((valid) => {
        if (valid) {
          const params = {
            id: that.detail.id,
            remark: that.detail.remark,
            type: that.detail.type
          }
          marketExamine(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '您已经' + msg + '该审批',
                type: 'success'
              })
              that.marketExamineDialog = false
              /**
               * 通知父组件更新
               */
              this.$emit('refresh')
            }
          })
        }
      })
    }
  }
}
</script>
