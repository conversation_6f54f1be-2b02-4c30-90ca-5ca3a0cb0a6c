<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="学生姓名"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.id"
        placeholder="学生账号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.id"
        placeholder="所属机构"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.gradeId" placeholder="年级" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="客户端" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.productTypeId" placeholder="所属班型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in productTypeList" :key="item.id" :label="item.typeName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.productTypeId" placeholder="课程评分" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in productTypeList" :key="item.id" :label="item.typeName" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="listQuery.expireStartTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="反馈时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="课程评价编号" show-overflow-tooltip prop="id">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </el-table-column>
      <af-table-column label="学生姓名" prop="productName" show-overflow-tooltip />
      <af-table-column label="学生账号" prop="clientName" show-overflow-tooltip />
      <af-table-column label="年级" prop="productTypeName" show-overflow-tooltip />
      <af-table-column label="客户端" prop="unitPrice" />
      <af-table-column label="所属机构" prop="unitPrice" />
      <af-table-column label="所属班型" prop="unitPrice" />
      <af-table-column label="课程名称" prop="unitPrice" />
      <af-table-column label="评价内容" prop="expireTime" />
      <af-table-column label="课程评分" prop="unitPrice" />
      <af-table-column label="时间" prop="expireTime" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { clientCode, grades } from '@/api/classType'
export default {
  name: 'CourseFeedback',
  components: {
    Pagination
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      clientCode: [],
      grades: [],
      productTypeList: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
      this.getList()
      this.getCode()
      this.getGrades()
    })
  },
  methods: {
    getGrades() {
      grades().then(res => {
        if (res.code === '000000') {
          this.grades = res.data
        }
      })
    },
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 2) || []
      })
    },
    async getList() {
      // const that = this
      // that.listLoading = true
      // const params = Object.assign(that.listQuery)
      //
      // await getGoodsList(params).then(response => {
      //   that.list = response.data.records
      //   that.total = response.data.total
      //   that.listLoading = false
      // }).catch(() => {
      //
      // })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getDetail(row) { // 获取列表详情

    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
