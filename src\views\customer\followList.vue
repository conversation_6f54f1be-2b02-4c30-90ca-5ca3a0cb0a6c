<template>
  <div class="app-container">
    <div class="filter-container" @keydown.enter="getList">
          <el-button-group class="filter-item">
            <el-button @click="changeDateType('toDay')" :type="dateType === 'toDay'? 'primary' : 'default'">今日</el-button>
            <el-button @click="changeDateType('toWeek')"  :type="dateType === 'toWeek'? 'primary' : 'default'">本周</el-button>
            <el-button @click="changeDateType('toMonth')"  :type="dateType === 'toMonth'? 'primary' : 'default'">本月</el-button>
          </el-button-group>
          <el-date-picker
            @change="dateChange"
            :editable="false"
            :clearable="false"
            class="w-[220px] filter-item"
            v-model="followDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
          <el-input  class="filter-item" style="width: 240px;" v-model.trim="queryParams.createName" clearable placeholder="跟进人" />
          <el-button class="filter-item" size="mini" type="primary" @click="getList" @keydown.enter="queryList">查询</el-button>
          <el-button class="filter-item" size="mini" @click="handleReset">重置</el-button>
    </div>
    <div class="st-flex st-justify-end st-items-center st-py-[10px]">
      <!-- 导出总表   -->
      <el-button class="filter-item" size="mini" type="primary" @click="handleExport" icon="el-icon-upload">导出总表</el-button>
      <!--  导出跟进明细  -->
      <el-button class="filter-item" size="mini" type="primary" @click="handleExportDetail" icon="el-icon-upload">导出跟进明细</el-button>
    </div>
    <el-table  v-loading="tableData.loading" :data="tableData.list" border fit stripe highlight-current-row>
      <el-table-column label="序号" type="index" width="50"></el-table-column>
      <el-table-column label="跟进人" prop="createBy"></el-table-column>
      <el-table-column label="跟进客户总数" prop="clueCount"></el-table-column>
      <el-table-column label="跟进总条数" prop="followCount"></el-table-column>
      <el-table-column label="统计周期" prop="followDate"></el-table-column>
      <el-table-column label="操作" >
        <template slot-scope="scope">
          <el-button @click="followDetail(scope.row)" type="text" >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableData.total>0"
      :total="tableData.total"
      layout="total, sizes, prev, pager, next"
      :page.sync="queryParams.pageIndex"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <flow-detail-list ref="flowDetailListRef"></flow-detail-list>
  </div>
</template>

<script>
import {exportFollowUpLog, exportFollowUpLogDetail, getSelectFollowUpLog} from "@/api/customer";
import FlowDetailList from "@/views/customer/componets/flowDetailList.vue";
import moment from "moment";
import Pagination from "@/components/Pagination/index.vue";

export default {
  name: 'emptyList',
  components: {Pagination, FlowDetailList},
  data() {
    return {
      dialogVisible:true,
      dateType: 'toWeek',
      followDate:[],
      tableData: {
        loading: false,
        total: 0,
        list:[]
      },
      listLoading: false,
      queryParams:{
        pageIndex:1,
        pageSize:10,
        createName: '',
        startDate: '',
        endDate: ''
      }
    }
  },

  mounted() {
    this.getWeekStartAndEndTime()
    this.getList()
  },

  methods:{
    changeDateType(type) {
      if(type === this.dateType) return;
      this.dateType = type
      switch (type) {
        case 'toDay':
          this.getToDayStartAndEndTime()
          break;
        case 'toWeek':
          this.getWeekStartAndEndTime()
          break;
        case 'toMonth':
          this.getToMonthStartAndEndTime()
          break;
      }
      this.queryParams.pageIndex = 1;
      this.getList()
    },
    dateChange(value) {
      this.queryParams.startDate = value[0];
      this.queryParams.endDate = value[1];
      //如果日期在今日，本周，本月的开始时间与结束时间，则将对应的按钮选中
      if(this.queryParams.startDate === this.getToDayDateArray()[0] && this.queryParams.endDate === this.getToDayDateArray()[1]) {
        this.dateType = 'toDay'
       } else if(this.queryParams.startDate === this.getWeekDateArray()[0] && this.queryParams.endDate === this.getWeekDateArray()[1]) {
        this.dateType = 'toWeek'
      } else if(this.queryParams.startDate === this.getMonthDateArray()[0] && this.queryParams.endDate === this.getMonthDateArray()[1]) {
        this.dateType = 'toMonth'
      }else{
        this.dateType = ''
      }
      this.queryParams.pageIndex = 1;
      this.getList()
    },
    handleReset(){
      this.queryParams.createName = "";
      this.dateType = 'toWeek';
      this.getWeekStartAndEndTime();
      this.getList();
    },
    queryList() {
      this.queryParams.pageIndex = 1;
      this.getList();
    },
    getList(){
      this.tableData.loading =  true
      getSelectFollowUpLog(this.queryParams).then(response => {
        if(!response.data){
          this.queryParams.pageIndex = 1;
          this.tableData.list = [];
          this.tableData.total = 0;
        }else{
          this.tableData.list = response.data.records
          this.tableData.total = response.data.total
        }
      }).finally(() => {
        this.tableData.loading = false
      })
    },

    followDetail(item){
      this.$refs.flowDetailListRef.showDialog(Object.assign(item,{
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
      }));
    },

    /**
     * 导出跟进汇总Excel
     */
    handleExport()  {
      exportFollowUpLog(this.queryParams).then(response => {
        const blob = new Blob([response.data]);
        // 创建下载链接并触发下载
        const urlObj = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = urlObj;
        link.download = this.queryParams.startDate+"至"+this.queryParams.endDate+" 跟进汇总.xlsx";
        document.body.appendChild(link);
        link.click();

        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(urlObj);
      }).catch(async error=>{
        const response = await this.blobToString(error.data,'utf-8')
        this.$message({
          type: 'warning',
          message: JSON.parse(response).msg
        })
      })
    },

    handleExportDetail(){
      if(moment(this.queryParams.endDate).diff(this.queryParams.startDate, 'days') > 90){
        this.$message({
          type: 'warning',
          message: '时间段不能超过90天，请重新选择时间段！'
        })
        return;
      }
      exportFollowUpLogDetail({
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
      }).then(response => {
        const blob = new Blob([response.data]);
        // 创建下载链接并触发下载
        const urlObj = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = urlObj;
        link.download = this.queryParams.startDate+"至"+this.queryParams.endDate+"跟进明细.xlsx";
        document.body.appendChild(link);
        link.click();

        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(urlObj);
      }).catch(async error => {
        const response = await this.blobToString(error.data,'utf-8')
        this.$message({
          type: 'warning',
          message: JSON.parse(response).msg
        })
      })
    },

    //获取本周开始时间，结束时间
    getWeekStartAndEndTime() {
      moment.locale('zh-cn');
      this.queryParams.startDate = moment().startOf('week').format('YYYY-MM-DD');
      this.queryParams.endDate = moment().endOf('week').format('YYYY-MM-DD');
      this.followDate = [this.queryParams.startDate, this.queryParams.endDate];
    },

    //获取今日开始时间，结束时间
    getToDayStartAndEndTime() {
      moment.locale('zh-cn');
      this.queryParams.startDate = moment().format('YYYY-MM-DD');
      this.queryParams.endDate = moment().format('YYYY-MM-DD');
      this.followDate = [this.queryParams.startDate, this.queryParams.endDate];
    },

    //获取本月开始时间，结束时间
    getToMonthStartAndEndTime() {
      moment.locale('zh-cn');
      this.queryParams.startDate = moment().startOf('month').format('YYYY-MM-DD');
      this.queryParams.endDate = moment().endOf('month').format('YYYY-MM-DD');
      this.followDate = [this.queryParams.startDate, this.queryParams.endDate];
    },

    getToDayDateArray() {
      return [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')];
    },
    getWeekDateArray() {
      return [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')];
    },
    getMonthDateArray() {
      return [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')];
    },

    blobToString(blob, encoding = 'utf-8') {
        return new Promise((resolve, reject) => {
          // 创建 FileReader 实例
          const reader = new FileReader();

          // 处理读取成功的情况
          reader.onload = () => {
            resolve(reader.result);
          };

          // 处理读取失败的情况
          reader.onerror = () => {
            reject(new Error('Blob 转换字符串失败: ' + reader.error));
          };

          // 根据指定编码读取 Blob 内容
          reader.readAsText(blob, encoding);
        });
}
  }
}
</script>

<style scoped>

</style>
