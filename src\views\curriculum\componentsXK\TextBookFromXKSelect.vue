<template>
  <el-select v-model="tmpId" filterable clearable placeholder="请选择教材版本" :disabled="disabled">
    <el-option
            v-for="item in optionList"
            :key="item.id"
            filterable
            :label="item.name"
            :value="Number(item.id)">
    </el-option>
  </el-select>
</template>
<script>
import { getTextbookFromXK } from '@/api/exercises'

export default {
  name: 'TextBookFromXKSelect',
  data: function () {
    return {
      optionList: []
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [Number,String],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    productCode:{
      type: [Number,String],
      required: true,
    },
    xkwSubjectId:{
      type: [Number,String],
      required: false,
    },
    textBookVersionId:{
      type: [Number,String],
      required: false,
    },
    gradeId:{
      type: [Number,String],
      required: false,
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId)  : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  watch: {
    xkwSubjectId(){
      this.handleChange('')
      this.getList()
    },
    gradeId(){
      this.handleChange('')
      this.getList()
    },
    textBookVersionId(){
      this.handleChange('')
      this.getList()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.name : ''
      return this.$emit('change', value, selectedOption)
    },
    getList() {
      if(!this.xkwSubjectId || !this.textBookVersionId || !this.gradeId){
        this.optionList =[]
        return
      }
      this.loading = true
      getTextbookFromXK({  'gradeId':this.gradeId,'xkwSubjectId': this.xkwSubjectId, 'textBookVersionId': this.textBookVersionId}).then(response => {
        if (response.code === '000000') {
          const data =  JSON.parse(response.data);
          // // 从字符串转换为json
          this.optionList = data.items
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped>
</style>
