<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.customer"
        placeholder="请输入考点名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.customer"
        placeholder="请输入大纲名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.assignSatuts" placeholder="请选择考点状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in assignSatuts" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.value1"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="创建日期"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="考点ID" show-overflow-tooltip prop="createTime" />
      <af-table-column label="考点名称" show-overflow-tooltip prop="customerName" />
      <af-table-column label="大纲名称" prop="customerPhone" show-overflow-tooltip />
      <af-table-column label="考点状态" prop="location" show-overflow-tooltip />
      <af-table-column label="创建时间" prop="customerSource" />
      <af-table-column label="更新时间" prop="paymentStatus" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改教材弹框-->
    <examination-pop ref="examination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ExaminationPop from './components/examinationPop'
export default {
  name: 'Examination',
  components: {
    Pagination,
    ExaminationPop
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 10
      },
      assignSatuts: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
    })
  },
  methods: {
    async getList() {

    },
    handleFilter() {

    },
    handleReset() {

    },
    handleCreate() {
      this.$refs.examination.examinationPop = true
      this.$refs.examination.examinationTitle = '新增考点'
    },
    handleUpdate(row) {
      this.$refs.examination.examinationPop = true
      this.$refs.examination.examinationTitle = '修改考点'
    }
  }
}
</script>

<style scoped>

</style>
