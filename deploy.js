const scpClient = require('scp2')

// 测试服务器
const testServer = {
  host: '**************',
  port: 22,
  username: 'root',
  password: 'mSCV0KpNQjs9z8kU',
  path: '/usr/local/software/crm'
}

function run() {
  scpClient.scp('./dist', testServer, err => {
    if (err) {
      console.log(`${testServer.host} 部署失败`)
      throw err
    } else {
      console.log(`${testServer.host} 部署成功`)
    }
  })
}

run()
