<template>
  <div class="app-container bgGrey">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>收货人信息</span>
          </div>
          <div class="item">
            <el-form
              ref="deliveryForm"
              :model="deliveryAddress"
              size="small"
              label-width="100px"
              :disabled="logicType"
            >
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="收货人：">
                    <el-input v-model="deliveryAddress.userName" disabled />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="手机号：">
                    <el-input v-model="deliveryAddress.mobile" disabled />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="收货地址：">
                    {{ deliveryAddress.provinceName }}{{ deliveryAddress.cityName }}{{ deliveryAddress.areaName }}{{
                      deliveryAddress.countyName }}{{ deliveryAddress.address }}
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="发货时间：">
                    <el-date-picker
                      v-model="deliveryBaseInfo.deliveryTime"
                      type="datetime"
                      placeholder=""
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-form
              ref="deliveryBaseInfo"
              :model="deliveryBaseInfo"
              :rules="baseInfoRules"
              size="small"
              label-width="100px"
              :disabled="logicType"
            >
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="物流公司：" prop="shipCode">
                    <el-select v-model="deliveryBaseInfo.shipCode" style="width: 100%" placeholder="" filterable>
                      <el-option
                        v-for="item in expressList"
                        :key="item.expressCode"
                        :label="item.expressName"
                        :value="item.expressCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="快递单号：" prop="shipNo">
                    <el-input v-model="deliveryBaseInfo.shipNo" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="快递费用：" prop="shipAmount">
                    <el-input v-model="deliveryBaseInfo.shipAmount" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="发货类型：" prop="shipType">
                    <el-select v-model="deliveryBaseInfo.shipType" style="width: 100%" disabled filterable>
                      <el-option
                        v-for="item in shipTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>发货单商品信息</span>
          </div>
          <div class="text">
            <el-row>
              <el-col :xs="{span:24}" :sm="{span:24}">
                <el-table
                  :data="deliveryOrderDetails"
                  border
                  fit
                  stripe
                  highlight-current-row
                  style="width: 100%;"
                >
                  <el-table-column type="index" label="#" width="100" align="center" />
                  <el-table-column label="产品名称" width="120" show-overflow-tooltip>
                    <template slot-scope="{row}">
                      {{ row.productName }}
                    </template>
                  </el-table-column>
                  <el-table-column label="实发数">
                    <template slot-scope="{row}">
                      <div>
                        <span class="el-form--inline box text-right">
                          <el-input v-model="row.productNum" disabled />
                        </span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="序列号">
                    <template slot-scope="{row}">
                      <el-input v-model="row.serialNumber" :disabled="row.productType != 6 || (row.productType == 6 && !canEditSerialNumber) || logicType" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6" :offset="9" class="text-center">
        <el-button v-if="!logicType" type="primary" @click="updateDelivery">确&nbsp;认</el-button>
        <el-button v-if="logicType" @click="closeTabView">关&nbsp;闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getExpress, deliveryDetail, createDeliveryOrder } from '@/api/handover'
import { converseEnToCn, getShipType } from '@/utils/field-conver'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'LogisticCreate',
  directives: { elDragDialog },
  components: { },
  props: {},
  data() {
    return {
      logicType: false,
      shipTypeList: getShipType,
      deliveryAddress: {}, // 收货人信息
      deliveryBaseInfo: {}, // 物流单信息
      expressList: [], // 物流
      deliveryOrderDetails: [], // 产品明细集合
      canEditSerialNumber: true,
      shipType: '',
      baseInfoRules: {
        shipNo: { required: true, message: ' ', trigger: 'blur' },
        shipCode: { required: true, message: ' ', trigger: 'change' },
        shipAmount: { required: true, message: ' ', trigger: 'blur' }
      },
      logicNameType: ''
    }
  },
  computed: {},
  created() {
    const orderId = this.$route.query.orderId || ''
    this.logicType = this.$route.query.type === 'view'// 查看
    this.shipType = (this.$route.query.shipType === '1') ? 'deliver' : 'recDeliver' // 合伙人/推荐人发货
    this.getDeliveryDetail(orderId)
    this.getDeliveryExpress()
    this.logicNameType = this.$route.query.type || ''
    const tagsName = this.logicNameType === 'view' ? '发货单查看' : '发货单修改'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    checkValid() {
      let res = {
        flag: true
      }
      const tmpList = JSON.parse(JSON.stringify(this.deliveryOrderDetails))
      tmpList.forEach(item => {
        if (!!Number(item.productNum) === false) {
          res = {
            flag: false,
            msg: '请输入实发数'
          }
          return res
        }
        if (item.productType === 6 && !item.serialNumber) {
          res = {
            flag: false,
            msg: '请输入序列号'
          }
          return res
        }
      })
      return res
    },
    showRepet() {
      let repeatFlag = {
        flag: true
      }
      const tmpLists = JSON.parse(JSON.stringify(this.deliveryOrderDetails))
      const serialList = tmpLists.filter(item => item.productType === 6)

      var arrids = []
      arrids = serialList.map(item => {
        return item.serialNumber
      })

      if (new Set(arrids).size !== arrids.length) {
        repeatFlag = {
          flag: false,
          msg: '发货单商品信息的序列号不允许重复'
        }
        return repeatFlag
      }
      return repeatFlag
    },
    /**
     * 查看详情
     */
    getDeliveryDetail(id) {
      deliveryDetail(id).then(res => {
        if (res.code === '000000') {
          const data = res.data
          this.deliveryAddress = data.deliveryAddress
          this.deliveryOrderDetails = data.deliveryOrderDetails
          this.deliveryBaseInfo = {
            shipAmount: data.shipAmount,
            shipCode: data.shipCode,
            shipName: data.shipName,
            shipNo: data.shipNo,
            shipType: data.shipType,
            id: data.id,
            deliveryTime: data.deliveryTime,
            orderId: data.orderId
          }
          this.shipType = (this.deliveryBaseInfo.shipType === 1) ? 'deliver' : 'recDeliver' // 合伙人/推荐人发货
          this.canEditSerialNumber = data['orderBase'][this.shipType] < 2
        }
      })
    },
    /**
     * 创建发货单
     * */
    updateDelivery() {
      this.$refs['deliveryBaseInfo'].validate(valid => {
        if (valid) {
          const tmpValid = this.checkValid()
          const repeatValid = this.showRepet()
          if (!tmpValid.flag) {
            this.$message({
              message: tmpValid.msg,
              type: 'warning'
            })
            return
          }
          if (!repeatValid.flag) {
            this.$message({
              message: repeatValid.msg,
              type: 'warning'
            })
            return
          }
          const params = Object.assign({}, this.deliveryBaseInfo, { deliveryDetails: this.deliveryOrderDetails })
          createDeliveryOrder(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.closeTabView()
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    getDeliveryExpress() {
      getExpress().then(res => {
        if (res.code === '000000') {
          this.expressList = res.data
        }
      })
    },
    /**
     * 关闭当前弹窗
     * */
    closeTabView() {
      this.$store.dispatch('tagsView/delView', this.$route).then(res => {
        this.$router.go(-1)
      })
    },
    /**
     * 数据转换
     */
    setShipType(data) {
      return converseEnToCn(getShipType, data)
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
      this.areaList.countyId = data.countyId
    }
  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-table__expanded-cell {
    padding: 20px 20px;
  }

  .box {
    display: inline-block;
    width: 100px;
  }

  .text-right {
    text-align: right;
  }
</style>
