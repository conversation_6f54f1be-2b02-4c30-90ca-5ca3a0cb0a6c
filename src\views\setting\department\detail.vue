<template>
  <el-dialog v-el-drag-dialog title="部门详情" :visible.sync="departmentDetail" :close-on-click-modal="!departmentDetail" class="departmentDialog">
    <el-form :model="detail" label-width="140px">
      <el-form-item label="部门名称:">
        <el-input v-model="detail.departmentName" placeholder="请输入部门名称" />
      </el-form-item>
      <el-form-item label="上级部门:">
        <el-select v-model="detail.parentId" placeholder="默认无上级部门" filterable clearable>
          <el-option v-for="item in parentDepartmentList" :key="item.id" :value="item.id" :label="item.departmentName" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEmployeeDetail">确 定</el-button>
      <el-button type="primary" @click="departmentDetail = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDepartmentList, addDepartment, updateDepartmentList } from '@/api/system-setting'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'DepartmentDetail',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      departmentDetail: false,
      detail: {},
      parentDepartmentList: [],
      parentDepartment: '',
      departmentName: ''
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    getDetail() {
      const that = this
      getDepartmentList({ 'pageSize': 9999 }).then(res => {
        that.parentDepartmentList = res.data.records
        that.departmentDetail = true
      })
    },
    editDetail(data) {

      const that = this
      that.detail = data
      that.detail.parentId = data.parentId === 0 ? '' : data.parentId
      that.$forceUpdate()
      that.departmentDetail = true
      that.getDetail()
    },
    /**
     * 确认修改信息
     */
    confirmEmployeeDetail() {
      const that = this
      const params = Object.assign({}, { id: that.detail.id ? that.detail.id : '', departmentName: that.detail.departmentName, parentId: that.detail.parentId ? that.detail.parentId : 0 })
      if (!that.isEdit) {
        addDepartment(params).then(res => {
          that.departmentDetail = false
          that.detail = {}
        })
      } else {
        updateDepartmentList(params).then(res => {
          that.departmentDetail = false
        })
      }
      /**
       * 通知父组件更新
       */
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
</style>
