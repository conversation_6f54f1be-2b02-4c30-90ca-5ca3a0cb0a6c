<template>
  <el-dialog :visible.sync="showEditFile" :title="editFile" :close-on-click-modal="!showEditFile" width="60%" @close="changeInit">
    <el-form label-width="120px" :model="fileForm">
      <el-row :gutter="10">
        <el-col :span="16">
          <el-form-item label="路径名称">
            <el-input v-model="fileForm.menuName" />
          </el-form-item>
          <!--封面-->
          <el-form-item label="封面" v-if="cover">
            <ObsUploader :single="true"
                         :on-success="setCover"
                         :on-remove="removeCover"
                         :limit="1"
                         business-path="santao_stip/crm/download/"
                         :file-list.sync="coverObj"
                         accept='.jpg, .jpeg, .png,.gif'
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="confirmFile">确定</el-button>
            <el-button type="default" size="mini" @click="changeInit">取消</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
import { addMenu, editFileList, editMenu } from '@/api/download'
import ObsUploader from '@/components/upload/ObsUploader.vue'
import { getFileUrlArrayByObj } from '@/utils'


export default {
  name: 'addFileCoverPop',
  data() {
    return {
      editFile: '修改目录名称及封面',
      showEditFile: false,
      treeExpandData: [],   //通过接口获取默认展开节点id
      coverObj: {},
      fileForm: {},
      props: {
        label: 'menuName',
        children: 'children'
      }
    }
  },
  components: {
    ObsUploader
  },
  props: {
    cover: {
      type: Boolean,
      default: false
    },
    tree: {
      type: Array,
      default() {
        return []
      }
    },
    menuId: {
      type: Number,
      default: 0
    },
    clientCode:{
      type: [String,Number],
      default: ''
    }
  },
  computed: {
    treeData: {
      get() {
        this.getAndExpandAll(this.menuId)
        return this.tree
      },
    }
  },
  watch: {
    showEditFile(val) {
      if (val) {
        this.coverObj = getFileUrlArrayByObj(this.fileForm, 'menuName', 'coverLink')
      }
    },
  },
  methods: {
    changeInit() {
      this.showEditFile = false
      this.fileForm = {}
    },
    removeCover() {
      this.fileForm.coverLink = ''
      this.coverObj = []
    },
    setCover(data, file, list) {
      this.fileForm.coverLink = data.url
    },
    confirmFile() {
      // const parameEdit = {
      //   clientCode: this.fileForm.clientCode,
      //   dataId: this.fileForm.id,
      //   coverLink: this.fileForm.coverLink,
      //   dataName: this.fileForm.name,
      //   remark: this.fileForm.remark,
      //   menuId: this.fileForm.menuId,
      //   sort: this.fileForm.sort
      // }
      // editFileList(parameEdit).then(res => {
      //   if (res.code === '000000') {
      //     this.$message({
      //       message: '修改成功！',
      //       type: 'success'
      //     })
      //     this.showEditFile = false
      //     this.fileForm = {}
      //     this.$emit('refresh')
      //   }
      // }).catch(() => {
      //
      // })

      if (this.fileForm.id) { // 修改
        const paramsEdit = {
          clientCode: this.clientCode,
          menuId: this.fileForm.id,
          menuName: this.fileForm.menuName,
          coverLink: this.fileForm.coverLink,
        }
        editMenu(paramsEdit).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.showEditFile = false
            this.fileForm = {}
            this.$emit('refresh')
          }
        }).catch(() => {

        })
      }
      // else { // 新增
      //   const paramsAdd = {
      //     clientCode: that.clientCode,
      //     parentMenuId: that.parentMenuId,
      //     menuName: data.menuName
      //   }
      //   addMenu(paramsAdd).then(res => {
      //     if (res.code === '000000') {
      //       that.$message({
      //         message: '添加成功',
      //         type: 'success'
      //       })
      //       that.getTreeData(that.clientCode)
      //       this.scrollToCurrent(data)
      //     }
      //   }).catch(() => {
      //
      //   })
      // }
    }
  }

}
</script>
<style>
</style>
