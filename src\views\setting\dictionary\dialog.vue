<template>
  <el-dialog v-el-drag-dialog title="字典详情" :visible.sync="dictionaryDialog" :close-on-click-modal="!dictionaryDialog" width="30%">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="120px"
      :rules="baseInfoRules"
    >
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="字典名称：" prop="dictName"><el-input v-model="detail.dictName" placeholder="字典名称" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="字典编码：" prop="dictCode"><el-input v-model="detail.dictCode" placeholder="字典编码" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="描述：" prop="description"><el-input v-model="detail.description" placeholder="描述" /></el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="是否启用：" prop="valid">
            <el-select v-model="detail.valid" placeholder="是否启用" filterable>
              <el-option
                v-for="item in menuValidList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer text-center">
      <el-button type="primary" @click="confirmExpressDetail">确 定</el-button>
      <el-button @click="closeExpressDetail">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addDictionary, editDictionary } from '@/api/system-setting'
import { menuValidList } from '@/utils/field-conver'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'DictionaryDialog',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dictionaryDialog: false,
      detail: {},
      baseInfoRules: {
        dictName: { required: true, message: '字典名称必填', trigger: 'blur' },
        dictCode: { required: true, message: '字典编码必填', trigger: 'blur' },
        description: { required: true, message: '描述必填', trigger: 'blur' },
        valid: { required: true, message: '是否启用必选', trigger: 'blur' }
      },
      menuValidList: menuValidList
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      const that = this
      that.detail = {}
      that.dictionaryDialog = true
    },
    editDetail(data) {
      const that = this
      that.detail = data
      that.dictionaryDialog = true
    },
    /**
     * 确认修改信息
     */
    confirmExpressDetail() {
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!that.isEdit) {
            addDictionary(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '新增成功！',
                  type: 'success'
                })
              }
              that.dictionaryDialog = false
              that.$emit('refresh')
            })
          } else {
            editDictionary(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              }
              that.dictionaryDialog = false
              that.$emit('refresh')
            })
          }
        }
      })
    },
    closeExpressDetail() {
      this.dictionaryDialog = false
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
</style>
