const TokenKey = 'Authorization'
const menuList = 'menuList'
const btnList = 'btnList'
const userInfo = 'userInfo'
export function getToken() {
  return window.sessionStorage.getItem(TokenKey)
}

export function setToken(token) {
  return window.sessionStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return window.sessionStorage.removeItem(TokenKey)
}

export function setMenuList(menu) {
  return window.sessionStorage.setItem(menuList, JSON.stringify(menu))
}

export function getMenuList() {
  return JSON.parse(window.sessionStorage.getItem(menuList))
}

export function setBtns(btns) {
  return window.sessionStorage.setItem(btnList, JSON.stringify(btns))
}

export function getBtns() {
  return JSON.parse(window.sessionStorage.getItem(btnList))
}
export function setUserinfo(info) {
  return window.sessionStorage.setItem(userInfo, JSON.stringify(info))
}

export function getUserInfo() {
  return JSON.parse(window.sessionStorage.getItem(userInfo))
}
