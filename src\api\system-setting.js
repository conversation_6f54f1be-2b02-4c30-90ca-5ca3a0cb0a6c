import request from '@/utils/request'
import Qs from 'qs'
/**
 * 获取员工列表
 * @param data
 */
export function getEmployeeList(data) {
  return request({
    url: 'user/pageUser',
    method: 'get',
    params: data
  })
}
/**
 * 查看员工详情
 * @param data
 */
export function getEmployeeDetail(data) {
  return request({
    url: 'user/' + data,
    method: 'GET'
  })
}
/**
 * 增加员工详情
 * @param data
 */
export function addEmployeeDetail(data) {
  return request({
    url: 'user',
    method: 'POST',
    data: data
  })
}
/**
 * 修改员工详情
 * @param data
 */
export function editEmployeeDetail(data) {
  return request({
    url: 'user',
    method: 'PUT',
    data: data
  })
}
/**
 * 删除员工
 * @param data
 */
export function deleteEmployee(data) {
  return request({
    url: 'user/' + data,
    method: 'DELETE'
  })
}
/**
 * 离职员工
 * @param data
 */
export function dimission(data) {
  return request({
    url: 'user/dimission/' + data,
    method: 'PUT'
  })
}
/**
 * 复职员工
 * @param data
 */
export function reinstated(data) {
  return request({
    url: 'user/reinstated/' + data,
    method: 'PUT'
  })
}
/**
 * 禁用员工
 * @param data
 */
export function disableUser(data) {
  return request({
    url: 'user/disableUser/' + data,
    method: 'PUT'
  })
}
/**
 * 恢复员工
 * @param data
 */
export function recoveryUser(data) {
  return request({
    url: 'user/recoveryUser/' + data,
    method: 'PUT'
  })
}
/**
 * 查询角色列表
 * @param data
 */
export function getRoleList(data) {
  return request({
    url: 'role/list',
    method: 'get',
    params: data
  })
}
/**
 * 创建角色
 * @param data
 */
export function createRole(data) {
  return request({
    url: 'role/add',
    method: 'POST',
    data: data
  })
}
/**
 * 修改角色
 * @param data
 */
export function editRole(data) {
  return request({
    url: 'role/edit',
    method: 'POST',
    data: data
  })
}
/**
 * 根据角色查询权限
 * @param data
 */
export function queryRolePermisson(data) {
  return request({
    url: 'role/getAuthority/' + data,
    method: 'get'
  })
}
/**
 * 设置、修改角色的权限
 * @param data
 */
export function setRolePermission(data) {
  return request({
    url: 'role/authorize',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取员工的权限
 * @param data
 */
export function getEmployeePermission(data) {
  return request({
    url: 'role/getUserRole/' + data,
    method: 'get'
  })
}
/**
 * 设置、修改员工的权限
 * @param data
 */
export function setEmployeePermission(data) {
  return request({
    url: 'user/authorize',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取系统的菜单列表
 * @param data
 */
export function getSystemMenus(pageIndex, pageSize) {
  return request({
    url: `menu/list?pageIndex=${pageIndex}&pageSize=${pageSize}`,
    method: 'PUT'
  })
}

/**
 * 菜单是否禁用
 * @param data
 */
export function setMenuEnable(data) {
  return request({
    url: '/menu/enable',
    method: 'PUT',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    data: Qs.stringify(data)
  })
}
/**
 * 菜单树
 * @param data
 */
export function getMenuTree(data) {
  return request({
    url: '/menu/menuTree',
    method: 'PUT',
    data: data
  })
}
/**
 * 新增菜单
 * @param data
 */
export function createMenu(data) {
  return request({
    url: '/menu/add',
    method: 'POST',
    data: data
  })
}
/**
 * 查询菜单
 * @param data
 */
export function getMenuDetail(data) {
  return request({
    url: '/menu/' + data,
    method: 'GET'
  })
}
/**
 * 修改菜单
 * @param data
 */
export function editMenus(data) {
  return request({
    url: 'menu/edit',
    method: 'PUT',
    data: data
  })
}

/**
 * 查询部门列表
 * @param data
 */
export function getDepartmentList(data) {
  return request({
    url: 'sys/depts/page',
    method: 'GET',
    params: data
  })
}
/**
 * 新增部门
 * @param data
 */
export function addDepartment(data) {
  return request({
    url: 'sys/depts',
    method: 'POST',
    data: data
  })
}
/**
 * 修改部门
 * @param data
 */
export function updateDepartmentList(data) {
  return request({
    url: 'sys/depts',
    method: 'PUT',
    data: data
  })
}
/**
 * 删除部门
 * @param data
 */
export function deleteDepartMent(data) {
  return request({
    url: 'sys/depts/' + data,
    method: 'DELETE'
  })
}
/**
 * 合同列表
 * @param data
 */
export function getContractList(data) {
  return request({
    url: 'contracts/page',
    method: 'GET',
    params: data
  })
}
/**
 * 快递列表
 * @param data
 */
export function getExpressList(data) {
  return request({
    url: 'express/page',
    method: 'GET',
    params: data
  })
}
/**
 * 新增快递
 * @param data
 */
export function addExpress(data) {
  return request({
    url: 'express',
    method: 'POST',
    data: data
  })
}
/**
 * 修改快递信息
 * @param data
 */
export function editExpress(data) {
  return request({
    url: 'express',
    method: 'POST',
    data: data
  })
}
/**
 * 启用/停用 快递
 * @param data
 */
export function editExpressStatus(data) {
  return request({
    url: 'express/' + data,
    method: 'PUT'
  })
}
/**
 * 项目集合
 */
export function getProjects() {
  return request({
    url: 'projects',
    method: 'GET'
  })
}
/**
 * 获取项目产品详情
 * @param data
 */
export function getProductDetail(data) {
  return request({
    url: 'product/getProduct/' + data,
    method: 'get'
  })
}
/**
 * 项目列表
 * @param data
 */
export function getProjectList(data) {
  return request({
    url: 'projects/page',
    method: 'GET',
    params: data
  })
}
/**
 * 新增项目
 * @param data
 */
export function addProject(data) {
  return request({
    url: 'projects',
    method: 'POST',
    data: data
  })
}
/**
 * 修改项目
 * @param data
 */
export function editProject(data) {
  return request({
    url: 'projects',
    method: 'POST',
    data: data
  })
}
/**
 * 启用/停用 项目
 * @param data
 */
export function editProjectStatus(data) {
  return request({
    url: 'projects/' + data,
    method: 'PUT'
  })
}
/**
 * 产品列表
 * @param data
 */
export function getProductList(data) {
  return request({
    url: 'product/list',
    method: 'GET',
    params: data
  })
}
/**
 * 新增产品
 * @param data
 */
export function addProduct(data) {
  return request({
    url: 'product/add',
    method: 'POST',
    data: data
  })
}
/**
 * 修改产品
 * @param data
 */
export function editProduct(data) {
  return request({
    url: 'product/modify',
    method: 'PUT',
    data: data
  })
}
/**
 * 删除套餐
 * @param data
 */
export function deleteProduct(data) {
  return request({
    url: 'product/remove',
    method: 'put',
    params: data
  })
}
/**
 * 启用/停用 产品
 * @param data
 */
export function editProductStatus(data) {
  return request({
    url: 'product/enable',
    method: 'PUT',
    params: data
  })
}
/**
 * 字典列表
 * @param data
 */
export function getDictionaryList(data) {
  return request({
    url: 'dicts/page',
    method: 'GET',
    params: data
  })
}
/**
 * 新增字典
 * @param data
 */
export function addDictionary(data) {
  return request({
    url: 'dicts',
    method: 'POST',
    data: data
  })
}
/**
 * 修改字典
 * @param data
 */
export function editDictionary(data) {
  return request({
    url: 'dicts',
    method: 'POST',
    data: data
  })
}
/**
 * 启用/停用 字典
 * @param data
 */
export function editDictStatus(data) {
  return request({
    url: 'dicts/' + data,
    method: 'PUT'
  })
}
/**
 * 字典内容列表
 * @param data
 */
export function getDictionaryDetailList(data) {
  return request({
    url: 'dicts/items/page',
    method: 'GET',
    params: data
  })
}
/**
 * 新增字典
 * @param data
 */
export function addDictionaryDetail(data) {
  return request({
    url: 'dicts/items',
    method: 'POST',
    data: data
  })
}
/**
 * 修改字典
 * @param data
 */
export function editDictionaryDetail(data) {
  return request({
    url: 'dicts/items',
    method: 'POST',
    data: data
  })
}

/**
 * 获取产品详情
 * @param data
 */
export function getDetailPro(id) {
  return request({
    url: 'product/getProduct/' + id,
    method: 'get'
  })
}
/**
 * app升级列表
 * @param data
 */
export function getAppList(data) {
  return request({
    url: 'appUpdateRegion/list',
    method: 'POST',
    data: data
  })
}
/**
 * 添加app
 * @param data
 */
export function addAppList(data) {
  return request({
    url: 'appUpdateRegion/save',
    method: 'POST',
    data: data
  })
}
/**
 * 获取app详情
 * @param data
 */
export function getAppDetail(id) {
  return request({
    url: `appUpdateRegion/getAppUpdateRegionById`,
    method: 'GET',
    params: id
  })
}
/**
 * 修改app
 * @param data
 */
export function editAppList(data) {
  return request({
    url: 'appUpdateRegion/update',
    method: 'POST',
    data: data
  })
}
/**
 * 获取系统设置-学校列表
 * @param data
 */
export function getSchoolList(data) {
  return request({
    url: 'school/list',
    method: 'POST',
    data: data
  })
}
/**
 * 添加学校
 * @param data
 */
export function addSchools(data) {
  return request({
    url: 'school/save',
    method: 'POST',
    data: data
  })
}
/**
 * 获取学校详情
 * @param data
 */
export function getSchoolDetail(id) {
  return request({
    url: `school/getAppUpdateRegionById`,
    method: 'GET',
    params: id
  })
}
/**
 * 修改学校详情
 * @param data
 */
export function editSchools(data) {
  return request({
    url: `school/update`,
    method: 'POST',
    data: data
  })
}
/**
 * 修改学校详情
 * @param data
 */
export function enableSchool(id) {
  return request({
    url: `school/schoolStatus`,
    method: 'GET',
    params: id
  })
}
/**
 * 机构后台菜单列表
 * @param data
 */
export function institutionsList(data) {
  return request({
    url: `stip/menus/page`,
    method: 'POST',
    data: data
  })
}
/**
 * 机构后台菜单开启/关闭
 * @param data
 */
export function institutionsEnable(id) {
  return request({ // stip/menus/enable/{id}
    url: `stip/menus/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 机构后台菜单详情
 * @param data
 */
export function institutionsDetail(id) {
  return request({
    url: `stip/menus/${id}`,
    method: 'GET'
  })
}
/**
 * 机构后台修改菜单
 * @param data
 */
export function institutionsEdit(data) {
  return request({
    url: `stip/menus`,
    method: 'PUT',
    data: data
  })
}
/**
 * 机构后台新增菜单
 * @param data
 */
export function institutionsAdd(data) {
  return request({
    url: `stip/menus`,
    method: 'POST',
    data: data
  })
}
/**
 * 机构后台父菜单
 * @param data
 */
export function institutionsarent(data) {
  return request({
    url: `stip/menus`,
    method: 'POST',
    data: data
  })
}
/**
 * 查询所有菜单列表(树)
 * @param params
 */
export function institutionParent(params) {
  return request({
    url: `stip/menus`,
    method: 'GET',
    params: params || {}
  })
}

/**
 * 获取所有可用角色
 * @param data
 */
export function roleList(data) {
  return request({
    url: `role/all`,
    method: 'GET'
  })
}
/**
 * 分页查询运营经理
 * @param data
 */
export function operatorsList(data) {
  return request({
    url: `operators`,
    method: 'GET',
    params: data
  })
}
/**
 * 删除运营经理
 * @param data
 */
export function operatorsDel(id) {
  return request({
    url: `operators/${id}`,
    method: 'DELETE'
  })
}
/**
 * 停用/启用 运营经理
 * @param data
 */
export function operatorsEnable(id) {
  return request({
    url: `operators/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 查询运营经理可选用户
 * @param data
 */
export function pageEnableUser(data) {
  return request({
    url: `user/pageEnableUser`,
    method: 'GET',
    params: data
  })
}
/**
 * 新增运营经理可选用户
 * @param data
 */
export function addOpera(data) {
  return request({
    url: `operators`,
    method: 'POST',
    data: data
  })
}
/**
 * 运营经理的详情
 * @param data
 */
export function operaDetail(id) {
  return request({
    url: `operators/${id}`,
    method: 'GET'
  })
}
/**
 * 修改运营经理
 * @param data
 */
export function operaUpdate(data) {
  return request({
    url: `operators`,
    method: 'PUT',
    data: data
  })
}
/**
 * 查询角色可选的校区项目权限
 * @param data
 */
export function getEnabledRoleIns(data) {
  return request({
    url: `role/getEnabledRoleIns`,
    method: 'GET',
    params: data
  })
}
/**
 * 查询区域树 两级
 * @param data
 */
export function getCityTrees(data) {
  return request({
    url: `role/getRegionTree`,
    method: 'GET'
  })
}

/**
 * 新增菜单url
 * @param data
 * @returns {AxiosPromise}
 */
export function addMenuUrl(data) {
  return request({
    url: '/stip/menus/url',
    method: 'POST',
    data: data
  })
}

/**
 * 分页查询菜单列表
 * @param params
 * @returns {AxiosPromise}
 */
export function menuPageQry(menuId) {
  return request({
    url: '/stip/menus/url/' + menuId,
    method: 'GET'
  })
}

/**
 * 根据主键id启用禁用url
 * @param id
 * @returns {AxiosPromise}
 */
export function updMenuUrl(id) {
  return request({
    url: '/stip/menus/url/' + id,
    method: 'PUT'
  })
}

/**
 * 根据主键id删除url
 * @param id
 * @returns {AxiosPromise}
 */
export function delMenuUrl(id) {
  return request({
    url: '/stip/menus/url/' + id,
    method: 'DELETE'
  })
}
