<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="searchField" placeholder="姓名/手机号" class="filter-item" style="width: 160px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="isAllotted" placeholder="分配状态" filterable clearable class="filter-item" style="width: 120px;">
        <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="userIdQuery" filterable placeholder="跟进人员" clearable class="filter-item" style="width: 160px;">
        <el-option v-for="item in followLists" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <area-picker :area-list="areaList" :level="'2'" area-style="'width:200px'" class="filter-item" @getAreaList="getAreaList" />
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="申请开始日期"
        end-placeholder="申请结束日期"
        style="width:350px"
      />
      <el-button class="filter-item" size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button class="filter-item" size="mini" type="primary" @click="handleReset">重置</el-button>
      <el-button v-permission="['website:agent:export']" class="filter-item" size="mini" type="primary" @click="handleExport">导出</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50px" />
      <af-table-column label="姓名" show-overflow-tooltip prop="name" />
      <af-table-column label="手机号" show-overflow-tooltip prop="phone" />
      <af-table-column label="区域" width="200px">
        <template slot-scope="scope">
          <span>
            <em v-if="scope.row.provinceName">{{ scope.row.provinceName }}</em>
            <em v-if="scope.row.cityName">{{ scope.row.cityName }}</em>
          </span>
        </template>
      </af-table-column>
      <af-table-column label="地址" prop="address" width="400px" />
      <af-table-column label="分配状态" prop="isAllotted" width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.isAllotted===1">已分配</span>
          <span v-if="scope.row.isAllotted===0">未分配</span>
        </template>
      </af-table-column>
      <af-table-column label="跟进人员" prop="realName" />
      <af-table-column label="申请时间" prop="createTime" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" align="center" width="180" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['website:agent:distribution']" type="primary" size="mini" @click="distributionShow=true,distributionPlan(row)">分配</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageIndex"
      :limit.sync="pageSize"
      @pagination="getList"
    />
    <!--分配人员-->
    <el-dialog :visible.sync="distributionShow" :title="distributionTitle" :close-on-click-modal="!distributionShow" width="40%">
      <el-form>
        <el-form-item>
          <span style="padding-right:10px">
            <i class="red">*</i>
            <em>分配员工</em>
          </span>
          <el-select v-model="userId" filterable placeholder="请分配跟进人员" clearable class="filter-item" style="width:70%">
            <el-option v-for="item in followLists" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item class="opera-btns">
          <el-button type="primary" size="mini" @click="confirmFollow">确定</el-button>
          <el-button type="default" size="mini" @click="cancelFollow">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--分配人员-->
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker2'
import { listAgent, followList, allottWebsite } from '@/api/website'
export default {
  name: 'NewsList',
  components: { AreaPicker, Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      grades: [{ id: '1', name: '高一' }, { id: '2', name: '高二' }, { id: '3', name: '高三' }],
      pageIndex: 1,
      pageSize: 10,
      searchField: '',
      isAllotted: '',
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      distributionShow: false,
      distributionTitle: '分配',
      person: null,
      hotShowFlagList: [],
      followDate: [],
      typeList: [
        {
          id: 1,
          title: '已分配'
        }, {
          id: 0,
          title: '未分配'
        }
      ],
      followLists: [],
      userId: null,
      agentId: null,
      userIdQuery: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getFollow()
    })
  },
  methods: {
    async getList() {
      const params = {
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        city: this.areaList.cityId,
        province: this.areaList.provinceId,
        isAllotted: this.isAllotted,
        searchField: this.searchField,
        userId: this.userIdQuery,
        beginTime: this.followDate.length > 0 && this.followDate[0] ? this.followDate[0] : '',
        endTime: this.followDate.length > 0 && this.followDate[1] ? this.followDate[1] : ''
      }
      listAgent(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(() => {
        this.$message({ message: '查询失败', type: 'error' })
      })
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    // 搜索申请代理
    handleSearch() {
      this.getList()
    },
    // 重置申请代理
    handleReset() {
      this.areaList.cityId = ''
      this.pageIndex = 1
      this.pageSize = 10
      this.isAllotted = ''
      this.searchField = ''
      this.userIdQuery = ''
      this.followDate = []
      this.areaList.provinceId = ''
      this.getList()
    },
    handleFilter() {
      this.pageIndex = 1
      this.getList()
    },
    getselect() {
      this.getList()
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'website/agents/export?pageIndex=' + that.pageIndex + '&pageSize=' + that.pageSize
      url = url + '&userId=' + `${this.userIdQuery ? this.userIdQuery : ''}` + '&isAllotted=' + `${that.isAllotted ? that.isAllotted : ''}` + '&searchField=' + `${that.searchField ? that.searchField : ''}` + '&beginTime=' + `${that.followDate.length > 0 && that.followDate[0] ? that.followDate[0] : ''}` + '&endTime=' + `${that.followDate.length > 0 && that.followDate[1] ? that.followDate[1] : ''}` + '&province=' + `${that.areaList.provinceId ? that.areaList.provinceId : ''}` + '&city=' + `${that.areaList.cityId ? that.areaList.cityId : ''}`
      that.$confirm('确定导出数据?', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        that.listLoading = true
        const params = Object.assign({
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          city: this.areaList.cityId,
          province: this.areaList.provinceId,
          isAllotted: this.isAllotted,
          searchField: this.searchField,
          userId: this.userIdQuery,
          beginTime: this.followDate.length > 0 && this.followDate[0] ? this.followDate[0] : '',
          endTime: this.followDate.length > 0 && this.followDate[1] ? this.followDate[1] : ''
        })
        listAgent(params).then(res => {
          that.list = res.data.records || []
          that.total = res.data.total || 0
          that.listLoading = false
          if (url && that.list.length > 0) {
            a.href = url
            a.target = '_blank'
            document.body.appendChild(a)
            a.click()
          } else {
            setTimeout(() => {
              that.$message({
                type: 'warning',
                message: '没有可以导出的数据'
              })
            }, 500)
          }
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '没有可以导出的数据'
          })
        })
      }).catch(() => {
        that.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    distributionPlan(row) {
      this.agentId = row.id
      this.userId = row.userId
      this.getFollow()
    },
    getFollow() {
      followList().then(res => {
        const arrs = []
        if (res.code === '000000') {
          res.data.length > 0 ? res.data.forEach(item => {
            const objs = {
              id: item.id,
              title: `${item.realName}(${item.mobile})`
            }
            arrs.push(objs)
          }) : []
          this.followLists = arrs
        }
      }).catch(error => {

      })
    },
    confirmFollow() {
      if (!this.userId) {
        this.$message({
          message: '请选择分配人员',
          type: 'error'
        })
        return false
      }
      allottWebsite(this.agentId, this.userId).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '分配成功',
            type: 'success'
          })
          this.userId = null
          this.agentId = null
          this.distributionShow = false
          this.getList()
        }
      }).catch(error => {

      })
    },
    cancelFollow() {
      this.userId = null
      this.agentId = null
      this.distributionShow = false
    }
  }
}
</script>

<style scoped>
.opera-btns{
  display: flex;
  justify-content: center;
}
</style>
