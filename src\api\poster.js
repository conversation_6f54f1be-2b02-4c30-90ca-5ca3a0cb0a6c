import request from '@/utils/request'
/**
 * 获取海报分类
 * */
export function posterCate(type) {
  return request({
    url: `dict/selectDictByDictTypeCode?dictTypeCode=${type}`,
    method: 'GET'
  })
}
/**
 * 获取海报列表
 * */
export function posterList(data) {
  return request({
    url: `stip/poster/selectSystemClientPosterPage`,
    method: 'POST',
    data: data
  })
}

/**
 * 新增海报
 * */
export function addPoster(data) {
  return request({
    url: `stip/poster/saveSystemClientPoster`,
    method: 'POST',
    data: data
  })
}
/**
 * 删除海报
 * */
export function deletPoster(id) {
  return request({
    url: `stip/poster/deleteSystemClientPoster?clientPosterId=${id}`,
    method: 'GET'
  })
}
/**
 * 上架海报
 * */
export function setPoster(id, status) {
  return request({
    url: `stip/poster/setSystemClientPosterStatus?clientPosterId=${id}&posterStatus=${status}`,
    method: 'GET'
  })
}
/**
 * 编辑海报
 * */
export function updatePoster(data) {
  return request({
    url: `stip/poster/updateSystemClientPoster`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取智能终端banner列表
 * */
export function terminalList(clientCode) {
  return request({
    url: `banner/getHomeBanner?clientCode=${clientCode}`,
    method: 'GET'
  })
}
/**
 * 获取智能终端banner列表
 * */
export function terminalSet(data) {
  return request({
    url: `banner/saveOrUpdateHomeBanner`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取智能终端banner列表
 * */
export function getCode(clientCode) {
  return request({
    url: `stip/classTypes/listAll/client/${clientCode}`,
    method: 'GET'
  })
}
/**
 * 查询首页顶部banner
 * */
export function listBannerTop(stage) {
  return request({
    url: `homePage/listBanner?stage=${stage}`,
    method: 'GET'
  })
}
/**
 * 查询首页顶部banner
 * */
export function listModule(stage) {
  return request({
    url: `homePage/listModule?stage=${stage}`,
    method: 'GET'
  })
}

/**
 * 添加模块课型
 * */
export function moduleCourse(data) {
  return request({
    url: `homePage/moduleRelation/add`,
    method: 'POST',
    data: data
  })
}
/**
 * 查询模块课型
 * */
export function moduleCourseDetail(id) {
  return request({
    url: `homePage/moduleRelation/detail/${id}`,
    method: 'GET'
  })
}
/**
 * 编辑模块课型
 * */
export function moduleCourseUpdate(data) {
  return request({
    url: `homePage/moduleRelation/edit`,
    method: 'POST',
    data: data
  })
}
/**
 * 删除模块课程班型系列信息
 * */
export function moduleCourseDel(id) {
  return request({
    url: `homePage/moduleRelation/delete/${id}`,
    method: 'GET'
  })
}
/**
 * 添加模块
 * */
export function addModule(data) {
  return request({
    url: `homePage/module/add`,
    method: 'GET',
    params: data
  })
}
/**
 * 编辑模块
 * */
export function editModule(data) {
  return request({
    url: `homePage/module/edit`,
    method: 'GET',
    params: data
  })
}
/**
 * 模块详情
 * */
export function detailModule(id) {
  return request({
    url: `homePage/module/detail/${id}`,
    method: 'GET'
  })
}
/**
 * 删除模块
 * */
export function delModule(id) {
  return request({
    url: `homePage/module/delete/${id}`,
    method: 'GET'
  })
}
