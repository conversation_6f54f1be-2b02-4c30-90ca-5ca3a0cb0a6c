<template>
  <el-dialog :title="areaTitle" :visible.sync="areaShow" @close="closed">
    <el-form ref="areaForms" :model="areaForm" :rules="areaRules">
      <el-form-item label="地址名称" prop="name">
        <el-input v-model="areaForm.name" placeholder="请输入地址名称" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="父区域id" prop="parentId">
        <el-input v-model="areaForm.parentId" placeholder="请输入父区域id" :disabled="isEdit" />
      </el-form-item>
      <el-form-item v-if="flagNum===1" label="地址编号" prop="id">
        <el-input v-model="areaForm.id" placeholder="请输入地址编号" />
      </el-form-item>
      <el-form-item label="区域行政码" prop="areaCode">
        <el-input v-model="areaForm.areaCode" placeholder="请输入区域行政码" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="区域等级" prop="level">
        <el-select v-model="areaForm.level" placeholder="请选择区域等级" filterable class="filter-item" style="width: 120px;" clearable :disabled="isEdit">
          <el-option
            v-for="item in grades"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-if="flagNum!==3" type="infor" size="mini" @click="cancelArea">取消</el-button>
        <el-button v-if="flagNum===1" type="primary" size="mini" @click="addConfirmArea">确定</el-button>
        <el-button v-if="flagNum===2" type="primary" size="mini" @click="editConfirmArea">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { grades } from '@/utils/field-conver'
import { addRegion, editRegion } from '@/api/addressList.js'
export default {
  data() {
    return {
      areaTitle: '',
      areaShow: false,
      grades: grades,
      areaForm: {

      },
      areaRules: {
        id: { required: true, message: '请输入地址编号', trigger: 'blur' },
        name: { required: true, message: '请输入地址名称', trigger: 'blur' },
        parentId: { required: true, message: '请输入上级地址编号id', trigger: 'blur' },
        level: { required: true, message: '请选择区域等级', trigger: 'blur' }
      },
      flagNum: -1,
      isCountry: '',
      isEdit: false
    }
  },
  methods: {
    setTile(title, flagNum, isCountry) { // 修改是2，新增是1
      if (title && flagNum) {
        this.areaTitle = title
        this.flagNum = flagNum
        this.isCountry = isCountry
      } else {
        this.areaTitle = ''
        this.flagNum = -1
        this.isCountry = ''
      }
    },
    setArea(obj) {
      if (obj) {
        this.areaForm = obj
      } else {
        this.areaForm = {}
      }
    },
    cancelArea() {
      this.areaShow = false
      this.flagNum = -1
      if (this.$refs.areaForms) {
        this.$refs.areaForms.clearValidate()
      }
    },
    closed() {
      this.areaShow = false
      this.flagNum = -1
      if (this.$refs.areaForms) {
        this.$refs.areaForms.clearValidate()
      }
    },
    addConfirmArea() { // 新增
      const parama = Object.assign({}, this.areaForm)
      this.$refs.areaForms.validate((valid) => {
        if (valid) {
          addRegion(parama).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增区域成功',
                type: 'success'
              })
              this.areaShow = false
              this.$emit('updateArea')
              this.areaForm = {}
              this.flagNum = -1
            }
          }).catch((res) => {
            this.areaShow = false
            this.areaForm = {}
            this.flagNum = -1

          })
        } else {

          return false
        }
      })
    },
    editConfirmArea() { // 修改区域
      const param = Object.assign({}, this.areaForm)
      this.$refs.areaForms.validate((valid) => {
        if (valid) {
          editRegion(param).then(res => {

            if (res.code === '000000') {
              this.$message({
                message: '修改区域成功',
                type: 'success'
              })
              this.areaShow = false
              this.$emit('updateArea')
              this.areaForm = {}
              this.flagNum = -1
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
</style>
