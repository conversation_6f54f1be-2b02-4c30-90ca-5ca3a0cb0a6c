<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="100px" :disabled="!isEdit" :rules="aiRules">
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 15px;">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约开始日期：" label-width="110px">
                    <span>{{ detail.contractOrder.signStartTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约结束日期：" label-width="110px">
                    <span>{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin:15px 0">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="22">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row :gutter="10">
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同编号：" label-width="120px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同名称：" label-width="120px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：" label-width="120px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本：" label-width="120px">
                    <div>特色班型</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="120px">
                    <el-radio-group v-model="detail.contractType" class="radios">
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="区域类型：" prop="cooperationType" label-width="120px">
                    <el-radio-group v-model="detail.cooperationType" class="radios" disabled>
                      <el-radio :label="1">独家</el-radio>
                      <el-radio :label="0">单点</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :sm="24" :md="24">
                  <el-form-item label="合同期限：" required label-width="120px">
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="金额/元：" label-width="120px" required>
                    <el-input v-model.number="cooperationFee" placeholder="填入合作费" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="课时/小时：" label-width="120px" required>
                    <el-input v-model.number="courseConsumption" placeholder="填入课耗小时数" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区地址：" label-width="120px" porp="schoolAddress" required>
                    <el-input v-model="detail.schoolAddress" placeholder="填入校区地址" maxlength="50" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                  <el-form-item label="备注：" label-width="120px">
                    <el-input v-model="detail.remark" type="textarea" maxlength="255" show-word-limit />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>
  </div>
</template>
<script>
import { getContractDetail, modifyContractDetail, getCreatContract } from '@/api/contract'
import {
  converseEnToCn,
  getAreaSingle,
  businessTypeList,
  channelList,
  orderStatusList,
  contractClass,
  getContractStatus
} from '@/utils/field-conver'
export default {
  name: 'Yc',
  data() {
    return {
      detail: {},
      aiRules: {
        cooperationType: { required: true, message: ' ', trigger: 'change' },
        contractType: { required: true, message: ' ', trigger: 'change' },
        schoolAddress: { required: true, trigger: 'blur', message: '请输入校区地址  ' },
        cooperationFee: { required: true, message: ' ', trigger: 'blur' },
        courseConsumption: { required: true, message: ' ', trigger: 'blur' }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      timeArr: [],
      cooperationFee: 0,
      courseConsumption: 0
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? '特色班型-编辑合同' : '特色班型-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = res.data
        that.timeArr = [that.detail.startTime, that.detail.endTime] || []

        that.cooperationFee = res.data.yeChenSpecialClassContract !== null ? res.data.yeChenSpecialClassContract.cooperationFee : 0
        that.courseConsumption = res.data.yeChenSpecialClassContract !== null ? res.data.yeChenSpecialClassContract.courseConsumption : 0
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getCreatContract() {
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = res.data
        that.timeArr = [that.detail.startTime, that.detail.endTime] || []

        that.cooperationFee = res.data.yeChenSpecialClassContract !== null ? res.data.yeChenSpecialClassContract.cooperationFee : 0
        that.courseConsumption = res.data.yeChenSpecialClassContract !== null ? res.data.yeChenSpecialClassContract.courseConsumption : 0
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.contractEnterprise = res.data.contractEnterprise || {}
        this.detail.contractSignatory = res.data.contractSignatory || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        this.detail.contractOrder = res.data.contractOrder || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    /**
     * 确认修改信息
     */
    confirmEdit(num) {
      if (!this.detail.contractType) {
        this.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!this.timeArr || (this.timeArr && this.timeArr.length < 2)) {
        this.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.schoolAddress) {
        this.$message({
          message: '校区地址不能为空',
          type: 'warning'
        })
        return
      }

      if (!this.cooperationFee) {
        this.$message({
          message: '合作费不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.courseConsumption) {
        this.$message({
          message: '课耗小时数不能为空',
          type: 'warning'
        })
        return
      }
      const that = this

      // that.detail.aiContractDTO = that.detail.aiContract
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          const yeChenSpecialClassContract = {
            cooperationFee: that.cooperationFee,
            courseConsumption: that.courseConsumption
          }
          const data = Object.assign(that.detail, { operateType: num, id: that.detail.contractId, yeChenSpecialClassContractDTO: yeChenSpecialClassContract, startTime: that.timeArr[0],
            endTime: that.timeArr[1] })
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '保存成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    }
  }

}
</script>

<style scoped>

</style>
