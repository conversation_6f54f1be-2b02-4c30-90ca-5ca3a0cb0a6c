import request from '@/utils/request'
/**
 * 获取校区管理
 * @param data
 */
export function getDetail(id) {
  return request({
    url: `stip/schoolManage/${id}`,
    method: 'get'
  })
}
/**
 * 删除校区流量包
 * @param data
 */
export function removeFlowPackage(id) {
  return request({
    url: `stip/partnerBalance/removeFlowPackage?id=${id}`,
    method: 'DELETE'
  })
}
/**
 * 充值/减扣校区流量包
 * @param data
 */
export function setFlowPackage(data) {
  return request({
    url: 'stip/partnerBalance/flowRecharge',
    method: 'POST',
    data: data
  })
}

/**
 * 设置校区智能终端开通的项目  0高中&初中 1普高 2初中
 * @param data
 * @returns {AxiosPromise}
 */
export function setOpenProject(data) {
  return request({
    url: '/stip/schoolManage/macUpdate',
    method: 'GET',
    params: data
  })
}
/**
 * 查询校区可选的流量包
 * @param data
 */
export function optionalPackage(data) {
  return request({
    url: 'stip/partnerBalance/getSelectFlowProducts',
    method: 'GET',
    params: data
  })
}
/**
 * 获取流量包商品可用班型或科目
 * @param data
 */
export function packageList(id) {
  return request({
    url: `stip/partnerBalance/getFlowUseRange/${id}`,
    method: 'GET'
  })
}
/**
 * 校区设置流量包
 * @param data
 */
export function setPackages(data) {
  return request({
    url: 'stip/partnerBalance/addFlowPackage',
    method: 'POST',
    data: data
  })
}
/**
 * 获取流量包详情
 * @param data
 */
export function packageDetail(id) {
  return request({
    url: `stip/partnerBalance/getFlowPackage/${id}`,
    method: 'GET'
  })
}
/**
 * 开通审课权限
 * @param data
 */
export function openAuditRole(id) {
  return request({
    url: `stip/schoolManage/openAuditRole?schoolId=${id}`,
    method: 'GET'
  })
}
/**
 * 设置播放源
 * @param data
 */
export function setUpPlay(playSourceId, schoolId) {
  return request({
    url: `stip/schoolManage/setUpPlay?playSource=${playSourceId}&schoolId=${schoolId}`,
    method: 'GET'
  })
}
/**
 * 转移学生到基地校
 * @param data
 */
export function transferBaseSchool(schoolId) {
  return request({
    url: `stip/schoolManage/transferBaseSchool?schoolId=${schoolId}`,
    method: 'GET'
  })
}
/**
 * 添加校区延期
 * @param data
 */
export function addDelayLog(data) {
  return request({
    url: 'stip/schoolManage/delay',
    method: 'PUT',
    data: data
  })
}

/**
 * 修改校区账号
 * @param data
 * @returns {AxiosPromise}
 */
export function changeSchoolAccount(data) {
  return request({
    url: `stip/schoolManage/resetAccount?schoolId=${data.schoolId}&account=${data.account}`,
    method: 'PUT',
    data: data
  })
}
/**
 * 获取学生列表
 * @param data
 */
export function getStudents(data, mainSchoolId) {
  return request({
    url: `stip/schoolManage/studentList/${mainSchoolId}`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取校区管理的智能终端列表
 * @param data
 */
export function getIntelligentList(data) {
  return request({
    url: 'stip/schoolManage/terminalList',
    method: 'POST',
    data: data
  })
}

/**
 * 同步盒子到其他校区
 * @param data
 * @returns {AxiosPromise}
 */
export function syncBoxToOtherSchool(data) {
  return request({
    url: 'stip/schoolManage/terminalSync',
    method: 'POST',
    data: data
  })
}

/**
 * 获取第三方升学规划的数据
 * @returns {*}
 */
export function getSXGHData(id) {
  return request({
    url: '/youxue/getYouXue',
    method: 'GET',
    params: { schoolId: id }
  })
}

/**
 * 对第三方升学规划进行充值
 * @param data
 * @returns {*}
 */
export function updateSXGHData(data) {
  return request({
    url: '/youxue/setYouXue',
    method: 'POST',
    data: data
  })
}

/**
 * 删除校区管理的智能终端列表
 * @param data
 */
export function delIntelligent(id) {
  return request({
    url: `stip/schoolManage/deleteDevice?id=${id}`,
    method: 'GET'
  })
}
/**
 * 批量删除校区管理的智能终端列表
 * @param data
 */
export function deleteDevices(data) {
  return request({
    url: '/stip/schoolManage/deleteDevices',
    method: 'POST',
    data
  })
}
/**
 * 新增校区管理的智能终端列表
 * @param data
 */
export function addlIntelligent(data) {
  return request({
    url: 'stip/schoolManage/saveDevice',
    method: 'POST',
    data: data
  })
}
/**
 * 修改校区管理的智能终端列表
 * @param data
 */
export function updateIntelligent(data) {
  return request({
    url: 'stip/schoolManage/updateDevice',
    method: 'POST',
    data: data
  })
}
/**
 * 学生列表查询条件-公办校
 * @param data
 */
export function partnerSchool(partnerSchoolId) {
  return request({
    url: `school/list/partnerSchool/${partnerSchoolId}`,
    method: 'GET'
  })
}
/**
 * 学生列表查询条件-学校
 * @param data
 */
export function schoolList(mainSchoolId) {
  return request({
    url: `stip/schools/listBranchSchools/${mainSchoolId}`,
    method: 'GET'
  })
}
/**
 * 校区管理--智能终端定位列表
 * @param data
 */
export function terminalGPSList(data) {
  return request({
    url: 'stip/schoolManage/terminalGPSList',
    method: 'POST',
    data: data
  })
}
/**
 * 校区管理--重置密码
 * @param data
 */
export function resetPwd(pwd, schoolId) {
  return request({
    url: `stip/schoolManage/resetPwd?pwd=${pwd}&schoolId=${schoolId}`,
    method: 'POST'
  })
}
/**
 * 校区管理--修改流量包
 * @param data
 */
export function editFlowPackage(data) {
  return request({
    url: 'stip/partnerBalance/editFlowPackage',
    method: 'PUT',
    data: data
  })
}
/**
 * 查询学生开通班型详情
 * @param data
 */
export function studentDetails(studentId) {
  return request({
    url: `stip/userProjects/getUserClassInfo/${studentId}`,
    method: 'GET'
  })
}
/**
 * 区域类型设置
 * @param data
 */
export function setLocationModelType(data) {
  return request({
    url: `stip/schoolManage/setLocationModelType`,
    method: 'PUT',
    data: data
  })
}
/**
 * 获取区域类型设置初始值
 * @param data
 */
export function getLocationModelType(institutionId, mainSchoolId) {
  return request({
    url: `stip/schoolManage/getLocationModelType/${institutionId}/${mainSchoolId}`,
    method: 'GET'
  })
}
/**
 * 开通/关闭跨区域
 * @param data
 */
export function openRegion(schoolId) {
  return request({
    url: `stip/schoolManage/openRegion?schoolId=${schoolId}`,
    method: 'GET'
  })
}
/**
 * 查询机构播课设备类型
 * @param data
 */
export function getSchoolPlayDeviceTypes(schoolId) {
  return request({
    url: `stip/schools/getSchoolPlayDeviceTypes/${schoolId}`,
    method: 'GET'
  })
}
/**
 * 设置机构播课设备类型
 * @param data
 */
export function setSchoolPlayDeviceTypes(playDeviceTypes, schoolId) {
  return request({
    url: `stip/schools/setSchoolPlayDeviceTypes?playDeviceTypes=${playDeviceTypes}&schoolId=${schoolId}`,
    method: 'PUT'
  })
}
/**
 * 查询学生转移校区记录
 * @param data
 */
export function getTransferSchool(userProjectId) {
  return request({
    url: `stip/userProjects/listTransferLogs/${userProjectId}`,
    method: 'GET'
  })
}
/**
 * 设置学生转移校区
 * @param data
 */
export function setTransferSchool(data) {
  return request({
    url: `stip/userProjects/transfer`,
    method: 'POST',
    data: data
  })
}
/**
 * 流量包转换记录
 * @param data
 */
export function transferPackages(schoolId, data) {
  return request({
    url: `stip/partnerBalance/pagePartnerBalanceConvertLogs/${schoolId}`,
    method: 'GET',
    params: data
  })
}
/**
 * 流量包课时转换
 * @param data
 */
export function convert(data) {
  return request({
    url: `stip/partnerBalance/convert`,
    method: 'POST',
    data: data
  })
}
/**
 * 校区流量包基本信息
 * @param data
 */
export function listSchoolHasFlows(schoolId) {
  return request({
    url: `stip/partnerBalance/listSchoolHasFlows/${schoolId}`,
    method: 'GET'
  })
}
/**
 * 停用/启用校区
 * @param data
 */
export function enableSchool(schoolId) {
  return request({
    url: `stip/schoolManage/enableSchool/${schoolId}`,
    method: 'PUT'
  })
}
/**
 * 停用/启用校区
 * @param data
 */
export function enablePackage(id) {
  return request({
    url: `stip/partnerBalance/enable/${id}`,
    method: 'PUT'
  })
}

/**
 * 流量包-科目数充值
 * @param data
 * @returns {AxiosPromise}
 */
export function updateSubjectNum(data) {
  return request({
    url: `stip/schoolManage/updateSubjectNum`,
    method: 'POST',
    data: data
  })
}

export function resetSchoolName(data) {
  return request({
    url: `stip/schoolManage/resetSchoolName`,
    method: 'POST',
    params: data
  })
}

export function resetSchoolPartnerName(data) {
  return request({
    url: `stip/schoolManage/resetSchoolPartnerName`,
    method: 'POST',
    params: data
  })
}

// 优学评测充值
export function youXuePingCeRecharge(data) {
  return request({
    url: `/youxue/recharge`,
    method: 'POST',
    params: data
  })
}

// 优学评测余额
export function youXuePingCeBalance(data) {
  return request({
    url: `/youxue/getBalance`,
    method: 'GET',
    params: data
  })
}

/**
 * 添加套餐
 * @param data
 * @returns {AxiosPromise}
 */
export function addPackage(data) {
  return request({
    url: 'stip/partnerBalance/addPackages',
    method: 'POST',
    data: data
  })
}

/**
 * 获取班型列表
 * @param schoolId
 * @returns {AxiosPromise}
 */
export function getClassTypes(schoolId) {
  return request({
    url: `stip/schoolManage/classTypes/${schoolId}`,
    method: 'GET'
  })
}

/**
 * 保存商品套餐-v2-增加校区id
 * @param data
 */
export function saveProductPackagesForV2(data) {
  return request({
    url: '/stip/packages/saveProductPackagesForV2',
    method: 'POST',
    data: data
  })
}

/**
 * 获取商品套餐-v2
 * @param data
 */
export function getProductPackagesForV2(data) {
  return request({
    url: '/stip/packages/selectProductPackagesForV2',
    method: 'post',
    data: data
  })
}

// 删除商品套餐
export function deleteProductPackage(id) {
  return request({
    url: `/stip/packages/${id}`,
    method: 'DELETE'
  })
}

// 设置ai题库
export function setAiQuestionBank(data) {
  return request({
    url: `/stip/schools/openQuestion`,
    method: 'POST',
    data
  })
}

/**
 * 获取独家单点列表
 * @param
 */
export function getExclusivePointList(params) {
  return request({
    url: `/exclusive/linked/school/listExclusiveLinkedSchoolByAgencyId`,
    method: 'POST',
    data: params
  })
}

/**
 * 新增独家单点
 * @param data
 */
export function addExclusivePoint(data) {
  return request({
    url: '/exclusive/linked/school/addExclusiveLinkedSchool',
    method: 'POST',
    data: data
  })
}

/**
 * 删除独家单点
 * @param id
 */
export function deleteExclusivePoint(id) {
  return request({
    url: `/exclusive/linked/school/delete/${id}`,
    method: 'DELETE'
  })
}

/**
 * 清除定位
 * @param schoolId
 */
export function clearLatAndLon(schoolId) {
  return request({
    url: '/exclusive/linked/school/clearLatAndLon',
    method: 'PUT',
    params: {
      schoolId
    }
  })
}

// 查询当前独家校区下面配置的单点校区
export function listBranchSchoolByAgencyId(params) {
  return request({
    url: '/exclusive/linked/school/listBranchSchoolByAgencyId',
    method: 'POST',
    data: params
  })
}

/**
 * 更新定位
 * @param data
 */
export function updateLatAndLon(data) {
  return request({
    url: '/exclusive/linked/school/updateLatAndLon',
    method: 'PUT',
    data: data
  })
}
