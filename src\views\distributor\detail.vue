<template>
  <div class="app-container bgGrey distributor">
    <el-row :gutter="10">
      <el-form
        ref="form"
        size="small"
        :model="distributor"
        label-width="130px"
      >
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>上级经销商基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="上级经销商联系人:">
                    <div>{{ distributor.parentContactName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="手机号:">
                    <div>{{ distributor.parentContactPhone }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:24}" class="mt20">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>经销商信息</span>
              <span v-show="distributor.status==2" class="reject-info">驳回原因:{{ distributor.auditMsg }}</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="经销商联系人:">
                    <div>{{ distributor.contactName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="经销商联系号码:">
                    <div>{{ distributor.contactPhone }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="经销商名称:">
                    <div>{{ distributor.distributorName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="经销区域:">
                    <em v-show="distributor.provinceName">{{ distributor.provinceName }}|</em>
                    <em v-show="distributor.cityName">{{ distributor.cityName }}|</em>
                    <em v-show="distributor.areaName">{{ distributor.areaName }}</em>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:12}" :sm="{span:6}">
                  <el-form-item label="详细地址:">
                    <div>{{ distributor.address }}</div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="营业执照:">
                    <div>
                      <el-image
                        class="distributor-img"
                        :src="licenseImg"
                        :preview-src-list="license"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证:">
                    <div>
                      <el-image
                        class="distributor-img"
                        :src="identityPortraitImg"
                        :preview-src-list="identityPort"
                      />
                      <el-image
                        class="distributor-img"
                        :src="identityEmblemImg"
                        :preview-src-list="identityEmblem"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注:">
                    <div class="remark">{{ distributor.remark }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-form>
    </el-row>
    <div class="btns">
      <el-button v-show="distributor.status===0" type="default" size="mini" round @click="rejectPop=true">驳回</el-button>
      <el-button v-show="distributor.status===0" type="primary" size="mini" round @click="distributorConfirm">通过</el-button>
      <el-button v-show="distributor.status===1||distributor.status===2" type="default" size="mini" round @click="closedDistributor">关闭</el-button>
    </div>
    <el-dialog title="驳回原因" :visible.sync="rejectPop" :close-on-click-modal="!rejectPop">
      <el-input v-model="auditMsg" type="textarea" />
      <div class="btns">
        <el-button type="default" size="mini" round @click="cancelReject">取消</el-button>
        <el-button type="primary" size="mini" round @click="confirmReject">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { distributorDetail, distributorReject, distributorResolve } from '@/api/customer'
export default {
  inject: ['reload'],
  name: 'DistributorDetail',
  data() {
    return {
      distributor: {},
      licenseImg: '',
      identityPortraitImg: '',
      identityEmblemImg: '',
      license: [],
      identityPort: [],
      identityEmblem: [],
      rejectPop: false,
      auditMsg: '',
      rejectIn: '',
      id: this.$store.state.user.distributorId
    }
  },
  mounted() {
    this.getDistributorDetail()
  },
  methods: {
    getDistributorDetail() {
      distributorDetail(this.id).then(res => {
        if (res.code === '000000') {

          this.distributor = res.data
          this.licenseImg = Object.keys(res.data.attachments).length > 0 && res.data.attachments.licenseImg ? res.data.attachments.licenseImg : ''
          this.identityPortraitImg = Object.keys(res.data.attachments).length > 0 && res.data.attachments.identityPortraitImg ? res.data.attachments.identityPortraitImg : ''
          this.identityEmblemImg = Object.keys(res.data.attachments).length > 0 && res.data.attachments.identityEmblemImg ? res.data.attachments.identityEmblemImg : ''
          this.license = this.licenseImg ? [this.licenseImg] : []
          this.identityPort = this.identityPortraitImg ? [this.identityPortraitImg] : []
          this.identityEmblem = this.identityEmblemImg ? [this.identityEmblemImg] : []
        }
      }).catch(err => {

      })
    },
    cancelReject() {
      this.rejectPop = false
      this.rejectInfo = ''
    },
    confirmReject() {
      const param = {
        applyId: this.id,
        auditMsg: this.auditMsg
      }
      distributorReject(param).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.rejectPop = false
          this.closeIt()
        }
      }).catch(err => {

      })
    },
    distributorConfirm() {
      const that = this
      that.$confirm('确认资质无误，审批开通，审批通过之后系统自动开通经销商账号', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        distributorResolve(that.id).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: '操作成功',
              type: 'success'
            })
            that.closeIt()
          }
        }).catch(err => {

        })
      }).catch(() => {
        that.$message({
          message: '取消提交',
          type: 'warning'
        })
      })
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 500)
      })
    },
    closedDistributor() {
      this.closeIt()
    }
  }
}
</script>

<style scoped>
    .mt20{
        margin-top:20px;
    }
    .distributor-img{
        width: 150px;
        height: 150px;
        margin-right: 15px;
    }
    .remark{
        width: 100%;
        padding:0 10px;
        border-radius: 5px;
        border:1px #eaeaea solid;
        background: #eee;
        min-height: 100px;
    }
    .btns{
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    .reject-info{
        padding-left: 10px;
        font-size: 12px;
        color: red;
    }
    .distributor>>> .el-dialog__body{
        padding:0 20px 20px 20px !important;
    }
</style>
