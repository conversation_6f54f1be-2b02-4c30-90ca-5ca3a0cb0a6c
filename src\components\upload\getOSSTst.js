const OSS = require('ali-oss')
import { stsToken } from '@/api/oss.js'

let ossConfig = null
// 取消上传控制项
let isCancel = false

// 设置客户端请求访问凭证的地址
const getOSSClient = async(bucket) => {
  const res = await stsToken()
  ossConfig = JSON.parse(res.data)
  const client = new OSS({
    // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
    bucket: bucket || 'santaokaoqin',
    region: 'oss-cn-beijing',
    accessKeyId: ossConfig.body.credentials.accessKeyId,
    accessKeySecret: ossConfig.body.credentials.accessKeySecret,
    stsToken: ossConfig.body.credentials.securityToken,
    timeout: 1800000,
    // HTTPS (secure: true) or HTTP (secure: false) protocol
    secure: true
    // refreshSTSTokenInterval: 3000000,
    // refreshSTSToken: stsToken().then(res => {
    //   if (res.code === '000000') {
    //     ossConfig = JSON.parse(res.data)
    //     // localStorage.setItem("stsToken", res.data);
    //   }
    // })
  })

  return client
}

// const getStaticOSSClient = function(bucket) {
//   // return new OSS({
//   //   region: 'oss-cn-beijing',
//   //   // accessKeyId: 'LTAI5tA3bYh1hAkEM596uvn9',
//   //   // accessKeySecret: '******************************',
//   //   accessKeyId: 'LTAI5tJNnzETsrXEEpiYYorF',
//   //   accessKeySecret: '******************************',
//   //   bucket: bucket
//   // })
//   return getOSSClient(bucket);
// }

// 分片上传方法（没有设置分片相关设置，采用默认）
async function put(fileName, file, bucket) {
  try {
    const oss = await getOSSClient(bucket)
    const result = await oss.multipartUpload(fileName, file, {
      'headers': {
        'Access-Control-Allow-Origin': '*'
      },
      'progress': (progress) => {
        // console.log('progress:', progress)
        if (isCancel) {
          oss.cancel()
          // 复位
          isCancel = false
        }
      }
    })
    return result
  } catch (e) {
    console.log(e)
  }
}

// 设置取消上传标志位为true
async function cancelUpload() {
  isCancel = true
  return true
}

// 获取oss文件临时路径
async function getUrl(name) {
  try {
    const oss = await getOSSClient()
    const result = await oss.signatureUrl(name)
    return result
  } catch (e) {
    console.log(e)
  }
}

export {
  getOSSClient
}
