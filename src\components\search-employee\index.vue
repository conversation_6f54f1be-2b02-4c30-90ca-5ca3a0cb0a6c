<template>
  <div>
    <el-form
      ref="transferForm"
      :model="transferForm"
      :rules="transferFormRules"
    >
      <el-form-item prop="employeeRequire">
        <span slot="label">{{ label }}</span>
        <el-select
          v-model="transferForm.employeeSearchField"
          size="small"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="手机号/姓名"
          :remote-method="getOriginCustomerList"
          :loading="employeeLoading"
          @change="getId"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.id"
            :label="item.realName + ' ' + item.mobile"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getEmployeeList } from '@/api/customer'
export default {
  name: 'SearchEmployee',
  props: {
    label: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      employeeLoading: false,
      transferForm: {
        employeeSearchField: '' // 员工搜索字段
      },
      employeeList: [], // 员工列表
      transferFormRules: {
        employeeRequire: {
          required: true,
          message: '请输入转让人姓名或手机号',
          trigger: 'blur'
        }
      }
    }
  },
  methods: {
    /**
     * 获取员工列表，用来查询被转让的员工
     * */
    getOriginCustomerList(query) {
      this.employeeLoading = true
      if (query !== '') {
        getEmployeeList({ search: query }).then(res => {
          this.employeeLoading = false
          this.employeeList = res.data
        })
      } else {
        this.employeeList = []
      }
    },
    getId() {
      this.$emit('setEmployeeId', this.transferForm.employeeSearchField)
    }
  }
}
</script>

<style scoped>

</style>
