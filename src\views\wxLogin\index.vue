<!--企业微信登录-->
<template>
  <div class="st-bg-[#e9eaeb] st-h-full st-w-full">
      <div class="st-flex st-flex-col st-justify-center st-items-center st-pt-[40px] st-pb-[20px]">
        <p class="st-text-[32px] st-font-[800] st-text-[#C9091B]">三陶教育CRM系统</p>
        <p class="st-text-[16px] st-font-[400] st-text-gray-500">请使用企业微信，授权登录</p>
      </div>
      <div id="ww_login" class="st-bg-white st-p-[8px] st-border st-border-[rgba(6,15,26,.1)] st-border-solid st-flex st-justify-center st-w-[480px] st-m-[0_auto] st-rounded-[8px]">
      </div>
<!--      <div class="st-flex st-justify-center st-py-[40px]">
        <el-button type="primary" @click="backLogin" plain class="st-w-[290px] st-h-[42px]">密码登录</el-button>
      </div>-->
  </div>
</template>

<script>
import * as ww from '@wecom/jssdk'
import logo from "@/assets/img/collapseLogo.png"
import {getWxToken} from "@/api/wxLogin";
export default {
  name: 'wxLogin',
  data() {
    return {
      logo,
      redirect: undefined,
      otherQuery: {},
    }
  },
  mounted() {
    const _this = this;
    // 初始化登录组件
      const wwLogin = ww.createWWLoginPanel({
        el: '#ww_login',
        params: {
          login_type: 'CorpApp',
          appid: process.env.VUE_APP_WX_CORPID,
          agentid: process.env.VUE_APP_WX_AGENTID,
          redirect_uri: process.env.VUE_APP_WX_REDIRECT_URI, //跳转到企业微信内建的回调页面
          state: 'loginState',
          redirect_type: 'callback',
        },
        onCheckWeComLogin(event) {
          console.log(event);
          //获取企业微信桌面端登录状态回调
          // isWeComLogin: true 已登录，false 未登录
        },
        onLoginSuccess({ code }) {
          //企业微信登录成功回调
          _this.$store.dispatch('user/wxLogin', {code:code}).then(res => {
            _this.$router.replace({ path: _this.redirect || '/', query: _this.otherQuery })
          })
        },
        onLoginFail(err) {
          console.log("登录失败")
          console.log(err)
        },
      })
  },
  methods: {
    // 密码登录
    backLogin() {
      this.$router.replace({ path: '/login' })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
}
</script>

<style lang="scss">
#ww_login{
  box-shadow: 0 8px 16px rgba(0, 0, 0, .12);
}
iframe{
  html{
    display: none !important;
  }
}
</style>
