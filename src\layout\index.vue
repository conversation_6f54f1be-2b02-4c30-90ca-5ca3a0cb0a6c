<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div :class="{hasTagsView:needTagsView}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
      <el-dialog
              :v-if='dialogVisible'
              :visible.sync="dialogVisible"
              :show-close='true'
              center
              append-to-body
              :custom-class="(!videoFlag && !vodFlag)?'hideImgViewDialog':''"
              @close="() => (dialogVisible = false)">
        <el-image-viewer
                :z-index="9999"
                :visible.sync="dialogVisible"
                v-if="dialogVisible && !videoFlag && !vodFlag"
                :url-list="dialogImageUrl"
                :on-close="() => (dialogVisible = false)"></el-image-viewer>
        <video v-if="dialogVisible && videoFlag" :src="dialogImageUrl[0]" style="width: 1200px; height: 100%" controls="controls">
          您的浏览器不支持视频播放
        </video>
        <div v-if="dialogVisible && vodFlag" id="player-con-dialog"></div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import elImageViewer  from 'element-ui/packages/image/src/image-viewer'

export default {
  name: 'Layout',
  provide() {
    return {
      showImgPreview: this.showImgPreview
    }
  },
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    ElImageViewer: elImageViewer,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  data() {
    return {
      dialogImageUrl: [],
      videoFlag: false,
      vodFlag: false,
      dialogVisible: false,
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    showImgPreview(file, videoFlag, vodFlag, vid) {
      this.videoFlag = !!videoFlag
      //如果是数组
      if (file instanceof Array) {
        this.dialogImageUrl = file.map((item) => {
          return item.url
        })
      }
      else {
        const src = (file && file.currentTarget && file.currentTarget.currentSrc) || ''
        this.dialogImageUrl = file.url || file.src || src || file
        this.dialogImageUrl = [this.dialogImageUrl]
      }
      this.dialogVisible = true
      if (vodFlag && vid) {
        this.vodFlag = !!vodFlag
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";
  /deep/ .hideImgViewDialog {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
