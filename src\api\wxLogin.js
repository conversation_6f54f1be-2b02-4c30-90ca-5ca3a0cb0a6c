import request from '@/utils/request'

/**
 * 获取token
 * @param code
 * @returns {*}
 */
export function getWxToken(code) {
  return request({
    url: `/wxLogin/getToken?code=${code}`,
    method: 'GET'
  })
}

/**
 * 获取企业微信绑定url
 * @param url
 * @returns {AxiosPromise}
 */
export function getRedirectUri(url) {
  return request({
    url: `/wxLogin/getRedirectUri?url=${encodeURIComponent(url)}`,
    method: 'GET'
  })
}

/**
 * 解绑企业微信
 * @returns {AxiosPromise}
 */
export function unBindWx() {
  return request({
    url: `/wxLogin/unBind`,
    method: 'GET'
  })
}
