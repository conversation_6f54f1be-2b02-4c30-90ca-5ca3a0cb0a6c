<template>
  <div class="app-container bgGrey download">
    <el-row :gutter="10">
      <el-col :sm="{span:24}" :md="{span:8}">
        <div class="left inner">
          <div class="search">
            <el-select v-model="clientCode" placeholder="选择项目类型" filterable class="filter-item" @change="getTreeData">
              <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
            </el-select>
            <el-input v-model="filterText" placeholder="输入关键词过滤" style="margin:15px 0" />
          </div>
          <div class="add-parent">
            <el-input v-model="parentName" placeholder="请输入一级菜单名称">
              <template slot="append">
                <el-button type="text" size="mini" @click="addParent">添加</el-button>
              </template>
            </el-input>
          </div>
          <div class="dom-nodes" id="tree-box">
            <el-tree
                    id="tree"
                    ref="tree"
                    v-loading="loads"
                    :data="nodeList"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :default-expanded-keys="treeExpandData"
                    draggable
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                    @node-drop="handleDrop"
                    empty-text="请先在上方选择项目类型"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span>
                  <em v-if="!data.isEdit">{{ data.menuName }}</em>
                  <el-input v-if="data.isEdit" v-model="node.data.title" @blur.stop="editTitle(data)" />
                  <em v-if="data.dataUpdateTime&&data.dataUpdateTime!==null&&((new Date().getTime()-new Date(data.dataUpdateTime.replace(/-/g,'/')))/1000/3600/24<3)"
                      class="sign">New</em>
                </span>
                <span>
<!--                  如果节点的 classId =1，则显示-->
                  <template  v-if="menuType === 'download'">
                   <em :class="{'red':data.classId}"
                       class="el-icon-link down-icon"
                       @click.stop="() => linkClassAuth(node, data)"
                   />
                    </template>
                  <em
                          v-permission="['download:center:treeDel']"
                          class="el-icon-delete down-icon"
                          title="删除"
                          @click.stop="() => remove(node, data)"
                  />
                  <em
                          v-permission="['download:center:treeAppend']"
                          class="el-icon-folder-add down-icon"
                          title="添加子目录"
                          @click.stop="() => append(data)"
                  />
<!--                  <em-->
<!--                          v-permission="['download:center:treeEdit']"-->
<!--                          class="el-icon-picture-outline down-icon"-->
<!--                          title="添加文件封面"-->
<!--                          @click.stop="() => addFilePathCover(data)"-->
<!--                  />-->
                  <em
                          v-permission="['download:center:treeEdit']"
                          class="el-icon-edit-outline down-icon"
                          title="编辑目录"
                          @click.stop="() => addFilePathCover(data)"
                  />
                </span>
              </span>
            </el-tree>
          </div>
          <div class="upload-file">
            <div>
              <h4>*注意：请先选择路径再进入上传资料的页面！</h4>
              <el-button v-permission="['download:center:uploadFile']" type="primary" size="mini" style="width:45%"
                         @click="uploadFiles">上传资料
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :sm="{span:24}" :md="{span:16}">
        <div class="left inner">
          <h3 class="file-title">
            <p v-if="labelList.length>0">
              <span class="el-icon-s-home" />
              <em v-for="(name,index) in labelList" :key="index">
                <i>{{ name }}</i>
                <i v-show="index!==labelList.length-1">></i>
              </em>
            </p>
            <p v-show="list.length>0">
              <span>共有<em class="red-tips">{{ list.length }}</em>文件</span>
              <el-button v-permission="['download:center:downloadAll']" size="mini" type="primary" @click="downloadAll(list)">
                全部下载
              </el-button>
            </p>
          </h3>
          <div class="dom-list">
            <el-table
                    ref="multipleTable"
                    v-loading="dataLoading"
                    :data="list"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
            >
              <el-table-column
                      type="selection"
                      width="55"
              />
              <el-table-column
                      type="index"
                      label="序号"
                      width="55"
              />
              <el-table-column
                      label="名称"
                      prop="name"
              />
              <el-table-column
                      label="类型"
                      prop="type"
                      width="80"
              />
              <el-table-column
                      prop="length"
                      label="大小(单位:MB)"
                      sortable
                      width="150"
              />
              <el-table-column
                      prop="updateTime"
                      label="更新时间"
                      sortable
                      width="150"
                      show-overflow-tooltip
              />
              <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="300">
                <template slot-scope="scope">
                  <a v-if="typePreview.indexOf(scope.row.type)!=-1" v-permission="['download:center:downloadPriview']"
                     :href="scope.row.downloadLink" target="_brank" class="priview-download">预览</a>
                  <el-button v-permission="['download:center:downloadSingle']" type="primary" size="mini"
                             @click="downloadFile(scope.row)">下载
                  </el-button>
                  <el-button v-permission="['download:center:downloadEdit']" type="primary" size="mini"
                             @click="editFile(scope.row)">编辑
                  </el-button>
                  <el-button v-permission="['download:center:downloadDel']" type="danger" size="mini" @click="delFile(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-show="multipleSelection.length>0" class="choice-file">
            <div>
              <span>
                已选中
                <em class="red-tips">{{ multipleSelection.length }}</em>
                个文件
              </span>
              <el-button v-permission="['download:center:downloadSelect']" type="primary" size="mini"
                         @click="downloadAll(multipleSelection)">下载
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
            title="设置关联权限"
            :visible.sync="dialogVisible"
            width="500px"
            :before-close="closeDialog"
    >
      <div>
        关联班型
        <el-select v-model="classId" placeholder="请选择" filterable>
          <el-option
                  v-for="item in classOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
          </el-option>
        </el-select>
        <el-alert
                style="margin-top: 5px;"
                title="设置后该路径下的文件将仅对开通此班型的机构"
                close-text="知道了"
                type="warning">
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="saveAuth">确 定</el-button>
      </span>
    </el-dialog>
    <upload-pop ref="uploadFile" @refreshList="getTable(treeColumn)" />
    <edit-file ref="editFiles" :tree="nodeList" :cover="clientCode>=9" @refresh="getTable(treeColumn)" />
    <add-file-cover-pop ref="addCover" :client-code="clientCode" :cover="clientCode>=9" @refresh="getTreeData(clientCode)" ></add-file-cover-pop>
  </div>
</template>
<script>
import streamSaver from 'streamsaver'
import { createWriter } from '@/assets/js/zip-stream.js'
import UploadPop from './components/uploadPop'
import EditFile from './components/editFilePop'
import {
  treeData,
  treeSort,
  editParentMenu,
  delMenu,
  editMenu,
  addMenu,
  fileList,
  delFileList,
  getClassData,
  saveMenuClass
} from '@/api/download'
import AddFileCoverPop from '@/views/download/components/addFilePathCoverPop.vue'

export default {
  name: 'Download',
  components: {
    AddFileCoverPop,
    UploadPop,
    EditFile
  },
  props: {
    menuType: {
      type: String,
      default: 'download',
    },
  },
  data() {
    return {
      dialogVisible: false,
      currentNode: null,
      currentNodeData: null,
      classId: null,
      classOptions: [],
      projectList: [
        {
          id: 1,
          projectName: '三陶教育'
        },
        {
          id: 2,
          projectName: '芝麻艺考'
        },
        {
          id: 3,
          projectName: '烨晨双师'
        }
      ],
      filterText: '',
      clientCode: '',
      nodeList: [],
      treeExpandData: [],   //通过接口获取默认展开节点id
      closeTree: true,
      demoString: '',
      list: [],
      treeColumn: {}, // 点击树形数据获取的对象
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      multipleSelection: [],
      loads: false,
      parentMenuId: null,
      defaultProps: {
        id: 'id',
        label: 'menuName',
        children: 'children'
      },
      dataLoading: false,
      typePreview: ['png', 'jpg', 'jpeg', 'mp4', 'mov', 'rmvb', 'avi', 'pdf', 'txt'], // 可以直接预览的文件类型
      labelList: [],
      nodeParentAll: [],
      parentName: ''
    }
  },
  created() {
    if (this.menuType === 'train') {
      this.projectList = [
        {
          id: 9,
          projectName: '校区运营培训'
        }
      ]
      this.clientCode = 9
      this.getTreeData(this.clientCode)
    }
    else if (this.menuType === 'train_employ') {
      this.projectList = [
        {
          id: 10,
          projectName: '铁军运营培训'
        }
      ]
      this.clientCode = 10
      this.getTreeData(this.clientCode)
    }else if (this.menuType === 'miniConfig') {
      this.projectList = [
        {
          id: 11,
          projectName: '三陶合伙人小程序'
        }
      ]
      this.clientCode = 11
      this.getTreeData(this.clientCode)
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    // 通过节点的key（这里使用的是数据中的code属性，node-key="code"）获取并高亮显示指定节点，并展开其所有父级节点
    getAndExpandAll(nodeDataId) {
      if (nodeDataId) {
        this.treeExpandData=[]
        this.$nextTick(() => { // 等待树组件渲染完成再执行相关操作
          // 获取节点
          const node = this.$refs.tree.getNode(nodeDataId)
          if (node) {
            // 获取其所有父级节点
            this.getParentAll(node)
            if (this.nodeParentAll.length > 0) {
              // 将获取到的所有父级节点进行展开
              for (var i = 0, n = this.nodeParentAll.length; i < n; i++) {
                this.treeExpandData.push(this.nodeParentAll[i].data.id)
                // this.$refs.tree.store.nodesMap[this.nodeParentAll[i].data.code].expanded = true
              }
            }else{
              this.treeExpandData=[nodeDataId]
            }
            // 将节点高亮显示
            // this.$refs.tree.setCurrentKey(nodeDataId)
          }
        })
      }
    },
    // 获取所有父级节点
    getParentAll(node) {
      if (node) {
        this.nodeParentAll = []
        // 节点的第一个父级
        var parentNode = node.parent
        // level为节点的层级 level=1 为顶级节点
        for (var j = 0, lv = node.level; j < lv; j++) {
          if (parentNode.level > 0) {
            // 将所有父级节点放入集合中
            this.nodeParentAll.push(parentNode)
          }
          // 继续获取父级节点的父级节点
          parentNode = parentNode.parent
        }

        if (this.nodeParentAll.length > 1) {
          // 如果集合长度>1 则将数组进行倒叙.reverse() 其是就是将所有节点按照 从 顶级节点 依次往下排
          this.nodeParentAll.reverse()
        }
      }
    },
    scrollToParent(node){
      const parentNode=node.parent;
      this.getAndExpandAll(parentNode.data.id)
    },
    scrollToCurrent(currentNode){
      this.getAndExpandAll(currentNode.data.id)
    },
    getTreeData(val) {
      this.list = []
      this.labelList = []
      treeData(val).then(res => {
        this.loads = true
        if (res.code === '000000') {
          const treeLists = res.data && res.data.length > 0 ? res.data : []
          this.nodeList = treeLists || []
          this.loads = false
        }
      }).catch(() => {
        this.loads = false
      })
      switch (val) {
        case 1:
          val = '101' //普高
          break
        case 2:
          val = '301'  //艺考
          break
        case 3:
          val = '201' //初中
          break
        default:
          val = '401' //校区运营辅导
          break
      }


      getClassData(val).then(res => {
        if (res.code === '000000') {
          this.classOptions = res.data.map(item => {
            return {
              value: item.id,
              label: item.title
            }
          })
        }
      })
    },
    saveAuth(data) {
      if (!this.classId) {
        this.$message({
          message: '请选择班型',
          type: 'warning'
        })
        return
      }
      saveMenuClass(this.currentNodeData.id, this.classId).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '设置成功',
            type: 'success'
          })
          this.getTreeData(this.clientCode)
          this.closeDialog()
          this.scrollToCurrent(this.currentNode)
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.currentNode = null
      this.currentNodeData = null
      this.classId = null
    },
    append(data) {
      const newChild = { label: '增加菜单', flag: 0, isEdit: true, title: '增加菜单', dataUpdateTime: null, children: [] }
      this.parentMenuId = data.id
      if (!data.children) {
        this.$set(data, 'children', [])
      }
      data.children.push(newChild)
      this.scrollToCurrent(data.parent)
    },
    linkClassAuth(node, data) {
      this.dialogVisible = true
      this.currentNode = node
      this.currentNodeData = data
      this.classId = data.classId || null
    },
    remove(node, data) {
      if (data.id) {
        const params = {
          clientCode: this.clientCode,
          menuId: data.id
        }
        this.$confirm('此操作将删除该菜单以及所有旗下的文件, 是否继续?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // if (!data.children) {
            delMenu(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.getTreeData(this.clientCode)
                this.scrollToParent(node)

              }
            }).catch(() => {
            })
          // }
          // else {
          //   this.$message({
          //     message: '请注意！不可删除非空菜单！',
          //     type: 'warning'
          //   })
          // }
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消删除'
          })
        })
      }
      else {
        this.$message({
          type: 'warning',
          message: '该菜单不存在'
        })
      }
    },
    edit(data) {
      this.$set(data, 'isEdit', true)
      this.$set(data, 'title', data.menuName)
    },
    editTitle(data) {
      const that = this
      this.$set(data, 'isEdit', false)
      this.$set(data, 'menuName', data.title)
      if (data.menuName) {
        if (data.id) { // 修改
          const paramsEdit = {
            clientCode: that.clientCode,
            menuId: data.id,
            menuName: data.menuName
          }
          editMenu(paramsEdit).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '修改成功',
                type: 'success'
              })
              that.getTreeData(that.clientCode)
              this.scrollToCurrent(data)
            }
          }).catch(() => {
          })
        }
        else { // 新增
          const paramsAdd = {
            clientCode: that.clientCode,
            parentMenuId: that.parentMenuId,
            menuName: data.menuName
          }
          addMenu(paramsAdd).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '添加成功',
                type: 'success'
              })
              that.getTreeData(that.clientCode)
              this.scrollToCurrent(data)
            }
          }).catch(() => {
          })
        }
      }
      else {
        that.$message({
          message: '菜单名称不能为空',
          type: 'warning'
        })
        that.getTreeData(that.clientCode)
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.menuName.indexOf(value) !== -1
    },
    handleNodeClick(data, node) { // 点击一行的回调函数
      const that = this
      const arrLabel = []
      let arrList = []
      that.getNodeLabel(node, (label) => {
        arrLabel.push(label)
      })
      arrList = (arrLabel.filter(item => item !== undefined)).reverse()
      that.treeColumn = data
      that.labelList = arrList
      if (!data.isEdit && (!data.children || data.children.length === 0)) {
        that.getTable(data)
      }
      else {
        that.dataLoading = false
      }
    },
    getTable(data) {
      const that = this
      const paramsList = {
        clientCode: this.clientCode,
        menuId: data.id
      }
      that.dataLoading = true
      fileList(paramsList).then(res => {
        if (res.code === '000000') {
          setTimeout(() => {
            that.dataLoading = false
          }, 500)
          that.list = res.data || []
        }
      }).catch(() => {
        that.dataLoading = false
      })
    },
    getNodeLabel(nodeObj, callback) {
      if (!nodeObj) return
      if (nodeObj && callback) callback(nodeObj.label)
      if (nodeObj.parent && Object.keys(nodeObj.parent).length > 0) {
        this.getNodeLabel(nodeObj.parent, callback)
      }
    },
    delFile(row) {
      const paramsDel = {
        clientCode: this.clientCode,
        menuId: row.id
      }
      this.$confirm('确定要删除此条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFileList(paramsDel).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.getTable(this.treeColumn)
          }
        }).catch(() => {
        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },
    editFile(row) {
      this.$refs.editFiles.fileForm = row
      this.$refs.editFiles.showEditFile = true
      if(this.clientCode>=9){
        this.$refs.editFiles.getAndExpandAll()
      }
    },
    addFilePathCover(row) {
      this.$refs.addCover.fileForm = row
      this.$refs.addCover.showEditFile = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    downloadAll(list) {
      const urlsAll = list.map(items => items.downloadLink)
      const downloadTypeAll = list.length === 1 ? list[0].type : 'zip'
      const folderNameAll = list.length === 1 ? `${ list[0].name }.${ list[0].type }` : `${ this.labelList.join(',') }.${ downloadTypeAll }`
      const arrListsAll = []
      list.forEach(item => {
        const titleAll = `${ item.name }-${ item.type }-${ item.length }`
        arrListsAll.push(titleAll)
      })
      if (Array.from(new Set(arrListsAll)).length !== arrListsAll.length) {
        this.$message({
          message: '不能下载重复的文件',
          type: 'error'
        })
        return false
      }
      this.downAllFile(folderNameAll, urlsAll, list)
    },
    uploadFiles() {
      const keys = Object.keys(this.treeColumn)
      if (keys.length > 0 && (!this.treeColumn.children || this.treeColumn.children.length === 0)) {
        this.$refs.uploadFile.showUpload = true
        this.$refs.uploadFile.labelList = this.labelList
        this.$refs.uploadFile.menuId = this.treeColumn.id
        this.$refs.uploadFile.clientCode = this.treeColumn.clientCode
      }
      else {
        this.$message({
          type: 'warning',
          message: '请先选择路径再上传资料！'
        })
      }
    },
    handleDrop(draggingNode, dropNode, dropType, ev) { // 拖拽成功的事件
      // dropType是before/after--排序,取dropNode的sort，inner-修改当前菜单的父菜单
      const that = this
      const tips = dropType === 'inner' ? `是否将<em style="color:red">${ draggingNode.label }</em>移到<em style="color:red">${ dropNode.label }</em>的里面?` : (dropType === 'before' ? `是否将<em style="color:red">${ draggingNode.label }</em>移到<em style="color:red">${ dropNode.label }</em>的前面` : `是否将<em style="color:red">${ draggingNode.label }</em>移到<em style="color:red">${ dropNode.label }</em>的后面`)
      const paramsSort = {
        clientCode: that.clientCode,
        menuId: draggingNode.data.id,
        menuName: draggingNode.data.menuName,
        sort: dropNode.data.sort
      }
      const paramsParent = {
        clientCode: that.clientCode,
        menuId: draggingNode.data.id,
        parentMenuId: dropType === 'inner' ? dropNode.data.id : dropNode.data.parentMenuId,
        sort: dropType === 'before' ? parseInt(dropNode.data.sort) - 1 : parseInt(dropNode.data.sort) + 1
      }
      that.$confirm(`${ tips }`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        if ((dropType === 'before' || dropType === 'after') && (draggingNode.data.parentMenuId === dropNode.data.parentMenuId)) {
          treeSort(paramsSort).then(res => { // 排序
            if (res.code === '000000') {
              that.$message({
                message: '排序成功',
                type: 'success'
              })
              that.getTreeData(that.clientCode)
            }
          }).catch(() => {
            that.getTreeData(that.clientCode)
          })
        }
        else if (dropType === 'inner' || ((dropType === 'before' || dropType === 'after') && (draggingNode.data.parentMenuId !== dropNode.data.parentMenuId))) {
          editParentMenu(paramsParent).then(res => {
            if (res.code === '000000') {
              that.$message({
                message: '修改成功',
                type: 'success'
              })
              that.getTreeData(that.clientCode)
            }
          }).catch(() => {
            that.getTreeData(that.clientCode)
          })
        }
      }).catch(() => {
        that.getTreeData(that.clientCode)
      })
    },
    addParent() {
      const that = this
      if (that.parentName && that.clientCode) {
        const parentParams = {
          clientCode: that.clientCode,
          parentMenuId: 0,
          menuName: that.parentName
        }
        addMenu(parentParams).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: '添加成功',
              type: 'success'
            })
            that.getTreeData(that.clientCode)
            that.parentName = ''
          }
        }).catch(() => {
        })
      }
      else if (!that.parentName) {
        that.$message({
          message: '请输入菜单名称',
          type: 'error'
        })
      }
      else {
        that.$message({
          message: '请选择项目类型',
          type: 'error'
        })
      }
    },
    downAllFile(folderName, urls, lists) { // 批量下载
      streamSaver.mitm = 'https://down.52santao.com/pack/mitm.html'
      const fileStream = streamSaver.createWriteStream(folderName)
      const readableZipStream = createWriter({
        async pull(ctrl) {
          for (var i = 0; i < urls.length; i++) {
            const url = urls[i]
            // Gets executed everytime zip.js asks for more data
            const name = decodeURIComponent(`${ lists[i].name }.${ lists[i].type }`)
            const res = await fetch(url)
            const stream = () => res.body
            ctrl.enqueue({ name, stream })
          }
          ctrl.close()
        }
      })
      // more optimized
      if (window.WritableStream && readableZipStream.pipeTo) {
        return readableZipStream.pipeTo(fileStream).then(() => console.log('done writing'))
      }
      // less optimized
      const writer = fileStream.getWriter()
      const reader = readableZipStream.getReader()
      const pump = () => reader.read().then(res => {
        res.done ? writer.close() : writer.write(res.value).then(pump)
      }).catch(() => {
      })
      pump()
    },
    downloadFile(data) { // 单独下载
      streamSaver.mitm = 'https://down.52santao.com/pack/mitm.html'
      const fileStream = streamSaver.createWriteStream(data.name + '.' + data.type)
      fetch(data.downloadLink).then(res => {
        const readableStream = res.body
        // more optimized
        if (window.WritableStream && readableStream.pipeTo) {
          return readableStream.pipeTo(fileStream).then(() => console.log('done writing'))
        }
        window.writer = fileStream.getWriter()
        const reader = res.body.getReader()
        const pump = () => reader.read()
          .then(res => res.done
            ? window.writer.close()
            : window.writer.write(res.value).then(pump))

        pump()
      }).catch((err) => {
      })
    }

  }
}
</script>
<style scoped lang="scss">
.red {
  color: red !important;
}

.left {
  background: #fff;
  padding: 10px;
  border-radius: 5px;
  height: 92vh;
  max-height: 92vh;
}

.inner {
  position: relative;
}

.upload-file {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-content: center;
}

.upload-file div {
  width: 58%;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f2f2f2;
  border-radius: 53px;
  font-size: 14px;
}

.upload-file div h4 {
  font-weight: normal;
  color: #6f7889;
  margin-bottom: 8px;
}

.choice-file {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-content: center;

  div {
    width: 210px;
    padding: 10px 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-content: center;
    background: #f2f2f2;
    border-radius: 30px;
    font-size: 14px;

    span {
      padding-right: 10px;
      color: #6f7889;
      padding-top: 3px;
    }
  }
}

.download > > > .custom-tree-node .sign {
  padding: 3px 5px;
  font-size: 12px;
  color: #fff;
  background: #539fff;
  border-radius: 5px;
  margin-left: 10px;
}

.download > > > .el-tree-node {
  font-size: 16px;
}

.download > > > .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  padding-right: 8px;
}

.down-icon {
  font-size: 20px;
  padding-left: 10px;
  color: #7b828f;
}

.download > > > .el-input--medium .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.download > > > .el-tree-node__content {
  height: 32px;
}

.file-title {
  display: flex;
  font-weight: normal;
  margin-bottom: 15px;

  p {
    width: 50%;
    text-align: left;
    color: #6f7889;

    &:first-child {
      span {
        font-size: 20px;
        padding-right: 8px;
      }

      em {
        font-size: 16px;
      }
    }

    &:last-child {
      text-align: right;

      span {
        font-size: 14px;
        color: #666;
        padding-right: 8px;

        em {
          padding: 0 5px;
        }
      }
    }
  }
}

.red-tips {
  color: red;
  font-size: 16px;
}

.priview-download {
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff;
  padding: 7px 16px;
  min-width: 60px;
  border-radius: 14px;
  margin-right: 10px;
}

.add-parent {
  display: flex;
  margin-bottom: 15px;
}

@media screen and (max-width: 1441px) {
  .upload-file div {
    width: 70%;
  }
}
</style>
