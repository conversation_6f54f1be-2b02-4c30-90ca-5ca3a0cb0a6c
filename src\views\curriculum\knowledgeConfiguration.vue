<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="10">
        <el-col :span="24">
          <div class="container-title">菁优网:</div>
          <ProductLineSelect
            @dataList="getDataList"
            :isTest="true"
            v-model="listQuery.clientCode"
          ></ProductLineSelect>
          <el-select
            v-model="listQuery.sid"
            filterable
            clearable
            placeholder="科目"
            style="width: 200px;"
          >
            <el-option
              v-for="item in bankSubjectList"
              :key="item.sid"
              :label="item.name"
              :value="`${item.sid}`"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="listQuery.bid"
            filterable
            clearable
            placeholder="教材版本"
            style="width: 200px;"
          >
            <el-option
              v-for="item in bankTextbookVersionList"
              :key="item.id"
              :label="item.editionName"
              :value="`${item.id}`"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="listQuery.subVersion"
            filterable
            clearable
            placeholder="教材子版本"
            style="width: 200px;"
          >
            <el-option
              v-for="item in bankSubVersionList"
              :key="item.gradeId"
              :label="item.gradeName"
              :value="`${item.gradeId}`"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <div class="container-title">三陶:</div>
          <ProductLineSelect
            :isTest="true"
            v-model="listQueryST.clientCode"
          ></ProductLineSelect>
          <el-select
            v-model="listQueryST.sid"
            filterable
            clearable
            placeholder="科目"
            style="width: 200px;"
          >
            <el-option
              v-for="item in subjectList"
              :key="item.id"
              :label="item.title"
              :value="`${item.id}`"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="listQueryST.bid"
            filterable
            clearable
            placeholder="教材版本"
            style="width: 200px;"
          >
            <el-option
              v-for="item in textbookVersionList"
              :key="item.id"
              :label="item.title"
              :value="`${item.id}`"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="listQueryST.subVersion"
            filterable
            clearable
            placeholder="教材子版本"
            style="width: 200px;"
          >
            <el-option
              v-for="item in subVersionList"
              :key="item.id"
              :label="item.title"
              :value="`${item.id}`"
            >
            </el-option>
          </el-select>

          <el-button
            v-waves
            class="filter-item"
            type="primary"
            size="mini"
            @click="getCategoryList('new')"
          >
            查询
          </el-button>
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            size="mini"
            @click="syncCourses"
          >
            同步
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="container-box">
      <div class="container-tree">
        <el-input placeholder="输入关键字进行过滤" v-model="filterText">
        </el-input>

        <el-tree
          class="filter-tree"
          :data="categoryDataList"
          :props="defaultProps"
          default-expand-all
          node-key="sid"
          :filter-node-method="filterNode"
          @node-click="handleClickLine"
          ref="tree"
        >
          <template #default="{ node, data }">
            <div
              class="tree-line"
              @mouseenter="showDeleteBtn = data.sid"
              @mouseleave="showDeleteBtn = null"
            >
              <span>
                {{ data.name }}
              </span>
              <div class="tree-buttons">
                <!-- 根据字段显示按钮，bindStatus 0: 未绑定 1: 已绑定 -->
                <el-button v-if="data.bindStatus === 1" size="mini" type="text">
                  已绑定
                </el-button>
                <!-- hover时显示删除按钮 -->
                <el-button
                  v-if="showDeleteBtn === data.sid"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  class="delete-btn"
                  @click.stop="handleDeleteNode(data, node)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="container-table">
        <el-button
          class="container-table-btn"
          v-waves
          :disabled="pointCourse.length > 0 || selectedNo === ''"
          type="primary"
          @click="bindCourse"
        >
          绑定课程
        </el-button>
        <el-table
          border
          :data="pointCourse"
          style="width: 100%"
          v-loading="bindLoading"
        >
          <el-table-column prop="courseId" label="课程编号"> </el-table-column>
          <el-table-column prop="courseName" label="课程名称">
          </el-table-column>
          <el-table-column
            label="操作"
            class-name="small-padding fixed-width action-warp"
            fixed="right"
          >
            <template v-slot="{ row }">
              <div :key="row.courseId">
                <el-button
                  v-permission="['curriculum:course:del']"
                  type="text"
                  size="mini"
                  class="action-deal-btn"
                  @click="handleDel(row)"
                  >解绑</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!--绑定课程弹框-->
    <el-dialog
      title="课程列表"
      :visible.sync="coursePop"
      :close-on-click-modal="!coursePop"
      width="60%"
      @close="changeInit"
    >
      <el-row :gutter="10" class="course-content">
        <el-col :span="24" class="search-course">
          <el-input
            v-model.trim="courseQuery.title"
            placeholder="知识点课程名称"
            class="filter-item"
            clearable
            style="width: 200px; margin-right: 15px;"
            @keyup.enter.native="resetPage"
            @clear="handleClearTitle"
            @blur="courseQuery.title = courseQuery.title.trim()"
          />
          <el-input
            v-model.trim="courseQuery.courseId"
            placeholder="知识点课程编号"
            class="filter-item"
            clearable
            style="width: 200px;"
            @clear="handleClearCourseId"
            @keyup.enter.native="resetPage"
            @blur="courseQuery.courseId = courseQuery.courseId.trim()"
          />
          <el-button
            v-waves
            class="filter-item"
            plain
            type="primary"
            size="mini"
            @click="resetPage"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
      <el-table
        v-loading="courseLoading"
        :data="courseList"
        height="400"
        border
        fit
        stripe
        highlight-current-row
      >
        <af-table-column
          label="知识点课程编号"
          prop="id"
          show-overflow-tooltip
        />
        <af-table-column
          label="知识点课程名称"
          prop="title"
          show-overflow-tooltip
        />
        <af-table-column
          label="所属科目"
          prop="subjectName"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作"
          class-name="small-padding fixed-width action-warp"
        >
          <template v-slot="{ row }">
            <div :key="row.id">
              <el-button
                type="text"
                size="mini"
                class="action-deal-btn"
                v-permission="['curriculum:course:questions']"
                @click="toBind(row)"
                :disabled="row.bindLoading"
                >{{ row.bindLoading ? "绑定中..." : "绑定" }}</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="courseQuery.pageIndex"
        :limit.sync="courseQuery.pageSize"
        @pagination="getCourseList"
      />
    </el-dialog>
  </div>
</template>

<script>
import ProductLineSelect from "@/components/Select/ProductLineSelect.vue";
import {
  courseList,
  getSubjectList,
  syncMaterial,
  getBankSubjectList,
  getBankBookList,
  getMaterialAndVersion,
  addPointCourse,
  deletePointCourse,
  getPointCourse,
  categoryList,
  deleteCategoryPoint
} from "@/api/classType";
import Pagination from "@/components/Pagination";
import { converseEnToCn, classType } from "@/utils/field-conver";
export default {
  name: "Course",
  components: {
    Pagination,
    ProductLineSelect
  },
  data() {
    return {
      // 查询课程列表参数
      lastClickName: "",
      // 存储临时数据
      courseQueryTemplt: {
        clientCode: "",
        subjectId: ""
      },
      dataList: [],
      // 最后点击的子节点  刷新时候使用
      lastClickNodeKey: "",
      // 选中的知识点编号
      selectedNo: "",
      // 控制删除按钮显示
      showDeleteBtn: null,
      classType: classType,
      filterText: "",
      coursePop: false,
      courseLoading: false,
      bindLoading: false,
      courseList: [],
      // 知识点编号
      courseNo: "",
      // 课程列表参数
      courseQuery: {
        pageIndex: 1,
        pageSize: 10,
        title: "",
        clientCode: "100",
        subjectId: "",
        status: 1,
        courseId: "",
        flash: true,
        classTypeId: ""
      },
      total: 0,
      // 知识点列表
      categoryDataList: [],
      data: [],
      // 默认展开的节点keys
      defaultExpandedKeys: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 菁优网参数
      listQuery: {
        clientCode: "100", // 产品线
        sid: "", // 科目
        bid: "", // 教材版本
        subVersion: "" // 教材子版本
      },
      // 三陶参数
      listQueryST: {
        clientCode: "100",
        sid: "", // 科目
        bid: "", // 教材版本
        subVersion: "" // 教材子版本
      },
      // 菁优网学科列表
      bankSubjectList: [],
      // 菁优网教材版本列表
      bankTextbookVersionList: [],
      // 菁优网教材子版本列表
      bankSubVersionList: [],
      // 三陶学科列表
      subjectList: [],
      // 三陶教材版本列表
      textbookVersionList: [],
      // 三陶教材子版本列表
      subVersionList: [],
      // 知识点关联课程列表
      pointCourse: []
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    // 菁优网产品线改变
    "listQuery.clientCode": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQuery.sid = "";
        this.listQuery.bid = "";
        this.listQuery.subVersion = "";
        if (newVal) {
          this.getBankSubjectList();
        }
      }
    },
    // 菁优网科目改变
    "listQuery.sid": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQuery.bid = "";
        this.listQuery.subVersion = "";
        if (newVal) {
          this.getBankBookList();
        }
      }
    },
    // 菁优网教材版本改变
    "listQuery.bid": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQuery.subVersion = "";
        const matchedItem = this.bankTextbookVersionList
          ? this.bankTextbookVersionList.find(
              item => Number(item.id) === Number(newVal)
            )
          : null;
        this.bankSubVersionList = matchedItem ? matchedItem.gradeList : [];
      }
    },
    // 三陶产品线改变
    "listQueryST.clientCode": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQueryST.sid = "";
        this.listQueryST.bid = "";
        this.listQueryST.subVersion = "";
        if (newVal) {
          this.getSubjectList();
        }
      }
    },
    // 三陶科目改变
    "listQueryST.sid": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQueryST.bid = "";
        this.listQueryST.subVersion = "";
        if (newVal) {
          this.getMaterialAndVersion();
        }
      }
    },
    // 三陶教材版本改变
    "listQueryST.bid": {
      deep: true,
      handler(newVal, oldVal) {
        this.listQueryST.subVersion = "";
        const matchedItem = this.textbookVersionList
          ? this.textbookVersionList.find(
              item => Number(item.id) === Number(newVal)
            )
          : null;
        this.subVersionList = matchedItem ? matchedItem.versions : [];
      }
    }
  },
  created() {},
  mounted() {
    this.getBankSubjectList();
    this.getSubjectList();
  },
  methods: {
    changeInit() {
      this.courseQuery = {
        pageIndex: 1,
        pageSize: 10,
        title: "",
        clientCode: "100",
        subjectId: "",
        status: 1,
        courseId: "",
        flash: true,
        classTypeId: ""
      };
    },
    getDataList(dataList) {
      this.dataList = dataList;
    },
    // 获取菁优网科目
    getBankSubjectList() {
      // 1 高中   2 初中
      let stage = this.listQuery.clientCode === "100" ? "1" : "2";
      getBankSubjectList({ stage })
        .then(res => {
          this.bankSubjectList = res.data;
        })
        .catch(err => {});
    },
    // 获取菁优网教材以及子版本
    getBankBookList() {
      getBankBookList({ sid: this.listQuery.sid })
        .then(res => {
          this.bankTextbookVersionList = res.data;
        })
        .catch(err => {});
    },
    // 获取三陶科目
    getSubjectList() {
      // 1 高中   2 初中
      getSubjectList({ clientCode: this.listQueryST.clientCode })
        .then(res => {
          this.subjectList = res.data;
        })
        .catch(err => {
          // 假数据
        });
    },
    // 获取三陶教材以及子版本
    getMaterialAndVersion() {
      getMaterialAndVersion({
        clientCode: this.listQueryST.clientCode,
        subjectId: this.listQueryST.sid
      })
        .then(res => {
          this.textbookVersionList = res.data;
        })
        .catch(err => {});
    },
    // 同步
    syncCourses() {
      // 校验规则配置
      const rules = [
        { field: "listQuery.sid", msg: "请选择菁优网科目!" },
        { field: "listQuery.bid", msg: "请选择菁优网教材版本!" },
        { field: "listQuery.subVersion", msg: "请选择菁优网教材子版本!" },
        { field: "listQueryST.sid", msg: "请选择三陶科目!" },
        { field: "listQueryST.bid", msg: "请选择三陶教材版本!" },
        { field: "listQueryST.subVersion", msg: "请选择三陶教材子版本!" }
      ];

      // 调用
      if (!this.validateFields(rules, this)) {
        return; // 校验不通过直接中断
      }
      let params = {
        bookSid: this.listQuery.subVersion,
        stage: this.listQuery.clientCode === "100" ? "1" : "2",
        clientCode: this.listQueryST.clientCode,
        sid: this.listQuery.sid,
        subjectId: this.listQueryST.sid,
        versionId: this.listQueryST.subVersion
      };
      // 获取菁优网名称
      const jingyouClientName =
        this.dataList.find(
          item => Number(item.code) === Number(this.listQuery.clientCode)
        )?.name || "";
      const jingyouSidName =
        this.bankSubjectList.find(
          item => Number(item.sid) === Number(this.listQuery.sid)
        )?.name || "";
      const jingyouVersionName =
        this.bankTextbookVersionList.find(
          item => Number(item.id) === Number(this.listQuery.bid)
        )?.editionName || "";

      const jingyouSubVersionName =
        this.bankSubVersionList.find(
          item => item.gradeId === this.listQuery.subVersion
        )?.gradeName || "";

      // 获取三陶名称
      const santaoClientName =
        this.dataList.find(
          item => Number(item.code) === Number(this.listQueryST.clientCode)
        )?.name || "";
      const santaoSidName =
        this.subjectList.find(
          item => Number(item.id) === Number(this.listQueryST.sid)
        )?.title || "";
      const santaoVersionName =
        this.textbookVersionList.find(
          item => Number(item.id) === Number(this.listQueryST.bid)
        )?.title || "";

      const santaoSubVersionName =
        this.subVersionList.find(
          item => Number(item.id) === Number(this.listQueryST.subVersion)
        )?.title || "";
      // 构建提示信息
      const confirmMessage = `
        <div style="text-align: left;">
          <p>你将同步：</p>
          <p  style="color: #f56c6c; font-weight: bold;">菁优网 - <span>${jingyouClientName}</span> - <span style="color: #f56c6c; font-weight: bold;">${jingyouSidName}</span> - <span>${jingyouVersionName}</span> - <span style="color: #f56c6c; font-weight: bold;">${jingyouSubVersionName}</span></p>
          <p>到</p>
          <p style="color: #f56c6c; font-weight: bold;">三陶 - <span >${santaoClientName}</span> - <span style="color: #f56c6c; font-weight: bold;">${santaoSidName}</span> - <span >${santaoVersionName}</span> - <span style="color: #f56c6c; font-weight: bold;">${santaoSubVersionName}</span></p>
          <p>是否确认同步？</p>
        </div>
      `;

      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true
      })
        .then(() => {
          this.filterText = "";
          syncMaterial(params)
            .then(res => {
              this.$message({
                type: "success",
                message: "同步成功!"
              });
              this.getCategoryList();
            })
            .catch(err => {
              this.$message({
                type: "error",
                message: "同步失败!"
              });
            });
        })
        .catch(() => {});
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.classType, row.status);
    },
    // 解绑
    handleDel(row) {
      this.$confirm(`是否确认解绑"${row.courseName}"课程?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          deletePointCourse(row.id).then(res => {
            this.$message({
              type: "success",
              message: "解绑成功!"
            });
            this.pointCourse = [];
            // 刷新树并保持搜索状态和定位
            this.refreshTreeWithState();
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "解绑失败!"
          });
        });
    },
    // 绑定课程
    toBind(row) {
      // 点击绑定按钮时立即开始当前行的 loading
      this.$set(row, "bindLoading", true);

      this.$confirm(`是否确认绑定"${row.title}"课程?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            courseId: row.id,
            no: this.selectedNo
          };

          addPointCourse(params)
            .then(res => {
              this.$message({
                type: "success",
                message: "绑定成功!"
              });
              this.coursePop = false;
              this.$set(row, "bindLoading", false);
              // 刷新树并保持搜索状态和定位
              this.refreshTreeWithState();
            })
            .catch(() => {
              // 绑定失败处理
              this.$message({
                type: "error",
                message: "绑定失败!"
              });
            })
            .finally(() => {
              // 无论成功失败都取消当前行的 loading
              this.$set(row, "bindLoading", false);
            });
        })
        .catch(() => {
          // 点击取消或关闭弹窗时取消当前行的 loading
          this.$set(row, "bindLoading", false);
        });
    },
    /**
     * 通用必填项校验
     * @param {Array} rules - 校验规则数组 [{ field: "对象.属性", msg: "提示语" }]
     * @param {Object} ctx - this 上下文（Vue 实例）
     * @returns {Boolean} - 通过返回 true，不通过返回 false
     */
    validateFields(rules, ctx) {
      for (const { field, msg } of rules) {
        const [objName, key] = field.split(".");
        if (!ctx[objName][key]) {
          ctx.$message({ type: "error", message: msg });
          return false;
        }
      }
      return true;
    },

    // 刷新树并保持搜索状态和节点定位
    refreshTreeWithState() {
      const currentFilterText = this.filterText;
      const currentNodeKey = this.lastClickNodeKey;
      const currentName = this.lastClickName;
      const currentSelectedNo = this.selectedNo;

      // 保存当前所有展开节点的状态
      const expandedKeys = [];
      if (this.$refs.tree) {
        // 获取所有展开的节点
        const expandedNodes = this.$refs.tree.store.nodesMap;
        for (let key in expandedNodes) {
          if (expandedNodes[key].expanded) {
            expandedKeys.push(key);
          }
        }
      }

      this.getCategoryList().then(() => {
        this.$nextTick(() => {
          // 延迟一点时间确保DOM完全渲染
          // 首先收起所有节点（因为default-expand-all会展开所有）
          if (expandedKeys.length > 0) {
            // 获取所有节点并收起
            const allNodes = this.$refs.tree.store.nodesMap;
            for (let key in allNodes) {
              if (allNodes[key].expanded && !expandedKeys.includes(key)) {
                allNodes[key].collapse();
              }
            }
          }

          // 重新应用搜索过滤器
          if (currentFilterText) {
            this.$refs.tree.filter(currentFilterText);
          }

          // 重新定位到之前选中的节点
          if (currentNodeKey) {
            this.$refs.tree.setCurrentKey(currentNodeKey);

            // 重新触发节点点击事件以加载课程列表
            if (currentSelectedNo) {
              this.handleClickLine({
                sid: currentNodeKey,
                no: currentSelectedNo,
                name: currentName
              });
            }
          }
        });
      });
    },

    // 处理删除节点
    handleDeleteNode(data, node) {
      this.$confirm(`确定要删除知识点 "${data.name}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params;

          if (data.id) {
            // 如果有id，获取当前id和所有子节点id集合
            const ids = this.collectNodeIds(data);
            params = { idList: ids };
          } else {
            // 如果没有id，使用sid
            params = { sid: data.sid };
          }

          deleteCategoryPoint(params)
            .then(() => {
              this.$message.success("删除成功");
              this.refreshTreeWithState();
            })
            .catch(() => {
              this.$message.error("删除失败");
            });
        })
        .catch(() => {
          // 取消删除
        });
    },

    // 收集节点id和所有子节点id
    collectNodeIds(nodeData) {
      const ids = [];

      // 添加当前节点id
      if (nodeData.id) {
        ids.push(nodeData.id);
      }

      // 递归收集所有子节点id
      const collectChildIds = node => {
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            if (child.id) {
              ids.push(child.id);
            }
            // 递归处理子节点的子节点
            collectChildIds(child);
          });
        }
      };

      collectChildIds(nodeData);

      return ids;
    },

    // 设置默认展开的节点keys（递归收集所有节点）
    setDefaultExpandedKeys(nodes) {
      const keys = [];
      const collectKeys = nodeList => {
        nodeList.forEach(node => {
          if (node.sid) {
            keys.push(node.sid);
          }
          if (node.children && node.children.length > 0) {
            collectKeys(node.children);
          }
        });
      };
      collectKeys(nodes);
      this.defaultExpandedKeys = keys;
    },

    getCategoryList(data) {
      // 校验规则配置
      const rules = [
        { field: "listQueryST.sid", msg: "请选择三陶科目!" },
        { field: "listQueryST.bid", msg: "请选择三陶教材版本!" },
        { field: "listQueryST.subVersion", msg: "请选择三陶教材子版本!" }
      ];

      // 调用
      if (!this.validateFields(rules, this)) {
        return Promise.reject(); // 校验不通过直接中断
      }
      // 查询时候给绑定课程所属产品线和科目赋值 只能绑定此个产品线和科目下的且已上架课程
      this.courseQueryTemplt.clientCode = this.listQueryST.clientCode;
      this.courseQueryTemplt.subjectId = this.listQueryST.sid;
      return categoryList({ versionId: this.listQueryST.subVersion })
        .then(res => {
          if (res.data && res.data.length > 0) {
            this.categoryDataList = this.transformPointsToChildren(res.data);
            // 如果是首次加载（没有保存的展开状态），则默认展开所有节点
            // if (this.defaultExpandedKeys.length === 0) {
            //   this.setDefaultExpandedKeys(this.categoryDataList);
            // }

            // 不是查询 就定位
            if (data !== "new" && this.lastClickNodeKey) {
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.lastClickNodeKey);

                // 展开该节点（如果是懒加载，需要确保节点已渲染）
                const node = this.$refs.tree.getNode(this.lastClickNodeKey);
                if (node) {
                  node.expand(); // 展开
                }
              });
            }
          } else {
            this.categoryDataList = [];
            this.defaultExpandedKeys = [];
          }
        })
        .catch(err => {});
    },
    bindCourse() {
      this.coursePop = true;
      this.getCourseList("init");
    },
    // 处理节点
    // utils/transformPoints.js
    transformPointsToChildren(data) {
      const transformPointToNode = point => ({
        ...point,
        psid: point.pno || "",
        des: "",
        seq: 0,
        sid: point.no,
        children: [],
        chapterId: point.sid
      });

      // 过滤掉 null 值
      const filteredData = data.filter(
        node => node !== null && node !== undefined
      );

      return filteredData.map(node => {
        const { children = [], point } = node;

        // 处理叶子节点的知识点转换
        if (children.length === 0 && point && point.length > 0) {
          // 过滤掉 point 数组中的 null 值
          const validPoints = point.filter(p => p !== null && p !== undefined);

          node.children = validPoints.map(p => {
            const nodeFromPoint = transformPointToNode(p);
            if (p.children?.length) {
              // 过滤掉子节点中的 null 值
              const validChildren = p.children.filter(
                child => child !== null && child !== undefined
              );
              if (validChildren.length > 0) {
                nodeFromPoint.children = this.transformPointsToChildren(
                  validChildren.map(child => transformPointToNode(child))
                );
              }
            }
            return nodeFromPoint;
          });
          node.point = undefined;
        }

        // 递归处理子节点
        if (children.length > 0) {
          // 过滤掉子节点中的 null 值
          const validChildren = children.filter(
            child => child !== null && child !== undefined
          );
          if (validChildren.length > 0) {
            node.children = this.transformPointsToChildren(validChildren);
          }
        }

        return node;
      });
    },
    // 获取同步提示语
    resetPage() {
      this.courseQuery.pageIndex = 1;
      this.getCourseList();
    },
    // 处理标题清除事件
    handleClearTitle() {
      this.courseQuery.title = "";
      this.resetPage();
    },
    handleClearCourseId() {
      this.courseQuery.courseId = "";
      this.resetPage();
    },
    // 课程弹窗列表
    async getCourseList(data) {
      this.courseLoading = true;
      // 班型： 初中 10000466  高中 10000465
      this.courseQuery.clientCode = this.courseQueryTemplt.clientCode;
      this.courseQuery.subjectId = this.courseQueryTemplt.subjectId;
      // 只有在首次打开弹窗时才回显知识点名称
      if (data === "init") {
        this.courseQuery.title = this.lastClickName;
      }
      // 其他情况（reset、查询、分页等）都保持用户当前输入的值，不做任何修改
      // 班型： 初中 10000466  高中 10000465
      this.courseQuery.classTypeId =
        this.courseQuery.clientCode === "100" ? 10000465 : 10000466;
      await courseList(this.courseQuery)
        .then(response => {
          this.courseList = response.data.records;
          this.total = response.data.total;
          this.courseLoading = false;
        })
        .catch(() => {
          this.courseLoading = false;
        });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name && data.name.indexOf(value) !== -1;
    },
    async handleClickLine(data, node, component) {
      // 有point并且下面有no才可以绑定课程
      if (data && data.no) {
        this.lastClickNodeKey = data.sid;
        this.lastClickName = data.name;
        this.bindLineVisible = true;
        this.bindLoading = true;
        this.selectedNo = data.no;
        await getPointCourse({ no: data.no })
          .then(response => {
            if (response.data !== null && typeof response.data === "object") {
              this.pointCourse = [response.data];
            } else {
              this.pointCourse = [];
            }
            this.bindLoading = false;
          })
          .catch(() => {
            this.bindLoading = false;
          });
      } else {
        this.lastClickNodeKey = "";
        this.selectedNo = "";
        this.lastClickName = "";
      }
    }
  }
};
</script>

<style scoped lang="scss">
.container-title {
  width: 100px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
  display: inline-block;
}
.container-box {
  height: calc(100vh - 200px);
  overflow-y: hidden;
  display: flex;
  .container-tree {
    width: 550px;
    border-right: 2px solid #eee;
    padding-right: 20px;
  }
  .container-table {
    flex: 1;
    padding-left: 20px;
  }
  .container-table-btn {
    margin-bottom: 20px;
  }
  .tree-line {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 2px 0;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .tree-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .delete-btn {
    color: #f56c6c !important;
    opacity: 0.8;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
      color: #f56c6c !important;
    }
  }
  .filter-tree {
    margin-top: 20px;
    height: calc(100vh - 280px);
    overflow-y: auto;
  }
}
.course-content {
  margin-bottom: 20px;
}
.search-course {
  display: flex;
  align-items: center;
  button {
    margin-left: 10px;
  }
}
</style>
