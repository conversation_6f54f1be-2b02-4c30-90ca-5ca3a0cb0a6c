<template>
  <div class="app-container sync-page">
    <div class="bgGreys">
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>基本信息<em class="red">*</em></span>
            </div>
            <div class="item">
              <el-row :gutter="20">
                <!-- 审课草稿课程 -->
                <el-col :lg="10" :md="10" :sm="24">
                  <h3 class="common-title">
                    <em>草稿课程</em>
                    <em>草稿最后更新时间:{{ lastUpdateTime }}</em>
                  </h3>
                  <el-form ref="classForms" :model="classForm">
                    <el-form-item prop="courseName" class="course-list-left">
                      <el-input v-model="classForm.courseName" disabled />
                    </el-form-item>
                    <el-form-item prop="classTypeName" class="course-list-left">
                      <el-input v-model="classForm.classTypeName" disabled />
                    </el-form-item>
                    <el-form-item prop="subjectName" class="course-list-left">
                      <el-input v-model="classForm.subjectName" disabled />
                    </el-form-item>
                    <el-form-item prop="gradeName" class="course-list-left">
                      <el-input v-model="classForm.gradeName" disabled />
                    </el-form-item>
                    <el-form-item prop="teacherName" class="course-list-left">
                      <el-input v-model="classForm.teacherName" disabled />
                    </el-form-item>
                    <el-form-item prop="videoId" class="course-list-left">
                      <el-input v-model="classForm.videoId" disabled />
                    </el-form-item>
                    <el-form-item class="course-list-left">
                      <el-input v-model="classForm.videoMinute" style="width:150px" disabled />分钟-
                      <el-input v-model="classForm.videoSecond" style="width:150px" disabled />秒
                    </el-form-item>
                  </el-form>
                </el-col>
                <!-- 博客正式课程 -->
                <el-col :lg="14" :md="14" :sm="24">
                  <h3 class="common-title sync-right">
                    <em>正式课程</em>
                    <em>课程最后同步时间:{{ lastSyncTime }}</em>
                  </h3>
                  <el-form ref="courseForms" :model="courseForm">
                    <el-form-item prop="title" class="course-list" label="课程名称">
                      <el-input v-model="courseForm.title" maxlength="50" />
                    </el-form-item>
                    <el-form-item prop="classTypeId" class="course-list" label="所属班型">
                      <el-select v-model="courseForm.classTypeId" filterable placeholder="请选择所属班型" disabled>
                        <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="subjectId" class="course-list" label="科目">
                      <el-select v-model="courseForm.subjectId" placeholder="请选择科目" clearable class="filter-item" disabled filterable>
                        <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="gradeId" class="course-list" label="年级">
                      <el-select v-model="courseForm.gradeId" placeholder="请选择年级" clearable class="filter-item" filterable>
                        <el-option v-for="item in gradesList" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="teacherId" class="course-list" label="主讲老师">
                      <el-select v-model="courseForm.teacherId" placeholder="请选择主讲老师" clearable class="filter-item" filterable @change="changeTips">
                        <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                      <p v-show="tips" class="red" style="font-size:12px">未匹配到教师，请主动选择</p>
                    </el-form-item>
                    <el-form-item prop="vid" class="course-list" label="阿里云链接">
                      <el-input v-model="courseForm.vid" maxlength="50" disabled />
                    </el-form-item>
                    <el-form-item prop="videoMinute" class="course-list" label="课程时长">
                      <el-input v-model="courseForm.videoMinute" maxlength="50" style="width:150px" disabled />分钟-
                      <el-input v-model="courseForm.videoSecond" maxlength="50" style="width:150px" disabled />秒
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 上线所需信息 -->
    <div class="bgGreys">
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>上线所需信息<em class="red">*</em></span>
            </div>
            <div class="item">
              <el-row :gutter="20">
                <el-col :lg="12" :md="12" :sm="24">
                  <el-form ref="courseForms" :model="courseForm" :rules="courseFormRule" label-width="100">
                    <el-form-item label="教材版本:" prop="materialId" class="course-list">
                      <el-select v-model="courseForm.materialId" placeholder="请选择教材版本" class="filter-item" filterable @change="getOutLineId">
                        <el-option v-for="item in materialList" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="大纲/章:" prop="outlineId" class="course-list">
                      <el-select v-model="courseForm.outlineId" placeholder="请选择大纲/章" class="filter-item" filterable @change="getKeynoteId">
                        <el-option v-for="item in outLines" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="考点/节:" prop="keynoteId" class="course-list">
                      <el-select v-model="courseForm.keynoteId" placeholder="请选择考点/节" class="filter-item" filterable>
                        <el-option v-for="item in keynoteList" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="计时规则" prop="billingType" class="course-list">
                      <el-radio-group v-model="courseForm.billingType">
                        <el-radio :label="1">计时</el-radio>
                        <el-radio :label="0">免费</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="课程状态" prop="status" class="course-list">
                      <el-radio-group v-model="courseForm.status">
                        <el-radio :label="0">未上架</el-radio>
                        <el-radio :label="1">已上架</el-radio>
                        <el-radio :label="2">已下架</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="课程排序" prop="sort" class="course-list">
                      <el-input v-model="courseForm.sort" maxlength="50" type="number" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" />
                    </el-form-item>
                    <el-form-item label="上线时间" prop="onlineDate" class="course-list">
                      <el-date-picker
                        v-model="courseForm.onlineDate"
                        type="date"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="上线时间"
                        style="width:100%"
                      />
                    </el-form-item>
                  </el-form>
                </el-col>
                <el-col :lg="12" :md="12" :sm="24">
                  <div class="upload-sync">
                    <div class="upload-sync-title">课程封面</div>
                    <div class="upload-imgs">
                      <div v-if="!resourcesImgUrl&&!isEdit">
                        <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="upload($event)">
                        <a class="add"><i class="el-icon-upload big-upload" /><p>点击上传</p></a>
                      </div>
                      <p class="img">
                        <img v-if="resourcesImgUrl" :src="resourcesImgUrl">
                        <a v-if="resourcesImgUrl&&!isEdit" class="close" @click="delImgA">
                          <i class="el-icon-delete" />
                        </a>
                      </p>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :md="24" :sm="24" class="sub-foot">
                  <el-button type="default" size="mini" @click="closeIt">取消</el-button>
                  <el-button type="primary" size="mini" :disabled="btnSync" @click="subCourseSync">同步课程</el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 相关附件 -->
    <div class="bgGreys">
      <el-row :gutter="10">
        <el-col :lg="24" :sm="12" :md="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>相关附件<em>(可选)</em></span>
            </div>
            <div class="item">
              <div class="relevant-module">
                <ul>
                  <li v-for="(item,index) in moduleList" :key="index" class="module-item">
                    <!-- attachmentType 2:课件 3:讲义 4:落实本 5:思维导图 6:切片 7:试卷 -->
                    <span v-show="item.attachmentType===2" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">课件<i v-show="item.spot" class="spot" /></span>
                    <span v-show="item.attachmentType===3" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">讲义<i v-show="item.spot" class="spot" /></span>
                    <span v-show="item.attachmentType===4" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">落实本<i v-show="item.spot" class="spot" /></span>
                    <span v-show="item.attachmentType===5" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">思维导图<i v-show="item.spot" class="spot" /></span>
                    <span v-show="item.attachmentType===6" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">切片<i v-show="item.spot" class="spot" /></span>
                    <span v-show="item.attachmentType===7" :class="[(item.allNum===null||item.allNum===0)?'sync-empty':item.checked?'sync-info':'sync-no']" class="module-info" @click="getProject(index,item)">试卷<i v-show="item.spot" class="spot" /></span>

                    <!-- 已选择 -->
                    <em v-show="item.checked" class="el-icon-check white sync-icon" />

                    <!-- 未选择未同步 -->
                    <em v-show="!item.checked&&(item.allNum!==null&&item.allNum!==0)" class="el-icon-refresh-left blue sync-icon" />

                    <!-- 未选择暂无数据未同步 -->
                    <em v-show="!item.checked&&(item.allNum===null||item.allNum===0)" class="el-icon-refresh-left blue sync-icon" />
                    <span v-show="!item.checked&&item.allNum&&(item.allNum>item.syncNum)" class="sync-tips">
                      <i>{{ (item.allNum-item.syncNum) }}</i>
                      <i>/{{ item.allNum }}未同步</i>
                    </span>
                    <span v-show="item.checked&&item.allNum&&(item.allNum>item.syncNum)" class="sync-tips-checked">
                      <i>{{ (item.allNum-item.syncNum) }}</i>
                      <i>/{{ item.allNum }}未同步</i>
                    </span>
                    <i v-show="item.lastSyncTime" class="sync-time">最后同步:{{ item.lastSyncTime }}</i>
                  </li>
                </ul>
              </div>

              <div class="sub-foot">
                <el-button type="default" size="mini" @click="closeIt">取消</el-button>
                <el-button type="primary" size="mini" :disabled="isSync" @click="subEnclosure">同步附件</el-button>
              </div>
            </div>

          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-dialog :visible.sync="enclosurePop" :title="enclosureTitle" :close-on-click-modal="!enclosurePop" width="40%">
      <el-tree :data="enclosureList" :props="defaultProps" @node-click="handleNodeClick" />
      <div class="sub-foot">
        <el-button type="default" size="mini" @click="cancelEnclosure">取消</el-button>
        <el-button type="primary" size="mini" @click="confirmEnclosure">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { uuid, obsClient } from '@/utils/index'
import { uploadSuccess } from '@/api/common'
import { courseProductionDetail, blogDetail, enclosureDetail, getTreeData, courseSync, enclosureSync } from '@/api/courseMgr'
import { getAllClassType, getAllSubjects, grades, classAndSubject, outLines, getKeynote, materialsList } from '@/api/classType'
export default {
  inject: ['reload'],
  data() {
    return {
      customerInfo: {},
      onlineForm: {},
      classForm: {},
      courseForm: {},
      draftTime: '',
      courseTime: '',
      productCodeList: [],
      resourcesImgUrl: null, // 课程封面
      isEdit: false,
      resourcesImgId: null, // 封面id
      moduleList: [],
      courseId: null, // 播课课程id
      courseMgrId: null, // 审课课程Id
      syncLogId: null, // 同步记录id
      subjectsAll: [],
      gradesList: [],
      teacherList: [],
      allClassType: [],
      clientCode: null,
      lastUpdateTime: null,
      lastSyncTime: null,
      enclosurePop: false,
      enclosureTitle: null,
      defaultProps: {
        id: 'id',
        label: 'menuName',
        children: 'children'
      },
      enclosureList: [],
      outLines: [],
      keynoteList: [],
      materialList: [],
      classTypeRelationId: null,
      downloadMenuId: null, // 选中的项目类型id
      selectIndex: null, // 选中的块
      courseClassTypeId: null, // 班型id
      isSync: true,
      tips: null,
      courseFormRule: {
        onlineDate: { required: true, trigger: 'blur', message: '请选择上线时间' },
        status: { required: true, trigger: 'blur', message: '请选择课程状态' },
        billingType: { required: true, trigger: 'blur', message: '请选择计时规则' }
      },
      btnSync: false
    }
  },
  watch: {
    'courseForm.materialId': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.courseForm.outlineId = ''
          this.courseForm.keynoteId = ''
          this.keynoteList = []
        }
      }
    },
    'courseForm.outlineId': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.courseForm.keynoteId = ''
        }
      }
    },
    teacherList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.courseForm.teacherId = ''
        }
      },
      deep: true
    }
  },
  mounted() {
    this.courseId = localStorage.getItem('courseId')
    this.courseMgrId = localStorage.getItem('courseMgrId')
    this.syncLogId = this.$route.params.syncLogId
    this.clientCode = parseInt(localStorage.getItem('clientCode'))
    this.lastUpdateTime = this.$route.params.lastUpdateTime
    this.lastSyncTime = this.$route.params.lastSyncTime
    this.courseClassTypeId = this.$route.params.courseClassTypeId
    this.classTypeRelationId = localStorage.getItem('classTypeRelationId')
    if (this.courseMgrId) this.getCourseProductionDetail(this.courseMgrId)// 查询审课详情
    if (this.courseId && this.courseId !== null && this.courseId !== 'null') this.getBlogDetail(this.courseId)// 查询博客详情
    if (this.courseMgrId) this.enclosureDetail(this.courseMgrId, this.syncLogId)// 查询附件同步记录
    this.isSync = !this.courseId// this.courseId未空表示未同步的状态
    this.getAllSubjects()// 有效科目
    this.getTreeData()
    this.getMaterial()
    this.getAllClassType()
    this.getGrades()
  },
  methods: {
    changeTips() {
      this.tips = false
    },
    getCourseProductionDetail(courseMgrId) { // 查询审课详情
      courseProductionDetail(courseMgrId).then(res => {
        if (res.code === '000000') {
          const videoArr = res.data.resources && res.data.resources.length > 0 ? res.data.resources.filter(item => item.resourceType === 1) : []
          let videoObj = {}
          if (videoArr.length > 0) videoObj = videoArr[0]

          this.classForm = Object.assign({}, res.data, {
            videoId: videoObj.videoId,
            videoMinute: Math.floor(videoObj.videoLength / 60),
            videoSecond: videoObj.videoLength % 60
          })
          if (!this.courseId || this.courseId === 'null') { // 没有的情况courseId，播课详情需要从审课详情获取
            this.courseForm = {
              title: res.data.courseName,
              classTypeId: this.courseClassTypeId,
              subjectId: res.data.subjectId,
              gradeId: res.data.gradeId,
              teacherId: res.data.teacherUserId,
              vid: videoObj.videoId,
              videoMinute: Math.floor(videoObj.videoLength / 60),
              videoSecond: videoObj.videoLength % 60
            }
            this.getTeacher()
          }
        }
      }).catch(() => {
      })
    },
    validateTeacher(val) {
      const isShow = this.teacherList.findIndex(item => item.id === val)
      return isShow
    },
    getBlogDetail(courseId) { // 查询博客详情
      blogDetail(courseId).then(res => {
        if (res.code === '000000') {
          this.courseForm = res.data
          this.resourcesImgId = res.data.resourcesImgId
          this.resourcesImgUrl = res.data.resourcesImgUrl
          this.getTeacher()
          if (this.courseForm.materialId) this.getOutLineId(this.courseForm.materialId)
          if (this.courseForm.outlineId) this.getKeynoteId(this.courseForm.outlineId)
        }
      }).catch(() => {
      })
    },
    enclosureDetail(courseMgrId, syncLogId) { // 查询附件同步记录
      enclosureDetail(courseMgrId, syncLogId).then(res => { // 附件类型attachmentType 2:课件 3:讲义 4:落实本 5:思维导图 6:切片 7:知识点 8:试卷
        if (res.code === '000000') {
          const syncArr = []
          res.data && res.data.length > 0 ? res.data.forEach(item => {
            let objs = {}
            objs = Object.assign({}, item, {
              checked: false,
              spot: !!((item.lastUpdateTime !== null) && (new Date(item.lastSyncTime).getTime()) < (new Date(item.lastUpdateTime).getTime()) && item.attachmentType !== 2 && item.attachmentType !== 3 && item.attachmentType !== 4)
            })
            syncArr.push(objs)
          }) : []

          this.moduleList = syncArr
        }
      }).catch(() => {
      })
    },
    getAllSubjects() { // 有效科目
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data
        }
      })
    },
    getGrades() { // 年级
      grades().then(res => {
        if (res.code === '000000') {
          this.gradesList = res.data
        }
      })
    },
    getTeacher() {
      const params = {
        classId: this.courseForm.classTypeId,
        subjectId: this.courseForm.subjectId
      }
      classAndSubject(params).then(res => {
        if (res.code === '000000') {
          this.teacherList = res.data
          if (this.validateTeacher(this.courseForm.teacherId) === -1 && this.teacherList.length > 0) {
            this.tips = true
            this.courseForm.teacherId = ''
          } else {
            this.tips = false
          }
        }
      })
    },
    getAllClassType() { // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data
      })
    },
    getProject(index, item) {
      this.selectIndex = index
      if ((item.allNum === null || item.allNum === 0)) {
        this.$message({
          message: '暂无数据，不能同步',
          type: 'warning'
        })
        return false
      }
      if ((item.attachmentType === 2 || item.attachmentType === 3 || item.attachmentType === 4) && !item.downloadMenuId) { // 未选中之前出现弹框
        const title = `同步至下载中心，项目类型:${this.clientCode === 100 ? '三陶教育' : this.clientCode === 200 ? '烨晨双师' : '芝麻艺考'}`
        this.enclosureTitle = title
        this.enclosurePop = true
      } else if ((item.attachmentType === 2 || item.attachmentType === 3 || item.attachmentType === 4) && item.downloadMenuId) { // 5:思维导图 6:切片 7:试卷
        this.enclosurePop = false
        this.$set(this.moduleList[this.selectIndex], 'downloadMenuId', null)
        this.$set(this.moduleList[this.selectIndex], 'checked', false)
      } else if ((item.attachmentType === 5 || item.attachmentType === 6 || item.attachmentType === 7)) {
        this.moduleList[this.selectIndex].checked = !this.moduleList[this.selectIndex].checked
      }
    },
    getTreeData() {
      getTreeData(this.clientCode).then(res => {
        if (res.code === '000000') {
          this.enclosureList = res.data
        }
      }).catch(() => {
      })
    },
    handleNodeClick(data) {
      if (data.children !== null && data.children.length > 0) {
        this.$message({
          message: '请选择子节点',
          type: 'error'
        })
      } else {
        this.downloadMenuId = data.id
        if (data.id) this.$set(this.moduleList[this.selectIndex], 'downloadMenuId', data.id)
      }
    },
    cancelEnclosure() {
      this.downloadMenuId = null
      this.enclosurePop = false
    },
    confirmEnclosure() {
      if (this.downloadMenuId) {
        this.$set(this.moduleList[this.selectIndex], 'downloadMenuId', this.downloadMenuId)
        this.$set(this.moduleList[this.selectIndex], 'checked', true)
      }
      this.enclosurePop = false
      this.downloadMenuId = null
    },
    subEnclosure() {
      if (!this.isSync && !this.courseId) { // 未同步
        this.$message({
          message: '课程未同步，请先同步课程',
          type: 'error'
        })
        return false
      }
      const subArrList = this.moduleList.filter(item => item.checked)
      const subArr = []
      subArrList.forEach(item => {
        const objs = {}
        objs['downloadMenuId'] = item.checked ? item.downloadMenuId : null
        objs['attachmentType'] = item.attachmentType
        subArr.push(objs)
      })
      const params = {
        syncLogId: this.syncLogId,
        syncAttachments: subArr
      }
      enclosureSync(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '附件同步成功',
            type: 'success'
          })
          this.moduleList = [...this.moduleList]
        }
      }).catch(() => {
        if (this.courseMgrId) this.enclosureDetail(this.courseMgrId, this.syncLogId)// 查询附件同步记录
      })
    },
    getMaterial() {
      materialsList().then(res => {
        if (res.code === '000000') {
          this.materialList = res.data
        }
      })
    },
    getOutLineId(val) {
      const params = {
        classId: this.courseForm.classTypeId,
        materialId: val,
        subjectId: this.courseForm.subjectId
      }
      outLines(params).then(res => {
        if (res.code === '000000') {
          this.outLines = res.data
        }
      })
    },
    getKeynoteId(val) {
      this.getKeynote(val)
    },
    getKeynote(outlineId) {
      getKeynote(outlineId).then(res => {
        if (res.code === '000000') {
          this.keynoteList = res.data
        }
      })
    },
    subCourseSync() { // 同步课程 title
      if (!this.courseForm.title) {
        this.$message({
          message: '请填写课程名称',
          type: 'error'
        })
        return false
      }
      if (!this.courseForm.teacherId) {
        this.$message({
          message: '请选择主讲老师',
          type: 'error'
        })
        return false
      }
      if (!this.courseForm.gradeId) {
        this.$message({
          message: '请选择年级',
          type: 'error'
        })
        return false
      }
      if (!this.courseForm.billingType && this.courseForm.billingType !== 0) {
        this.$message({
          message: '请选择计时规则',
          type: 'error'
        })
        return false
      }
      if (!this.courseForm.status && this.courseForm.status !== 0) {
        this.$message({
          message: '请选择课程状态',
          type: 'error'
        })
        return false
      }
      if (!this.courseForm.onlineDate) {
        this.$message({
          message: '请选择上线时间',
          type: 'error'
        })
        return false
      }

      const params = Object.assign({}, this.courseForm, {
        resourcesImgId: this.resourcesImgId,
        courseMgrId: this.courseMgrId,
        classTypeRelationId: this.classTypeRelationId,
        courseId: this.courseId,
        syncLogId: this.syncLogId
      })

      this.btnSync = true
      courseSync(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '同步课程成功',
            type: 'success'
          })
          this.courseId = res.data.courseId
          this.classTypeRelationId = res.data.classTypeRelationId
          this.courseMgrClassTypeId = res.data.courseMgrClassTypeId
          this.courseMgrId = res.data.courseMgrId
          this.syncLogId = res.data.id
          this.getCourseProductionDetail(this.courseMgrId)// 查询审课详情
          if (this.courseId && this.courseId !== null) this.getBlogDetail(this.courseId)// 查询博客详情
          setTimeout(() => {
            this.isSync = false
            this.btnSync = false
          }, 3000)
        }
      }).catch(() => {
        setTimeout(() => {
          this.isSync = false
          this.btnSync = false
        }, 3000)
      })
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 100)
      })
    },
    delImgA() {
      this.resourcesImgId = ''
      this.resourcesImgUrl = ''
    },
    upload(e) { // 课程图片
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 5) {
        this.$message({
          type: 'warning',
          message: '上传的课程图片不能大于5M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.resourcesImgId = res.data.id
                that.resourcesImgUrl = res.data.url
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.sync-tips{
  color: #539fff;
  position: absolute;
  z-index: 111;
  width: 100%;
  display: inline-block;
  left: 0;
  bottom: 40px;
}
.sync-tips-checked{
  color: #fff;
  position: absolute;
  z-index: 111;
  width: 100%;
  display: inline-block;
  left: 0;
  bottom: 40px;
}
.red{
  color: red;
  padding-left: 5px;
  font-size: 20px;
}
.spot{
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: red;
  display: inline-block;
  margin-left: 5px;
}
.sync-right{
  width: 90%;
  margin-left: 10%;
}
.course-list-left{
  margin-bottom: 19px;
}
.big-upload{
  font-size: 20px;
}
.sub-foot{
  padding: 20px 0;
  border-top: 1px #e9e9e9 solid;
  display: flex;
  justify-content: center;
}
.sync-title{
  width: 100%;
  text-align: center;
  span{
    padding-right: 20px;
  }
}
.common-title{
  font-weight: normal;
  background: #539fff;
  display: flex;
  justify-content: space-between;
  padding: 8px;
  color: #fff;
  font-size: 14px;
  margin-bottom: 20px;
  border-radius: 3px;
}
.upload-sync{
  display: flex;
}
.upload-sync-title{
  color: #606266;
  font-size: 14px;
  padding-right: 15px;
}
.course-list{
  margin-bottom: 15px;
  display: flex;
}
.sync-page>>> .el-card .el-form-item__label{
  width: 90px;
  text-align: left;
}
.sync-page>>> .el-form-item--medium .el-form-item__content{
  width: 100%;
}
.upload-imgs{
    position: relative;
    width: 300px;
    height: 190px;
    font-size: 14px;
    display: inline-block;
    padding: 10px;
    margin-right: 25px;
    border: 1px solid #539fff;
    text-align: center;
    vertical-align: middle;
    border-radius: 5px;
  }
  .upload-imgs .add{
    display: block;
    background-color: #fff;
    color: #539fff;
    box-sizing: border-box;
    height: 168px;
    padding-top: 20%;
  }
  .upload-imgs .add .iconfont{
    padding: 10px 0;
    font-size: 40px;
  }
  .upload-imgs .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 300px;
    height: 190px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    position: relative;
    width: 168px;
    height: 168px;
    line-height: 168px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
    width: 275px;
    height: 168px;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:-10px;
    left: -10px;
    width:300px;
    height:190px;
    line-height: 190px;
    background: rgba(0,0,0,.5);
    text-align: center;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
  .relevant-module{
    height: 160px;
    ul{
      display: flex;
      li{
        position: relative;
        width: 175px;
        height: 130px;
        text-align: center;
        margin-right: 20px;
        cursor: pointer;
        .module-info{
          font-size: 16px;
          position: absolute;
          z-index: 111;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          border:1px #539fff solid;
          border-radius: 5px;
          text-align: center;
          height: 100px;
          line-height: 100px;
          &.sync-info{
            color: #fff;
            background: #539fff;
          }
          &.sync-no{
            color: #539fff;
            background: #fff;
          }
          &.sync-empty{
            color: #539fff;
            background: #ccc;
          }
        }
        .sync-icon{
          position: absolute;
          z-index: 333;
          top: 5px;
          right: 8px;
          font-size: 26px;
          &.blue{
            color: #539fff;
          }
          &.white{
            color: #fff;
          }
        }
        .sync-time{
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          text-align: center;
          font-size: 12px;
          color: #666;
          z-index: 333;
        }
      }
    }
  }
</style>
<style>
.el-card .el-card__header{
  padding:10px 20px;
}
</style>
