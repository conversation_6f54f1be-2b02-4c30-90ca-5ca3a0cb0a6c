<template>
  <span>
      <el-select v-if="showType==='select'" v-model="tmpId" clearable placeholder="样式类型" style="width: 200px;"
                 :disabled="disabled">
        <el-option
          v-for="item in optionList"
          :key="item.value"
          filterable
          :label="item.label"
          :value="Number(item.value)">
        </el-option>
      </el-select>
      <el-radio-group v-else-if="showType==='radio'" v-model="tmpId" :disabled="disabled">
        <el-radio
          v-for="item in optionList"
          :key="item.value"
          :label="Number(item.value)">
          {{ item.label }}
        </el-radio>
      </el-radio-group>
  </span>
</template>
<script>
import { styleTags } from '@/utils/field-conver'

/**
 * 班型科目样式选择框
 */
export default {
  name: 'StyleSelect',
  data: function () {
    return {
      optionList: styleTags
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showType: {
      type: String,
      default: 'select',
      validator(val) {
        return ['select', 'radio'].indexOf(val) !== -1
      }
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId) : null
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.value === value)
      return this.$emit('change', value, selectedOption)
    },
  }
}
</script>
<style scoped>
</style>
