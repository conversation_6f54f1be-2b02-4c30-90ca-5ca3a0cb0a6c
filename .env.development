# just a flag
ENV = 'development'

# base api
# VUE_APP_BASE_API = 'http://127.0.0.1:3062'

# 老沈环境
#VUE_APP_BASE_API = 'http://192.168.0.222:3062'

# 测试环境数
VUE_APP_BASE_API = 'http://47.101.50.162:3062'

# VUE_APP_BASE_API = 'http://192.168.1.22:3062'
# VUE_APP_BASE_API = 'http://47.101.50.162:3062'

# 生产环境
# VUE_APP_BASE_API = 'https://saspi.52santao.com/crm'


# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

#企业微信相关配置
VUE_APP_WX_CORPID=wwf90d1542e96c13e3
VUE_APP_WX_AGENTID=1000041
VUE_APP_WX_REDIRECT_URI=http://pre.crm.52santao.com/redirect.html
VUE_APP_WX_NOT_SUPPORTED=http://pre.crm.52santao.com/#/codeLogin
#浏览器跳转移动端CRM
#VUE_APP_REDIRECT_URI=https://crm.52santao.com/m/
