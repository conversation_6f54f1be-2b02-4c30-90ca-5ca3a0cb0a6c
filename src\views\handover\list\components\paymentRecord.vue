<template>
  <el-dialog
          v-el-drag-dialog
          width="80%"
          title="打款记录"
          :visible.sync="dialogFollow"
          :close-on-click-modal="!dialogFollow"
  >
    <el-row :gutter="10">
      <el-button v-if="!showCreate" v-permission="['order:list:payment:create']" type="primary" size="mini" class="fr"
                 @click="createFollow">新增
      </el-button>
      <el-col v-if="!showCreate" :span="24" class="mt10">
        <el-table
                v-loading="followListLoading"
                :data="followList"
                border
                fit
                stripe
                highlight-current-row
                style="width: 100%;"
        >
          <el-table-column label="#" type="index" width="50" />
          <el-table-column label="财务审核状态" prop="auditStatus" width="120" :formatter="getAuditStatus" />
          <el-table-column label="打款金额" prop="payAmount" />
          <el-table-column label="打款方式" prop="payTypeName" />
          <el-table-column label="打款类型" prop="tradeType" :formatter="getTrade" />
          <el-table-column label="打款时间" width="120" prop="payTime" />
          <el-table-column label="交易流水号" prop="transactionNo" width="120" show-overflow-tooltip />
          <el-table-column label="转款人姓名" prop="tradeCustomer" width="120" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" width="100" show-overflow-tooltip />
          <el-table-column label="操作" min-width="230">
            <template slot-scope="{row}">
              <template v-if="row.auditStatus === 10">
                <el-button type="primary" plain size="mini" @click="handleUpdate(row)">
                  修改
                </el-button>
                <el-button type="danger" plain size="mini" @click="handleDelete(row)">
                  删除
                </el-button>
                <el-button type="primary" size="mini" @click="handleSubmit(row)">
                  提交审核
                </el-button>
              </template>
              <template v-if="row.auditStatus !== 10">
                <el-button type="info" size="mini" v-if="row.auditStatus === 20" :disabled="true" >
                  您已提交审核，请耐心等待
                </el-button>
                <el-button type="success" size="mini" v-if="row.auditStatus === 30" :disabled="true" >
                  打款已到账
                </el-button>
                <el-button type="warning" size="mini" v-if="row.auditStatus == 40" @click="resubmit(row)">
                  打款未到账，重新填写
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-form v-if="showCreate" ref="followForm" :inline="true" :model="followModule" :rules="getRules"
               class="demo-form-inline" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="打款类型:" prop="tradeType">
              <el-radio-group v-model="followModule.tradeType">
                <el-radio v-for="(item,i) in trades" :key="i" :label="item.id">{{ item.itemName }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="followModule.tradeType !== 163 ">
            <el-form-item label="打款方式:" prop="payType">
              <el-select v-model="followModule.payType" filterable placeholder="打款方式" class="width-100">
                <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-popover
              placement="bottom-start"
              width="auto"
              trigger="click">
              <el-button size="mini"  slot="reference" type="warning" icon="el-icon-warning" plain>打款账户对照表</el-button>
              <CityContractSignView />
            </el-popover>
             关爱财务人员，请选择正确的收款账户
          </el-col>
        </el-row>
        <el-row v-if="followModule.tradeType !== 163 ">
          <el-col :span="24">
            <el-form-item label="打款金额:" prop="payAmount">
              <el-input v-model="followModule.payAmount" maxlength="10" placeholder="请输入打款金额" style="width: 195px;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="交易流水号:" prop="transactionNo">
              <el-input v-model="followModule.transactionNo" maxlength="10" placeholder="请输入交易流水号后五位"
                        style="width: 195px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="followModule.tradeType !== 163 ">
          <el-col :span="24">
            <el-form-item label="打款时间：" prop="payTime">
              <el-date-picker
                      v-model="followModule.payTime"
                      type="date"
                      placeholder="打款时间"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 195px;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="转款人姓名:" prop="tradeCustomer">
              <el-input v-model="followModule.tradeCustomer" type="text" maxlength="20" placeholder="请输入转款人姓名"
                        style="width: 195px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input v-model="followModule.remark" type="textarea" maxlength="255" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
    <div slot="footer" class="dialog-footer text-center">
      <el-button v-if="showCreate" type="primary" @click="confirmAddFollow">确定</el-button>
      <el-button v-if="showCreate" @click="cancelAdd">取消</el-button>
      <el-button v-show="!showCreate" @click="dialogFollow = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  createPaymentDetail,
  getOrderPaymentRecordList,
  modifyPaymentDetail,
  submitPaymentRecord,
  deletePaymentRecord
} from '@/api/payment'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { followStyleList, converseEnToCn, payMethod, auditStatus, converseEnToCnList } from '@/utils/field-conver'
import { parseTime } from '@/utils'
import { payAmount } from '@/utils/validate.js'
import {
  getPayType
} from '@/api/common'
import CityContractSignView from '@/views/handover/list/components/CityContractSignView.vue'

export default {
  name: 'PaymentRecord',
  directives: { elDragDialog },
  components: { CityContractSignView },
  props: {},
  data() {
    return {
      currentCustomerId: '',
      dialogFollow: false,
      followListLoading: false,
      followList: [],
      total: 0,
      showCreate: false,
      followModule: {},
      followStyleList: followStyleList,
      followModuleRules: {},
      payTimeFormat: '',
      payMethod: payMethod,
      isEdit: false,
      payTypes: [],
      trades: [],
      collectionId: null,
    }
  },
  computed: {
    getRules(){
      if(this.followModule.tradeType === 163){
          return {
            remark: { required: true, message: '后付款订单必须输入备注 ', trigger: 'blur' },
          }
      }else{
        return {
          payAmount: { required: true, trigger: 'blur', validator: (rule, value, callback) => {
              return payAmount(rule, value, callback)
            } 
          },
          payType: { required: true, message: '请选择打款方式 ', trigger: 'blur' },
          payTime: { required: true, message: '请选择打款时间', trigger: 'blur' },
          transactionNo: { required: true, message: '请输入交易流水号后五位', trigger: 'blur' },
          tradeCustomer: { required: true, message: '请输入转款人姓名', trigger: 'blur' },
          tradeType: { required: true, message: '请选择打款类型', trigger: 'blur' }
        }
      }
    }
  },
  created() {
  },
  mounted() {
    this.getAccountType3('pay_type')
    this.tradeList('trade_type')
  },
  methods: {
    /**
     * 获取打款记录
     * */
    getLists(id) {
      this.dialogFollow = true
      this.currentCustomerId = id
      getOrderPaymentRecordList({ orderId: id }).then(res => {
        this.followList = res.data
      })
    },
    tradeList(str) { // 打款类型
      const that = this
      getPayType(str).then(res => {
        that.trades = res.data && res.data.length > 0 ? res.data.filter(item => item.itemValue !== -1) : []
      })
    },
    /**
     * 新增打款记录详情
     */
    confirmAddFollow() {
      this.$refs['followForm'].validate((valid) => {
        if (valid) {
          if (!this.isEdit) {
            const params = Object.assign({
              orderId: this.currentCustomerId,
              payMethod: this.followModule.payType
            }, this.followModule)
            createPaymentDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '新增记录成功，收款完成后别忘了提交审核！',
                  type: 'success'
                })
                this.getLists(this.currentCustomerId)
                this.showCreate = false
              }
            })
          }
          else {
            const params = Object.assign({ payMethod: this.followModule.payType }, this.followModule)
            this.followModule.payTimeFormat = parseTime(this.followModule.payTime, '{y}-{m}-{d}')
            modifyPaymentDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '修改打款记录成功',
                  type: 'success'
                })
                this.getLists(this.currentCustomerId)
                this.showCreate = false
              }
            })
          }
        }
        else {

          return false
        }
      })
    },
    getFollowStyle(row) {
      return converseEnToCn(followStyleList, row.followType)
    },
    createFollow() {
      this.followModule = {}
      this.showCreate = true
      this.isEdit = false
    },
    handleUpdate(row) {
      this.followModule = row
      this.showCreate = true
      this.isEdit = true
    },
    resubmit(row) {
      this.followModule = row
      this.followModule.id = null
      this.followModule.remark = ''
      this.showCreate = true
      this.isEdit = false
    },
    /**
     * 删除打款记录
     * */
    handleDelete(row) {
      const params = {
        id: row.id
      }
      deletePaymentRecord(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '打款记录删除成功',
            type: 'success'
          })
          this.getLists(this.currentCustomerId)
        }
      })
    },
    /**
     * 提交打款记录
     */
    handleSubmit(row) {
      const params = {
        id: row.id
      }
      submitPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '打款记录提交审核成功',
            type: 'success'
          })
          this.getLists(this.currentCustomerId)
          this.$emit('submit')
          //遍历列表 判断状态 如果状态为10 则不关闭
          let close = true
          if (this.followList.length > 0) {
            this.followList.forEach(item => {
              if (item.auditStatus === 10 && item.id !== row.id) {
                close=false;
              }
            })
          }
          if(close){
            this.dialogFollow = false
          }


        }
      })
    },
    cancelAdd() {
      this.showCreate = false
    },
    getFollowTime(row) {
      return parseTime(row.nextFollowTime, '{y}-{m}-{d}')
    },
    getAuditStatus(data) {
      return converseEnToCn(auditStatus, data.auditStatus)
    },
    getPayMethod(row) {
      return converseEnToCn(payMethod, row.payMethod)
    },
    getAccountType3(str) {
      const that = this
      getPayType(str).then(res => {
        that.payTypes = res.data
      })
    },
    getTrade(row) {
      return converseEnToCnList(this.trades, row.tradeType)
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog__body {
    padding: 12px 20px 20px;
}

@media screen and (min-width: 1920px) {
    /deep/ .el-textarea {
        width: 662px;
    }
}

@media screen and (max-width: 1900px) and (min-width: 1600px ) {
    /deep/ .el-textarea {
        width: 580px;
    }
}

@media screen and (max-width: 1599px) and (min-width: 1440px ) {
    /deep/ .el-textarea {
        width: 535px;
    }
}

@media screen and (max-width: 1366px) {
    /deep/ .el-textarea {
        width: 520px;
    }
}
</style>
<style>
.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 186px;
}
</style>
