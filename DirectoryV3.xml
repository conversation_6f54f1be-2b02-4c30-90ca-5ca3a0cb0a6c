<?xml version="1.0" encoding="UTF-8"?>
 <trees>
     <tree path="/src/utils/field-conver.js" title="本地字典" />
     <tree path="/src/views/curriculum/exercises.vue" title="习题详情页" />
     <tree path="/src/views/handover/paymentRecord/index.vue" title="打款记录" />
     <tree path="/src/views/handover/list/components/choose-qualification.vue" title="选择资质弹出窗" />
     <tree path="/src/views/contract/list/index.vue" title="合同列表" extension="" presentableText="" tooltipTitle="" icon=""
           textColor="" backgroundColor="" />
     <tree path="/src/views/handover/list/components/create.vue" title="创建交接单" />
     <tree path="/src/views/handover/list/components/paymentRecord.vue" title="打款记录新增弹窗" extension="" presentableText=""
           tooltipTitle="" icon="" textColor="" backgroundColor="" />
     <tree path="/src/views/contract/list/components/features.vue" title="特殊班型加盟页面" />
     <tree path="/src/views/contract/list/components/common.vue" title="普通加盟" />
     <tree path="/src/views/handover/list/components/CityContractSignView.vue" title="签约主体对照表" />
    <tree path="/src/views/schoolProject/campus.vue" title="校区管理" />
    <tree path="/src/views/schoolProject/components/studentsPop.vue" title="校区管理>学生数" />
    <tree path="/src/views/curriculum/componentsXK/GenerateQuestionFromXk.vue" title="学科网试题卡片" />
    <tree path="/src/views/curriculum/componentsXK/TabGenerateQuestionFromXk.vue" title="学科网TAB" />
    <tree path="/src/views/curriculum/componentsXK/KPointFromXKSelect.vue" title="知识点弹框" />
    <tree path="/src/views/curriculum/componentsXK/SubjectFromXKSelect.vue" title="选择科目" />
    <tree path="/src/views/handover/paymentRecord/detail.vue" title="打款记录详情-审核弹窗" />
    <tree path="/src/views/schoolProject/components/boxLocation.vue" title="校区详情》智能终端定位（功能重复，作废）" />
    <tree path="/src/views/resources/index.vue" title="盒子总览" />
    <tree path="/src/views/resources/components/interregionalPop.vue" title="查看位置" />
    <tree path="/src/router/index.js" title="全局路由" />
    <tree path="/src/views/curriculum/course.vue" title="课程列表" />
  <tree path="/src/views/curriculum/componentsXK/TextBookVersionFromXKSelect.vue" title="版本选择框" extension="" presentableText=""
        tooltipTitle="" icon="" textColor="" backgroundColor="" />
  <tree path="/src/views/curriculum/componentsXK/EduSystemFromXKSelect.vue" title="学制" />
  <tree path="/src/views/curriculum/componentsXK/QuestionDifficultyFromXKSelect.vue" title="难度选择框" />
  <tree path="/src/views/curriculum/componentsXK/TextBookFromXKSelect.vue" title="教材列表" />
  <tree path="/src/views/curriculum/componentsXK/GradeFromXKSelect.vue" title="年级选择" />
  <tree path="/src/views/curriculum/componentsXK/CatalogFromXKSelect.vue" title="章节或课程" extension="" presentableText=""
        tooltipTitle="" icon="" textColor="" backgroundColor="" />
  <tree path="/src/components/Select/KnowledgeByClassAndSubjectSelectForAdd.vue" title="用作新增课程" />
</trees>
