<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="100px" :disabled="!isEdit" :rules="aiRules">
      <!--    05 AI -->
      <el-row :gutter="10">
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合伙人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户编号：">
                    <span>{{ detail.contractClue.clueNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="客户名称：">
                    <span>{{ detail.contractClue.customer }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="联系电话：">
                    <span>{{ detail.contractClue.mobile }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.contractClue.institution }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="机构区域：">
                    <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="机构地址：">
                    <span>{{ detail.contractClue.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>项目所属校区</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区编号：">
                    <span>{{ detail.clueSchool.schoolCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="校区名称：">
                    <span>{{ detail.clueSchool.schoolName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="签约区域：">
                    <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                      | {{ detail.clueSchool.countyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="详细地址：">
                    <span>{{ detail.clueSchool.address }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" />
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>交接单基本信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="订单编号：">
                    <span>{{ detail.contractOrder.orderCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="区域类型：">
                    <span>{{ areaSingle }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="业务类型：">
                    <span>{{ businessType }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="应收金额：">
                    <span>{{ detail.contractOrder.payAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="实收金额：">
                    <span>{{ detail.contractOrder.realAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐名称：">
                    <span>{{ detail.contractOrder.policyName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:6}">
                  <el-form-item label="套餐价格：">
                    <span>{{ detail.contractOrder.policyPrice }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="签约期限：" label-width="110px">
                    <span v-if="detail.contractOrder.signStartTime">{{ detail.contractOrder.signStartTime }}</span>
                    <span v-if="detail.contractOrder.signEndTime">-{{ detail.contractOrder.signEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐渠道：">
                    <span>{{ channel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:9}">
                  <el-form-item label="推荐人：">
                    <span>{{ detail.contractOrder.recName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="备注：">
                    <span>{{ detail.contractOrder.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col v-if="detail.contractSignatory != null" :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>签约人信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="姓名：">
                    <span>{{ detail.contractSignatory.userName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="身份证号：">
                    <span>{{ detail.contractSignatory.idCard }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="手机号：">
                    <span>{{ detail.contractSignatory.phone }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col v-if="detail.contractEnterprise != null" :lg="{span:12}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>企业资质信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业名称：">
                    <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="统一社会信用代码：" label-width="160px">
                    <span>{{ detail.contractEnterprise.creditCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="法人代表：">
                    <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="企业地址：">
                    <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row v-if="detail.contractSignatory != null" :gutter="22">
        <el-col :lg="{span:24}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同签约信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同编号：" label-width="130px">
                    <span>{{ detail.contractCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同名称：" label-width="130px">
                    <span>{{ detail.contractName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="加盟项目：" label-width="130px">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同类型：" prop="contractType" label-width="130px">
                    <el-radio-group v-model="detail.contractType" class="radios" :disabled="detail.orderStatus!==21">
                      <el-radio :label="1">个人合同</el-radio>
                      <el-radio :label="2">企业合同</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="合同版本：" label-width="130px">
                    <div>{{ detail.versionType === 2 ? '预签' : '正式' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :sm="{span:24}" :lg="{span:12}">
                  <el-form-item label="AI版本：" label-width="130px" required>
                    <el-radio-group v-model="detail.aiContract.serviceEdition" class="radios" @change="refresh">
                      <el-radio v-for="(item,index) in baseService" :key="index" :label="item.id">{{ item.itemName }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="12">
                  <el-form-item label="合同期限：" required label-width="130px">
                    <el-date-picker
                      v-model="timeArr"
                      style="width: 100%"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="合同开始日期"
                      end-placeholder="合同结束日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="基础服务：" label-width="130px">
                    <el-checkbox-group v-model="baseServiceTypes">
                      <el-checkbox v-for="(item,i) in baseServiceList" :key="i" :label="item.id">{{ item.itemName }}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="增值服务：" label-width="130px">
                    <el-checkbox-group v-model="valueAddedServiceTypes">
                      <el-checkbox v-for="(item,i) in serviceList" :key="i" :label="item.id">{{ item.itemName }}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="总服务费/元：" label-width="130px" required>
                    <el-input v-model="detail.aiContract.totalAmount" placeholder="填入总服务费" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="基础服务费/元：" label-width="130px" required>
                    <el-input v-model="detail.aiContract.baseServiceAmount" placeholder="基础服务费" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="增值服务费/元：" label-width="130px" required>
                    <el-input v-model="detail.aiContract.valueAddedServiceAmount" placeholder="增值服务费" type="number" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                  <el-form-item label="补充内容：" label-width="130px" required>
                    <el-input v-model="detail.remark" type="textarea" maxlength="300" :row="2" show-word-limit placeholder="请输入补充内容，若无补充内容，请输入无" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
    </div>

  </div>
</template>

<script>
import { getContractDetail, modifyContractDetail, getCreatContract } from '@/api/contract'
import { converseEnToCn, businessTypeList, channelList, orderStatusList, contractClass, getContractStatus, getAreaSingle } from '@/utils/field-conver'
import { getPayType } from '@/api/common'
export default {
  name: 'AiContract',
  // components: { AreaPicker },
  props: {
  },
  data() {
    return {
      detail: {
      },
      aiRules: {
        cooperationType: { required: true, message: ' ', trigger: 'change' },
        contractType: { required: true, message: ' ', trigger: 'change' },
        publicSchool: { required: true, message: ' ', trigger: 'blur' },
        schoolAddress: { required: true, message: '请输入校区地址 ', trigger: 'blur' }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1,
      timeArr: [],
      serviceList: [],
      baseServiceList: [],
      baseService: [],
      valueAddedServiceTypes: [],
      baseServiceTypes: [],
      publicSchool: '',
      schoolAddress: ''
    }
  },
  mounted() { // ai_value_added_service_type
    this.getService('ai_service_edition')
    this.AddedService('ai_value_added_service_type')
    this.AddedBaseService('ai_base_service_type')
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? 'AI-编辑合同' : 'AI-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getService(str) {
      getPayType(str).then(res => {
        if (res.code === '000000') {
          this.baseService = res.data || []
        }
      })
    },
    AddedService(str) {
      getPayType(str).then(res => {
        if (res.code === '000000') {

          this.serviceList = res.data || []
        }
      })
    },
    AddedBaseService(str) {
      getPayType(str).then(res => {
        if (res.code === '000000') {

          this.baseServiceList = res.data || []
        }
      })
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        that.detail.id = that.id
        that.detail.aiContract = {
          serviceEdition: res.data.aiContract !== null && res.data.aiContract.serviceEdition !== null ? res.data.aiContract.serviceEdition : '',
          totalAmount: res.data.aiContract !== null && res.data.aiContract.totalAmount !== null ? res.data.aiContract.totalAmount : null,
          valueAddedServiceAmount: res.data.aiContract !== null && res.data.aiContract.valueAddedServiceAmount !== null ? res.data.aiContract.valueAddedServiceAmount : null,
          baseServiceAmount: res.data.aiContract !== null && res.data.aiContract.baseServiceAmount !== null ? res.data.aiContract.baseServiceAmount : null
        }
        that.valueAddedServiceTypes = res.data.aiContract !== null && res.data.aiContract.valueAddedServiceTypes !== null ? res.data.aiContract.valueAddedServiceTypes : []


        that.baseServiceTypes = res.data.aiContract !== null && res.data.aiContract.baseServiceTypes !== null ? res.data.aiContract.baseServiceTypes : []

        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle) // 区域类型
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getCreatContract() {
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = JSON.parse(JSON.stringify(res.data))
        that.timeArr = that.detail.startTime !== null && that.detail.endTime !== null ? [that.detail.startTime, that.detail.endTime] : []
        that.detail.id = res.data.contractId
        that.detail.aiContract = {
          serviceEdition: res.data.aiContract !== null && res.data.aiContract.serviceEdition !== null ? res.data.aiContract.serviceEdition : '',
          totalAmount: res.data.aiContract !== null && res.data.aiContract.totalAmount !== null ? res.data.aiContract.totalAmount : null,
          valueAddedServiceAmount: res.data.aiContract !== null && res.data.aiContract.valueAddedServiceAmount !== null ? res.data.aiContract.valueAddedServiceAmount : null,
          baseServiceAmount: res.data.aiContract !== null && res.data.aiContract.baseServiceAmount !== null ? res.data.aiContract.baseServiceAmount : null
        }
        that.valueAddedServiceTypes = res.data.aiContract !== null && res.data.aiContract.valueAddedServiceTypes !== null ? res.data.aiContract.valueAddedServiceTypes : []
        that.baseServiceTypes = res.data.aiContract !== null && res.data.aiContract.baseServiceTypes !== null ? res.data.aiContract.baseServiceTypes : []
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle) // 区域类型
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
       * 确认修改信息
       */
    confirmEdit(num) {
      if (!this.isValid()) {
        this.$message({
          type: 'warning',
          message: '合同内容模块必填!'
        })
        return
      }
      if (!this.detail.contractType) {
        this.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      if (!this.timeArr || (this.timeArr && this.timeArr.length < 2)) {
        this.$message({
          message: '合同期限不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.aiContract.serviceEdition) {
        this.$message({
          message: 'AI版本不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.aiContract.totalAmount) {
        this.$message({
          message: '总服务费不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.remark) {
        this.$message({
          message: '补充内容不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.aiContract.baseServiceAmount) {
        this.$message({
          message: '基础服务费不能为空',
          type: 'warning'
        })
        return
      }
      if (!this.detail.aiContract.valueAddedServiceAmount && this.detail.aiContract.valueAddedServiceAmount !== 0) {
        this.$message({
          message: '增值服务费不能为空',
          type: 'warning'
        })
        return
      }
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {

          const aiData = Object.assign({}, { baseServiceAmount: that.detail.aiContract.baseServiceAmount, serviceEdition: that.detail.aiContract.serviceEdition, totalAmount: that.detail.aiContract.totalAmount, valueAddedServiceAmount: that.detail.aiContract.valueAddedServiceAmount, valueAddedServiceTypes: that.valueAddedServiceTypes, baseServiceTypes: that.baseServiceTypes })

          const data = Object.assign(that.detail, { operateType: num, aiContractDTO: aiData, startTime: that.timeArr[0],
            endTime: that.timeArr[1] })
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '保存成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    /**
       * 必填校验
       */
    isValid() {
      let valid = true
      if (!this.detail.aiContract.totalAmount) {
        valid = false
      }
      return valid
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
  i{
    font-style: normal;
  }
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .total-money{
    display: flex;
  }
</style>
