/**
 * 业绩部门选择组件
 * @description 业绩部门选择组件
 */

<template>
  <el-row>
    <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
      <el-select
        v-model="selectedDept"
        clearable
        style="width: 100%"
        @change="handleChange"
        :disabled="disabled"
      >
        <el-option
          v-for="item in departmentList"
          :key="item.itemValue"
          :label="item.itemName"
          :value="item.itemValue"
          :disabled="disabled"
        />
      </el-select>
    </el-col>
  </el-row>
</template>

<script>
import { getPerformanceDepartment } from "@/api/handover";

export default {
  name: "PerformanceDepartment",
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Number],
      default: "",
    },
    message: {
      type: String,
      default: "请选择业绩部门",
    },
    isRequired: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      departmentList: [],
    };
  },
  computed: {
    selectedDept: {
      get() {
        return this.value;
      },
      set(newVal) {
        this.$emit("update:value", newVal);
      },
    },
  },

  created() {
    this.getDepartmentList();
  },
  methods: {
    async getDepartmentList() {
      try {
        const res = await getPerformanceDepartment();
        this.departmentList = res.data;
      } catch (error) {
        console.error("获取业绩部门列表失败:", error);
      }
    },
    handleChange(value) {
      this.$emit("change", value);
    },
  },
};
</script>

<style scoped>
.box-card {
  margin-top: 10px;
}
</style>