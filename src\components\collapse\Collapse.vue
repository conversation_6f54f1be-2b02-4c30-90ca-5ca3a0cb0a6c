<template>
  <div>
    <div @click="collapse" class="trigger" v-if="!unfold">{{ isCollapse?'收起' : '查看更多' }}</div>
    <slot name="default" v-if="isCollapse"></slot>
  </div>
</template>
<script>
export default {
  name: 'Collapse',
  data() {
    return {
      isCollapse: false
    }
  },
  props: {
    unfold: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.isCollapse = this.unfold
  },
  methods: {
    collapse() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>
<style scoped>
.trigger{
  font-size: 12px;
  line-height: 12px;
  text-align: right;
  padding: 5px 0;
  background: linear-gradient(to bottom, #f4f4f4, #ffffff);
}
</style>
