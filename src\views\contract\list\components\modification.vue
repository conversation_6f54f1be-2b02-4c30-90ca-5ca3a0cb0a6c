<template>
  <div class="app-container">
    <div class="bgGreys">
      <el-row :gutter="10">
        <!--合同信息-->
        <el-col :lg="{span:24}">
          <el-form
            ref="oldCustomerInfo"
            size="small"
            label-width="100px"
            :disabled="flags===0"
          > <!--仅合同创建时 是可编辑的，其他都是不可编辑的-->
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>合同信息</span>
              </div>
              <div class="item">
                <!--合同详情 显示-->
                <el-row v-if="flags===0">
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同编号：" label-width="130px" required>
                      <span>{{ newContractInfo.newContractCode }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同名称：" label-width="130px" required>
                      <span>{{ newContractInfo.newContractName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item required label="原协议编号:" label-width="130px">
                      <el-select v-model="agreementModel" placeholder="请选择原协议编号" class="filter-item" style="width: 100%" @change="getAgreementNumber" filterable>
                        <el-option
                          v-for="item in agreementList"
                          :key="item.contractCode"
                          :label="item.contractCode"
                          :value="item.contractId"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="原协议名称:" label-width="130px" required>
                      <el-input v-model="oldCustomerInfo.contractName" :disabled="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="原协议加盟项目:" label-width="130px">
                      <el-input v-model="oldCustomerInfo.projectName" :disabled="true" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label-width="130px" label="原协议签订日期:">
                      <el-date-picker
                        v-model="oldCustomerInfo.signDate"
                        style="width: 100%"
                        type="date"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd"
                        :disabled="true"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                    <el-form-item label="甲方:" label-width="130px">
                      <el-input v-model="oldCustomerInfo.jiaName" :disabled="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                    <el-form-item label="乙方:" label-width="130px" required>
                      <el-input v-model="oldCustomerInfo.partyB" :disabled="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="乙方负责人:" label-width="130px" required>
                      <el-input v-model="oldCustomerInfo.partyBPeople" :disabled="true" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:15}">
                    <el-form-item label="乙方社会信用统一代码/身份证号码:" label-width="265px" required>
                      <el-input v-model="oldCustomerInfo.code" :disabled="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <!--原协议内容-->
        <el-col :lg="{span:11}">
          <el-form
            ref="oldCustomerInfoElse"
            size="small"
            :model="oldCustomerInfo"
            label-width="100px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>原协议内容</span>
              </div>
              <div class="item">
                <el-row :gutter="10">
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议套餐:">
                      <span>{{ oldCustomerInfo.policyName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议价格/元:">
                      <span>{{ oldCustomerInfo.policyPrice }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议校区名称:">
                      <span>{{ oldCustomerInfo.schoolName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议法定代表人/联系人:">
                      <span>{{ oldCustomerInfo.userName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议身份证号码:">
                      <span>{{ oldCustomerInfo.idCard }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议经营区域:">
                      <span>{{ oldAreaName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议经营地址:">
                      <span>{{ oldCustomerInfo.address }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" style="margin-top: 10px">
                    <el-form-item label-width="180px" label="原协议期限:">
                      <el-date-picker
                        v-model="oldTimeArr"
                        style="width: 100%"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="协议开始日期"
                        end-placeholder="协议结束日期"
                        value-format="yyyy-MM-dd"
                        :disabled="true"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
        <!--变更内容-->
        <el-col :lg="{span:13}">
          <el-form
            ref="supplementContract"
            size="small"
            :model="supplementContract"
            label-width="100px"
            :disabled="!isEdit"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>变更内容</span>
              </div>
              <div class="item">
                <el-row :xs="{span:24}" :sm="{span:24}" :gutter="5" style="margin-top: 10px">
                  <el-col :span="9">
                    <el-form-item label="变更套餐:">
                      <el-select v-model="supplementContract.serviceType" placeholder="请选择升级/降级" class="filter-item" style="width: 100%" filterable>
                        <el-option
                          v-for="item in comboLevels"
                          :key="item.value"
                          :label="item.label"
                          :value="item.label"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="supplementContract.servicePlan" @input="updateView($event)">
                      <el-button slot="append" type="mini" @click="getPolicy">选择套餐</el-button>
                    </el-input>
                  </el-col>
                  <el-col :span="7">
                    <el-input v-model="supplementContract.servicePrice" placeholder="请输入套餐应付价格/元" />
                  </el-col>
                </el-row>
                <el-row :xs="{span:24}" :sm="{span:24}" :gutter="5" style="margin-top: 10px">
                  <el-col :span="14">
                    <el-form-item label="变更价格/元:" label-width="130px">
                      <el-select v-model="supplementContract.updateType" placeholder="请选择补差价类型" class="filter-item" style="width: 100%" filterable>
                        <el-option
                          v-for="item in differencePriceTypes"
                          :key="item.value"
                          :label="item.label"
                          :value="item.label"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-input v-model="supplementContract.differencePrice" placeholder="请输入补差价" />
                  </el-col>
                </el-row>
                <el-row :gutter="10" :span="24">
                  <el-col>
                    <el-form-item label="变更校区名称:" label-width="130px">
                      <el-input v-model="supplementContract.updateSchoolName" placeholder="请输入变更后的校区名称" />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更法定代表人/联系人:" label-width="170px">
                      <el-input v-model="supplementContract.updateLegalPerson" placeholder="请输入变更后的法定代表人/联系人" />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更身份证号码:" label-width="130px">
                      <el-input v-model="supplementContract.updateIdCard" placeholder="请输入变更后的身份证号码" />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更经营区域:" label-width="130px">
                      <area-picker
                        :area-list="areaList"
                        :level="'4'"
                        area-style="width:100%"
                        class="filter-item"
                        @getAreaList="getAreaList"
                        @getAreaName="getAreaName"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更经营地址:" label-width="130px">
                      <el-input v-model="supplementContract.updateAddress" placeholder="请输入变更后的经营地址" />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更协议期限:" label-width="130px">
                      <el-date-picker
                        v-model="timeArr"
                        style="width: 100%"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col style="margin-top: 10px">
                    <el-form-item label="变更协议补充内容:" label-width="130px">
                      <el-input v-model="supplementContract.supplementContent" type="textarea" placeholder="请输入变更协议补充内容" maxlength="300" show-word-limit />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
      </el-row>
      <div class="text-wrapper" style="margin:0 auto;width:50%; height:50%;">
        <span style="color: red">
          1. 变更合同升级，原始合同需补价时，请创建复购交接单进行补价；

          2. 变更合同降级，原始合同需退差价时，请联系财务人员创建退款交接单；

          3. 升级/降级涉及流量充减时，联系运营手动设置；
        </span>
      </div>
    </div>
    <el-row>
      <el-col v-if="isEdit" :span="6" :offset="9" class="text-center">
        <div class="add-customer submit" @click="confirmEdit()">提交</div>
      </el-col>
    </el-row>
    <choose-policy ref="choosePolicy" @chooseSuccess="getPolicyCallBack" />
  </div>
</template>
<script>
import {
  getContractNo,
  getOldContract,
  modifyContractDetail,
  getSupplement
} from '@/api/contract'
import ChoosePolicy from '@/views/handover/list/components/choose-policy'
import AreaPicker from '@/components/area-picker'
export default {
  name: 'Modification',
  components: { ChoosePolicy, AreaPicker },
  data() {
    return {
      // 新合同信息
      newContractInfo: {
        newContractCode: '',
        newContractId: 0,
        newContractInfo: '',
        newContractName: '',
        newEndDate: '',
        newStartDate: ''
      },
      agreementList: [], // 协议编号列表
      agreementModel: '', // 当前选择的合同协议编号信息
      // 原合同信息
      oldCustomerInfo: {
        contractName: '',
        projectName: '',
        signDate: '',
        jiaName: '',
        partyB: '',
        partyBPeople: '',
        code: '',
        policyName: '',
        policyPrice: '',
        schoolName: '',
        userName: '',
        idCard: '',
        address: ''
      },
      // 变更内容
      supplementContract: {
        serviceType: '',
        servicePlan: '',
        servicePrice: '',
        updateType: '',
        differencePrice: '',
        updateSchoolName: '',
        updateLegalPerson: '',
        updateIdCard: '',
        updateAddress: '',
        supplementContent: ''
      },
      // 套餐升级/降级 列表
      comboLevels: [
        {
          value: 0,
          label: '升级'
        },
        {
          value: 1,
          label: '降级'
        }
      ],
      // 补差价类型 列表
      differencePriceTypes: [
        {
          value: 0,
          label: '应补价'
        },
        {
          value: 1,
          label: '退差价'
        }
      ],
      // 地址选择
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: '',
        provinceName: '',
        cityName: '',
        areaName: '',
        countyName: ''
      },
      timeArr: ['', ''],
      oldTimeArr: ['', ''],
      oldAreaName: '',
      isEdit: false,
      flags: -1
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同详情页面:isEdit区分是否可编辑，1是指从校区项目列表点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getContractNo()
    }
    const tagsName = this.isEdit ? `变更/补充合同-编辑合同` : `变更/补充合同-合同详情`
    this.setTagsViewTitle(tagsName)
  },
  mounted() {
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    /**
     * 获取套餐列表
     * */
    getPolicy() {
      if (!this.oldCustomerInfo.projectId || !this.oldCustomerInfo.businessType) {
        this.$message({
          message: '项目或业务类型为空',
          type: 'warning'
        })
        return
      }
      const params = {
        businessType: this.oldCustomerInfo.businessType,
        projectId: this.oldCustomerInfo.projectId
      }
      this.$refs.choosePolicy.getLists(params)
    },
    /**
     * 套餐选择的回调函数
     * */
    getPolicyCallBack(data) {
      this.supplementContract.servicePlan = data.policyName || ''
      this.supplementContract.servicePrice = data.price
    },
    /**
     * 获取省市区的地址 id
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
      this.areaList.countyId = data.countyId
    },
    /**
     * 获取省市区的地址 名称
     * */
    getAreaName(name) {
      this.areaList.provinceName = name.provinceName
      this.areaList.cityName = name.cityName
      this.areaList.areaName = name.areaName
      this.areaList.countyName = name.countyName
    },
    /**
     * 编辑合同 新合同 经营区域 和 原合同参数
     * */
    setParamsValue() {
      // 地址区域名称
      this.supplementContract.updateProvinceName = this.areaList.provinceName
      this.supplementContract.updateCityName = this.areaList.cityName
      this.supplementContract.updateAreaName = this.areaList.areaName
      this.supplementContract.updateCountyName = this.areaList.countyName
      // 地址区域id
      this.supplementContract.updateProvinceId = this.areaList.provinceId
      this.supplementContract.updateCityId = this.areaList.cityId
      this.supplementContract.updateAreaId = this.areaList.areaId
      this.supplementContract.updateCountyId = this.areaList.countyId
      // 原合同id
      this.supplementContract.oldContractId = this.oldCustomerInfo.contractId
    },
    getDetail() { // 从合同列表点进到合同详情页面
      const that = this
      const data = this.id
      getSupplement(data).then(res => {
        const detail = JSON.parse(JSON.stringify(res.data))
        that.newContractInfo = detail
        // 原合同信息
        that.oldCustomerInfo = detail.oldContractVO
        // 原合同原协议编号
        that.agreementModel = that.oldCustomerInfo.contractCode
        // 原合同协议期限
        that.oldTimeArr = that.oldCustomerInfo.startDate !== null && that.oldCustomerInfo.endDate !== null ? [that.oldCustomerInfo.startDate, that.oldCustomerInfo.endDate] : []
        // 原合同经营区域
        that.oldAreaName = (that.oldCustomerInfo.provinceName || '') + (that.oldCustomerInfo.cityName || '') + (that.oldCustomerInfo.areaName || '') + (that.oldCustomerInfo.countyName || '')
        // 新的协议期限
        that.timeArr = detail.newStartDate !== null && detail.newEndDate !== null ? [detail.newStartDate, detail.newEndDate] : []
        if (detail.supplementContract !== null) {
          that.supplementContract = detail.supplementContract
          // 地址区域渲染到地址区域选择器中
          this.areaList = {
            provinceId: that.supplementContract.updateProvinceId || null,
            cityId: that.supplementContract.updateCityId || null,
            areaId: that.supplementContract.updateAreaId || null,
            countyId: that.supplementContract.updateCountyId || null
          }
        }
      })
    },
    /**
     * 变更/补充合同查看合同编号列表
     * */
    getContractNo() {
      const that = this
      const data = this.id
      getContractNo(data).then(res => {
        that.agreementList = JSON.parse(JSON.stringify(res.data))
        that.agreementModel = that.agreementList[0].contractId // 默认第一个协议编号==目标value
        // 根据默认协议编码 请求 原合同详情
        this.getAgreementNumber(that.agreementModel, true)
      })
    },
    /**
     * 获取合同的原协议编号
     * isRender: 是否是页面渲染时的赋值
     * */
    getAgreementNumber(val, isRender) {
      const that = this
      if (!val) {
        this.$message({
          message: '请先选择原协议编号',
          type: 'warning'
        })
        return
      }
      // 如果是手动改变，置空
      if (!isRender) {
        that.oldCustomerInfo = {
          contractName: '',
          projectName: '',
          signDate: '',
          jiaName: '',
          partyB: '',
          partyBPeople: '',
          code: '',
          policyName: '',
          policyPrice: '',
          schoolName: '',
          userName: '',
          idCard: '',
          address: ''
        }
        that.oldTimeArr = ['', '']
        that.oldAreaName = ''
      }
      // 原合同详情
      getOldContract(val).then(res => {
        const details = JSON.parse(JSON.stringify(res.data))
        if (details !== null) {
          that.oldCustomerInfo = details
          // 原合同协议期限
          that.oldTimeArr = that.oldCustomerInfo.startDate !== null && that.oldCustomerInfo.endDate !== null ? [that.oldCustomerInfo.startDate, that.oldCustomerInfo.endDate] : []
          // 原合同经营区域
          that.oldAreaName = (that.oldCustomerInfo.provinceName || '') + (that.oldCustomerInfo.cityName || '') + (that.oldCustomerInfo.areaName || '') + (that.oldCustomerInfo.countyName || '')
        }
      })
    },
    /**
     * 确认修改信息
     */
    confirmEdit() {
      const that = this
      // 合同列表 编辑 num=1；校区列表 合同创建 num=2
      const num = this.flags === 1 ? 2 : 1
      that.$refs['supplementContract'].validate(valid => {
        if (valid) {
          this.setParamsValue()
          const data = {
            operateType: num,
            versionType: 10,
            startTime: that.timeArr[0],
            endTime: that.timeArr[1],
            supplementContractDTO: that.supplementContract
          }
          if (this.flags === 0) {
            // 合同编辑 需要传新合同id
            data.id = this.newContractInfo.newContractId
          }
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '编辑成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          })
        }
      })
    },
    /**
     * 公共刷新方法
     * */
    updateView(e) {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .add-customer {
    border-radius: 18px;
    height: 36px;
    color: #539FFF;
    font-size: 15px;
    line-height: 36px;
    text-align: center;
    font-weight: 500;
    border: 1px dashed #DCDCDC;
    margin: 10px auto;
    cursor: pointer;
  }
  .add-customer:hover{
    background: #46a6ff;
    color: #fff;
    border: 1px dashed #46a6ff;
  }
  .add-customer.submit {
    background-color: #46a6ff;
    color: #ffffff;
    border: 1px solid #1890ff
  }
  .text-wrapper {
    white-space: pre-wrap;
    background: yellow;
    padding: 5px;
  }
</style>
