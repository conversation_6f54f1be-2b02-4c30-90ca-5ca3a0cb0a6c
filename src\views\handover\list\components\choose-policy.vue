<template>
  <el-dialog
    v-el-drag-dialog
    width="40%"
    title="选择套餐"
    :visible.sync="dialogPolicy"
  >
    <el-row :gutter="10">
      <el-col :span="24">
        <el-table
          v-loading="policyListLoading"
          :data="policyList"
          border
          fit
          stripe
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column type="expand" label="#">
            <template slot-scope="props">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-row v-for="item in props.row.policyProductList" :key="item.productId">
                  <el-col :span="12">
                    <el-form-item label="产品名称">
                      <span>{{ item.productName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品数量">
                      <span>{{ item.productNum }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="套餐名称" prop="policyName" min-width="300" show-overflow-tooltip />
          <el-table-column label="套餐价格" prop="price" width="80" />
          <el-table-column label="套餐备注" prop="remark" show-overflow-tooltip />
          <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="80">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="handleChoose(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!--    <div slot="footer" class="dialog-footer">-->
    <!--      <el-row>-->
    <!--        <el-col :span="24">-->
    <!--&lt;!&ndash;          <router-link :to="'/customer/list/'" class="link-type">&ndash;&gt;-->
    <!--            <div class="add-customer">-->
    <!--              <i class="el-icon-plus" style="font-size: 12px" />&nbsp;-->
    <!--              去新增套餐-->
    <!--            </div>-->
    <!--&lt;!&ndash;          </router-link>&ndash;&gt;-->
    <!--        </el-col>-->
    <!--      </el-row>-->
    <!--    </div>-->
  </el-dialog>
</template>

<script>
import { getPolicyList } from '@/api/handover'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'ChoosePolicy',
  directives: { elDragDialog },
  components: {},
  props: {},
  data() {
    return {
      dialogPolicy: false,
      policyListLoading: false,
      policyList: []
    }
  },
  methods: {
    /**
     * 获取客户列表
     * */
    getLists(obj) {
      const that = this
      getPolicyList(obj).then(res => {
        that.dialogPolicy = true
        that.policyList = res.data
      })
    },
    /**
     * 关联客户
     */
    handleChoose(data) {

      this.$emit('chooseSuccess', data)
      this.dialogPolicy = false
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
  }

  /deep/ .el-input--medium .el-input__inner {
    border-radius: 18px;
  }

  .add-customer {
    border-radius: 18px;
    height: 36px;
    background-color: #F8F8F8;
    color: #539FFF;
    font-size: 15px;
    line-height: 36px;
    text-align: center;
    font-weight: 500;
  }
  /deep/ .el-table__expanded-cell {
    padding: 5px 50px;
  }
  /deep/ .el-table__expanded-cell .el-form-item {
    margin-bottom: 0;
  }
</style>
