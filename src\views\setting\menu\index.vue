<template>
  <div class="app-container">
    <div class="filter-container">
      <!--<el-button v-waves class="filter-item" type="primary" @click="handleFilter">-->
      <!--查询-->
      <!--</el-button>-->
      <el-button v-waves v-permission="['setting:menu:create']" class="filter-item" type="primary" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="menuList"
      border
      fit
      stripe
      highlight-current-row
      row-key="id"
      :tree-props="{children: 'childes'}"
      :default-sort="{prop: 'sort'}"
      style="width: 100%;"
    >
      <af-table-column label="菜单名称" prop="name" />
      <af-table-column label="菜单别名" prop="alias" />
      <af-table-column label="菜单编码" prop="code" />
      <af-table-column label="菜单类型" prop="category" :formatter="getMenuCategory" width="120px"/>
      <af-table-column label="菜单排序" prop="sort" sortable width="120px"/>
      <el-table-column label="菜单状态" prop="valid" width="120px">
        <template slot-scope="{row}">
          <el-tag v-if="row.valid === 1" type="success">启用中</el-tag>
          <el-tag v-if="row.valid === 0" type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:menu:update']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button type="primary" size="mini" @click="handleMenuEnable(row)">
            <span v-if="row.valid == 0">启用</span>
            <span v-if="row.valid == 1" v-permission="['setting:menu:enable']">禁用</span>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <menu-detail ref="treeList" :disabled="isEdit" :title="title" @refreh="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { menuCategoryList, converseEnToCn, menuValidList } from '@/utils/field-conver'
import { getSystemMenus, setMenuEnable } from '@/api/system-setting'
import MenuDetail from './detail'
export default {
  name: 'Employee',
  components: { Pagination, MenuDetail },
  directives: {},
  data() {
    return {
      menuList: [],
      listLoading: false,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      isEdit: false,
      title: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleFilter() {
      this.getList()
    },
    handleAdd() {
      this.title = '新增菜单'
      this.isEdit = true
      this.$refs.treeList.getMenuTreeList()
      this.$refs.treeList.isUpdate = false
    },
    /**
     * 查询列表
     * */
    getList() {
      this.listLoading = true
      getSystemMenus(this.listQuery.pageIndex, this.listQuery.pageSize).then(res => {
        this.menuList = res.data.records
        this.total = res.data.total
        this.listLoading = false
      })
    },
    /**
     * 修改菜单
     * */
    handleUpdate(row) {
      this.title = '修改菜单'
      this.isEdit = true
      this.$refs.treeList.getMenusDetail(row.id)
      this.$refs.treeList.getMenuTreeList()
      this.$refs.treeList.isUpdate = true
    },
    /**
     * 菜单启用、禁用
     * */
    handleMenuEnable(row) {
      const params = {
        id: row.id,
        type: row.valid === 0 ? 1 : 0
      }
      const msg = row.valid === 0 ? '该菜单已禁用，确认是否开启?' : '该菜单已开启，是否确认禁用？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setMenuEnable(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '禁用成功',
              type: 'success'
            })
          }
          this.getList()
        })
      })
    },
    /**
     * 获取菜单类型。 菜单/按钮
     */
    getMenuCategory(row) {
      return converseEnToCn(menuCategoryList, row.category)
    },
    /**
     * 获取菜单是否禁用
     */
    getMenuValid(row) {
      return converseEnToCn(menuValidList, row.valid)
    }
  }
}
</script>

<style scoped>
</style>
