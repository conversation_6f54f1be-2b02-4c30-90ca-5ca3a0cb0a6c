<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.searchField"
        placeholder="手机号/客户名/校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.clueCode"
        placeholder="客户编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.customerType" placeholder="客户类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in customerTypeList" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
       <el-input
        v-model="listQuery.originName"
        placeholder="信息来源"
        class="filter-item"
        style="width: 200px;"
      />
      <el-select v-model="listQuery.resourceOwner" filterable placeholder="资源所属人" clearable class="filter-item"
                 style="width: 220px;">
        <el-option v-for="item in followLists" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['customer:list:create']" class="filter-item" size="mini" type="success"
                 @click="handleCreate">新增客户
      </el-button>
      <!--      <el-button v-waves v-permission="['customer:list:export']" class="filter-item" type="primary" size="mini" @click="handleDownload">-->
      <!--        导出excel模板-->
      <!--      </el-button>-->
      <el-button
        v-waves
        v-permission="['customer:list:transfer:bulk']"
        class="filter-item"
        type="warning"
        size="mini"
        @click="getTransferDialog"
      >
        批量转让
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="40" align="center" fixed="left" />
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="客户编号" show-overflow-tooltip width="80">
        <template slot-scope="{ row }">
          <router-link :to="{ path: '/customer/detail/'+row.id, query: {title:'客户-'+row.customer}}"
                       class="link-type">
            <span>{{ row.clueCode }}</span>
          </router-link>
        </template>
      </af-table-column>
      <!--      <af-table-column label="客户类型" show-overflow-tooltip width="80">-->
      <!--        <template slot-scope="{ row }">-->
      <!--          <span v-if="row.type > 0">合伙人</span>-->
      <!--          <span v-if="row.type < 1">&#45;&#45;</span>-->
      <!--        </template>-->
      <!--      </af-table-column>-->
      <el-table-column label="客户名称" prop="customer"  show-overflow-tooltip />
      <af-table-column label="校区名称" prop="institution" show-overflow-tooltip />
      <af-table-column label="所在区域" prop="fullAddress" width="200"  show-overflow-tooltip />
      <el-table-column label="客户类型" prop="customerType" width="200" >
        <template slot-scope="{row}">
          {{
            (customerTypeList.find(item => item.key === row.customerType) || {}).value || '-'
          }}
        </template>
      </el-table-column>
      <af-table-column label="资源所属人" prop="resourceOwnerName" width="100" />
      <af-table-column label="信息来源" prop="originName"  show-overflow-tooltip width="120" />
      <af-table-column label="录入时间" prop="createTime" width="100" :formatter="formatterTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['customer:list:follow']" type="text" size="mini" @click="handleFollow(row)">跟进</el-button>
          <el-button v-permission="['customer:list:deal']" class="ml-10" type="text" size="mini" @click="toDealHandover(row)">
            成交
          </el-button>
          <el-dropdown
            v-permission="['customer:list:update','customer:list:follow','customer:list:transfer']"
            trigger="click"
            @command="handleCommand"
          >
            <el-button type="text" size="mini">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-permission="['customer:list:toPaterner']" :command="beforeHandleCommand('a',row)">
                <span v-show="row.type < 1">转为合伙人</span>
                <span v-show="row.type > 0">取消合伙人</span>
              </el-dropdown-item>
              <el-dropdown-item v-permission="['customer:list:update']" :command="beforeHandleCommand('b',row)">修改
              </el-dropdown-item>
              <el-dropdown-item v-permission="['customer:list:transfer']" :command="beforeHandleCommand('c',row)">转让
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      layout="total, sizes, prev, pager, next"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog width="460px" title="客户转让" :visible.sync="dialogTransfer" :close-on-click-modal="!dialogTransfer">
      <search-employee ref="transfer" :label="'被转让人'" @setEmployeeId="getEmployeeId" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelTransfer">取 消</el-button>
        <el-button type="primary" @click="batchTransfer">确 定</el-button>
      </div>
    </el-dialog>
    <customer-create ref="createOrUpdate" :title="createDialogTitle" :is-edit="editCustomer" @updateList="getList" />
    <follow-list ref="followRef" @refresh="getList" />
  </div>
</template>
<script>
import {
  getCustomerList,
  putCustomerTransfer,
  customerToParterner
} from '@/api/customer'
import { followList } from '@/api/website'
import {
  parseTime
} from '@/utils'
import {
  originList,
  joinStatusList,
  intentionList,
  converseEnToCn,
  customerTypeList
} from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import SearchEmployee from '@/components/search-employee'
import AreaPicker from '@/components/area-picker'
import CustomerCreate from './create'
import FollowList from './componets/followList'
import SchoolJoinStatusTag from '@/components/StatusTag/SchoolJoinStatusTag.vue'
import CustomJoinStatusTag from '@/components/StatusTag/CustomJoinStatusTag.vue'
import CustomerDetailLink from '@/components/link/CustomerDetailLink.vue'
import Collapse from '@/components/collapse/Collapse.vue'

export default {
  name: 'CustomerList',
  components: {
    Collapse,
    CustomerDetailLink,
    CustomJoinStatusTag,
    SchoolJoinStatusTag,
    Pagination,
    SearchEmployee,
    AreaPicker,
    CustomerCreate,
    FollowList
  },
  directives: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      originList: originList,
      joinStatusList: joinStatusList,
      customerTypeList: customerTypeList,
      isPartner: [{
        value: 0,
        label: '否'
      },
        {
          value: 1,
          label: '是'
        }],
      editCustomer: true,
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        originName: undefined,
        searchField: '',
        customerType: undefined,
        clueCode: undefined
      },
      dialogTransfer: false, // 批量转让的弹窗
      multipleSelection: [], // 多选框被选中的row
      multipleSelectCustomerId: [], // 多选框选中的客户id
      beTransferCustomer: '',
      exportSetting: { // 导出按钮
        downloadLoading: false
      },
      createDialogTitle: '新增客户',
      testDialog: false,
      followDate: [],
      followStatusList: [
        {
          id: 1,
          label: '已跟进'
        },
        {
          id: 0,
          label: '未跟进'
        }
      ],
      followLists: []
    }
  },
  computed: {
    $_isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  },
  mounted() {
  },
  created() {
    this.listLoading = false
    this.getList()
    this.getFollow()
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, that.areaList, {
        startFollowDate: that.followDate[0] ? that.followDate[0] : '',
        endFollowDate: that.followDate[1] ? that.followDate[1] : ''
      })
      await getCustomerList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 跳转创建交接单页面
     * type:
     * create: 创建交接单
     * update: 修改交接单
     * renew: 续约
     * upgrade： 升级交接单
     * */
    toDealHandover(row) {
      this.$router.push({
        name: 'HandoverCreate',
        query: {
          type: 'create',
          name: row.customer
        },
        params: {
          clueId: row.id
        }
      })
    },
    /**
     * 更多按钮
     * */
    handleCommand(command) {
      switch (command.type) {
        case 'a':
          // 修改客户
          this.toParterner(command.row)
          break
        case 'c':
          // 转让
          this.getTransferDialog(command.row)
          break
        case 'b':
          // 修改
          this.handleUpdate(command.row)
          break
        case 'd':
        // // 添加校区
        // this.addSchool(command.row)
      }
    },
    beforeHandleCommand(type, row) {
      return {
        'type': type,
        'row': row
      }
    },
    /**
     * 默认客户转为合伙人
     */
    toParterner(row) {
      const that = this
      const title = row.type > 0 ? '是否确认取消合伙人?' : '是否确认转为合伙人?'
      const tipes = row.type > 0 ? '取消成功' : '设置成功'
      that.$confirm(`${ title }`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        customerToParterner(row.id).then(res => {
          if (res.code === '000000') {
            that.$message({
              type: 'success',
              message: `${ tipes }`
            })
            that.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    /**
     * 添加校区
     * */
    // addSchool() {
    //
    // },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
     * 格式化时间
     */
    formatterTime(row) {
      return parseTime(row.createTime, '{y}-{m}-{d}')
    },
    /**
     * 转换加盟状态
     */
    getJoinStatusCN(row) {
      return converseEnToCn(this.joinStatusList, row.joinStatus)
    },
    /**
     * 转换意向度key与value
     */
    getIntention(row) {
      return converseEnToCn(intentionList, row.clueType)
    },
    // 重置
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.followDate = []
      this.getList()
    },
    sortChange(data) {
    },
    handleCreate() {
      this.createDialogTitle = '新增客户'
      this.editCustomer = false
      this.$refs.createOrUpdate.openDialog()
    },
    handleUpdate(row) {
      this.createDialogTitle = '修改客户信息'
      this.editCustomer = true
      this.$refs.createOrUpdate.updateInfo(row.id)
    },
    handleFollow(row) {
      this.$refs.followRef.getLists(row.id)
    },
    /**
     * 批量转让的弹窗
     * */
    getTransferDialog(row) {
      if (row.id) {
        this.multipleSelectCustomerId = [row.id]
      }
      if (this.multipleSelectCustomerId && this.multipleSelectCustomerId.length > 0) {
        this.dialogTransfer = true
      }
      else {
        this.$message({
          message: '请先选择被转让人 ',
          type: 'warning'
        })
      }
    },
    /**
     * 获取搜索筛选之后的用户id
     **/
    getEmployeeId(id) {
      this.beTransferCustomer = id
    },
    /**
     * 批量转让线索、客户
     * @param row
     */
    batchTransfer(row) {
      if (!this.beTransferCustomer) {
        this.$message({
          message: '请先选择被转让人',
          type: 'warning'
        })
        return
      }
      const params = {
        clueIds: this.multipleSelectCustomerId,
        userId: this.beTransferCustomer
      }
      putCustomerTransfer(params).then(res => {
        this.$message({
          message: '转让成功',
          type: 'success'
        })
        this.dialogTransfer = false
        this.getList()
        this.$refs.transfer.transferForm.employeeSearchField = ''
      })
    },
    updateData() {
    },
    handleDelete(row) {
    },
    handleFetchPv(pv) {
    },
    handleDownload() {
      this.exportSetting.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['客户编号', '客户名称', '客户手机号', '校区名称', '所在区域', '加盟状态', '信息来源', '下次跟进时间', '意向度', '跟进次数',
          '资源所属人', '录入时间'
        ]
        const filterVal = ['clueCode', 'customer', 'mobile', 'institution', 'fullAddress', 'joinStatus',
          'originName', 'nextFollowDate', 'clueType', 'followUpNum', 'resourceOwnerName', 'createTime'
        ]
        let list
        this.getList().then(res => {
          list = this.list
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: '客户列表', // 文件名
            autoWidth: true, // 是否自动自适应宽度
            bookType: 'xlsx' // 导出的文件类型.xls
          })
          this.exportSetting.downloadLoading = false
        })
      })
    },
    /**
     * 多选框的返回值
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.multipleSelectCustomerId = this.multipleSelection.map(item => {
        return item.id
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'joinStatus') {
          return converseEnToCn(this.joinStatusList, v[j])
        }
        else {
          return v[j] || 0
        }
      }))
    },
    cancelTransfer() {
      this.dialogTransfer = false
      this.$refs.transfer.transferForm.employeeSearchField = ''
    },
    getFollow() {
      followList().then(res => {
        const arrs = []
        if (res.code === '000000') {
          res.data.length > 0 ? res.data.forEach(item => {
            const objs = {
              id: item.id,
              title: `${ item.realName }(${ item.mobile })`
            }
            arrs.push(objs)
          }) : []
          this.followLists = arrs
        }
      }).catch(error => {

      })
    }
  }
}
</script>
