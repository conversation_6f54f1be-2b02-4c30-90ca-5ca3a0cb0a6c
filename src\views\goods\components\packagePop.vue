<template>
  <el-dialog :visible.sync="packagePop" :title="packageTitle" :close-on-click-modal="!packagePop" width="60%" @close="cancelClass">
    <div class="assing-info">
      <el-form ref="packageForm" :model="listQuery" :rules="rules" label-width="150px">
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="listQuery.productName" placeholder="请输入商品名称" maxlength="50" :disabled="isEdit" @input="change" />
        </el-form-item>
        <el-form-item label="产品线" prop="clientCode">
          <el-select v-model="listQuery.clientCode" placeholder="请选择产品线" filterable :disabled="isEdit" @change="getAllClassAvailable">
            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="计费规则" prop="productTypeId">
          <el-radio-group v-model="listQuery.productTypeId" :disabled="isEdit" @change="getProductType">
            <el-radio :label="1">流量包</el-radio>
            <el-radio :label="5">单一科目</el-radio>
            <el-radio :label="2">班型打包</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="listQuery.productTypeId===2" label="商品定价方式" prop="pricingType" required>
          <el-radio-group v-model="listQuery.pricingType" :disabled="isEdit">
            <el-radio :label="1">班型定价</el-radio>
            <el-radio :label="2">科目定价</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="listQuery.productTypeId===1">
          <el-form-item label="范围类型" prop="rangeType" required>
            <el-radio-group v-model="listQuery.rangeType" :disabled="isEdit||isRangeType">
              <el-radio :label="1">班型</el-radio>
              <el-radio :label="2">科目</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="listQuery.rangeType===1">
            <el-checkbox-group v-model="targetIds" :disabled="isEdit">
              <el-checkbox v-for="(item,index) in allClassType" :key="index" :label="item.id">{{ item.title }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="listQuery.rangeType===2">
            <el-checkbox-group v-model="targetIds" :disabled="isEdit">
              <el-checkbox v-for="item in subjectsAll" :key="item.id" :label="item.id">{{ item.title }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="开通首次购买" class="buy">
            <el-checkbox v-model="listQuery.firstBuyDemand" />
            <el-input v-model="listQuery.firstBuyCount" placeholder="请输入购买数量" maxlength="20" :disabled="isEdit||listQuery.firstBuyDemand===false" style="width:92%" @input="change" />
            <em v-if="listQuery.firstBuyDemand===true" class="tips">小时</em>
          </el-form-item>
          <el-form-item label="计费优先排序" prop="sort">
            <el-input v-model.number="listQuery.sort" placeholder="请输入计费优先排序" :disabled="isEdit" @input="change" />
          </el-form-item>
        </div>
        <el-form-item v-if="listQuery.productTypeId===5||listQuery.productTypeId===2" label="班型" prop="classTypeId">
          <el-select v-model="listQuery.classTypeId" placeholder="请选择班型名称" filterable clearable class="filter-item" :disabled="isEdit" @change="getClassList">
            <el-option v-for="(item,index) in allClassType" :key="index" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="listQuery.productTypeId===5" label="科目" prop="subjectId">
          <el-select v-model="listQuery.subjectId" placeholder="请选择科目" filterable :disabled="isEdit">
            <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="listQuery.productTypeId===1" label="单价（元/小时）" required>
          <el-input v-model="listQuery.unitPrice" placeholder="请输入单价" maxlength="20" :disabled="isEdit" @input="change" />
        </el-form-item>
        <el-form-item label="过期时间" prop="expireTime">
          <el-date-picker
            v-model="listQuery.expireTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="过期时间"
            :disabled="isEdit"
            :picker-options="pickerOptions"
            @click="change"
          />
          <em class="require-tips">设置有效期2099-12-31 视为永不过期</em>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="listQuery.status" :disabled="isEdit">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">已上架</el-radio>
            <el-radio :label="2">已下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="packagePop=false,cancelClass()">取消</el-button>
      <!--      新增-->
      <el-button v-if="flags===1" type="primary" size="mini" @click="custormClass">确定</el-button>
      <!--      修改-->
      <el-button v-if="flags===0" type="primary" size="mini" @click="editPackage">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { clientCode, classAvailable, getAllSubjects } from '@/api/classType'
import { getPackage, addPackage, getProDetail, editPackageInfo } from '@/api/goods'
export default {
  name: 'PackagePop',
  data() {
    return {
      packageTitle: '',
      packagePop: false,
      listQuery: {
        productTypeId: 1,
        rangeType: 1,
        firstBuyDemand: false
      },
      targetIds: [],
      rules: {
        productName: { required: true, trigger: 'blur', message: '请输入商品名称' },
        clientCode: { required: true, trigger: 'blur', message: '请选择产品线' },
        productTypeId: { required: true, trigger: 'blur', message: '请选择计费规则' },
        status: { required: true, trigger: 'blur', message: '请输入状态' },
        sort: { required: true, trigger: 'blur', message: '请输入计费优先排序' },
        rangeType: { required: true, trigger: 'blur', message: '请选择范围类型' },
        expireTime: { required: true, trigger: 'blur', message: '请选择过期时间' }
      },
      clientCode: [],
      flags: -1, // 新增1 修改0
      isEdit: false,
      packageList: [],
      allClassType: [],
      subjectsAll: [],
      availableClassCode: '',
      subjectId: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      isRangeType: false
    }
  },
  watch: {
    'listQuery.clientCode': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.listQuery.classTypeId = ''
        }
      }
    },
    'listQuery.classTypeId': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) {
          this.listQuery.defaultTraffic = ''
        }
      }
    },
    allClassType: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.listQuery.classTypeId = ''
        }
      }
    },
    packageList: {
      deep: true,
      handler(newVal) {
        if (newVal.length === 0) {
          this.listQuery.defaultTraffic = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getCode()
      this.getAllClassType()
      this.getAllSubjects()
    })
  },
  methods: {
    getCode() {
      clientCode().then(res => {
        this.clientCode = res.data.filter(item => item.level === 1) || []
      })
    },
    getAllClassAvailable(val) {
      this.availableClassCode = val
      this.getAllClassType(val)
    },
    getAllClassType(val) { // 根据clientCode查询所有可用班型
      if (val) {
        classAvailable(val).then(res => {
          this.allClassType = res.data || []
        })
      } else {

      }
    },
    getAllSubjects() { // 有效科目
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data || []
        }
      })
    },
    getPackage(classTypeId) {
      getPackage(classTypeId).then(res => {
        if (res.code === '000000') {
          const arr = []
          for (let i = 0; i < res.data.length; i++) {
            const obj = {}
            obj.id = res.data[i].id
            obj.title = `${res.data[i].unitPrice}元,${res.data[i].productName}`
            arr.push(obj)
          }
          this.packageList = arr || []
        }
      }).catch(() => {

      })
    },
    getProDetail(ids) {
      getProDetail(ids).then(res => {
        if (res.code === '000000') {
          this.listQuery = res.data
          this.listQuery.firstBuyDemand = res.data.firstBuyDemand === 1
          this.listQuery.rangeType = res.data.rangeType ? res.data.rangeType : 1
          this.targetIds = res.data.targetIds || []
          this.getAllClassType(res.data.clientCode)
          this.isRangeType = this.listQuery.productTypeId === 1
          if ((this.listQuery.productTypeId === 5 || this.listQuery.productTypeId === 2) && this.listQuery.clientCode !== 400 && this.listQuery.clientCode !== 500) {
            this.getPackage(this.listQuery.classTypeId)
          }
        }
      })
    },
    getClassList(val) {
      this.subjectId = val
      if (!this.listQuery.clientCode) {
        this.$message({
          type: 'warning',
          message: '请先选中产品线'
        })
      }
      this.getPackage(val)
    },
    cancelClass() {
      this.listQuery.productTypeId = 1
      this.isRangeType = false
      this.listQuery.rangeType = 1
      this.listQuery.firstBuyDemand = false
      this.listQuery.firstBuyCount = ''
      this.listQuery.productName = ''
      this.listQuery.clientCode = ''
      this.listQuery.sort = ''
      this.listQuery.classTypeId = ''
      this.listQuery.subjectId = ''
      this.listQuery.defaultTraffic = ''
      this.listQuery.unitPrice = ''
      this.listQuery.expireTime = ''
      this.listQuery.pricingType = ''
      this.listQuery.status = ''
      this.targetIds = []
      if (this.$refs.packageForm) {
        this.$refs.packageForm.clearValidate()
      }
    },
    custormClass() {
      this.$refs.packageForm.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.listQuery, { id: this.listQuery.id ? this.listQuery.id : '', targetIds: this.targetIds, firstBuyDemand: this.listQuery.firstBuyDemand ? 1 : 0, firstBuyCount: this.listQuery.firstBuyDemand ? Number(this.listQuery.firstBuyCount) : '' })
          if (this.listQuery.productTypeId === 1 && this.listQuery.rangeType && this.targetIds.length === 0) {
            this.$message({
              type: 'warning',
              message: '请选择范围类型'
            })
            return false
          }
          if ((this.listQuery.productTypeId === 5 || this.listQuery.productTypeId === 2) && this.listQuery.clientCode !== 400 && this.listQuery.clientCode !== 500 && !this.listQuery.classTypeId) {
            this.$message({
              type: 'warning',
              message: '请选择班型'
            })
            return false
          }
          if (this.listQuery.productTypeId === 5 && !this.listQuery.subjectId) {
            this.$message({
              type: 'warning',
              message: '请选择科目'
            })
            return false
          } if (this.listQuery.productTypeId === 2 && !this.listQuery.pricingType) {
            this.$message({
              type: 'warning',
              message: '请选择商品定价方式'
            })
            return false
          } if (this.listQuery.productTypeId === 1 && !this.listQuery.unitPrice) {
            this.$message({
              type: 'warning',
              message: '请输入单价'
            })
            return false
          }
          addPackage(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$emit('addPackageList')
              this.listQuery.productTypeId = 1
              this.listQuery.rangeType = 1
              this.listQuery.firstBuyDemand = false
              this.listQuery.productName = ''
              this.listQuery.clientCode = ''
              this.listQuery.sort = ''
              this.listQuery.classTypeId = ''
              this.listQuery.subjectId = ''
              this.listQuery.defaultTraffic = ''
              this.listQuery.unitPrice = ''
              this.listQuery.expireTime = ''
              this.listQuery.pricingType = ''
              this.listQuery.status = ''
              this.targetIds = []
              this.allClassType = []
              this.packagePop = false
              this.$refs.packageForm.clearValidate()
            }
          }).catch(() => {

          })
        } else {
          return false
        }
      })
    },
    editPackage() {
      this.$refs.packageForm.validate((valid) => {
        if (valid) {

          if (this.listQuery.productTypeId === 1 && this.listQuery.rangeType && this.targetIds.length === 0) {
            this.$message({
              type: 'warning',
              message: '请选择范围类型'
            })
          } else
          if ((this.listQuery.productTypeId === 5 || this.listQuery.productTypeId === 2) && this.listQuery.clientCode !== 400 && this.listQuery.clientCode !== 500 && !this.listQuery.classTypeId) {
            this.$message({
              type: 'warning',
              message: '请选择班型'
            })
          } else
          if (this.listQuery.productTypeId === 5 && !this.listQuery.subjectId) {
            this.$message({
              type: 'warning',
              message: '请选择科目'
            })
          } else if (this.listQuery.productTypeId === 2 && !this.listQuery.pricingType) {
            this.$message({
              type: 'warning',
              message: '请选择商品定价方式'
            })
          } else
          if (this.listQuery.productTypeId === 1 && !this.listQuery.unitPrice) {
            this.$message({
              type: 'warning',
              message: '请输入商品单价'
            })
          } else {
            const classId = this.allClassType.map(item => item.id)
            const targetId = classId.filter(item => this.targetIds.indexOf(item) > -1)
            const targetIdList = targetId.length === 0 ? this.targetIds : targetId
            const params = Object.assign({}, this.listQuery, { id: this.listQuery.id ? this.listQuery.id : '', targetIds: targetIdList, firstBuyDemand: this.listQuery.firstBuyDemand ? 1 : 0, firstBuyCount: this.listQuery.firstBuyDemand ? Number(this.listQuery.firstBuyCount) : '' })
            editPackageInfo(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.$emit('addPackageList')
                this.listQuery.productTypeId = 1
                this.listQuery.rangeType = 1
                this.listQuery.firstBuyDemand = false
                this.listQuery.productName = ''
                this.listQuery.clientCode = ''
                this.listQuery.sort = ''
                this.listQuery.classTypeId = ''
                this.listQuery.subjectId = ''
                this.listQuery.defaultTraffic = ''
                this.listQuery.unitPrice = ''
                this.listQuery.expireTime = ''
                this.listQuery.pricingType = ''
                this.listQuery.status = ''
                this.targetIds = []
                this.packagePop = false
                this.$refs.packageForm.clearValidate()
              }
            }).catch(() => {

            })
          }
        } else {
          return false
        }
      })
    },
    change() {
      this.$forceUpdate()
    },
    getProductType(val) {
      if (val === 2) this.$set(this.listQuery, 'pricingType', 1)
    }
  }
}
</script>

<style scoped lang="scss">
  .buy{
    position: relative;
    .tips{
      position: absolute;
      right: 0;
      top:12%;
    }
  }
  .assign-operas{
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .require-tips{
    font-size: 12px;
    color: red;
  }
</style>
