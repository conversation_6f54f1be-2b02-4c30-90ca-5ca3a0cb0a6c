<template>
  <div class="app-container">
    <div class="app-container bgGrey campus-info">
      <el-form ref="form" size="small" label-width="120px">
        <el-card class="box-card campus-list" shadow="hover">
          <div slot="header" class="clearfix">
            <span>上报人信息</span>
          </div>
          <div class="item">
            <el-row>
              <el-col :sm="24" :md="8">
                <el-form-item label="姓名：">
                  <div>{{ list.realName || "-" }}</div>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item label="部门：">
                  <div>{{ list.deptName || "-" }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-form>

      <el-form ref="form" size="small" label-width="120px">
        <el-card class="box-card campus-list" shadow="hover">
          <div slot="header" class="clearfix">
            <div class="flex-between">
              <span>需求信息 </span>
            </div>
          </div>
          <div class="item">
            <el-row>
              <el-col :sm="24" :md="5">
                <el-form-item label="上报时间：">
                  <span>{{ list.time || "-" }}</span>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="14">
                <el-form-item label="附件：">
                  <div class="images-list">
                    <el-image
                      v-for="(url, index) in list.ossUrl"
                      style="width: 100px; height: 100px"
                      :src="url"
                      :preview-src-list="[url]"
                    >
                    </el-image>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :sm="24" :md="24">
                <el-form-item label="需求描述：">
                  {{ list.content || "-" }}</el-form-item
                >
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-form>
      <el-form ref="form" size="small" label-width="120px">
        <el-card class="box-card campus-list" shadow="hover">
          <div slot="header" class="clearfix">
            <div class="flex-between">
              <span>处理信息 </span>
            </div>
          </div>
          <div class="item">
            <el-row>
              <el-col :sm="24" :md="5">
                <el-form-item label="处理状态：">
                  <div>{{ getStatusName(list.status) }}</div>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="5">
                <el-form-item label="处理反馈：">
                  <span>{{
                    list.status === "2"
                      ? list.completeContent || "-"
                      : list.replyContent || "-"
                  }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-form>
      <div class="dispose-btns" v-if="list.status === '0'">
        <el-button type="primary" @click="openDialog('1')">排期处理</el-button>
        <el-button type="danger" @click="openDialog('2')">不处理</el-button>
      </div>
    </div>
    <RequirementsDialog ref="requirementsDialog"> </RequirementsDialog>
  </div>
</template>
<script>
import RequirementsDialog from "./requirementsDialog.vue";
import { requirementInfo } from "@/api/feedback";
import { requirementsStatusList } from "@/utils/field-conver";

export default {
  name: "RequirementsInfo",
  components: {
    RequirementsDialog
  },
  data() {
    return {
      id: "",
      list: [],
      url:
        "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      srcList: [
        "https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg",
        "https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg"
      ],
      requirementsStatusList
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.query.refresh === "true" || to.query.refresh === true) {
        vm.getDetail(to.query.id); // 用 to.query 拿参数
      }
    });
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getDetail(this.id);
  },
  methods: {
    openDialog(status) {
      this.$refs.requirementsDialog.init(status, this.id);
    },
    getStatusName(status) {
      console.log(
        this.requirementsStatusList,
        status,
        this.requirementsStatusList.find(item => item.key === status)
      );
      if (
        this.requirementsStatusList &&
        this.requirementsStatusList.length &&
        status
      ) {
        return this.requirementsStatusList.find(item => item.key === status)
          .value;
      } else {
        return "";
      }
    },
    getDetail(id) {
      requirementInfo(id)
        .then(res => {
          if (res.code === "000000") {
            this.list = res.data;
          }
        })
        .catch(() => {});
    }
  }
};
</script>
<style scoped lang="scss">
.images-list {
  .el-image {
    margin-right: 15px;
  }
}
.dispose-btns {
  background-color: #fff;
  width: 100%;
  position: fixed;
  padding: 30px 0;
  bottom: 0px;
  display: flex;
  justify-content: center;
}
.el-form {
  /* margin-bottom: 100px; */
  margin-bottom: 10px;
}
</style>
