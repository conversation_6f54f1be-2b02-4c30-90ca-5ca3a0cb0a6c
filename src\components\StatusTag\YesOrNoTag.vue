<template>
  <el-tag :type=" this.getStatus.type" :size="size">{{ this.getStatus.label }}</el-tag>
</template>
<script>
import { yesOrNo } from '@/utils/field-conver'

export default {
  name: 'YesOrNoTag',
  props: {
    status:{
      type: Number,
      default: 0,
      required: true
    },
    size: {
      type: String,
      default: 'medium',
      required: false
    }
  },
  computed: {
    getStatus(){
      return yesOrNo.filter(item => item.value === this.status)[0]
    }
  }
}
</script>
<style scoped>
</style>
