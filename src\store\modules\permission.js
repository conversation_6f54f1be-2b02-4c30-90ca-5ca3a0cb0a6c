import { asyncRoutes, constantRoutes } from '@/router'
import { getUserInfo } from '@/utils/auth'
/**
 * 使用meta.code确定当前用户是否具有权限
 * @param roles
 * @param route
 * some:一真即真
 */
function hasPermission(route, codes) {
  if (route.meta && route.meta.code) {
    const routerCode = route.meta.code.replace(/\s*/g, '')
    return codes.some(item => routerCode === item.code.replace(/\s*/g, '')) || getUserInfo()['userName'] === 'admin'
  } else {
    return true
  }
}
/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
function filterAsyncRoutes(routes, codes) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(tmp, codes)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, codes)
      }
      res.push(tmp)
    }
  })
  return res
}
const state = {
  routes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, menus) {
    return new Promise(resolve => {
      const accessedRoutes = filterAsyncRoutes(asyncRoutes, menus)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
