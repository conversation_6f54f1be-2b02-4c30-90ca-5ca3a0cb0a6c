<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form class="queryForm" ref="formRef" :model="queryParam" :rules="formRules" inline>
        <el-form-item label="项目类型" prop="clientCode" required>
          <el-select v-model="queryParam.clientCode" placeholder="请选择项目类型" filterable>
            <el-option v-for="item in clientCodeList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="适用客户端" prop="suitType">
          <el-select v-model="queryParam.suitType" placeholder="请选择适用客户端" filterable>
            <el-option label="PC" :value="1" />
            <el-option label="校区助手小程序" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-waves class="filter-item" size="medium" type="primary" @click="getList">
            查询
          </el-button>
          <el-button v-waves v-permission="['menu:institutions:add']" size="medium" class="filter-item" type="primary" @click="handleAdd">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="listLoading"
      :data="menuList"
      border
      fit
      stripe
      highlight-current-row
      row-key="id"
      :tree-props="{children: 'subMenus'}"
      :default-sort="{prop: 'sort'}"
    >
      <af-table-column label="菜单名称" prop="name" width="300"/>
      <af-table-column label="项目类型" prop="clientName" />
      <af-table-column label="前端CODE" prop="code" />
      <af-table-column label="菜单层级" prop="menuType">
        <template slot-scope="{row}">
          <span v-if="row.menuType===1">菜单</span>
          <span v-if="row.menuType===2">按钮</span>
        </template>
      </af-table-column>
      <af-table-column label="分校是否适用" prop="allowBranch">
        <template slot-scope="scope">
          <span v-if="scope.row.allowBranch===1">适用</span>
          <span v-if="scope.row.allowBranch===0">不适用</span>
        </template>
      </af-table-column>
      <af-table-column label="是否开通" prop="status">
        <template slot-scope="scope">
          <span v-if="scope.row.status===1">开通</span>
          <span v-if="scope.row.status===99">关闭</span>
        </template>
      </af-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="250">
        <template slot-scope="{row}">
          <el-button v-permission="['menu:institutions:edit']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['menu:institutions:opera']" type="primary" size="mini" @click="handleMenuEnable(row)">
            <span v-if="row.status === 1">关闭</span>
            <span v-if="row.status === 99">开通</span>
          </el-button>
          <el-button v-permission="['menu:institutions:edit']" type="primary" size="mini" @click="handleUrlConfig(row)">
            URL配置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <detai-iinstitutions ref="institutions" :institutions-title="title" @refreh="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { institutionsEnable, institutionsList } from '@/api/system-setting'
import DetaiIinstitutions from './detaiIinstitutions'
import {clientCode} from "@/api/classType.js";
export default {
  name: 'Institutions',
  components: { Pagination, DetaiIinstitutions },
  directives: {},
  data() {
    return {
      menuList: [],
      clientCodeList: [],
      listLoading: false,
      total: 0,
      title: '',
      urlConfigVisible:true,
      queryParam: {
        clientCode:"",
        suitType: '',
      },
      formRules: {
        clientCode:  { required: true, message: '请选择项目类型', trigger: 'change' },
        suitType:  { required: true, message: '请选择适用客户端', trigger: 'change' },
      }
    }
  },
  created() {
    clientCode().then(res => {
      this.clientCodeList = (res.data || []).filter(item => (item.code === 100 || item.code === 200));
      this.queryParam.clientCode = 100;  // 初始化查询条件 三陶普高
      this.queryParam.suitType = 1;  // 初始化查询条件 适用客户端
      this.getList()
    })
  },
  methods: {
    handleFilter() {
      this.getList()
    },
    handleAdd() {
      this.title = '新增菜单'
      this.$refs.institutions.institutionsCreateMenu = true
      this.$refs.institutions.getMenusDetail()
    },
    /**
     * 查询列表
     * */
    getList() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.listLoading = true
          institutionsList({
            ...this.queryParam
          }).then(res => {
            this.menuList = res.data;
            this.total = res.data.total
            this.listLoading = false
          })
        }
      })
    },
    /**
     * 修改菜单
     * */
    handleUpdate(row) {
      this.title = '修改菜单'
      this.$refs.institutions.institutionsCreateMenu = true
      this.$refs.institutions.getMenusDetail(row.id)
    },
    /**
     * 菜单启用、禁用
     * */
    handleMenuEnable(row) {
      const msg = row.status === 1 ? '该菜单已被开启，确认是否关闭?' : '该菜单已关闭，是否确认开通？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        institutionsEnable(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
          }
          this.getList()
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    },

    /**
     * url配置弹框
     * @param row
     */
    handleUrlConfig(row){
      this.$router.push({
        path:'/setting/urlConfig',
        query:{
          menuId:row.id
        }
      })
    }
  }
}
</script>

<style scoped>
::v-deep .queryForm{
  display: flex;
  align-items: center;
  .el-form-item{
    margin-bottom: 0;
  }
}
</style>
