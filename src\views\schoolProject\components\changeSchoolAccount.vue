<template>
  <el-dialog
          title="修改校区账号"
          :visible.sync="dialogVisible"
          width="30%"
          :before-close="handleClose">
    <el-row :gutter="10">
      <el-col :span="22">
        <el-form label-width="120px">
          <el-form-item label="新账号：" >
            <el-input v-model="account" placeholder="请输入新账号"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { changeSchoolAccount } from '@/api/schoolCampus'

export default {
  name: 'changeSchoolAccount',
  props: {
    schoolId: {
      type: [String, Number],
      default: '',
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      account: ''
    }
  },
  methods: {
    show() {
      this.dialogVisible = true
    },
    submit() {
      changeSchoolAccount({ schoolId: this.schoolId, account: this.account }).then(res => {
        if (res.code === "000000") {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$emit('ok')
          this.dialogVisible = false
        }
      })
    },
    handleClose() {
      this.account=''
      this.dialogVisible = false
    }
  }
}
</script>
<style scoped>
</style>
