<template>
  <el-tag :type=" this.getStatus.type" :size="size">{{ this.getStatus.label }}</el-tag>
</template>
<script>
import { styleTags } from '@/utils/field-conver'

export default {
  name: 'StyleTag',
  props: {
    val:{
      type: Number,
      default: 0,
      required: true
    },
    size: {
      type: String,
      default: 'medium',
      required: false
    }
  },
  computed: {
    getStatus(){
      return styleTags.filter(item => item.value === this.val)[0]
    }
  }
}
</script>
<style scoped>
</style>
