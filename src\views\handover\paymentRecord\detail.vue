<template>
  <div>
    <el-dialog v-el-drag-dialog title="打款记录详情" :visible.sync="getPaymentRecordDetail" :close-on-click-modal="!getPaymentRecordDetail" class="departmentDialog" width="50%">
      <el-form ref="detailForm" :model="detail" :rules="getRules" label-width="110px">
        <el-row>
          <el-col :lg="{span:12}">
            <el-form-item label="当前打款编号：">
              <div>{{ detail.payRecordCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="订单编号：">
              <div>{{ detail.orderCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="应付金额：">
              <div>{{ detail.orderPayAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="已到账金额：">
              <div>{{ detail.orderRealAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:24}">
            <el-form-item label="已打款编号：">
              <div v-show="detail.relationPayRecords&&detail.relationPayRecords.length>0">
                <a v-for="item in detail.relationPayRecords" :key="item.id" class="codes" @click="getRelationPaymentRecordDetail=true,currentDetail(item)">{{ item.payRecordCode }}</a>
              </div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="财务审核状态：">
              <div>{{ getAuditStatus(detail.auditStatus) }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="打款金额：">
              <div>{{ detail.payAmount }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="签约主体：">
              <div>{{ detail.signPartyName }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="打款方式：">
              <div>{{ detail.payTypeName }}</div>
            </el-form-item>
          </el-col>
          <el-col :lg="{span:12}">
            <el-form-item label="打款类型：" prop="tradeType">
              <el-radio-group disabled v-model="detail.tradeType">
                <el-radio v-for="(item,i) in trades" :key="i" :label="item.id">{{ item.itemName }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="detail.tradeType !== 163 ">
            <el-col :lg="{span:12}" >
              <el-form-item label="打款时间：">
                <div>{{ detail.payTime }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="交易流水号：">
                <div>{{ detail.transactionNo }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="打款状态：" prop="status">
                <el-radio-group v-model="detail.status">
                  <el-radio :label="30">已到账</el-radio>
                  <el-radio :label="40">未到账</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="detail.status===30" :lg="{span:12}">
              <el-form-item label="开票时间：">
                <el-date-picker
                  v-model="detail.invoiceTime"
                  type="datetime"
                  placeholder="开票时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="转款人姓名：" prop="tradeCustomer">
                <el-input v-model="detail.tradeCustomer" type="text" maxlength="20" />
              </el-form-item>
            </el-col>
          </template>

          <el-col :lg="{span:24}">
            <el-form-item label="备注：" prop="remark">
              <el-input v-model="detail.remark" type="textarea" maxlength="255" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="confirmAudit">确 认</el-button>
        <el-button @click="getPaymentRecordDetail = false">关 闭</el-button>
      </div>
      <!--      其他打款信息的弹框-->
      <el-dialog v-el-drag-dialog title="打款记录详情" :visible.sync="getRelationPaymentRecordDetail" :close-on-click-modal="!getRelationPaymentRecordDetail" class="departmentDialog" width="40%" append-to-body>
        <el-form ref="detailForm" :model="currentPay" :rules="detailFormRules" label-width="140px">
          <el-row>
            <el-col :lg="{span:12}">
              <el-form-item label="当前打款编号：">
                <div>{{ currentPay.payRecordCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="订单编号：">
                <div>{{ currentPay.orderCode }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="应付金额：">
                <div>{{ currentPay.orderPayAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="已到账金额：">
                <div>{{ currentPay.orderRealAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:24}">
              <el-form-item label="其他打款编号：">
                <div v-show="currentPay.relationPayRecords">
                  <span v-for="item in currentPay.relationPayRecords" :key="item.id" class="codes-list">{{ item.payRecordCode }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="财务审核状态：">
                <div>{{ getAuditStatus(currentPay.auditStatus) }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="打款金额：">
                <div>{{ currentPay.payAmount }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="签约主体：">
                <div>{{ currentPay.signPartyName }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="打款方式：">
                <div>{{ currentPay.payTypeName }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="打款类型：">
                <el-radio-group v-model="currentPay.tradeType" disabled>
                  <el-radio v-for="(item,i) in trades" :key="i" :label="item.id">{{ item.itemName }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="打款时间：">
                <div>{{ currentPay.payTime }}</div>
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="交易流水号：">
                <div>{{ currentPay.transactionNo }}</div>
              </el-form-item>
            </el-col>
            <!-- <el-col :lg="{span:12}">
              <el-form-item label="打款状态：">
                <el-radio-group v-model="currentPay.status" disabled>
                  <el-radio :label="30">已到账</el-radio>
                  <el-radio :label="40">未到账</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col> -->
            <el-col v-if="currentPay.status===30" :lg="{span:12}">
              <el-form-item label="开票时间：">
                <el-date-picker
                  v-model="currentPay.invoiceTime"
                  type="datetime"
                  placeholder="开票时间"
                  disabled
                  style="width: 220px;"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="{span:12}">
              <el-form-item label="转款人姓名：">
                <el-input v-model="currentPay.tradeCustomer" type="text" disabled />
              </el-form-item>
            </el-col>
            <el-col :lg="{span:24}">
              <el-form-item label="备注：">
                <el-input v-model="currentPay.remark" type="textarea" disabled />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </el-dialog>
  </div>

</template>

<script>
import { confirmPaymentRecord, getOrderPaymentRecord } from '@/api/payment'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { converseEnToCn, auditStatus, payMethod } from '@/utils/field-conver'
import {
  getPayType
} from '@/api/common'
import { payAmount } from '@/utils/validate'
export default {
  name: 'PaymentRecordDetail',
  directives: {
    elDragDialog
  },
  props: {},
  data() {
    return {
      getPaymentRecordDetail: false,
      getRelationPaymentRecordDetail: false,
      detail: {},
      currentPay: {},
      detailFormRules: {
        status: [{ required: true, trigger: 'blur', message: '打款状态必选' }],
        remark: [{ required: true, trigger: 'blur', message: '备注必填' }],
        tradeType: [{ required: true, trigger: 'blur', message: '打款类型必选' }],
        tradeCustomer: [{ required: true, trigger: 'blur', message: '转款人姓名必填' }]
      },
      trades: []
    }
  },
  created() {
  },
  mounted() {
    this.tradeList('trade_type')
  },
  computed: {
    getRules(){
      if(this.detail.tradeType === 163){
        return {
          remark: { required: true, message: '后付款订单必须输入备注 ', trigger: 'blur' },
        }
      }else{
        return {
          status: [{ required: true, trigger: 'blur', message: '打款状态必选' }],
          remark: [{ required: true, trigger: 'blur', message: '备注必填' }],
          tradeType: [{ required: true, trigger: 'blur', message: '打款类型必选' }],
          tradeCustomer: [{ required: true, trigger: 'blur', message: '转款人姓名必填' }]
        }
      }
    }
  },
  methods: {
    getDetail(data) {
      const that = this
      const params = {
        id: data.id
      }
      getOrderPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          that.detail = res.data
          that.detail.relationPayRecords = res.data.relationPayRecords || []
          that.getPaymentRecordDetail = true
        }
      })
    },
    currentDetail(data) {
      const that = this
      const params = {
        id: data.id
      }
      getOrderPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          that.currentPay = res.data
          that.currentPay.relationPayRecords = res.data.relationPayRecords || []
          that.getPaymentRecordDetail = true
        }
      })
    },
    tradeList(str) { // 打款类型
      const that = this
      getPayType(str).then(res => {
        that.trades = res.data && res.data.length > 0 ? res.data.filter(item => item.itemValue !== -1) : []
      })
    },
    /**
       * 确认修改信息
       */
    confirmAudit() {
      const that = this

      that.$refs.detailForm.validate((valid) => {
        if (valid) {
          // if (that.detail.status === 30 && !that.detail.invoiceTime) {
          //   that.$message({
          //     type: 'warning',
          //     message: '请选择开票时间'
          //   })
          //   return
          // }
          if(that.detail.tradeType === 163){
            that.detail.status = 30
          }

          const params = Object.assign({}, { payId: that.detail.id, status: that.detail.status, remark: that.detail.remark, invoiceTime: that.detail.status === 30 ? that.detail.invoiceTime : '', tradeCustomer: that.detail.tradeCustomer, tradeType: that.detail.tradeType })
          confirmPaymentRecord(params).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '审核成功!'
              })
              that.getPaymentRecordDetail = false
              /**
                 * 通知父组件更新
                 */
              this.$emit('refresh')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    getAuditStatus(status) {
      return converseEnToCn(auditStatus, status)
    },
    getPayMethod(status) {
      return converseEnToCn(payMethod, status)
    }
    // getRelationPay(ids) {
    //   const params = {
    //     id: ids
    //   }
    //   getOrderPaymentRecord(params).then(res => {
    //     if (res.result === '0000') {
    //       this.detail = res.data
    //       this.detail.relationPayRecords = res.data.relationPayRecords || []
    //       this.getPaymentRecordDetail = true
    //     }
    //   })
    // }
  }
}
</script>
<style scoped>
  .codes{
    padding-right: 8px;
    font-weight: bold;
    color: #0a76a4;
  }
  .codes-list{
    padding-right:8px;
  }
</style>
