<template>
  <el-dialog :visible.sync="schoolPop" :title="schoolTitle" :close-on-click-modal="!schoolPop" width="50%" @close="cancelClass">
    <div class="assing-info">
      <el-form ref="schoolForm" :model="listQuery" :rules="rules" label-width="100px">
        <el-form-item label="学校名称" prop="name">
          <el-input v-if="flags === 1" type="textarea" :rows="5" v-model="listQuery.name" placeholder="换行可批量添加学校名称"  :disabled="isEdit" />
          <el-input v-else v-model="listQuery.name" placeholder="请输入学校名称" maxlength="30" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="学校省市区" required>
          <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" :is-edit-school="isEdit" @getAreaList="getAreaList" />
        </el-form-item>
        <el-form-item label="学校状态" prop="status">
          <el-radio-group v-model="listQuery.status" :disabled="isEdit">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="99">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="schoolPop=false,cancelClass()">取消</el-button>
      <!--      新增-->
      <el-button v-if="flags===1" type="primary" size="mini" :disabled="enableFlagsCreate" @click="custormClass">确定</el-button>
      <!--      修改-->
      <el-button v-if="flags===0" type="primary" size="mini" :disabled="enableFlagsUpdate" @click="editSchoolDetail">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AreaPicker from '@/components/area-picker'
import { addSchools, getSchoolDetail, editSchools } from '@/api/system-setting'
export default {
  name: 'AddApp',
  components: {
    AreaPicker
  },
  data() {
    return {
      schoolPop: false,
      schoolTitle: '',
      listQuery: {},
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      rules: {
        name: { required: true, trigger: 'blur', message: '请输入学校名称' },
        status: { required: true, trigger: 'blur', message: '请选择学校状态' }
      },
      isEdit: false,
      flags: -1,
      enableFlagsCreate: false,
      enableFlagsUpdate: false
    }
  },
  mounted() {
    this.$nextTick(() => {
    })
  },
  methods: {
    getSchoolDetail(ids) {
      if (ids) {
        const params = {
          id: ids
        }
        getSchoolDetail(params).then(res => {

          if (res.code === '000000') {
            this.listQuery = res.data
            this.areaList = {
              provinceId: Number(res.data.provinceId) || '',
              cityId: Number(res.data.cityId) || '',
              areaId: Number(res.data.areaId) || ''
            }
          }
        })
      }else{
        this.areaList.provinceId = ''
        this.areaList.cityId = ''
        this.areaList.areaId = ''
      }
    },
    cancelClass() { // schoolForm
      this.listQuery = {}
      if (this.$refs.schoolForm) {
        this.$refs.schoolForm.clearValidate()
      }
    },
    custormClass() {
      this.enableFlagsCreate = true
      this.$refs.schoolForm.validate((valid) => {
        if (valid) {
          debugger
          if (this.areaList.provinceId && this.areaList.cityId && this.areaList.areaId) {
            const params = Object.assign({}, this.listQuery, this.areaList)
            params.nameList = params.name.split('\n').filter(item => item.trim() !== '');
            delete params.name;
            addSchools(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '添加成功'
                })
                this.listQuery = {}
                this.listQuery.name = ''
                this.enableFlagsCreate = false
                this.$emit('updateSchool')
              }
            })
          } else if (!this.areaList.provinceId) {
            this.$message({
              type: 'warning',
              message: '请选择省份'
            })
            this.enableFlagsCreate = false
          } else if (!this.areaList.cityId) {
            this.$message({
              type: 'warning',
              message: '请选择城市'
            })
            this.enableFlagsCreate = false
          } else if (!this.areaList.areaId) {
            this.$message({
              type: 'warning',
              message: '请选择区县'
            })
            this.enableFlagsCreate = false
          }
        } else {
          this.enableFlagsCreate = false
          return false
        }
      })
    },
    editSchoolDetail() {
      this.enableFlagsUpdate = true
      this.$refs.schoolForm.validate((valid) => {
        if (valid) {
          if (this.areaList.provinceId && this.areaList.cityId && this.areaList.areaId) {
            const params = Object.assign({}, this.listQuery, this.areaList)
            editSchools(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.schoolPop = false
                this.listQuery = {}
                this.areaList = {}
                this.enableFlagsUpdate = false
                this.$emit('updateSchool')
              }
            })
          } else if (!this.areaList.provinceId) {
            this.$message({
              type: 'warning',
              message: '请选择省份'
            })
            this.enableFlagsUpdate = false
          } else if (!this.areaList.cityId) {
            this.$message({
              type: 'warning',
              message: '请选择城市'
            })
            this.enableFlagsUpdate = false
          } else if (!this.areaList.areaId) {
            this.$message({
              type: 'warning',
              message: '请选择区县'
            })
            this.enableFlagsUpdate = false
          }
        } else {
          this.enableFlagsUpdate = false
          return false
        }
      })
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .multipleSelect{
  .el-select__tags{
    .el-select__input{
      padding: 0 !important;
      border:  0 !important;
      margin: 0;
      background-color: transparent;
      margin-right: 10px!important;
      margin-left: 10px!important;
    }
  }
  .el-icon-arrow-up{
    display: none !important;
  }
}
</style>
