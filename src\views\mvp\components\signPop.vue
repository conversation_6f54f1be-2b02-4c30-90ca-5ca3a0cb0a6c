<template>
  <el-dialog title="标记发货" :visible.sync="signShow" :close-on-click-modal="!signShow" width="30%">
    <ul class="sign-list">
      <li>
        <em>订单号:</em>
        <div>
          <span>{{ sign.orderNo }}</span>
        </div>
      </li>
      <li>
        <em>收货校区:</em>
        <div>
          <span>{{ sign.agencyName }}</span>
        </div>
      </li>
      <li>
        <em>收货信息:</em>
        <div>
          <p v-if="sign.deliveryName"><i>收货人:</i><i>{{ sign.deliveryName }}</i></p>
          <p v-if="sign.postAddress"><i>收货地址:</i><i>{{ sign.postAddress }}</i></p>
        </div>
      </li>
      <li v-if="!isBatch">
        <em>货物信息:</em>
        <div>
          <p v-for="(item,index) in goods" :key="index"><i>{{ index+1 }}:</i><i>{{ item.productName }}</i><i>*{{ item.productNum }}</i></p>
        </div>
      </li>
      <li v-if="isBatch">
        <em>批量货物信息:</em>
        <div>
          <p v-for="(item,index) in goods" :key="index"><i>{{ index+1 }}:</i><i>{{ item.productName }}</i><i>*{{ item.productNum }}</i></p>
        </div>
      </li>
      <li>
        <em>物流单号:</em>
        <div>
          <el-input v-model="postNo" placeholder="请输入物流单号" />
        </div>
      </li>
      <li class="sign-btn">
        <el-button type="infor" size="mini" @click="signCancel">取消</el-button>
        <el-button type="primary" size="mini" @click="signBtn">确定</el-button>
      </li>
    </ul>
  </el-dialog>
</template>

<script>
import { markPost, getBatchGoods } from '@/api/mvp.js'
export default {
  props: {
    userOrderIds: {
      type: Array,
      default: () => {
        return []
      }
    },
    isBatch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      signShow: false,
      postNo: '',
      sign: {},
      goods: []
    }
  },
  created() {
    // this.getInit()
  },
  methods: {
    getBatchGoods(orderIds) {
      getBatchGoods(orderIds).then(res => {
        this.goods = res.data || []
      })
    },
    getInit(obj) {
      if (obj) {
        this.sign = obj
      } else {
        this.sign = {}
      }
    },
    // getGoods(arr) {
    //   if (arr.length > 0) {
    //     this.goods = arr
    //   } else {
    //     this.goods = []
    //   }
    // },
    signBtn() {
      if (!this.postNo) {
        this.$message({
          type: 'warning',
          message: '请输入物流单号'
        })
      }
      const params = Object.assign({}, { postNo: this.postNo, userOrderIds: this.userOrderIds })
      if (this.postNo && this.userOrderIds.length > 0) {
        markPost(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '标记成功!'
            })
            this.signShow = false
            this.$emit('updeteLogisticsList')
            this.sign = {}
            this.goods = []
            this.postNo = ''
          }
        }).catch(res => {

          this.signShow = false
          this.sign = {}
          this.goods = []
          this.postNo = ''
        })
      }
    },
    signCancel() {
      this.signShow = false
      this.sign = {}
      this.$emit('updeteLogisticsList')
      this.goods = []
      this.postNo = ''
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
  em,i{
    font-style: normal;
  }
  .sign-list{
    margin: 0;
    padding: 0;
    li{
      display: flex;
      margin-bottom:15px;
      &.sign-btn{
        justify-content: center;
        align-items: center;
        padding: 15px 0;
      }
      p{
        margin-bottom: 10px;
      }
      em{
        font-weight: bold;
        width:30%;
        display: inline-block;
        text-align: right;
        padding-right: 8px;
      }
    }
  }
</style>
<style>
  .el-dialog__body{
    padding: 10px 20px;
  }
</style>
