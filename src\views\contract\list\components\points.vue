<template>
  <div class="app-container">
    <div class="bgGreys">
      <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="100px" :disabled="!isEdit" :rules="txtRules">
        <!--    04陶小桃 -->
        <el-row :gutter="10">
          <el-col :lg="{span:12}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>合伙人信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="客户编号：">
                      <span>{{ detail.contractClue.clueNo }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="客户名称：">
                      <span>{{ detail.contractClue.customer }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="联系电话：">
                      <span>{{ detail.contractClue.mobile }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="校区名称：">
                      <span>{{ detail.contractClue.institution }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="机构区域：">
                      <span>{{ detail.contractClue.provinceName }} | {{ detail.contractClue.cityName }} | {{ detail.contractClue.areaName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="机构地址：">
                      <span>{{ detail.contractClue.address }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          <el-col :lg="{span:12}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>项目所属校区</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="校区编号：">
                      <span>{{ detail.clueSchool.schoolCode }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="校区名称：">
                      <span>{{ detail.clueSchool.schoolName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="签约区域：">
                      <span>{{ detail.clueSchool.provinceName }} | {{ detail.clueSchool.cityName }} | {{ detail.clueSchool.areaName }}
                        | {{ detail.clueSchool.countyName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="详细地址：">
                      <span>{{ detail.clueSchool.address }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="10" />
        <el-row :gutter="10">
          <el-col :lg="{span:24}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>交接单基本信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="订单编号：">
                      <span>{{ detail.contractOrder.orderCode }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="加盟项目：">
                      <span>{{ detail.projectName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="业务类型：">
                      <span>{{ businessType }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="区域类型：">
                      <span>{{ areaSingle }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="应收金额：">
                      <span>{{ detail.contractOrder.payAmount }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="实收金额：">
                      <span>{{ detail.contractOrder.realAmount }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="套餐名称：">
                      <span>{{ detail.contractOrder.policyName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:6}">
                    <el-form-item label="套餐价格：">
                      <span>{{ detail.contractOrder.policyPrice }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="签约开始日期：" label-width="110px">
                      <span>{{ detail.contractOrder.signStartTime }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="签约结束日期：" label-width="110px">
                      <span>{{ detail.contractOrder.signEndTime }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="推荐渠道：">
                      <span>{{ channel }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:9}">
                    <el-form-item label="推荐人：">
                      <span>{{ detail.contractOrder.recName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="备注：">
                      <span>{{ detail.contractOrder.remark }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col v-if="detail.contractSignatory != null" :lg="{span:12}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>签约人信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="姓名：">
                      <span>{{ detail.contractSignatory.userName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="身份证号：">
                      <span>{{ detail.contractSignatory.idCard }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="手机号：">
                      <span>{{ detail.contractSignatory.phone }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          <el-col v-if="detail.contractEnterprise != null" :lg="{span:12}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>企业资质信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="企业名称：">
                      <span>{{ detail.contractEnterprise.enterpriseName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="统一社会信用代码：" label-width="160px">
                      <span>{{ detail.contractEnterprise.creditCode }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="法人代表：">
                      <span>{{ detail.contractEnterprise.enterpriseLegal }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="企业地址：">
                      <span>{{ detail.contractEnterprise.enterpriseAddress }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row v-if="detail.contractSignatory != null" :gutter="22">
          <el-col :lg="{span:24}">
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>合同签约信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同编号：" label-width="120px">
                      <span>{{ detail.contractCode }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同名称：" label-width="120px">
                      <span>{{ detail.contractName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="加盟项目：" label-width="120px">
                      <span>{{ detail.projectName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同类型：" label-width="120px" porp="contractType">
                      <el-radio-group v-model="detail.contractType" class="radios" :disabled="detail.orderStatus!==21">
                        <el-radio :label="1">个人合同</el-radio>
                        <el-radio :label="2">企业合同</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同版本：">
                      <div v-if="detail.versionType === 1">正式</div>
                      <div v-if="detail.versionType === 2">预签</div>
                      <div v-if="detail.versionType === 3">抢分</div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="display: none;">
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="单点/区县：" label-width="120px">
                      <el-radio-group v-model="detail.txtContract.txtwordContractType" class="radios">
                        <el-radio :label="1">单点合同</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同开始日期：" label-width="120px" prop="startTime">
                      <el-date-picker v-model="detail.startTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="合同结束日期：" label-width="120px" prop="endTime">
                      <el-date-picker v-model="detail.endTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="校区地址：" prop="schoolAddress" label-width="120px">
                      <el-input v-model="detail.schoolAddress" maxlength="50" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:24}" label-width="120px">
                    <el-form-item label="备注：" label-width="120px">
                      <el-input v-model="detail.remark" type="textarea" maxlength="255" show-word-limit />
                    </el-form-item>
                  </el-col>
                </el-row>

              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 编辑新版陶小桃 -->
        <el-row :gutter="22">
          <el-col :lg="24">
            <el-card>
              <div slot="header" class="clearfix">
                <span>其他信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="金额/元:" label-width="150px" prop="pointContract.payRMB">
                      <el-input v-model="detail.pointContract.payRMB" :disabled="!isEdit" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="帐号个数/个:" label-width="150px" prop="pointContract.accountNums">
                      <el-input v-model="detail.pointContract.accountNums" :disabled="!isEdit" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isEdit" :span="24">
                    <div class="dialog-footer">
                      <el-button type="primary" @click="confirmEdit(2)">提交</el-button>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getContractDetail, modifyContractDetail, getCreatContract } from '@/api/contract'
// import AreaPicker from '@/components/area-picker'
import { converseEnToCn, getAreaSingle, businessTypeList, channelList, orderStatusList, contractClass, getContractStatus } from '@/utils/field-conver'
import { payRMB, accountNums } from '@/utils/validate.js'
export default {
  name: 'PointsContract',
  props: {
  },
  data() {
    return {
      detail: {
        pointContract: {
          accountNums: 0,
          payRMB: 0 // 01:县区单点校区，02：乡镇单点校区
        }

      },
      txtRules: {
        schoolAddress: { required: true, message: '请输入校区地址', trigger: 'blur' },
        startTime: { required: true, message: ' ', trigger: 'blur' },
        endTime: { required: true, message: ' ', trigger: 'blur' },
        pointContract: {
          payRMB: { required: true, trigger: 'blur', validator: payRMB },
          accountNums: { required: true, trigger: 'blur', validator: accountNums }
        }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false,
      flags: -1
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.flags = this.$route.query.flags
    if (Number(this.$route.query.flags) === 0) { // 0是指从合同列表点击到合同编辑页面，1是指从交接单点击创建合同模板进入到合同编辑页面
      this.getDetail()
    } else if (Number(this.$route.query.flags) === 1) {
      this.getCreatContract()
    }
    const tagsName = this.isEdit ? '抢分-编辑合同' : '抢分-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = res.data
        that.detail.id = res.data.contractId
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        // that.detail.contractType = that.detail.contractEnterprise.enterpriseName ? 2 : 1 // 设置默认的合同类型
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {
          txtwordContractType: 1,
          txtVersion: 1,
          cooperationSchoolLevel: '1',
          cooperationSchoolType: '01' // 01:县区单点校区，02：乡镇单点校区
        }
        that.detail.pointContract = res.data.pointContract || {
          accountNums: 0,
          payRMB: 0
        }
        that.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getCreatContract() {
      const that = this
      const data = this.id
      getCreatContract(data).then(res => {
        that.detail = res.data
        that.detail.id = res.data.contractId
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.contractType = that.detail.contractEnterprise.enterpriseName ? 2 : 1 // 设置默认的合同类型
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.txtContract = res.data.txtContract || {
          txtwordContractType: 1,
          txtVersion: 1,
          cooperationSchoolLevel: '1',
          cooperationSchoolType: '01' // 01:县区单点校区，02：乡镇单点校区
        }
        that.detail.pointContract = res.data.pointContract || {
          accountNums: 0,
          payRMB: 0
        }
        that.detail.contractOrder = res.data.contractOrder || {}
        this.detail.contractClue = res.data.contractClue || {}
        this.detail.clueSchool = res.data.clueSchool || {}
        that.areaSingle = converseEnToCn(getAreaSingle, that.detail.contractOrder.areaSingle)
        that.businessType = converseEnToCn(businessTypeList, that.detail.contractOrder.businessType)
        that.channel = converseEnToCn(channelList, that.detail.contractOrder.channel)
        that.orderStatusList = converseEnToCn(orderStatusList, that.detail.contractOrder.status)
        that.contractClass = converseEnToCn(contractClass, that.detail.contractClass)
        that.getContractStatus = converseEnToCn(getContractStatus, that.detail.status)
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
     * 确认修改信息
     */
    confirmEdit(num) {
      // if (!this.isValid()) {
      //   this.$message({
      //     type: 'error',
      //     message: '其他信息有必填项'
      //   })
      //   return
      // }
      if (!this.detail.contractType) {
        this.$message({
          type: 'warning',
          message: '合同类型必选!'
        })
        return
      }
      const that = this
      const data = Object.assign(that.detail, { operateType: num, pointContractDTO: that.detail.pointContractDTO })

      that.detail.pointContractDTO = that.detail.pointContract
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          modifyContractDetail(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '修改成功!'
              })
              this.$store.dispatch('tagsView/delView', this.$route).then(res => {
                this.$router.go(-1)
              })
            }
          }).catch(() => {

          })
        } else {
          return false
        }
      })
    },
    // 选择合同
    changeContractType(type) {
      this.detail.txtContract.accountNums = null
      this.detail.txtContract.price = null
      this.detail.txtContract.leastAccountNums = null
      if (type === 2) {
        this.detail.txtContract.cooperationSchoolLevel = null
      } else {
        this.detail.txtContract.cooperationSchoolLevel = '1'
      }
    }

  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .el-card .el-form-item{
    margin-bottom: 20px;
  }
</style>
