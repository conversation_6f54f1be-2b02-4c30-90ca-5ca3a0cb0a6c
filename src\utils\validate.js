/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
// export function validUsername(str) {
//   const valid_map = ['admin', 'editor']
//   return valid_map.indexOf(str.trim()) >= 0
// }

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * 手机号的校验
 * @param rule
 * @param value
 * @param callback
 */
export function validPhone(rule, value, callback) {
  const myreg = /^1[3456789]\d{9}$/
  if (value === '') {
    callback(new Error('请输入手机号'))
  } else if (!myreg.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback() // 重点在这  如果在验证通过后不添加callback()函数在验证时是条件会为false
  }
}

/**
 * 社会统一码
 * @param rule
 * @param value
 * @param callback
 */
export function validCreditCode(rule, value, callback) {
  const myreg = /(^[0-9A-GY]{8}[0-9A-Z]{10}$)|(^\d{15}$)/
  if (value === '') {
    callback(new Error('请输入统一社会信用代码'))
  } else if (!myreg.test(value)) {
    callback(new Error('请输入正确的统一社会信用代码'))
  } else {
    callback() // 重点在这  如果在验证通过后不添加callback()函数在验证时是条件会为false
  }
}
/**
 * 办学年限
 * @param rule
 * @param value
 * @param callback
 */
export function maxNumberSchool(rule, value, callback) {
  if (value === '') {
    callback(new Error('请输入办学年限'))
  } else
  if (Number(value) > 999) {
    callback(new Error('请输入小于三位数的数字'))
  } else {
    callback()
  }
}
/**
 * 在职教师数
 * @param rule
 * @param value
 * @param callback
 */
export function teacherNumber(rule, value, callback) {
  if (value === '') {
    callback(new Error('请输入在职教师数'))
  } else
  if (Number(value) > 99999) {
    callback(new Error('请输入小于五位数的数字'))
  } else {
    callback()
  }
}
/**
 * 在校学生数
 * @param rule
 * @param value
 * @param callback
 */
export function studentNumber(rule, value, callback) {
  if (value === '') {
    callback(new Error('请输入在校学生数'))
  } else
  if (Number(value) > 999999) {
    callback(new Error('请输入小于六位数的数字'))
  } else {
    callback()
  }
}

/**
 * 客户名称
 * @param rule
 * @param value
 * @param callback
 */
export function customer(rule, value, callback) {
  const str = /^[\u4e00-\u9fa5()（）a-zA-Z0-9 ]+$/
  if (value === '') {
    callback(new Error('请输入客户名称'))
  } else
  if (!str.test(value)) {
    callback(new Error('请输入正确的客户名称'))
  } else {
    callback()
  }
}

/**
 * 微信号
 * @param rule
 * @param value
 * @param callback
 */
export function weixin(rule, value, callback) {
  const str = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/g
  if (str.test(value)) {
    callback(new Error('请输入正确的微信号'))
  } else {
    callback()
  }
}
/**
 * 邮箱
 * @param rule
 * @param value
 * @param callback
 */
export function mail(rule, value, callback) {
  const reg = /^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,6}$/
  if (value !== '' && value !== null && value !== undefined && !reg.test(value)) {
    callback(new Error('请输入正确的邮箱'))
  } else {
    callback()
  }
}
/**
 * 数字验证
 * @param rule
 * @param value
 * @param callback
 */
export function payRMB(rule, value, callback) {
  const reg = /^\d{1,7}$/
  if (!value) {
    callback(new Error('请输入金额'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入不能大于百万的数字'))
  } else {
    callback()
  }
}
/**
 * 数字验证
 * @param rule
 * @param value
 * @param callback
 */
export function wordsAccountNums(rule, value, callback) {
  const reg = /^\d{1,7}$/
  if (!value) {
    callback(new Error('请输入帐号个数'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入不能大于百万的数字'))
  } else {
    callback()
  }
}
/**
 * 月份验证
 * @param rule
 * @param value
 * @param callback
 */
export function oldContractDelay(rule, value, callback) {
  const reg = /^\d{1,2}$/
  if (!value) {
    callback(new Error('请输入有效期延长月份'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的月份'))
  } else {
    callback()
  }
}
export function oldContractEffective(rule, value, callback) {
  const reg = /^\d{1,2}$/
  if (!value) {
    callback(new Error('请输入原协议有效期'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的月份'))
  } else {
    callback()
  }
}
export function delayContractEffective(rule, value, callback) {
  const reg = /^\d{1,2}$/
  if (!value) {
    callback(new Error('请输入延期协议有效期'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的月份'))
  } else {
    callback()
  }
}
/**
 * 帐号个数
 * @param rule
 * @param value
 * @param callback
 */
export function accountNums(rule, value, callback) {
  const reg = /^\d+$/
  if (!value) {
    callback(new Error('帐号个数'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的帐号个数'))
  } else {
    callback()
  }
}
/**
 * 身份证
 * @param rule
 * @param value
 * @param callback
 */
export function idCard(rule, value, callback) {
  const reg = /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  if (!value) {
    callback(new Error('请输入身份证号'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}
/**
 * 银行卡
 * @param rule
 * @param value
 * @param callback
 */
export function bankNumber(rule, value, callback) {
  const reg = /^(\d{16,19})$/
  if (!value) {
    callback(new Error('请输入银行卡号'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的银行卡号'))
  } else {
    callback()
  }
}
/**
 * 企业名称
 * @param rule
 * @param value
 * @param callback
 */
export function legal(rule, value, callback) {
  const reg = /^[\u4e00-\u9fa5\（\u4e00-\u9fa5）]+$/g
  if (!value) {
    callback(new Error('请输入企业名称'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确企业名称'))
  } else {
    callback()
  }
}
/**
 * 企业法人
 * @param rule
 * @param value
 * @param callback
 */
export function legalPerson(rule, value, callback) {
  const reg = /^[\u4e00-\u9fa5]+$/g
  if (!value) {
    callback(new Error('请输入企业法人名称'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确企业法人名称'))
  } else {
    callback()
  }
}
/**
 * 打款金额
 * @param rule
 * @param value
 * @param callback
 */
export function payAmount(rule, value, callback) {
  value = Math.abs(value)
  const reg = /^[0-9]+([.]{1}[0-9]+){0,1}$/g
  if (value === undefined || value === '') {
    callback(new Error('请输入打款金额'))
  } else if (value && !reg.test(value)) {
    callback(new Error('请输入正确的打款金额'))
  } else {
    callback()
  }
}
