import request from '@/utils/request'
/**
 * 获取活动列表
 * @param data
 */
export function activelist(data) {
  return request({
    url: 'market/introduces/page',
    method: 'POST',
    data: data
  })
}
/**
 * 删除活动
 * @param data
 */
export function activeDel(id) {
  return request({
    url: `market/introduces/${id}`,
    method: 'DELETE'
  })
}
/**
 * 活动上下架
 * @param data
 */
export function activeOpera(id) {
  return request({
    url: `market/introduces/up/${id}`,
    method: 'PUT'
  })
}
/**
 * 活动详情
 * @param data
 */
export function activeDetail(id) {
  return request({
    url: `market/introduces/${id}`,
    method: 'GET'
  })
}
/**
 * 根据系列查询可用班型
 * @param data
 */
export function activeClass(seriesId, activityStartTime, activityEndTime, classType) {
  return request({
    url: `stip/classTypes/list/series/${seriesId}/?activityStartTime=${activityStartTime}&activityEndTime=${activityEndTime}&classType=${classType}`,
    method: 'GET'
  })
}
/**
 * 根据系列查询可用班型
 * @param data
 */
export function activeClassType(seriesId, classType) {
  return request({
    url: `stip/classTypes/list/series/${seriesId}/?classType=${classType || ''}`,
    method: 'GET'
  })
}
/**
 * 根据系列查询可用班型2
 * @param data
 */
export function activeAbleClass(seriesId, stageId) {
  return request({
    url: `stip/classTypes/list/series/${seriesId}/?stageId=${stageId}`,
    method: 'GET'
  })
}
/**
 * 新增活动
 * @param data
 */
export function addMarket(data) {
  return request({
    url: `market/introduces`,
    method: 'POST',
    data: data
  })
}
/**
 * 编辑活动
 * @param data
 */
export function updateMarket(data) {
  return request({
    url: `market/introduces`,
    method: 'PUT',
    data: data
  })
}

/**
 * 新增拼团活动
 * @param data
 */
export function addMarketPin(data) {
  return request({
    url: `market/group`,
    method: 'POST',
    data: data
  })
}
/**
 * 编辑拼团活动
 * @param data
 */
export function updateMarketPin(data) {
  return request({
    url: `market/group`,
    method: 'PUT',
    data: data
  })
}
