<template>
  <el-dialog :visible.sync="faqTitlePop" :title="faqParentTitle" :close-on-click-modal="!faqTitlePop" width="60%" @close="changeInit">
    <div>
      <el-form ref="addChildTitle" :model="faqTitleForm" :rules="rules" class="parent-title">
        <el-form-item label="" prop="title">
          <el-input v-model="faqTitleForm.title" placeholder="二级标题" maxlength="20" />
        </el-form-item>
        <el-form-item label="" prop="sort" class="sort-title">
          <el-input v-model="faqTitleForm.sort" placeholder="排序" maxlength="4" type="number" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="handelCreated">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="assignTab"
        v-loading="listLoading"
        :data="list"
        border
        fit
        stripe
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column type="index" />
        <af-table-column label="二级标题编号" show-overflow-tooltip prop="id" width="150px" />
        <af-table-column label="二级标题" show-overflow-tooltip prop="title" />
        <af-table-column label="排序" prop="sort" show-overflow-tooltip />
        <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
            <el-button type="primary" size="mini" @click="handleDelet(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :visible.sync="faqTitleEdit" :title="parentEditTitle" :close-on-click-modal="!faqTitleEdit" width="60%" append-to-body @close="handleCancel()">
      <el-form ref="editChildTitle" :model="childForm" :rules="rulesChild" label-width="150px">
        <el-form-item label="二级问题分类名称" prop="title">
          <el-input v-model="childForm.title" placeholder="二级标题" maxlength="30" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="childForm.sort" placeholder="排序" maxlength="1000" type="number" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="btnUpdated">确定</el-button>
          <el-button type="infor" size="mini" @click="faqTitleEdit=false,handleCancel()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-dialog>
</template>
<script>
import { levelTwo, addTitle, titleIn, editTitle, delTitle } from '@/api/faq'
export default {
  name: 'ParentTitle',
  data() {
    return {
      faqTitleForm: {},
      faqTitlePop: false,
      faqParentTitle: '',
      faqTitleEdit: false,
      parentEditTitle: '',
      list: [],
      listLoading: false,
      childForm: {},
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入二级标题' },
        sort: { required: true, trigger: 'blur', message: '请输入排序' }
      },
      rulesChild: {
        title: { required: true, trigger: 'blur', message: '请输入二级标题' },
        sort: { required: true, trigger: 'blur', message: '请输入排序' }
      },
      parentId: null
    }
  },
  methods: {
    getList(ids) {
      if (ids) {
        levelTwo(ids).then(res => {
          if (res.code === '000000') {
            this.listLoading = false
            this.list = res.data
          }
        }).catch(() => {

        })
      }
    },
    handelCreated() {
      this.$refs.addChildTitle.validate((valid) => {
        if (valid && this.parentId) {
          const data = Object.assign({}, this.faqTitleForm, { parent: this.parentId, level: 2 })
          addTitle(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '二级标题新增成功'
              })
              this.faqTitleForm = {}
              this.getList(this.parentId)
              this.$emit('refreshTitle')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    btnUpdated() {
      this.$refs.editChildTitle.validate((valid) => {
        if (valid && this.parentId) {
          const data = Object.assign({}, this.childForm, { parent: this.parentId, level: 2 })
          editTitle(data).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '二级标题修改成功'
              })
              this.faqTitleEdit = false
              this.childForm = {}
              this.getList(this.parentId)
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    handleUpdate(row) {
      this.faqTitleEdit = true
      titleIn(row.id).then(res => {
        if (res.code === '000000') {
          this.childForm = res.data
        }
      }).catch(() => {

      })
    },
    handleDelet(row) {
      this.$confirm('是否确认删除该标题?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delTitle(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList(this.parentId)
            this.$emit('refreshTitle')
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    changeInit() {
      if (this.$refs.addChildTitle) {
        this.$refs.addChildTitle.clearValidate()
      }
    },
    handleCancel() {
      if (this.$refs.editChildTitle) {
        this.$refs.editChildTitle.clearValidate()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.parent-title{
  width: 100%;
  display: flex;
  justify-content: flex-end;
  .sort-title{
    margin: 0 15px;
  }
}
</style>
