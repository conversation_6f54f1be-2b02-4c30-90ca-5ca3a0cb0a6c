<template>
  <div class="course-detail">
    <el-row>
      <el-col :sm="{span:24}" :md="{span:10}">
        <el-form ref="questionInfoForm" :model="basis" :rules="rules">
          <div class="basis-info">
            <h2>基础信息</h2>
            <el-row>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="产品线" prop="clientCode">
                  <el-select filterable v-model="basis.clientCode" placeholder="选择产品线" clearable class="filter-item" @change="getQuestionOutline">
                    <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="科目" prop="subjectId">
                  <el-select filterable v-model="basis.subjectId" placeholder="选择科目" clearable class="filter-item" @change="getQuestionOutline">
                    <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="题目类型" prop="type">
                  <el-input v-model="basis.type" disabled placeholder="选择题" />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="大纲" prop="outlineId">
                  <el-select filterable v-model="basis.outlineId" placeholder="大纲" clearable class="filter-item" @change="getQuestionKeynote">
                    <el-option v-for="item in outline" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="考点" prop="keynoteId">
                  <el-select filterable v-model="basis.keynoteId" placeholder="考点" clearable multiple class="filter-item">
                    <el-option v-for="item in keynote" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="questions-info">
            <div class="question-tips">
              <h2>试题问题</h2>
              <a title="点击下载批量导入试题模板" href="试题批量导入格式要求v2.0.beta.docx" target="_blank" download="试题批量导入格式要求v2.0.beta.docx">导入模板下载</a>
            </div>
            <div class="hello">
              <div class="upload">
                <div v-if="imgList.length===0" class="upload_warp">
                  <div class="upload_warp_left" @click="fileClick">
                    <img src="../../../assets/img/upload.png">
                  </div>
                  <div class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
                    或者将文件拖到此处
                  </div>
                </div>
                <div class="upload_warp_text">
                  <i v-if="imgList.length>0">选中{{ imgList.length }}张文件，</i><i v-if="size">共{{ bytesToSize(size) }}</i>
                </div>
                <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
                <div v-show="imgList.length!=0" class="upload_warp_img">
                  <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
                    <div class="upload_warp_img_div_top">
                      <div class="upload_warp_img_div_text">
                        {{ item.file.name }}
                      </div>
                      <img src="../../../assets/img/del.png" class="upload_warp_img_div_del" @click="fileDel(index)">
                    </div>
                    <img :src="item.file.src">
                  </div>
                </div>
              </div>
            </div>
            <div class="basis-info">
              <el-form-item>
                <el-button v-if="showFlag==='upload'" size="mini" type="primary" :disabled="subFlag" @click="addQuestionInfo">上传试题</el-button>
                <el-button size="mini" type="infor" @click="cancelSubmit">取消</el-button>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-col>
      <el-col v-if="previewInfos" :sm="{span:24}" :md="{span:14}">
        <div class="preview-in">
          <h3 class="preview-info-title" style="padding-left:0">
            <span>
              预览内容
            </span>
            <el-button v-if="previewInfos&&dataStatus!==2" size="mini" type="primary" @click="uploadConfirm">确认导入</el-button>
          </h3>
          <h4 v-if="tips" class="tips" style="font-size:16px">{{ tips }}</h4>
          <div id="question-id" class="preview-infos">
            <div v-if="previewInfos" v-html="previewInfos" />
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog v-el-drag-dialog :visible.sync="showProgress" :modal="showModal" :append-to-body="showB" title="试题导入进度" width="40%" @close="closedImport">
      <h2 v-if="showWarning" class="warning">数据导入未结束，请勿关闭进度框！</h2>
      <h3 v-if="progressStatus===1" class="progress-title">导入成功</h3>
      <h3 v-if="progressStatus===2" class="progress-title">正在导入中</h3>
      <h3 v-if="progressStatus===3" class="progress-title">导入失败</h3>
      <h3 v-if="progressStatus===4" class="progress-title">数据不存在</h3>
      <p v-if="progressStatus===3&&reason">{{ reason }}</p>
      <el-progress v-if="progressStatus!==3&&progressStatus!==4" :text-inside="true" :stroke-width="26" :percentage="percentage" />
    </el-dialog>
    <!-- 进度条弹框 -->
  </div>
</template>

<script>
import { getQuestionOutline, getQuestionKeynote, getSubjects, batchAdd, batchImportMissionStatus } from '@/api/admissions'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'CourseUpload',
  inject: ['reload'],
  components: {
  },
  directives: { elDragDialog },
  data() {
    return {
      enableList: [],
      radio: null,
      basis: {
      },
      rules: {
        subjectId: { required: true, trigger: 'change', message: '请选择科目' },
        outlineId: { required: true, trigger: 'change', message: '请选择大纲' },
        clientCode: { required: true, trigger: 'change', message: '请选择产品线' }
      },
      isEdit: false,
      subjectsList: [],
      outline: [],
      keynote: [],
      imgIndex: null,
      questionId: null,
      showFlag: '',
      subFlag: false,
      uuid: '',
      imgList: [],
      size: 0,
      previewInfos: '',
      tips: '',
      showProgress: false,
      progressStatus: null,
      percentage: 0,
      showModal: false,
      showB: false,
      missionId: null,
      showWarning: false,
      dataStatus: null,
      productCodeList: [],
      reason: null
    }
  },
  watch: {
    outline: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.basis.outlineId = ''
          this.basis.keynoteId = ''
        }
      },
      deep: true
    },
    keynote: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.basis.keynoteId = ''
        }
      },
      deep: true
    },
    'basis.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.basis.outlineId = ''
          this.keynote = []
        }
      }
    },
    'basis.outlineId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.basis.keynoteId = ''
        }
      }
    },
    'percentage': {
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal === 1) {
          this.percentage = 100
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {

      this.productCodeList = this.$route.params.productCodeList
      this.isEdit = this.$route.params.isEdit
      this.questionId = this.$route.params.id
      this.showFlag = localStorage.getItem('showFlag')
      const name = localStorage.getItem('questionName')
      this.setTagsViewTitle(name)
      this.getSubjects()
    })
  },
  methods: {
    setTagsViewTitle(name) {
      const cRoute = Object.assign({}, this.$route)
      const title = '试题'
      const route = Object.assign({}, cRoute, { title: `${name}-${title}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getQuestionOutline(val) { // 大纲
      if (val) {
        getQuestionOutline(val).then(res => {
          if (res.code === '000000') {
            this.outline = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    getQuestionKeynote(val) { // 考点
      if (val) {
        getQuestionKeynote(val).then(res => {
          if (res.code === '000000') {
            this.keynote = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择大纲'
        })
      }
    },
    addQuestionInfo() { // 新增试题folders
      const that = this
      if (!that.basis.clientCode) {
        that.$message({
          message: '请选择产品线',
          type: 'error'
        })
        return false
      }
      if (!that.basis.subjectId) {
        that.$message({
          message: '请选择科目',
          type: 'error'
        })
        return false
      }
      if (!that.basis.outlineId) {
        that.$message({
          message: '请选择大纲',
          type: 'error'
        })
        return false
      }

      var formData = new FormData()
      formData.append('wordFile', that.imgList[0].file)
      formData.append('keynoteId', that.basis.keynoteId)
      formData.append('outlineId', that.basis.outlineId)
      formData.append('subjectId', that.basis.subjectId)
      formData.append('clientCode', that.basis.clientCode)

      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      batchAdd(formData).then(res => {
        if (res.code === '000000') {
          that.tips = res.msg ? res.msg : ''
          that.previewInfos = res.data.preview ? res.data.preview : ''
          that.missionId = res.data.missionId
          that.dataStatus = res.data.status
          // if (that.dataStatus !== 2 && res.data.missionId) {
          //   that.uploadConfirm()
          // }
          setTimeout(() => {
            loading.close()
          }, 100)
          that.$nextTick(() => {
            if (that.commonsVariable.isMathjaxConfig) {
              that.commonsVariable.initMathjaxConfig()
            }
            that.commonsVariable.MathQueue('question-id')
          })
        }
      }).catch((error) => {

        this.tips = error.msg ? error.msg : ''
        setTimeout(() => {
          loading.close()
        }, 500)
      })
    },
    uploadConfirm() {
      const that = this
      if (that.missionId) {
        const interval = setInterval(() => {
          batchImportMissionStatus(that.missionId).then(resources => { // progressStatus
            if (resources.code === '000000') {
              setTimeout(() => {
                that.showProgress = true
              }, 500)
              that.progressStatus = resources.data.status
              that.reason = resources.data.reason
              that.percentage = resources.data.process && resources.data.total ? Math.round(resources.data.process / resources.data.total * 100) : 0
            }
          }).catch(() => {

          })
          if (that.progressStatus === 1) { // 1-导入成功
            clearInterval(interval)
            setTimeout(() => {
              that.showProgress = false
              that.closeIt()
            }, 5000)
          } else if (that.progressStatus === 3) { // 3-导入失败
            clearInterval(interval)
          }
        }, 500)
      }
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 100)
      })
    },
    cancelSubmit() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
      })
    },
    closedImport() {
      if (this.progressStatus === 1) {
        this.showProgress = false
        this.showWarning = false
      } else if (this.progressStatus === 3 || this.progressStatus === 4) {
        this.showProgress = false
        this.showWarning = false
      }
    },
    // 拖拽上传文档
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {

      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        this.fileAdd(files[i])
        // 判断是否为文件夹
        // if (files[i].type !== '') {
        //   this.fileAdd(files[i])
        // } else {
        //   // 文件夹处理
        //   this.folders(fileList.items[i])
        // }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = require('./word.png')

        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(index) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      this.imgList.splice(index, 1)
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    }
  }
}
</script>

<style scoped lang="scss">
  .course-detail{
    position: relative;
  }
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 130px;
    color: #999;
  }

  .upload_warp_left img {
    margin-top: 32px;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
  }

  .upload_warp {
    margin: 14px;
    height: 130px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
  .progress-pop{
    position: absolute;
    right: 10px;
    bottom: 20%;
    width: 500px;
    height: 100px;
    border-radius: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  .progress-title{
    padding-bottom: 8px;
  }
  .warning{
    color: red;
  }
  .question-tips{
    display: flex;
    justify-content: space-between;
    a{
      color: #1890ff;
      font-size:14px;
    }
  }
</style>

<style>
  .el-form-item__label{
    font-weight: normal;
    color: #67727c;
  }
</style>
