<template>
  <div class="app-container">
    <div class="bgGreys">
      <el-row :gutter="10">
        <!--      合伙人信息-->
        <el-col :lg="{span:24}">
          <el-form
                  ref="customerInfo"
                  size="small"
                  :model="customerInfo"
                  label-width="100px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>合伙人信息</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:8}" :md="{span:6}">
                    <el-form-item label="客户编码：">
                      <div>{{ customerInfo.clueCode }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:8}" :md="{span:8}">
                    <el-form-item label="客户名称：">
                      <div>{{ customerInfo.customer }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:8}" :md="{span:4}">
                    <el-form-item label="手机号码：">
                      <div>{{ customerInfo.mobile }}</div>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :xs="{span:24}" :sm="{span:12}" :md="{span:6}">
                    <el-form-item label="所在区域：">
                      <div>{{ customerInfo.provinceName }} | {{ customerInfo.cityName }} | {{ customerInfo.areaName }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}" :md="{span:24}">
                    <el-form-item label="机构地址：">
                      <div>{{ customerInfo.address }}</div>
                    </el-form-item>
                  </el-col> -->
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <!--      交接单信息-->
        <el-col :lg="{span:13}">
          <el-form
                  ref="orderBaseInfo"
                  size="small"
                  :model="orderBaseInfo"
                  :rules="orderBaseInfoRules"
                  label-width="140px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>交接单信息</span>
              </div>
              <div class="item">
                <el-row style="margin-bottom: 0">
                  <!--                  项目类型： 1 普高 2 艺考 3烨晨 4陶小桃 5AI 6 加推-->
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="加盟项目：" prop="projectNameValid">
                      <el-select v-model="joinProject.id" class="filter-item" style="width: 100%"
                                 :disabled="optionsType === 'upgrade'||optionsType === 'renew'||optionsType === 'editData' || (optionsType === 'update' && (orderBaseInfo.businessType === 2 ||  orderBaseInfo.businessType === 3))"
                                 @change="getProductOfProject">
                        <el-option
                                v-for="item in comProjectList"
                                :key="item.id"
                                :label="item.projectName"
                                :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isthreeThousandPlanets"></el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}" v-if="!isthreeThousandPlanets">
                    <el-form-item label="合同版本：" required>
                      <el-select v-model="orderBaseInfo.versionType" :disabled="optionsType === 'editData'"
                                 @change="changeVersion">
                        <el-option v-for="item in versionTypes" :key="item.id" :label="item.label" :value="item.id"
                                   :disabled="item.disabled" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item  label-width="40px" label="" required class="no-label-item">
                        <el-checkbox v-if="isShowContract" :disabled="optionsType === 'editData'" v-model="orderBaseInfo.businessVersion" :label="1">初高中合同</el-checkbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-show="true" :xs="{span:24}" :sm="{span:12}" v-if="!isthreeThousandPlanets">
                    <el-form-item label="签约主体：" required>
                      <div style="display: flex;">
                        <el-select v-model="orderBaseInfo.signPartyId" :placeholder="singPartyStr" :disabled="onlyEditProductInformation" filterable class="filter-item">
                          <el-option
                                  v-for="item in listSignParty"
                                  :key="item.id"
                                  :label="item.companyName"
                                  :value="item.id"
                          />
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col  v-show="true" :xs="{span:24}" :sm="{span:12}" v-if="(!orderBaseInfo.id ||(orderBaseInfo.id&&orderBaseInfo.signPartyId)) && !isthreeThousandPlanets">
                      <el-popover
                              placement="bottom-start"
                              width="auto"
                              trigger="click">
                        <el-button size="mini"  slot="reference" type="text" plain>签约主体地区对照表</el-button>
                        <CityContractSignView v-if="filterStr" :province="filterStr" />
                      </el-popover>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:24}" v-if="!isthreeThousandPlanets">
                    <el-form-item label="区域类型：" required>
                      <el-radio-group v-model="orderBaseInfo.areaSingle"
                                      :disabled="orderBaseInfo.versionType === 2||orderBaseInfo.versionType===3||optionsType === 'editData'"
                                      @change="changeAreaSingle">
                        <el-radio :label="1" :disabled="joinProject.id===3&&orderBaseInfo.versionType===4">区县独家</el-radio>
                        <el-radio :label="0">区县单点</el-radio>
                        <el-radio :label="2" :disabled="joinProject.id===3&&orderBaseInfo.versionType===4">乡镇独家</el-radio>
                        <el-radio :label="3">乡镇单点</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="应付金额：" required>
                      <el-input v-model="orderBaseInfo.payAmount" :disabled="optionsType === 'editData'"
                                @input="updateView($event)">
                        <el-button slot="append" type="primary" @click="getPolicy">选择套餐</el-button>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-bottom: 0">
                  <el-col v-if="policyName" :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="套餐名称：">
                      <el-input v-model="policyName" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="policyAmount||policyAmount===0" :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="套餐金额：">
                      <el-input v-model="policyAmount" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <div v-if="!isthreeThousandPlanets">
                    <el-col :xs="{span:24}" :sm="{span:12}">
                      <el-form-item label="校区编码：" required>
                        <el-input v-model="schoolInfo.schoolCode" placeholder="请选择校区" disabled>
                          <el-button v-if="!schoolId && !(optionsType === 'update' && (orderBaseInfo.businessType === 2 || orderBaseInfo.businessType === 3))"
 slot="append" type="primary" @click="getCustomerSchool">选择校区</el-button>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="{span:24}" :sm="{span:12}">
                      <el-form-item label="校区名称：">
                        <div>{{ schoolInfo.schoolName || '请先选择校区' }}</div>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="{span:24}" :sm="{span:24}">
                      <el-form-item label="签约区域：">
                        <div v-if="schoolInfo.provinceName">{{ schoolInfo.provinceName }} | {{ schoolInfo.cityName }}
                          |{{ schoolInfo.areaName }}| {{ schoolInfo.countyName }}
                        </div>
                        <div v-if="!schoolInfo.provinceName">请先选择校区</div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="签约详细地址：">
                        <div>{{ schoolInfo.address || '请先选择校区' }}</div>
                        <!--                    <el-input placeholder="请选择校区" v-model="schoolInfo.schoolName" disabled />-->
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item v-if="orderBaseInfo.versionType!==4 && (optionsType === 'update' || optionsType === 'editData')"  label="签约周期：" required>
                        <el-date-picker
                                v-model="orderBaseInfo.timeArr"
                                style="width: 100%"
                                type="daterange"
                                :disabled="onlyEditProductInformation"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                        />
                      </el-form-item>
                      <el-form-item label="合同周期：" v-if="optionsType === 'create' || optionsType === 'upgrade' || optionsType === 'renew'">
                        <el-radio-group v-model="timer"
                                        @change="changeTimer">
                          <el-radio :label="3" >3个月</el-radio>
                          <el-radio :label="6" >6个月</el-radio>
                          <el-radio :label="12">12个月</el-radio>
                          <el-radio :label="13">13个月</el-radio>
                          <el-radio :label="14">14个月</el-radio>
                          <el-radio :label="15">15个月</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item v-if="(joinProject.id===3&&orderBaseInfo.versionType===4) || optionsType === 'create'|| optionsType === 'upgrade' || optionsType === 'renew'" label="签约开始日期：" required>
                        <el-date-picker
                                v-model="startTime"
                                :disabled="onlyEditProductInformation"
                                @change="changeTimer"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="开始日期"
                        />
                      </el-form-item>
                      <el-form-item v-if="joinProject.id===3&&orderBaseInfo.versionType===4 || optionsType === 'create'|| optionsType === 'upgrade' || optionsType === 'renew'" label="签约结束日期：" required>
                        <el-date-picker
                                v-model="endTime"
                                :disabled="timer !== 1"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="签约结束日期"
                        />
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :xs="{span:24}" :sm="{span:12}">
                      <el-form-item label="完款时间：" prop="completionTime">
                        <el-date-picker
                          v-model="orderBaseInfo.completionTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择日期"
                        />
                      </el-form-item>
                    </el-col> -->
                    <!--                  预签2 不显示公办校，抢分也不显示公办校，单点显示0，独家1不显示-->
                    <el-col v-if="(joinProject.id < 4&&(orderBaseInfo.areaSingle===0||orderBaseInfo.areaSingle===3)&&orderBaseInfo.versionType === 1)||(joinProject.id < 4&&optionsType==='upgrade'&&(orderBaseInfo.areaSingle===0||orderBaseInfo.areaSingle===3))||(joinProject.id < 4&&optionsType==='renew'&&(orderBaseInfo.areaSingle===0||orderBaseInfo.areaSingle===3))"
                            :xs="{span:24}" :sm="{span:12}">
                      <el-form-item label="公办校：">
                        <el-input :disabled="onlyEditProductInformation" v-model="orderBaseInfo.publicSchool" maxlength="30" @change="refresh" />
                      </el-form-item>
                    </el-col>
                  </div>
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="备注：">
                      <el-input :disabled="onlyEditProductInformation" v-model="orderBaseInfo.remark" type="textarea" placeholder="" maxlength="255" show-word-limit />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
            <el-card class="box-card" shadow="hover" v-if="!isthreeThousandPlanets">
              <div slot="header" class="clearfix">
                <span>签约合同类型</span>
              </div>
              <div class="item">
                <el-row style="margin-bottom: 0">
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="签约合同类型：" required>
                      <el-radio-group :disabled="onlyEditProductInformation || status >= 6" v-model="orderBaseInfo.contractType" class="radios">
                        <el-radio :label="1">
                          <span>个人合同</span>
                           <el-tooltip class="item" effect="dark" content="无营业执照" placement="top">
                    <i class="el-icon-question" style="font-size: 16px; padding: 0;  padding-left: 10px; color: #606266;"></i>
                  </el-tooltip>
                        </el-radio>
                        <el-radio :label="2">
                          <span>企业合同</span>
                           <el-tooltip class="item" effect="dark" content="有营业执照" placement="top">
                    <i class="el-icon-question" style="font-size: 16px; padding: 0;  padding-left: 10px; color: #606266;"></i>
                  </el-tooltip>
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row style="margin-bottom: 0">
                  <el-col :xs="{span:24}" :sm="{span:24}">
                    <el-form-item label="补充内容：">
                      <el-input :disabled="onlyEditProductInformation || status >= 6" v-model="orderBaseInfo.reinforCement" type="textarea" :row="2" show-word-limit maxlength="300" placeholder="请输入补充内容" @input="refresh" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
        <!--      产品信息列表-->
        <el-col :lg="{span:11}">
          <el-card class="box-card productInfo" shadow="hover">
            <div slot="header" class="clearfix">
              <span>产品信息</span>
              <!--            <el-button size="mini" type="primary" @click="getPolicy">选择套餐</el-button>-->
            </div>
            <div class="text">
              <el-form
                      ref="projectInfo"
                      size="small"
                      label-width="100px"
              >
                <el-row>
                  <el-col :span="24">
                    <el-table
                            :data="projectInfo"
                            border
                            fit
                            stripe
                            highlight-current-row
                            style="width: 100%;"
                    >
                      <el-table-column type="index" label="#" align="center" width="40" />
                      <el-table-column label="产品名称">
                        <template slot-scope="{row}">
                          <el-select :disabled="!canShowDelete(row.isOld)" v-model="row.productId" size="small" filterable style="width: 100%">
                            <el-option
                                    v-for="item in productProjects"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.productName"
                            />
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="产品数量/课时">
                        <template slot-scope="{row}">
                          <el-input :disabled="!canShowDelete(row.isOld)" v-model.number="row.productNum" maxlength="4" />
                        </template>
                      </el-table-column>
                      <el-table-column label="产品价格">
                        <template slot-scope="{row}">
                          <el-input v-model="row.productPrice" disabled />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
                        <template slot-scope="scope">
                          <el-button type="primary"
                          v-if="canShowDelete(scope.row.isOld)" size="mini" @click="deleteCustomerProject(scope.$index, 'projectInfo')">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="6" :offset="9" class="text-center">
                    <div class="add-customer" @click="addProductProject('projectInfo')"><i class="el-icon-plus"
                                                                                           style="font-size: 12px" />添加
                    </div>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <!--      业绩所属人-->
        <el-col
                :lg="{span:13}">
          <el-form
                  ref="recommendInfo"
                  size="small"
                  :model="recommendInfo"
                  label-width="100px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>业绩所属人</span>
              </div>
              <div class="item">
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="推荐渠道：" required>
                      <!--                      customerInfo.joinStatus 1:已加盟 2：未加盟-->
                      <el-select :disabled="onlyEditProductInformation" v-model="recommendInfo.channel" class="filter-item" clearable style="width:100%"
                                 @change="changeChannel">
                        <el-option
                                v-for="item in channelList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :disabled="(joinProject.id != 3 || (customerInfo.joinStatus && customerInfo.joinStatus == 1)) && item.value == 3"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="姓名：" required>
                      <el-select
                              v-model="recommendInfo.recClueId"
                              size="small"
                              filterable
                              clearable
                              remote
                              reserve-keyword
                              placeholder="请输入推荐人姓名"
                              :remote-method="getOriginUserList"
                              :loading="recommendLoading"
                              :disabled="!recommendInfo.channel || onlyEditProductInformation"
                              style="width: 100%"
                              @change="setRecommendMobile"
                      >
                        <el-option
                                v-for="item in recommendUserList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="手机号：">
                      <el-input  v-model="referrerDeliveryAddress.mobile" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="recommendInfo.channel == '2'" :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="校区：" required>
                      <el-select :disabled="onlyEditProductInformation" v-model="recommendInfo.recInstitutionId" filterable @change="refresh">
                        <el-option
                                v-for="item in schoolLists"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="recommendInfo.channel == '2'" :span="24">
                    <!--                  !!recommendInfo.recClueId &&-->
                    <el-form-item label="收货地址：" required>
                      <area-picker
                              :level="'3'"
                              :area-list="recommendInfo.recommendCityPicker"
                              :isEditSchool="onlyEditProductInformation"
                              :area-style="'width:100%'"
                              @getAreaList="getRecommendAddress"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="recommendInfo.channel == '2'" :span="24">
                    <el-form-item label="详细地址：" required>
                      <el-input :disabled="onlyEditProductInformation"  v-model="referrerDeliveryAddress.address" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>

        <el-col :lg="{ span: 13 }">
          <el-form
            ref="form"
            size="small"
            :model="recommendInfo"
            label-width="100px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>业绩部门</span>
              </div>
              <div class="text">
                <el-form-item label="业绩部门：" required>
                  <PerformanceDepartment
                    :disabled="onlyEditProductInformation"
                    :value.sync="recommendInfo.salesDept"
                  />
                </el-form-item>
              </div>

            </el-card>
          </el-form>
        </el-col>



        <!-- 如果加盟升级或者续约，则显示业绩部门 -->
        <!--      推荐人产品-->
        <el-col v-if="recommendInfo.channel == '2'&&optionsType!=='renew'&&optionsType!=='upgrade'&&orderBaseInfo.businessType!==2&&orderBaseInfo.businessType!==3"
                :lg="{span:11}">

          <el-form
                  ref="form"
                  size="small"
                  label-width="100px"
          >
            <el-card class="box-card productInfo" shadow="hover">
              <div slot="header" class="clearfix">
                <span>推荐人产品</span>
              </div>
              <div class="text">
                <el-row>
                  <el-col :span="24">
                    <el-table
                            :data="referrerProductList"
                            border
                            fit
                            stripe
                            highlight-current-row
                            style="width: 100%;"
                    >
                      <el-table-column type="index" label="#" align="center" width="40" />
                      <el-table-column label="产品名称">
                        <template slot-scope="{row}">
                          <el-select v-model="row.productId" size="small" filterable style="width: 100%">
                            <el-option
                                    v-for="item in recommendProducts"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.productName"
                            />
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="产品数量/课时">
                        <template slot-scope="{row}">
                          <el-input v-model="row.productNum" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
                        <template slot-scope="scope">
                          <el-button type="primary" size="mini" @click="deleteCustomerProject(scope.$index,'referrerProductList')">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="6" :offset="9" class="text-center">
                    <div class="add-customer" @click="addProductProject('referrerProductList', true)"><i class="el-icon-plus"
                                                                                                         style="font-size: 12px" />添加
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <el-row>
      <el-col :span="6" :offset="9" class="text-center">
        <div class="add-customer submit" @click="submitOrder">确&nbsp;认</div>
      </el-col>
    </el-row>
    <choose-school ref="chooseSchool" @chooseSuccess="getSchoolCallBack" />
    <choose-policy ref="choosePolicy" @chooseSuccess="getPolicyCallBack" />
    <el-dialog title="请认真核对以下信息" :visible.sync="confirmDialogVisible" width="30%">
      <div  class="confirm-dialog">
        <div>加盟项目：{{dialogContent[0]}}</div>
        <div v-if="!isthreeThousandPlanets">区域类型：{{dialogContent[1]}}</div>
        <div v-if="!isthreeThousandPlanets">签约周期：{{dialogContent[2][0]}} ~ {{dialogContent[2][1]}}</div>
        <div>应付金额：￥{{dialogContent[3]}}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="confirmDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDialog">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createOrder, addSqXqOrder, editOrder, updateSqXqOrder, getOrderDetail, editOrderProductDTO } from '@/api/handover'
import { getAllProject, getProjectProduct, getRecommentList, getRecommentProductList, getListSignParty } from '@/api/common'
import { getCustomerDetail, getJoinProjects } from '@/api/customer'
import { getSchoolDetail } from '@/api/school'
import {
  businessTypeList,
  channelList,
  converseEnToCn,
  intentionList,
  joinStatusList,
  originList,
  productTypeList
} from '@/utils/field-conver'
import ChooseSchool from './choose-school'
import ChoosePolicy from './choose-policy'
import AreaPicker from '@/components/area-picker'
import { checkBtnPermission } from '@/utils/permission'
import CityContractSignView from '@/views/handover/list/components/CityContractSignView.vue'
import PerformanceDepartment from '@/components/PerformanceDepartment/PerformanceDepartment.vue'

export default {
  name: 'HandoverCreate',
  components: { CityContractSignView, ChooseSchool, ChoosePolicy, AreaPicker, PerformanceDepartment },
  directives: {},
  inject: ['reload'],
  data() {
    const projectNameValid = (rule, value, callback) => {
      if (!this.joinProject.id) {
        return callback(new Error('加盟项目是必填项'))
      }
      else {
        callback()
      }
    }
    return {
      isthreeThousandPlanets: false, // 是否是三千星球
      isShowContract: false, // 是否显示初高中合同
      params: {},
      dialogContent: ['', '', ['', ''], ''], // 确认弹窗内容
      confirmDialogVisible: false,
      status: null, // 订单状态
      timer: 6,
      onlyEditProductInformation: false, //  状态13 已生效仅修改产品信息
      listSignParty: [],
      optionsType: '',
      orderId: '', // 订单Id（交接单）
      clueId: '', // 客户Id
      customerName: '', // 客户名称
      schoolId: '', // 校区id
      schoolProjectId: '', // 校区项目id
      projectId: '', // 项目id
      reverse: false,
      hideTime: true,
      recommendUserList: [], // 推荐人列表
      recommendLoading: false, // 推荐人搜索的加载动画
      orderBaseInfoRules: {
        businessType: { required: true, message: ' ', trigger: 'change' },
        projectNameValid: { required: true, validator: projectNameValid, message: ' ', trigger: 'change' }
        // completionTime: { required: true, message: ' ', trigger: 'change' }
      },
      activities: [], // 进度
      isProductMustChoose: false, // 客户产品是否必选
      productProjects: [], // 项目下的产品列表
      recommendProducts: [], // 推荐人项目产品列表
      customerSchoolList: [], // 客户校区列表
      channelList: channelList, // 推荐人渠道
      businessTypeList: businessTypeList, // 业务类型
      policyList: [], // 套餐列表
      comProjectList: [], // 项目列表
      orderDetailInfo: {}, // 交接单详情数据
      customerInfo: {}, // 合伙人信息
      schoolInfo: {}, // 校区信息
      contractingCompany: '', //签约主体
      orderBaseInfo: {
        signPartyId: '',
        versionType: '1',
        areaSingle: 1,
        contractType: '', // 签约合同类型
        remark: '加盟升级',
        reinforCement: '', // 补充说明
      }, // 交接单基础信息
      projectInfo: [], // 产品信息
      recommendInfo: {
        salesDept: '',
        channel: '',
        recommendCityPicker: {
          provinceId: '',
          cityId: '',
          areaId: ''
        }
      }, // 推荐人基本信息
      referrerProductList: [], // 推荐人产品列表
      referrerDeliveryAddress: {}, // 推荐人地址信息
      joinProject: {}, // 加盟的项目信息
      paymentRecordList: [], // 打款记录列表
      showFlag: -1, // 是否显示公办校的标识
      copeMoney: 0,
      startTime: '',
      endTime: '',
      schoolLists: [],
      policyName: '',
      policyAmount: '',
      policyId: '',
      versionTypes: [
        {
          id: 1,
          label: '正式',
          disabled: false
        },
        // {
        //   id: 2,
        //   label: '预签',
        //   disabled: true
        // }
      ]
    }
  },
  computed: {
    filterStr(){
      return this.joinProject.id!==8?this.customerInfo.provinceName:'抖音'
    },
    singPartyStr(){
      return this.joinProject.id!==8?'请按地区进行选择签约主体':'抖音云连锁请选择上海恒提文化有限公司'
    }
  },
  watch: {
    'orderBaseInfo.versionType': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal === 4) {
          this.orderBaseInfo.areaSingle = 0
          this.endTime = '2020-08-31'
        }
      }
    },
    'recommendInfo.recClueId': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) {
          this.recommendInfo.recInstitutionId = ''
        }
      }
    },
    'schoolLists': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.recommendInfo.recInstitutionId = ''
        }
      }
    }
  },
  created() {
    this.optionsType = this.$route.query.type // 是否是编辑、修改
    this.status = this.$route.query.status;
    this.onlyEditProductInformation = this.status === 13;
    this.orderId = this.$route.query.orderId || '' // 如果是创建交接单 没有orderId 为空
    this.clueId = this.$route.params.clueId || '' // 客户id
    this.customerName = this.$route.query.name || '' // 客户名称
    this.schoolId = this.$route.query.slId || '' // 校区id
    this.schoolProjectId = this.$route.query.spId || '' // 校区项目id
    this.projectId = this.$route.query.pjId || '' // 项目id
    // 创建交接单时候默认开始时间为当日时间 结束时间根据时长自动生成 且不可编辑
    if (this.optionsType === 'create' || this.optionsType === 'upgrade' || this.optionsType === 'renew') {
      this.startTime = this.$moment().format('YYYY-MM-DD');
      this.endTime = this.addMonthsToDate(this.startTime, this.timer);
      this.setDefaultOrderInfo()
    }
    if (this.optionsType === 'update' || this.optionsType === 'editData' || this.optionsType === 'renew' || this.optionsType === 'upgrade') { // 修改订单或续约需要查订单详情
      if (this.orderId) {
        this.getOrderDetailInfo(this.orderId)
      }
    }
    if (this.optionsType !== 'create') {
       this.schoolId && this.setDefaultSchoolInfo(this.schoolId)
    }

    this.clueId && this.getCustomerInfo(this.clueId) // 获取客户模块信息
    this.getProject() // 获取项目列表
    this.setTagsViewTitle(this.customerName) // 设置当前tab 名
    if (this.optionsType === 'upgrade') { // 加盟升级
      this.$set(this.orderBaseInfo, 'areaSingle', 1)
      this.orderBaseInfo.remark = '加盟升级'
    }
    this.addRecommonProductList() // 获取推荐人产品列表
    this.getListSingParty()//获取签约主体列表
    // this.getProjectList(this.clueId)
  },
  mounted () {
    // 由于选择加盟项目页面刷新 所以写在mounted
    // 如果创建 默认签约合同类型为企业合同 业绩所属人姓名自动带入创建交接单的人 默认推荐渠道三陶员工
    if (this.optionsType === 'create' || !this.orderId || this.optionsType === 'renew' || this.optionsType === 'upgrade') {
      this.$set(this.recommendInfo, 'channel', 1);
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'));
      this.$set(this.recommendInfo, 'recClueId', userInfo.userId);
      this.getOriginUserList(userInfo.realName);
    }
    if (this.optionsType === 'create' || !this.orderId) {
      this.$set(this.orderBaseInfo, 'contractType', '');
    }
  },
  methods: {
   handleRadioChange(value) {
      if (this.orderBaseInfo.businessVersion === value) {
        this.orderBaseInfo.businessVersion = null;
      } else {
        this.orderBaseInfo.businessVersion = value;
      }
    },
    disposeProjectInfo () {
      this.projectInfo = this.projectInfo.map(item => ({
        ...item,
        isOld: true
      }));
    },
    hasPermission(code) {
      return checkBtnPermission(code);
    },
    canShowDelete(productId) {
      if (this.hasPermission(['order:list:detail:productInfo'])) {
        return true; // 有权限，无论是否有productId都可删除
      } else {
        return !productId; // 无权限，仅当productId为空时可删除
      }
    },
    getListSingParty() {
      getListSignParty().then(res => {
        this.listSignParty = res.data
      })
    },
    /**
     * 选择合同版本
     * 1 正式
     * 2 预签 默认选择单点
     * */
    changeVersion(val) {
      if (Number(val) === 2) {
        this.$set(this.orderBaseInfo, 'areaSingle', 0)
        this.$set(this.orderBaseInfo, 'publicSchool', '')
      }
      else if (Number(val) === 3) {
        this.$set(this.orderBaseInfo, 'areaSingle', 0)
      }
      // 是否显示初高中合同
      this.isShowContractBtn();
    },
    isShowContractBtn() {
      // 三陶教育 且正式
      if (this.joinProject.id === 1 && this.orderBaseInfo.versionType === 1) {
        this.isShowContract = true;
      } else {
        this.isShowContract = false;
        this.orderBaseInfo.businessVersion = ''
      }
    },
    /**
     * 公共刷新方法
     * */
    updateView(e) {
      this.$forceUpdate()
    },
    /**
     * 查看交接单详情
     * @param row
     */
    getOrderDetailInfo(id) {
      getOrderDetail(id).then(res => {
        this.orderDetailInfo = JSON.parse(JSON.stringify(res.data))
        this.policyId = this.orderDetailInfo.policyId
        this.policyName = this.optionsType === 'renew' || this.optionsType === 'upgrade' ? "" : this.orderDetailInfo.policyName || ''
        this.policyAmount = this.optionsType === 'renew' || this.optionsType === 'upgrade' ? "" : this.orderDetailInfo.policyPrice
        this.customerInfo = this.orderDetailInfo.clueInfo // 合伙人信息
        this.schoolInfo = this.orderDetailInfo.joinSchool // 项目所属校区资料
        this.copeMoney = this.orderDetailInfo.payAmount
        this.orderBaseInfo = { // 交接单基本信息
          id: this.orderDetailInfo.id,
          businessVersion: this.orderDetailInfo.businessVersion === 1,
          businessType: this.optionsType === 'renew' || this.optionsType === 'upgrade' ? this.orderBaseInfo.businessType : this.orderDetailInfo.businessType,
          payAmount: this.optionsType === 'renew' || this.optionsType === 'upgrade' ? "" : this.orderDetailInfo.payAmount,
          completionTime: this.orderDetailInfo.completionTime,
          remark: this.orderDetailInfo.remark,
          areaSingle: this.orderDetailInfo.areaSingle,
          versionType: this.orderDetailInfo.versionType,
          timeArr:  [this.orderDetailInfo.signStartTime || '', this.orderDetailInfo.signEndTime || ''],
          publicSchool: this.orderDetailInfo.publicSchool,
          orderQueryNo: this.orderDetailInfo.orderQueryNo,
          signPartyId: this.orderDetailInfo.signPartyId,
          schoolId: this.orderDetailInfo.schoolId,
          contractType: this.optionsType === 'renew' || this.optionsType === 'upgrade' ? "" : this.orderDetailInfo.contractType,
          reinforCement: this.optionsType === 'renew' || this.optionsType === 'upgrade' ? "" : this.orderDetailInfo.reinforCement,
        }
        this.projectInfo = this.orderDetailInfo.clueProductList // 产品列表信息
        // 每个套餐加个标识  判断是否是已有
        this.disposeProjectInfo();
        // 业绩所属人创建 续约  加盟升级默认
        if (this.optionsType !== 'create'  && this.optionsType !== 'renew' && this.optionsType !== 'upgrade') {
          this.recommendInfo = { // 业绩所属人
            recClueId: this.orderDetailInfo.recClueId,
            recName: this.orderDetailInfo.recName,
            channel: this.orderDetailInfo.channel,
            recInstitutionId: Number(this.orderDetailInfo.recInstitutionId),
          }
        }
        this.$set(this.recommendInfo, 'salesDept', this.orderDetailInfo.salesDept);
        this.getProjectList(this.orderDetailInfo.recClueId)

        this.recommendInfo.recommendCityPicker = {
          provinceId: this.orderDetailInfo.referrerDeliveryAddress ? this.orderDetailInfo.referrerDeliveryAddress.provinceId : '',
          cityId: this.orderDetailInfo.referrerDeliveryAddress ? this.orderDetailInfo.referrerDeliveryAddress.cityId : '',
          areaId: this.orderDetailInfo.referrerDeliveryAddress ? this.orderDetailInfo.referrerDeliveryAddress.areaId : ''
        }
        if (this.orderDetailInfo.referrerDeliveryAddress != null) {
          this.referrerDeliveryAddress = this.orderDetailInfo.referrerDeliveryAddress // 推荐人地址信息
        }
        this.referrerProductList = this.orderDetailInfo.referrerProductList // 推荐人产品列表
        this.joinProject = this.orderDetailInfo.joinProject // 加盟的项目
        if (this.joinProject.id === 1) {
          this.versionTypes = [
            {
              id: 1,
              label: '正式',
              disabled: false
            },
            {
              id: 9,
              label: '特色班型合同',
              disabled: false
            },
            // {
            //   id: 2,
            //   label: '预签',
            //   disabled: true
            // },
            // {
            //   id: 3,
            //   label: '抢分',
            //   disabled: true
            // },
            // {
            //   id: 7,
            //   label: '高考绝招班',
            //   disabled: true
            // }
          ]
        }
        else if (this.joinProject.id === 3) {
          this.versionTypes = [
            {
              id: 1,
              label: '正式',
              disabled: false
            },
            {
              id: 9,
              label: '特色班型合同',
              disabled: false
            },
            // {
            //   id: 2,
            //   label: '预签',
            //   disabled: true
            // },
            // {
            //   id: 4,
            //   label: '特色班型'
            // },
            // {
            //   id: 5,
            //   label: '烨晨市代',
            //   disabled: true
            // },
            // {
            //   id: 6,
            //   label: '烨晨渠道',
            //   disabled: true
            // },
            // {
            //   id: 8,
            //   label: '招生服务合同',
            //   disabled: true
            // }
          ]
        }
        else if (this.joinProject.id === 2) {
          this.versionTypes = [
            {
              id: 1,
              label: '正式',
              disabled: false
            },
            // {
            //   id: 2,
            //   label: '预签',
            //   disabled: true
            // }
          ]
        }
        this.startTime = this.joinProject.id === 3 && this.orderBaseInfo.versionType === 4 ? this.orderDetailInfo.signStartTime : '';
        // 根据已有的项目id查询产品列表
        this.getProductOfProject(this.joinProject.id, true)
        if (this.optionsType !== 'create' && this.optionsType !== 'renew' && this.optionsType !== 'upgrade') {
          this.getOriginUserList(this.orderDetailInfo.recName ? this.orderDetailInfo.recName : '') // 根据推荐人渠道获取推荐人
        }
      })
    },
    // 根据时长自动算截止时间， 然后再检查结果是否在月底。如果超过了该月的天数，就将日期调整为该月的最后一天。
   addMonthsToDate(startDate, monthsToAdd) {
      let result = this.$moment(startDate).add(monthsToAdd, 'months');
      // 如果日相等 往前推一天
      if (result.date() === this.$moment(startDate).date()) {
        result = result.subtract(1, 'day');
      }
      // 检查加完月数后的日期是否大于该月的最后一天
      if (result.date() > this.$moment(result).endOf('month').date()) {
          result = result.endOf('month');
      }
      // 如果日期为0，说明跨月调整时出现了无效日期
      if (result.date() === 0) {
          // 设置为上个月的最后一天
          result = result.subtract(1, 'day').endOf('month');
      }

      return result.format('YYYY-MM-DD');
    },
    // 确认弹窗
    confirmDialog () {
      let msg = ''
      let params = this.params;
      params.businessVersion = params.businessVersion ? 1 : 0;
      switch (this.optionsType) {
        case 'create':
          msg = '创建交接单成功'
          this.createHandover(params, msg)
          break
        case 'update':
          msg = '修改交接单成功'
          this.updateHandover(params, msg)
          break
        case 'editData':
          msg = '修改交接单成功'
          this.updateHandover(params, msg)
          break
        case 'renew':
          msg = '续约交接单成功'
          delete params.id;
          this.createHandover(params, msg)
          break
        case 'upgrade':
          msg = '交接单加盟升级成功'
          this.createHandover(params, msg)
          break
      }
    },
    /**
     * 确认提交
     * */
    submitOrder() {
      let val = true
      if (this.joinProject.id !== 9) {
        if (this.orderBaseInfo.areaSingle === undefined || this.orderBaseInfo.areaSingle === null || this.orderBaseInfo.areaSingle === '') {
          this.$message({
            message: '区域类型不能为空',
            type: 'error'
          })
          return
        }
        if (this.optionsType !== 'create' && this.optionsType !== 'upgrade' && this.optionsType !== 'renew' && this.orderBaseInfo.versionType !== 4 && !this.orderBaseInfo.timeArr || (this.orderBaseInfo.timeArr && this.orderBaseInfo.timeArr.length < 2)) {
          this.$message({
            message: '签约周期期不能为空',
            type: 'error'
          })
          return
        }
        if ((this.orderBaseInfo.versionType === 4 || this.optionsType === 'create' || this.optionsType === 'upgrade' || this.optionsType === 'renew') && (!this.startTime || !this.endTime)) {
          this.$message({
            message: '签约周期期不能为空',
            type: 'error'
          })
          return
        }
        if (!this.orderBaseInfo.contractType) {
          this.$message({
            message: '签约合同类型不能为空',
            type: 'error'
          })
          return
        }
      }
      if (this.orderBaseInfo.payAmount === '') {
        this.$message({
          message: '应付金额不能为空',
          type: 'error'
        })
        return
      }
      if (!this.recommendInfo.channel) {
        this.$message({
          message: '推荐渠道不能为空',
          type: 'error'
        })
        return
        if (this.optionsType !== 'renew' && this.optionsType !== 'upgrade' && this.orderBaseInfo.businessType !== 2 && this.orderBaseInfo.businessType !== 3 && (!this.recommendInfo.recommendCityPicker.areaId || !this.recommendInfo.recommendCityPicker.provinceId || !this.recommendInfo.recommendCityPicker.cityId) && this.recommendInfo.channel === '2') {
          this.$message({
            message: '推荐人收货地址不能为空',
            type: 'error'
          })
          return
        }
        if (this.optionsType !== 'renew' && this.optionsType !== 'upgrade' && this.orderBaseInfo.businessType !== 2 && this.orderBaseInfo.businessType !== 3 && !this.referrerDeliveryAddress.address && this.recommendInfo.channel === 2) {
          this.$message({
            message: '推荐人详细地址不能为空',
            type: 'error'
          })
          return
        }
        if (!this.recommendInfo.recInstitutionId && this.recommendInfo.channel === 2) {
          this.$message({
            message: '校区不能为空',
            type: 'error'
          })
          return
        }
        if (!this.schoolInfo.id) {
          this.$message({
            message: '项目所属校区未选择',
            type: 'error'
          })
          return
        }
        if(!this.orderBaseInfo.signPartyId) {
          this.$message({
            message: '签约主体未选择',
            type: 'error'
          })
          return
        }
      }
      if (!this.recommendInfo.recClueId) {
        this.$message({
          message: '推荐人姓名不能为空',
          type: 'error'
        })
        return
      }
      this.$refs['orderBaseInfo'].validate(valid => {
        if (!valid) {
          this.$message({
            message: '交接单信息填写有误',
            type: 'error'
          })
          val = false
        }
      })

      // 业绩所属人校验
      if (!this.recommendInfo.salesDept) {
        this.$message({
          message: '业绩部门不能为空',
          type: 'error'
        })
        return
      }


      if (this.isProductMustChoose && this.projectInfo.length < 1) {
        this.$message({
          message: '产品信息必选',
          type: 'error'
        })
        return
      }

      if (!this.policyId) {
        this.$message({
          message: '套餐必选',
          type: 'error'
        })
        return
      }

      if (val) {

        const recInstitutionId = this.recommendInfo.channel === 2 ? this.recommendInfo.recInstitutionId : ''


        this.orderBaseInfo.schoolId = this.joinProject.id === 9 ? '' : this.schoolInfo.id// 校区id
        this.orderBaseInfo.signStartTime = this.orderBaseInfo.versionType === 4 || this.optionsType === 'create' || this.optionsType === 'upgrade' || this.optionsType === 'renew' ? this.startTime : this.orderBaseInfo.timeArr[0]
        this.orderBaseInfo.signEndTime = this.orderBaseInfo.versionType === 4 || this.optionsType === 'create' || this.optionsType === 'upgrade' || this.optionsType === 'renew' ? this.endTime : this.orderBaseInfo.timeArr[1]
        const params = Object.assign({}, this.orderBaseInfo, { institutionId: this.schoolProjectId }, { referrerDeliveryAddress: this.referrerDeliveryAddress },
          { referrerProductList: this.referrerProductList }, { projectId: this.joinProject.id }, this.recommendInfo,
          { clueProductList: this.projectInfo }, { clueId: this.clueId || this.customerInfo.id }, { recInstitutionId: recInstitutionId }, { policyId: this.policyId || '' })
          this.params = params;
          this.disposeDialogContent();
          this.confirmDialogVisible = true;
      }
    },
    // 处理弹窗内容 加盟项目  区域类型  签约周期  应付金额
    disposeDialogContent () {
      // 加盟项目
      let arr1 = this.comProjectList.filter(item => item.id === this.params.projectId)[0]?.projectName;
      // 区域类型
      let areaSingleList = ['区县单点', '区县独家', '乡镇独家', '乡镇单点']
      let arr2 = areaSingleList[this.params.areaSingle];
      // 签约周期
      let arr3 = [this.params.signStartTime, this.params.signEndTime]
      // 应付金额
      let arr4 = this.params.payAmount;
      this.dialogContent = [arr1, arr2, arr3, arr4]
    },

    // 处理三千星球参数
    disposeThreeThousandPlanetsParams (params) {
      const {
        id,
        projectId,
        payAmount,
        remark,
        salesDept,
        policyId,
        channel,
        clueId,
        recClueId,
        clueProductList
      } = params;
      let submitParams = {
        id,
        projectId,
        payAmount,
        remark,
        salesDept,
        policyId,
        channel,
        clueId,
        recClueId,
        clueProductList
      };
      return  submitParams;
    },
    /**
     * 创建交接单
     * */
    createHandover(params, msg) {
      let submitParams = params;
      // 三千星球
      if (params.projectId === 9) {
        submitParams = this.disposeThreeThousandPlanetsParams(params);
      }
      let addPort = params.projectId === 9 ? addSqXqOrder : createOrder
      addPort(submitParams).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: msg,
            type: 'success'
          })
          this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
             this.$router.go(-1)
             setTimeout(() => {
               this.reload()
             }, 100)
           })
         }
       }).catch(res => {

       })
    },
    /**
     * 编辑交接单
     * */
    updateHandover(params, msg) {
      let submitInterface = this.onlyEditProductInformation ? editOrderProductDTO : params.projectId === 9 ? updateSqXqOrder : editOrder;
      let submitParams = params;
      // 三千星球
      if (params.projectId === 9) {
        submitParams = this.disposeThreeThousandPlanetsParams(params);
      }
      submitInterface(submitParams).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: msg,
            type: 'success'
          })
          this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
            this.$router.go(-1)
          })
        }
      }).catch(res => {

      })
    },
    /**
     * 设置订单默认的交接单信息
     * */
    setDefaultOrderInfo() {
      let businessType = -1
      switch (this.optionsType) {
        case 'create':
          businessType = 1
          break
        case 'renew':
          businessType = 2
          break
        case 'upgrade':
          businessType = 3
          break
      }
      this.orderBaseInfo = {
        businessType: businessType
      }
      this.joinProject = {
        id: this.projectId
      }
      // 根据已有的项目id查询产品列表
      this.joinProject.id && this.getProductOfProject(this.joinProject.id, true)
    },
    /**
     * 设置默认校区信息
     * */
    setDefaultSchoolInfo(id) {
      const params = {
        schoolId: id
      }
      getSchoolDetail(params).then(res => {
        if (res.code === '000000') {
          this.schoolInfo = res.data
        }
      })
    },
    setTagsViewTitle(name) {

      let title = ''
      switch (this.optionsType) {
        case 'create':
          title = '创建交接单'
          break
        case 'update':
          title = '修改交接单'
          break
        case 'renew':
          title = '交接单续约'
          break
        case 'upgrade':
          title = '加盟升级'
          break
      }
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, { title: `${ title }-${ name }` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.comProjectList = res.data
      })
      // orderBaseInfo
    },
    /**
     * 获取套餐列表
     * */
    getPolicy() {
      if (!this.joinProject.id || !this.orderBaseInfo.businessType) {
        this.$message({
          message: '请先选择项目与业务类型',
          type: 'warning'
        })
        return
      }
      const params = {
        businessType: this.orderBaseInfo.businessType,
        projectId: this.joinProject.id
      }
      this.$refs.choosePolicy.getLists(params)
    },
    /**
     * 获取项目下的产品列表
     * isRender: 是否是页面渲染时的赋值
     * */
    getProductOfProject(val, isRender) {
      // 是否三千星球加盟项目
      this.isthreeThousandPlanets = val === 9;
      this.joinProject.id = val
      this.showFlag = val
      if (!val) {
        this.$message({
          message: '请先选择项目',
          type: 'warning'
        })
        return
      }

      // 如果是手动改变加盟项目，置空
      if (!isRender) {
        this.projectInfo = []
        this.orderBaseInfo.payAmount = ''
        // 如果创建 推荐渠道不清空
        if (this.optionsType !== 'create') {
          this.recommendInfo.channel = ''
        }
        this.policyName = ''
        this.policyAmount = ''
        this.policyId = ''
        this.$set(this.orderBaseInfo, 'versionType', 1)
      }

      if (val === 1 || val === 2 || val === 3) {
        this.versionTypes.forEach(item => {
          if (item.label === '预签') {
            item['disabled'] = true
          }
          else {
            item['disabled'] = false
          }
        })
      }

      // 当前选择的项目是否是必选产品
      const currentChoose = this.comProjectList.filter(item => {
        return item.id === val
      })
      this.isProductMustChoose = currentChoose && currentChoose.length > 1 && currentChoose[0].productFlag === 1
      const params = {
        projectId: val
      }
      getProjectProduct(params).then(res => {
        this.productProjects = res.data
      })
      if (val === 1) {
        this.versionTypes = [
          {
            id: 1,
            label: '正式',
            disabled: false
          },
          {
            id: 9,
            label: '特色班型合同',
            disabled: false
          },
          // {
          //   id: 2,
          //   label: '预签',
          //   disabled: true
          // },
          // {
          //   id: 3,
          //   label: '抢分',
          //   disabled: true
          // },
          // {
          //   id: 7,
          //   label: '高考绝招班',
          //   disabled: true
          // }
        ]
      }
      else if (val === 3) {
        this.versionTypes = [
          {
            id: 1,
            label: '正式',
            disabled: false
          },
          {
            id: 9,
            label: '特色班型合同',
            disabled: false
          },
          // {
          //   id: 2,
          //   label: '预签',
          //   disabled: true
          // },
          // {
          //   id: 4,
          //   label: '特色班型'
          // },
          // {
          //   id: 5,
          //   label: '烨晨市代',
          //   disabled: true
          // },
          // {
          //   id: 6,
          //   label: '烨晨渠道',
          //   disabled: true
          // },
          // {
          //   id: 8,
          //   label: '招生服务合同',
          //   disabled: true
          // }

        ]
      }
      else if (this.joinProject.id === 2||this.joinProject.id === 5) {
        this.versionTypes = [
          {
            id: 1,
            label: '正式',
            disabled: false
          },
          // {
          //   id: 2,
          //   label: '预签',
          //   disabled: true
          // }
        ]
      }
      this.isShowContractBtn();
    },
    /**
     * 获取推荐人产品信息列表
     */
    addRecommonProductList() {
      const that = this
      getRecommentProductList().then(res => {
        if (res.code === '000000') {
          that.recommendProducts = res.data
        }
        else {
          that.$message({
            message: '获取推荐人产品列表失败',
            type: 'warning'
          })
        }
      })
    },
    /**
     * 添加产品列表
     * prop : referrerProductList 推荐人产品列表
     * prop: projectInfo 当前用户产品列表 referrerProductList
     * isRec: 是否是推荐人产品列表
     * */
    addProductProject(prop, isRec) {
      if (!this.joinProject.id && !isRec) {
        this.$message({
          message: '请先选择项目',
          type: 'warning'
        })
        return
      }
      if (!isRec && this.productProjects && this.productProjects.length < 1) {
        this.$message({
          message: '该项目下没有产品，请重新选择项目',
          type: 'warning'
        })
        return
      }
      this[prop].push({
        productId: '',
        productName: '',
        productNum: 1,
        productType: undefined
      })
    },
    /**
     *  删除已选产品
     * */
    deleteCustomerProject(index, prop) {
      this[prop].splice(index, 1)
    },
    /**
     * 获取客户的校区列表
     * */
    getCustomerSchool() {
      const params = {
        clueId: this.clueId
      }
      this.$refs.chooseSchool.getLists(params)
    },
    /**
     * 获取客户详情
     * */
    getCustomerInfo(customerId) {
      getCustomerDetail(customerId).then(res => {
        this.customerInfo = res.data
      })
    },
    /**
     * 获取推荐人列表
     * */
    getOriginUserList(query) {
      if (this.recommendInfo.channel !== null && this.recommendInfo.channel) {
        this.recommendLoading = true
        getRecommentList({ search: query, channel: this.recommendInfo.channel }).then(res => {
          this.recommendLoading = false
          this.recommendUserList = res.data
          const tmpRow = res.data.filter(item => {
            return item.id === this.recommendInfo.recClueId
          })
          this.referrerDeliveryAddress.mobile = tmpRow[0] ? tmpRow[0].mobile : ''
        })
      }
    },
    // 更换推荐人
    changeChannel(val) {
      this.$forceUpdate()
      this.recommendInfo.recClueId = ''
      this.referrerDeliveryAddress.mobile = ''
    },
    /**
     * 切换推荐人姓名时
     * */
    setRecommendMobile(val) {


      const tmpRow = this.recommendUserList.filter(item => {
        return item.id === val
      })
      this.referrerDeliveryAddress.mobile = tmpRow[0] ? tmpRow[0].mobile : ''
      if (val) {
        this.recommendInfo.recInstitutionId = ''
      }
      this.$set(this.recommendInfo, 'recClueId', val)
      this.$forceUpdate()
      this.getProjectList(val)
    },
    getProjectList(val) {
      if (val) {
        const params = { clueId: val }
        const arrs = []
        getJoinProjects(params).then(res => {
          if (res.code === '000000') {
            const joinList = res.data.filter(item => item.status === 2)

            joinList.forEach(item => {
              const obj = {}
              obj['id'] = item.id
              obj['name'] = `${ item.projectName && item.projectName !== null ? item.projectName : '' }${ item.institutionName && item.institutionName !== null ? '-' : '' }${ item.institutionName && item.institutionName !== null ? item.institutionName : '' }`
              arrs.push(obj)
            })
            this.schoolLists = arrs || []

          }
          else {
            this.schoolLists = []
          }
        }).catch(() => {

          this.schoolLists = []
        })
      }
      else {
        this.schoolLists = []
      }
    },
    /**
     * 校区选择回调函数
     * */
    getSchoolCallBack(data) {
      this.schoolInfo = data
    },
    /**
     * 套餐选择的回调函数
     * */
    getPolicyCallBack(data) {
      this.orderBaseInfo.payAmount = this.optionsType === 'editData' ? this.copeMoney : data.price
      this.policyName = data.policyName || ''

      this.policyAmount = data.price
      this.policyId = data.id || ''
      this.projectInfo = [].concat(data.policyProductList)
      this.disposeProjectInfo();
    },
    /**
     * 获取推荐人的收货地址
     * */
    getRecommendAddress(data) {
      this.referrerDeliveryAddress.provinceId = data.provinceId
      this.referrerDeliveryAddress.cityId = data.cityId
      this.referrerDeliveryAddress.areaId = data.areaId
      this.recommendInfo.recommendCityPicker = data
    },
    getInfoOrigin(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(originList, isNaN(data) ? data.origin : data)
    },
    /**
     * 转换加盟状态
     */
    getJoinStatusCN(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(joinStatusList, isNaN(data) ? data.status : data)
    },
    /**
     * 转换意向度key与value
     */
    getIntention(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(intentionList, isNaN(data) ? data.clueType : data)
    },
    getBusinessTypeList(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(businessTypeList, isNaN(data) ? data.businessType : data)
    },
    /**
     * 产品类别
     */
    getProductType(row) {
      return converseEnToCn(productTypeList, row.productType)
    },
    changeAreaSingle(val) { // 获取区域类型的值

      this.$set(this.orderBaseInfo, 'areaSingle', val)
      this.$forceUpdate()
      if (val === 1) {
        this.$set(this.orderBaseInfo, 'publicSchool', '')
      }
    },
    // 时长改变
    changeTimer() {
      // 自定义时候， 结束时间可以编辑
      this.endTime = this.addMonthsToDate(this.startTime, this.timer);
    },
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  margin-bottom: 10px;
}

/deep/ .el-card .el-card__header {
  position: relative;
}

.el-card__header .el-button {
  position: absolute;
  right: 20px;
  top: 10px;
}

.add-customer {
  border-radius: 18px;
  height: 36px;
  color: #539FFF;
  font-size: 15px;
  line-height: 36px;
  text-align: center;
  font-weight: 500;
  border: 1px dashed #DCDCDC;
  margin: 10px auto;
  cursor: pointer;
}

.add-customer:hover {
  background: #46a6ff;
  color: #fff;
  border: 1px dashed #46a6ff;
}

.add-customer.submit {
  background-color: #46a6ff;
  color: #ffffff;
  border: 1px solid #1890ff
}

.project-list {
  background-color: rgba(83, 159, 255, 1);
  border-radius: 4px;
  padding: 10px;
}

.project-item {
  height: 118px;
  border-radius: 4px;
  background-color: #ffffff;
  margin-right: 10px;
}

.time-line-col {
  background-color: #ffffff;
  padding: 12px 30px;
  border-radius: 30px;
}

/deep/ .el-timeline-item__tail {
  position: absolute;
  top: 8px;
  width: 100%;
  border-top: 2px solid #dfe4ed;
  left: 0;
  height: 0;
  border-left: 0;
}

/deep/ .el-timeline-item {
  position: relative;
  display: inline-block;
  padding-right: 0;
  padding-bottom: 0;
  width: 94px;
}

/deep/ .el-timeline-item__wrapper {
  position: relative;
  padding: 10px 0;
  left: -3px;
  top: 16px;
}

/deep/ .el-timeline-item__node--normal {
  width: 17px;
  height: 17px;
}

/deep/ .el-timeline-item__content {
  font-size: 12px;
}

/deep/ .el-date-editor.el-input {
  width: 100%;
}

/deep/ .el-radio-group {
  width: 100%;
}

/deep/ .productInfo td {
  padding: 0;
}
.confirm-dialog {
  font-size: 16px;
  line-height: 24px;
}
.no-label-item {
  line-height: 38px;
  height: 38px;
  margin-top: 4px;
  .el-form-item__content{
    line-height: 38px;
  }
}
</style>
