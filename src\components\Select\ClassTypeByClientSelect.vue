<template>
  <el-select v-model="tmpId" style="width: 220px;" filterable clearable placeholder="班型" no-data-text="请先选择产品线">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="`${item.title}`"
      :value="`${item.id}`">
      <span class="fl">{{ item.title }}</span>
      <span class="subTitle">{{ item.id }}</span>
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getClassType } from '@/api/classType'

/**
 * 班型选择框
 */
export default {
  name: 'ClassTypeByClientSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
    clientCode: {
      type: [String, Number],
      required: false
    }
  },
  watch: {
    clientCode(val) {
      this.dataList=[]
      if (val) {
        this.getList()
      }
    }
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.title : ''
      return this.$emit('change', value, selectedName)
    },
    setDefaults(){
      const selectedOption = this.dataList.find(option => option.id == this.id)
      if(!selectedOption || !selectedOption.id){
        this.handleChange(null)
      }
    },
    getList() {
      if (!this.clientCode) {
        return
      }
      this.loading = true
      getClassType(this.clientCode).then(res => {
        if (res.code === SUCCESS) {
          this.loading = false
          this.dataList = res.data
          this.setDefaults()
        }
        else {
          this.loading = false
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.subTitle {
  float: right;
  color: #8492a6;
  font-size: 13px
}
</style>
