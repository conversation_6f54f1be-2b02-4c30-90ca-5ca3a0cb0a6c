<template>
  <div class="app-container bgGrey">
    <el-form v-if="detail.contractId" ref="detailForm" :model="detail" label-width="200px" :rules="jyRules" :disabled="!isEdit">
      <!--    解约合同 -->
      <el-row :gutter="20">
        <el-col :lg="{span:22}">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>合同信息</span>
            </div>
            <div class="item">
              <el-row>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议编号：">
                    <span>{{ detail.revokeContract.oldContractId }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议名称：">
                    <span>{{ detail.revokeContract.oldContractName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议加盟项目：">
                    <span>{{ detail.projectName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议签订日期：" :required="rescissionType == 3">
                    <el-date-picker v-model="detail.revokeContract.oldContractSignDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" :disabled="rescissionType == 2" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议开始时间：" :required="rescissionType == 3">
                    <el-date-picker v-model="detail.revokeContract.oldContractBeginTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" :disabled="rescissionType == 2" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="原协议结束时间：" :required="rescissionType == 3">
                    <el-date-picker v-model="detail.revokeContract.oldContractEndTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" :disabled="rescissionType == 2" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="甲方：" prop="revokeContract.firstParty">
                    <el-input v-model="detail.revokeContract.firstParty" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="甲方负责人：" prop="revokeContract.firstPartyDelegate">
                    <el-input v-model="detail.revokeContract.firstPartyDelegate" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="甲方身份证/统一信用代码：">
                    <span>{{ detail.revokeContract.firstPartyNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="乙方：" prop="revokeContract.secondParty">
                    <el-input v-model="detail.revokeContract.secondParty" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="乙方负责人：" prop="revokeContract.secondPartyDelegate">
                    <el-input v-model="detail.revokeContract.secondPartyDelegate" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="乙方身份证/统一信用代码：" prop="revokeContract.secondPartyNo">
                    <el-input v-model="detail.revokeContract.secondPartyNo" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="协议终止时间：" prop="revokeContract.terminationTime">
                    <el-date-picker
                      v-model="detail.revokeContract.terminationTime"
                      type="date"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:12}">
                  <el-form-item label="本协议签署时间：">
                    <span>{{ detail.revokeContract.signingTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="{span:24}" :sm="{span:24}">
                  <el-form-item label="协议签订地点：">
                    <span>{{ detail.revokeContract.signingAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="isEdit" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmEdit">确 定</el-button>
    </div>

  </div>
</template>

<script>
import { getContractDetail, modifyRescission } from '@/api/contract'
import { validCreditCode } from '@/utils/validate.js'
export default {
  name: 'RescissionContract',
  props: {
  },
  data() {
    return {
      detail: {},
      rescissionType: '',
      jyRules: {
        revokeContract: {
          firstParty: { required: true, message: ' ', trigger: 'blur' },
          firstPartyDelegate: { required: true, message: ' ', trigger: 'blur' },
          secondParty: { required: true, message: ' ', trigger: 'blur' },
          secondPartyNo: { required: true, validator: validCreditCode, trigger: 'blur' },
          secondPartyDelegate: { required: true, message: ' ', trigger: 'blur' },
          terminationTime: { required: true, message: ' ', trigger: 'blur' }
        }
      },
      areaList: {},
      areaSingle: false,
      id: 0,
      isEdit: false
    }
  },
  created() {
    this.id = this.$route.query.id
    this.isEdit = this.$route.query.isEdit === 'true'
    this.rescissionType = this.$route.query.type // 2：普通合同解约 3：校区项目解约

    this.getDetail()
    const tagsName = this.isEdit ? '解约-编辑合同' : '解约-合同详情'
    this.setTagsViewTitle(tagsName)
  },
  methods: {
    setTagsViewTitle(name) {
      const currentRoute = Object.assign({}, this.$route) // 设置当前tab名
      const route = Object.assign({}, currentRoute, {
        title: `${name}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getDetail() {
      const that = this
      const data = this.id
      getContractDetail(data).then(res => {
        that.detail = res.data
        that.detail.aiContract = res.data.aiContract || {}
        that.detail.contractClue = res.data.contractClue || {}
        that.detail.contractEnterprise = res.data.contractEnterprise || {}
        that.detail.contractSignatory = res.data.contractSignatory || {}
        that.detail.jiaTuiContract = res.data.jiaTuiContract || {}
        that.detail.normalContract = res.data.normalContract || {}
        that.detail.revokeContract = res.data.revokeContract || {}
        that.detail.endRevokeContract = res.data.endRevokeContract || {} // 校区解约合同详情
        const tmpObj = JSON.parse(JSON.stringify(that.detail.endRevokeContract))
        if (Number(that.rescissionType) === 3) {
          that.detail.revokeContract = tmpObj
        }
        that.detail.txtContract = res.data.txtContract || {}
        that.detail.contractOrder = res.data.contractOrder || {}
      })
    },
    getAreaList(data) {
      this.areaList = data
    },
    /**
     * 确认修改信息
     */
    confirmEdit() {
      const that = this
      that.detail.revokeContract.id = that.id
      const data = that.detail.revokeContract
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          modifyRescission(data).then(res => {
            if (res.code === '000000') {
              that.$message({
                type: 'success',
                message: '修改成功!'
              })
              that.$store.dispatch('tagsView/delView', that.$route).then(res => {
                that.$router.go(-1)
              })
            }
          })
        }
      })
    }

  }
}
</script>

<style scoped>
  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
</style>
