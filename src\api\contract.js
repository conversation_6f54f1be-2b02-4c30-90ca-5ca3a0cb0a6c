import request from '@/utils/request'

/**
 * 获取合同详情
 * */
export function getContractDetail(data) {
  return request({
    url: '/contracts/' + data,
    method: 'GET'
  })
}
/**
 * 创建合同模板
 * */
export function getCreatContract(data) {
  return request({
    url: '/contracts/create/' + data,
    method: 'POST'
  })
}
/**
 * 修改合同
 * */
export function modifyContractDetail(data) {
  if (data.operateType === 2) {
    data.confirmTerms = 1
  }
  return request({
    url: '/contracts/V3',
    method: 'PUT',
    data: data
  })
}
/**
 * 解约合同
 * */
export function sendRescission(data) {
  return request({
    url: '/contracts/rescission/' + data,
    method: 'PUT'
  })
}
/**
 * 修改解约合同
 * */
export function modifyRescission(data) {
  return request({
    url: '/contracts/revoke',
    method: 'PUT',
    data: data
  })
}
/**
 * 签署合同
 */
export function toDealContract(data) {
  return request({
    url: 'contracts/' + data,
    method: 'PUT'
  })
}
/**
 * 撤销合同
 */
export function doContractCancel(contractId) {
  return request({
    url: `contracts/rescindV3/${contractId}`,
    method: 'POST'
  })
}

/**
 * 变更合同
 */
export function updateContractType(contractId) {
  return request({
    url: `/contracts/updateContractType/${contractId}`,
    method: 'POST'
  })
}
/**
 * 下载合同
 */
export function downloadContractCancel(contractId) {
  return request({
    url: `contracts/getDownloadUrl/${contractId}`,
    method: 'GET'
  })
}
/**
 * 生效合同
 */
export function completionContract(contractId) {
  return request({
    url: `contracts/finish/${contractId}`,
    method: 'POST'
  })
}
/**
 * 查看合同
 */
export function ViewContract(contractId) {
  return request({
    url: `contracts/getViewUrl/${contractId}`,
    method: 'GET'
  })
}

/**
 * 查看本地合同
 * @param contractId
 */
export function viewLocalContract(contractId) {
  return request({
    url: `contracts/getContractFiles/${contractId}`,
    method: 'GET'
  })
}
/**
 * 查看电子合同
 */
export function viewContractPdf(data) {
  return request({
    url: 'contracts/getContractPdf/' + data,
    method: 'GET'
  })
}
/**
 * 编辑延期合同
 */
export function delayContract(params) {
  return request({
    url: 'contracts/editDelay',
    method: 'PUT',
    data: params
  })
}
/**
 * 生成电子合同
 */
export function generateContract(contractId) {
  return request({
    url: `contracts/fillV3/${contractId}`,
    method: 'POST'
  })
}
/**
 * 变更/补充合同查看合同编号列表
 * @institutionId 机构id
 */
export function getContractNo(institutionId) {
  return request({
    url: `contracts/getContractNo/${institutionId}`,
    method: 'GET'
  })
}
/**
 * 根据合同ID获取合同信息
 * @id 合同id
 */
export function getOldContract(id) {
  return request({
    url: `contracts/getOldContract/${id}`,
    method: 'GET'
  })
}
/**
 * 查询补充合同明细
 * @contractId 合同id
 */
export function getSupplement(contractId) {
  return request({
    url: `contracts/getSupplement/${contractId}`,
    method: 'GET'
  })
}
/**
 * 根据项目ID获取班型信息
 * @projectId 项目ID
 */
export function getClassTypes(projectId) {
  return request({
    url: `contracts/getClassTypes/${projectId}`,
    method: 'GET'
  })
}
