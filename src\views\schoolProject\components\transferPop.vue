<template>
  <el-dialog
    :visible.sync="transferFlag"
    :title="transferFlagTitle"
    :close-on-click-modal="!transferFlag"
    append-to-body
    width="60%"
  >
    <div class="add-transfer">
      <el-row>
        <el-col :sm="{span:24}" :md="{span:8}">
          <el-input
            v-model="listQuery.schoolId"
            placeholder="请输入转移校区的机构ID"
            class="filter-item"
          />
        </el-col>
        <el-col :sm="{span:24}" :md="{span:13}" style="margin-left: 15px;">
          <el-input
            v-model="listQuery.remark"
            placeholder="请输入转移校区的原因"
            class="filter-item"
            show-word-limit
            max-length="255"
          />
        </el-col>
        <el-col :sm="{span:24}" :md="{span:2}" class="transfer-btn">
          <el-button v-waves class="filter-item" type="primary" size="mini" @click.native="addTransfer">
            转移
          </el-button>
        </el-col>
      </el-row>
    </div>
    <el-table
      v-if="!isBatch"
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column type="index" />
      <af-table-column label="转移校区机构ID" show-overflow-tooltip prop="changeAfterSchool" />
      <af-table-column label="转移校区原因" show-overflow-tooltip prop="remark" />
      <af-table-column label="转移前校区" show-overflow-tooltip prop="changeBeforeSchoolName" />
      <af-table-column label="操作人" show-overflow-tooltip prop="operationUser" />
      <af-table-column label="转移时间" show-overflow-tooltip prop="operationTime" />
    </el-table>
  </el-dialog>
</template>

<script>
import { getTransferSchool, setTransferSchool } from '@/api/schoolCampus'
export default {
  name: 'TransferPop',
  data() {
    return {
      listLoading: true,
      total: 0,
      list: [],
      listQuery: {},
      transferFlagTitle: '',
      transferFlag: false,
      userProjectIds: null,
      isBatch: false // 是否批量转移  true 是  false 否
    }
  },
  methods: {
    getTransfer(userProjectId) {
      getTransferSchool(userProjectId).then(res => {
        if (res.code === '000000') {
          this.listLoading = false
          this.list = res.data || []
        }
      }).catch(() => {

      })
    },
    addTransfer() {
      if (this.listQuery.schoolId && this.userProjectIds && this.listQuery.remark) {
        const projectArr = Array.isArray(this.userProjectIds) ? this.userProjectIds : [this.userProjectIds]

        const params = Object.assign({}, this.listQuery, { userProjectIds: projectArr })
        this.$confirm('是否确定将学生转移至该校区', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          setTransferSchool(params).then(res => {
            if (res.code === '000000' && !this.isBatch) {
              this.getTransfer(this.userProjectIds)
            }
            this.listQuery = {}
            this.$emit('transferRefresh')
            // 批量关闭转移弹窗
            if (this.isBatch) {
              this.transferFlag = false;
            }
          }).catch(() => {

            this.listQuery = {}
          })
        }).catch(() => {
          this.$message({
            type: 'warning',
            message: '取消转移'
          })
        })
      } else if (!this.listQuery.schoolId) {
        this.$message({
          type: 'warning',
          message: '请输入校区机构ID'
        })
      } else if (!this.listQuery.remark) {
        this.$message({
          type: 'warning',
          message: '请输入转移原因'
        })
      }
    }
  }
}
</script>

<style scoped>
  .add-transfer{
     margin-bottom: 20px;
   }
  .transfer-btn{
    display: flex;
    justify-content: flex-end;
  }
</style>
