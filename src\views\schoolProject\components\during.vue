<template>
  <el-dialog :visible.sync="duringPop" :title="duringTitle" :close-on-click-modal="!duringPop" width="70%" @close="closedDur">
    <div class="filter-container">
      <el-input v-model="listQuery.searchField" placeholder="合伙人/手机号码" class="filter-item" style="width: 140px;" @input="refreshData" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 140px;" clearable @change="refreshData">
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="schoolDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="校区到期开始时间"
        end-placeholder="校区到期结束时间"
      />

      <el-button v-waves class="filter-item" type="primary" size="mini" :disabled="listLoading" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" :disabled="listLoading" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" :disabled="listLoading" @click="handleExport">
        导出
      </el-button>
    </div>
    <el-table v-loading="listLoading" :data="list" border fit stripe highlight-current-row @sort-change="sortChange">
      <af-table-column label="项目校区编号" prop="institutionCode" />
      <af-table-column label="客户名称（手机号）" width="200px">
        <template slot-scope="scope">
          <span v-if="scope.row.clueName">{{ scope.row.clueName }}</span>
          <span v-if="scope.row.cluePhone">({{ scope.row.cluePhone }})</span>
        </template>
      </af-table-column>
      <af-table-column label="签约区域">
        <template slot-scope="scope">
          <span v-if="scope.row.provinceName">{{ scope.row.provinceName }}</span>
          <span v-if="scope.row.cityName">{{ scope.row.cityName }}</span>
          <span v-if="scope.row.areaName">{{ scope.row.areaName }}</span>
          <span v-if="scope.row.countyName">{{ scope.row.countyName }}</span>
        </template>
      </af-table-column>
      <af-table-column label="加盟项目" prop="projectName" />
      <af-table-column label="发送次数" prop="remindNums" />
      <af-table-column label="是否已读" prop="isRead">
        <template slot-scope="scope">
          <span v-if="scope.row.isRead===1">已读</span>
          <span v-if="scope.row.isRead===0">未读</span>
        </template>
      </af-table-column>
      <af-table-column label="校区到期时间" prop="schoolEndDate" width="130" sortable="custom" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="160" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="expireType!==0&&(row.projectName!=='三陶AI'||row.projectName!=='加推项目')" type="primary" size="mini" @click="handleContract(row)">续约提醒</el-button>
          <el-button v-if="expireType===0" type="primary" size="mini" @click="renewHandover(row)">续约</el-button>
          <el-button v-if="expireType===0&&(row.projectName!=='三陶AI'||row.projectName!=='加推项目')" type="primary" size="mini" @click="handleDelay(row)">延期</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList(expireType)"
    />
    <delay-pop ref="delay" />
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import DelayPop from './delayPop'
import { getInstitutionList, remind } from '@/api/school-project'
import { converseEnToCn } from '@/utils/field-conver'
export default {
  components: {
    Pagination, DelayPop
  },
  props: {
    projectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      duringPop: false,
      duringTitle: '',
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        expireSort: 'desc'
      },
      schoolDate: [],
      listLoading: true,
      list: [],
      total: 0,
      expireType: null,
      showAppend: false,
      exportSetting: { // 导出按钮
        downloadLoading: false
      }
    }
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList(this.expireType)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.schoolDate = []
      this.getList(this.expireType)
    },
    handleExport() {
      this.exportSetting.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['项目校区编号', '客户名称', '签约区域', '加盟项目', '校区到期时间']
        const filterVal = ['institutionCode', 'clueName', 'provinceArea', 'projectName', 'schoolEndDate']
        this.listQuery = {
          pageIndex: 1,
          pageSize: 200
        }
        const params = Object.assign({}, this.listQuery, {
          expireType: this.expireType,
          schoolStartDate: this.schoolDate && this.schoolDate.length > 0 ? this.schoolDate[0] : '',
          schoolEndDate: this.schoolDate && this.schoolDate.length > 0 ? this.schoolDate[1] : ''
        })

        let list
        getInstitutionList(params).then(res => {
          if (res.code === '000000') {
            list = res.data.records || []
            list.forEach(item => {
              item.provinceArea = item.provinceName ? item.provinceName : '' + item.cityName ? item.cityName : '' + item.areaName ? item.areaName : '' + item.countyName ? item.countyName : ''
            })

            const data = this.formatJson(filterVal, list)
            excel.export_json_to_excel({
              header: tHeader,
              data,
              filename: this.duringTitle, // 文件名
              autoWidth: true, // 是否自动自适应宽度
              bookType: 'xlsx' // 导出的文件类型.xls
            })
            this.exportSetting.downloadLoading = false
          }
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'joinStatus') {
          return converseEnToCn(this.joinStatusList, v[j])
        } else {
          return v[j] || 0
        }
      }))
    },

    sortChange(e) {
      switch (e.order) {
        case 'ascending':
          this.listQuery.expireSort = 'asc'
          break
        case 'descending':
          this.listQuery.expireSort = 'desc'
          break
        default:
          this.listQuery.expireSort = ''
          break
      }
      this.getList(this.expireType)
    },
    handleContract(row) {
      this.$confirm('校区即将到期，是否给合伙人发送续约提醒', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        remind(row.agencyId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '续约提醒已发送!'
            })
            this.getList(this.expireType)
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },
    getList(type) {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery, {
        expireType: type,
        schoolStartDate: this.schoolDate && this.schoolDate.length > 0 ? this.schoolDate[0] : '',
        schoolEndDate: this.schoolDate && this.schoolDate.length > 0 ? this.schoolDate[1] : ''
      })
      getInstitutionList(params).then(res => {
        if (res.code === '000000') {
          this.list = res.data.records || []
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(error => {

      })
    },
    renewHandover(row) { // 续约
      if (this.$refs.delay.showAppend) this.$refs.delay.showAppend = false
      this.duringPop = false
      this.$router.push({
        name: 'HandoverRenew',
        query: {
          orderId: '',
          type: 'renew',
          name: row.clueName,
          pjId: row.projectId, // 项目id
          spId: row.id, // 校区项目id
          slId: row.schoolId // 校区id
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    handleDelay(row) {
      this.$refs.delay.delayPop = true
      this.$refs.delay.showAppend = true
      this.$refs.delay.schoolId = row.agencyId
      this.$refs.delay.institutionId = row.id
      this.$refs.delay.getDetail(row.id)
      this.$refs.delay.orderDelay = row.id
    },
    closedDur() {
      this.listQuery.searchField = null
      this.listQuery.projectId = null
      this.$emit('getExpireInstitutionNums')
    },
    refreshData(val) {

      this.$forceUpdate()
    }
  }

}
</script>

<style lang="scss" scoped>

</style>
