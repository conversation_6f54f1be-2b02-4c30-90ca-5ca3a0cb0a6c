// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.el-button--mini {
  padding: 7px 10px;
  min-width: 60px;
  border-radius: 14px;
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-dropdown-menu {
  a {
    display: block
  }
}

.el-dropdown .el-button {
  margin-left: 10px;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-card {
  border-radius: 2px;
  border:0;
  .el-card__header {
    padding: 18px 25px 10px;
    border-bottom: 0;
    color: #666666;
    font-size: 16px;
    font-weight: bolder;
  }
  .el-card__body {
    padding: 0 20px 20px 20px;
    .item {
      background-color: $cardBg;
      margin-bottom: 10px;
      padding: 16px 16px 4px 16px;
    }
    color: #999999;
  }
  .el-form-item__label {
    font-weight: normal;
  }
  .el-form-item {
    margin-bottom: 2px;
  }
  .item-header {
    margin-bottom: 8px;
    font-size: 14px;
  }
}
.el-dropdown .el-button {
  margin-left: 0;
}
.el-dialog__headerbtn{
    top: 15px;
  }
  .el-dialog__headerbtn .el-dialog__close{
    font-size: 28px;
  }
