<template>
  <div>
    <div v-for="(item,index) in questionList" :key="index" class="questions-list">
      <div class="questions-list-title">
        <div>
          <span class="questions-title-left">
            <em>第</em>
            <em>{{ index+1 }}</em>
            <em>题</em>
          </span>
          <el-tag type="warning">选择题</el-tag>
        </div>
        <div class="questions-btns">
          <el-button type="primary" size="mini">排序</el-button>
          <el-button type="danger" size="mini">删除</el-button>
        </div>
      </div>
      <div v-show="item.isShow" class="questions-knowledge">
        <h2>{{ item.title }}</h2>
        <p>{{ item.info }}</p>
        <div class="questions-knowledge-list"><i>知识点:</i><el-tag v-for="itemKnowledge in item.knowledge" :key="itemKnowledge.id" type="info" size="mini">{{ itemKnowledge.title }}</el-tag></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionInfo'
}
</script>

<style scoped>

</style>
