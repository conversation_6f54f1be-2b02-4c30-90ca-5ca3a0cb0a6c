<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.realName"
        placeholder="员工姓名"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.regionId" placeholder="省" filterable clearable style="width: 150px;">
        <el-option value="">全部</el-option>
        <el-option v-for="item in sheng" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:opera:add']" class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <el-table-column label="员工姓名" show-overflow-tooltip prop="realName" />
      <el-table-column label="手机号" prop="mobile" show-overflow-tooltip />
      <el-table-column label="项目-省份">
        <template slot-scope="scope">
          <div v-if="scope.row.operationAuths&&scope.row.operationAuths.length>0">
            <p v-for="(item,i) in scope.row.operationAuths" :key="i">
              <span>{{ item.projectName }}-</span>
              <span>{{ item.regionName }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :formatter="getOperaStatus" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:opera:edit']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['setting:opera:enable']" type="primary" size="mini" @click="handleEnable(row)">{{ row.status===1?'停用':'启用' }}</el-button>
          <el-button v-permission="['setting:opera:delet']" type="primary" size="mini" @click="operaDel(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <opera-pop ref="operas" :sheng="sheng" :project-list="projectList" @refresh="getList" />
  </div>
</template>
<script>
import { operatorsList, operatorsDel, operatorsEnable } from '@/api/system-setting'
import { operatorsStatus, converseEnToCn } from '@/utils/field-conver'
import { getAllProject, getArea } from '@/api/common'
import Pagination from '@/components/Pagination'
import operaPop from './components/operaPop'
export default {
  name: 'Operating',
  components: {
    Pagination,
    operaPop
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      operatorsStatus: operatorsStatus,
      projectList: [],
      sheng: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getProject()
      this.getsheng()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)
      await operatorsList(params).then(res => {
        if (res.code === '000000') {
          that.list = res.data.records
          that.total = res.data.total
          that.listLoading = false
        }
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getOperaStatus(row) {
      return converseEnToCn(this.operatorsStatus, row.status)
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    // 获取省
    getsheng() {
      const _this = this
      getArea(0).then(res => {
        _this.sheng = res.data // 将获取的数据赋值
      }).catch(err => {

      })
    },
    operaDel(row) {
      this.$confirm('确定要删除此条数据吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        operatorsDel(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    handleEnable(row) {
      const title = row.status === 1 ? '确定要停用该运营经理吗?' : '确定要启用该运营经理吗?'
      const tips = row.status === 1 ? '停用成功' : '启用成功'
      this.$confirm(`${title}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        operatorsEnable(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${tips}`
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    handleAdd() {
      this.$refs.operas.operaPopFlag = true
      this.$refs.operas.operaTitle = '运营经理授权'
      this.$refs.operas.subFlag = 'create'
      this.$refs.operas.getEnableUser()
    },
    handleUpdate(row) {
      this.$refs.operas.operaPopFlag = true
      this.$refs.operas.operaTitle = '修改运营经理授权'
      this.$refs.operas.subFlag = 'update'
      this.$refs.operas.getEnableUser()
      this.$refs.operas.getOperaDetail(row.id)
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
