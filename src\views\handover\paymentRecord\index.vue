<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.orderCode" placeholder="订单编号" class="filter-item" style="width: 140px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.clueCode" placeholder="客户编号" class="filter-item" style="width: 140px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.schoolCode" placeholder="校区编号" class="filter-item" style="width: 140px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.institutionCode" placeholder="校区项目编号" class="filter-item" style="width: 140px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.searchField" placeholder="合伙人/手机号/校区名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.projectId" placeholder="加盟项目" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.auditStatus" placeholder="审批状态" filterable class="filter-item" style="width: 180px;" clearable @keyup.enter.native="handleFilter">
        <el-option v-for="item in activeUsers" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.tradeType" placeholder="打款类型" class="filter-item" style="width: 150px;" clearable filterable>
        <el-option v-for="item in trades" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.payType" placeholder="收付方式" class="filter-item" style="width: 150px;" clearable filterable>
        <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="payTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="收付开始日期"
        end-placeholder="收付结束日期"
        unlink-panels
      />
      <el-date-picker
        v-model="createTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="创建开始日期"
        end-placeholder="创建结束日期"
        unlink-panels
      />
      <area-picker :area-list="areaList" level="'3'" area-style="width:350px" class="filter-item" @getAreaList="getAreaList" />
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="收付编号" prop="payRecordCode">
        <template slot-scope="scope">
          <span class="link-type" @click="collectionDetail(scope.row)">{{ scope.row.payRecordCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="订单编号">
        <template slot-scope="scope">
          <span class="link-type" @click="toDetailHandover(scope.row)">{{ scope.row.orderCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="客户编号" prop="clueCode" />
      <af-table-column label="客户名称" prop="clueName" align="center" />
      <af-table-column label="校区编号" prop="schoolCode" align="center" />
      <af-table-column label="校区名称" prop="schoolName" align="center" />
      <af-table-column label="校区项目编号" prop="institutionCode" align="center" />
      <af-table-column label="加盟项目" prop="projectName" align="center" />
      <el-table-column label="审批状态" prop="auditStatus" :formatter="getAuditStatus" min-width="120" />
      <af-table-column label="打款类型" prop="tradeTypeName" />
      <af-table-column label="收付金额" prop="payAmount" />
      <af-table-column label="签约主体" prop="signPartyName" />
      <af-table-column label="收付方式" prop="payTypeName" />
      <af-table-column label="收付时间" prop="payTime" :formatter="dateFormat" />
      <af-table-column label="交易流水号" prop="transactionNo" />
      <af-table-column label="备注" prop="remark" show-overflow-tooltip width="300" />
      <af-table-column label="创建时间" prop="createTime" :formatter="dateFormat" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right" min-width="200">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus == 20" v-permission="['order:paymentRecord:exmamine']" type="primary" size="mini" @click="handleView(row)">
            审核
          </el-button>
          <el-popconfirm
            title="确定删除吗？"
            v-permission="['order:paymentRecord:remove']"
            @confirm="handleDel(row)"
          >
            <el-button  slot="reference"  type="danger" size="mini">
              财务强删
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <payment-record-detail ref="paymentRecordDetail" @refresh="getList" />
    <collect-detail ref="collect" />
  </div>
</template>

<script>
import { forceDelPaymentRecord, getPaymentRecordList } from '@/api/payment'
import Pagination from '@/components/Pagination'
import { payMethod, converseEnToCn, auditStatus, businessList } from '@/utils/field-conver'
import { getPayType, getAllProject } from '@/api/common'
import paymentRecordDetail from './detail'
import CollectDetail from './collectDetail'
import AreaPicker from '@/components/area-picker'
export default {
  name: 'PaymentRecord',
  components: { Pagination, AreaPicker, paymentRecordDetail, CollectDetail },
  directives: {},
  data() {
    return {
      payTime: [],
      createTime: [],
      listLoading: false,
      payTypes: [],
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        orderCode: '',
        clueCode: '',
        schoolCode: '',
        institutionCode: '',
        searchField: '',
        projectId: '',
        auditStatus: '',
        tradeType: '',
        payType: '',
        startTime: '',
        endTime: '',
        createStartTime: '',
        createEndTime: ''
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      parentDepartmentList: [],
      auditStatus: [],
      payTime: '',
      payMethod: [],
      businessList: businessList,
      trades: [],
      projectList: []
    }
  },
  computed: {
    activeUsers: function() {
      return this.auditStatus.filter(function(user) {
        return user.value !== 10
      })
    }
  },
  created() {

  },
  mounted() {
    if (this.$route.query.orderCode) {
      this.$set(this.listQuery, 'orderCode', this.$route.query.orderCode);
      this.handleFilter();
    }
    this.getList()
    this.getAccountType3('pay_type')
    this.getProject()
    this.tradeList('trade_type')
    this.auditStatus = auditStatus
    this.payMethod = payMethod
  },
  methods: {
    getAccountType3(str) {
      const that = this
      getPayType(str).then(res => {
        that.payTypes = res.data
      })
    },
    handleDel(row){
      forceDelPaymentRecord(row.id).then(
        res => {
          if (res.code === '000000') {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.getList()
          }
        }
      )
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1;
      if (this.payTime && this.payTime.length > 0) {
        this.listQuery.startTime = this.payTime[0]
        this.listQuery.endTime = this.payTime[1]
      }
      else {
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
      if (this.createTime && this.createTime.length > 0) {
        this.listQuery.createStartTime = this.createTime[0]
        this.listQuery.createEndTime = this.createTime[1]
      }
      else {
        this.listQuery.createStartTime = ''
        this.listQuery.createEndTime = ''
      }
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        orderCode: '',
        clueCode: '',
        schoolCode: '',
        institutionCode: '',
        searchField: '',
        projectId: '',
        auditStatus: '',
        tradeType: '',
        payType: '',
        startTime: '',
        endTime: '',
        createStartTime: '',
        createEndTime: ''
      },
      this.payTime = [];
      this.createTime = [];
      this.areaList = {}
      this.getList()
    },
    dateFormat(row, column) {
      const daterc = row[column.property]
      if (daterc != null) {
        const dateMat = new Date(daterc)
        const year = dateMat.getFullYear()
        const month = dateMat.getMonth() + 1
        const day = dateMat.getDate()
        const timeFormat = year + '-' + month + '-' + day
        return timeFormat
      }
    },
    getList() {
      const that = this
      that.listLoading = false
      if (that.payTime) {
        that.listQuery.startTime = this.payTime[0]
        that.listQuery.endTime = this.payTime[1]
      } else {
        that.listQuery.startTime = ''
        that.listQuery.endTime = ''
      }
      const params = Object.assign(that.listQuery, that.areaList)
      getPaymentRecordList(params).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
     * 查看详细信息
     * */
    handleView(row) {
      this.$refs.paymentRecordDetail.getDetail(row)
    },
    getPayMethod(row) {
      return converseEnToCn(payMethod, row.payMethod)
    },
    getAuditStatus(row) {
      return converseEnToCn(auditStatus, row.auditStatus)
    },
    collectionDetail(row) {
      this.$refs.collect.collectFlag = true
      this.$refs.collect.getDetail(row.id)
    },
    toDetailHandover(row) {
      if (row.businessType === 4) {
        this.$router.push({
          name: 'CustomerPurchase',
          query: {
            spId: row.orderId, // 校区项目id
            type: 'details',
            name: row.clueName
          }
        })
      } else {
        this.$router.push({
          name: 'HandoverDetail',
          query: {
            name: row.clueName,
            orderId: row.orderId
          }
        })
      }
    },
    tradeList(str) { // 打款类型
      const that = this
      getPayType(str).then(res => {
        that.trades = res.data && res.data.length > 0 ? res.data.filter(item => item.itemValue !== -1) : []
      })
    },
    /**
       * 项目列表
       * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    }
    // toDetailHandover(row) {
    //   this.$refs.paymentRecordDetail.getList(row)
    // }
  }
}
</script>

<style scoped>

</style>
