<template>
  <!--  dialog弹窗编辑或新增页面-->
  <el-dialog
    title="学业诊断设置"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :modal="true"
    :modal-append-to-body="true"
  >
    <el-row :gutter="10" v-loading="loading">
      <el-col :span="22">
        <el-form :model="form" ref="formRef"
                 size="mini"
                 label-width="160px"
                 label-position="right">
          <el-form-item label="余额:">
            <el-input v-model="balance" type="number" disabled />
          </el-form-item>
          <el-form-item label="充值金额:" prop="amount">
            <el-input v-model="form.amount" placeholder="请输入金额" type="number" />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" round>取 消</el-button>
      <el-button type="primary" @click="handleSubmit" round>确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { youXuePingCeRecharge, youXuePingCeBalance } from '@/api/schoolCampus'

export default {
  name: 'SYGXDialog',
  data() {
    return {
      dialogVisible: true,
      form: {
        amount: 0,
      },
      balance: 0,
      loading: false
    }
  },
  props: {
    schoolId: {
      type: [String, Number],
      required: true
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.loading = true 
      youXuePingCeBalance({ schoolId: this.schoolId }).then(res => {
        if (res.code === SUCCESS) {
          this.balance = res.data
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleSubmit() {
      const data = Object.assign({}, this.form, { schoolId: this.schoolId })
      youXuePingCeRecharge(data).then(res => {
        if (res.code === SUCCESS) {
          this.$message({
            message: '设置成功',
            type: 'success'
          })
          this.handleClose()
        }
      })
    },
    handleClose(done) {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">
.el-tag {
  margin-right: 10px;
}

.button-new-tag {
  height: 26px;
  line-height: 26px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 120px;
  vertical-align: bottom;
}
</style>
