<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.outlineName"
        placeholder="大纲/章"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.classTypeId" placeholder="班型名称" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in allClassType" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.materialId" placeholder="教材版本" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in materialsList" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select
        v-model="listQuery.subjectId"
        placeholder="科目名称"
        clearable
        class="filter-item"
        style="width: 140px;"
      >
        <el-option v-for="item in subjectsAll" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="大纲状态"
        clearable
        class="filter-item"
        style="width: 140px;"
      >
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:outline:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <el-table-column label="大纲编号" show-overflow-tooltip prop="id" style="width: 100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </el-table-column>
      <af-table-column label="大纲/章" show-overflow-tooltip prop="title" />
      <af-table-column label="班型名称" prop="classTypeName" show-overflow-tooltip />
      <af-table-column label="教材版本" prop="materialName" show-overflow-tooltip />
      <el-table-column label="科目" prop="subjectName" width="100px" />
      <el-table-column label="大纲状态" prop="status" :formatter="getJoinStatusCN" width="80px" />
      <el-table-column label="大纲排序" prop="sort" width="80px" />
      <el-table-column label="创建时间" prop="createTime" width="180px" />
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp"
        width="230"
      >
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:outline:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
          <el-button v-permission="['curriculum:outline:opera']" type="primary" size="mini" @click="enable(row)">{{ row.status===1?'禁用':'启用' }}</el-button>
          <el-button v-permission="['curriculum:outline:examination']" type="primary" size="mini" @click="handleExamination(row)">考点管理</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改教材弹框-->
    <outline-pop ref="outline" @addOutLineList="getList" />
    <examination-pop ref="examination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import OutlinePop from './components/outlinePop'
import ExaminationPop from './components/examinationPop'
import { enableList, converseEnToCn } from '@/utils/field-conver'
import { getAllSubjects, outlineList, outlineEnable, getAllClassType, materialsList } from '@/api/classType'
export default {
  name: 'Outline',
  components: {
    Pagination,
    OutlinePop,
    ExaminationPop
  },
  data() {
    return {
      list: [
        {
          ids: 34
        }
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      enableList: enableList,
      subjectsAll: [],
      followDate: [],
      allClassType: [],
      materialsList: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getAllSubjects()
      this.getAllClassType()
      this.getMaterials()
    })
  },
  methods: {
    getAllSubjects() {
      getAllSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsAll = res.data
        }
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })
      await outlineList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.outline.outlinePop = true
      this.$refs.outline.outlineTitle = '新增大纲'
      this.$refs.outline.isEdit = false
      this.$refs.outline.listQuery.status = 1
    },
    getAllClassType() { // 获取可用班型
      getAllClassType().then(res => {
        this.allClassType = res.data
      })
    },
    getMaterials() {
      materialsList().then(res => {
        if (res.code === '000000') {
          this.materialsList = res.data
        }
      })
    },
    handleUpdate(row) {
      this.$refs.outline.outlinePop = true
      this.$refs.outline.outlineTitle = `修改${row.title}`
      this.$refs.outline.getOutLine(row.id)
      this.$refs.outline.isEdit = false
    },
    handleExamination(row) {
      this.$refs.examination.outlinePop = true
      this.$refs.examination.outlineId = row.id
      this.$refs.examination.getList()
      this.$refs.examination.title = row.title
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    enable(row) { // 启用/禁用
      const title = row.status === 1 ? '禁用此项会造成引用此项的数据显示异常，确认删除?' : '确定要启用吗?'
      const tips = row.status === 1 ? '禁用成功' : '启用成功'
      this.$confirm(`${title}`, {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(() => {
        outlineEnable(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${tips}`
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    getDetail(row) {
      this.$refs.outline.outlinePop = true
      this.$refs.outline.outlineTitle = `${row.title}详情`
      this.$refs.outline.getOutLine(row.id)
      this.$refs.outline.isEdit = true
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
