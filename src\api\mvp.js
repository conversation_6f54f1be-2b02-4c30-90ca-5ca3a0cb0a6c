import request from '@/utils/request'
/**
 * 订单详情查询接口
 * @param data
 */
export function getCustomerList(params) {
  return request({
    url: 'customerClues/page',
    method: 'get',
    params: params
  })
}
/**
 * 获取机构列表
 * @param data
 */
export function getMechanismList(params) {
  return request({
    url: 'agencies/page',
    method: 'get',
    params: params
  })
}
/**
 * 客户资源指派/批量
 * @param data
 */
export function custormAssign(params) {
  return request({
    url: 'customerClues/assign',
    method: 'PUT',
    data: params
  })
}
/**
 * 添加家长线索
 * @param data
 */
export function customerClues(params) {
  return request({
    url: 'customerClues',
    method: 'POST',
    data: params
  })
}
/**
 * 导出数据
 * @param data
 */
export function exportCustorm(params) {
  return request({
    url: 'customerClues/export',
    method: 'GET',
    params: params
  })
}
/**
 * 交易列表订单详情查询接口
 * @param data
 */
export function getransactionList(params) {
  return request({
    url: 'userOrders/page',
    method: 'GET',
    params: params
  })
}
/**
 * 交易记录结算
 * @param data
 */
export function settlement(data) {
  return request({
    url: 'userOrders/settlement',
    method: 'PUT',
    data: data
  })
}
/**
 * 交易记录批量结算
 * @param data
 */
export function batchPayment(data) {
  return request({
    url: 'userOrders/batchSettlement',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取物流批量标记/标记的商品信息
 * @param data
 */
export function getBatchGoods(data) {
  return request({
    url: 'userOrders/deliveries/product?orderIds=' + data,
    method: 'GET'
  })
}
/**
 * 物流批量标记/标记
 * @param data
 */
export function markPost(data) {
  return request({
    url: 'userOrders/markPost',
    method: 'PUT',
    data: data
  })
}
