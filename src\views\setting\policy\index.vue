<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.policyName" placeholder="套餐名称" clearable class="filter-item" style="width: 200px;" />
      <el-select v-model="listQuery.businessType" placeholder="业务类型" filterable class="filter-item" style="width: 150px;" clearable>
        <el-option
          v-for="item in businessTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.projectId" placeholder="关联项目" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in projectList"
          :key="item.id"
          :label="item.projectName"
          :value="item.id"
        />
      </el-select>
      <el-select v-model="listQuery.valid" placeholder="套餐状态" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in policyStatus"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:employee:create']" size="mini" class="filter-item" type="primary" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="套餐名称" prop="policyName">
        <template slot-scope="scope">
          <span class="policy-name" @click="handleQuery(scope.row)">{{ scope.row.policyName }}</span>
        </template>
      </af-table-column>
      <af-table-column label="套餐价格" prop="price" />
      <af-table-column label="套餐关联项目" prop="projectName" />
      <af-table-column label="业务类型" prop="businessType" :formatter="getBusinessTypeList" />
      <el-table-column label="套餐状态" prop="valid" width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.valid===1">已启用</span>
          <span v-if="scope.row.valid===0">已禁用</span>
        </template>
      </el-table-column>
      <el-table-column label="套餐备注" prop="remark" width="300px" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <!-- <el-button v-permission="['setting:policy:view']" type="primary" size="mini" @click="handleQuery(row)">
            查看
          </el-button> -->
          <el-button v-permission="['setting:policy:edit']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['setting:policy:delete']" type="primary" size="mini" @click="handleDelete(row)">
            删除
          </el-button>
          <el-button v-permission="['setting:policy:enable']" type="primary" size="mini" @click="handleEnable(row)">
            {{ row.valid===1?'禁用':'启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <policy-detail ref="policy" @refresh="getList" />
  </div>
</template>

<script>
import { getPolicyList, deletePolicy, enablePolicy } from '@/api/policy'
import Pagination from '@/components/Pagination'
import PolicyDetail from './detail'
import { businessTypeList, converseEnToCn } from '@/utils/field-conver'
import { getAllProject } from '@/api/common'
export default {
  name: 'Employee',
  components: { Pagination, PolicyDetail },
  directives: {},
  data() {
    return {
      listLoading: false,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      list: [],
      businessTypeList: businessTypeList,
      total: 0,
      projectList: [],
      policyStatus: [
        {
          id: 1,
          title: '已启用'
        },
        {
          id: 0,
          title: '已禁用'
        }
      ]
    }
  },
  created() {
    this.getProject()
    this.getList()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      const that = this
      getPolicyList(that.listQuery).then(res => {
        if (res.code === '000000') {
          that.list = res.data.records
          that.total = res.data.total
        }
      })
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    /**
     * 更多按钮
     * */
    handleCommand(command) {
      switch (command.type) {
        case 'a':
          // 授权
          this.handleSetEmployeePermission(command.row.id)
          break
      }
    },
    beforeHandleCommand(type, row) {
      return {
        'type': type,
        'row': row
      }
    },
    handleAdd() {
      this.$refs.policy.createNewPolicy('create')
    },
    /**
     * 查看详情
     * */
    handleQuery(row) {
      this.$refs.policy.getDetail(row.id, false)
    },
    /**
     * 修改
     * */
    handleUpdate(row) {
      this.$refs.policy.getDetail(row.id, 'update')
    },
    /**
     * 删除
     * */
    handleDelete(row) {
      this.$confirm('是否确认删除该套餐', '提示？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deletePolicy(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(action => {

      })
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    getBusinessTypeList(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(businessTypeList, isNaN(data) ? data.businessType : data)
    },
    handleEnable(row) {
      const that = this
      that.$confirm(`${row.valid === 1 ? '确定要禁用吗?' : '确定要启用吗?'}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enablePolicy(row.id).then(res => {
          if (res.code === '000000') {
            that.$message({
              message: `${row.valid === 1 ? '禁用成功' : '启用成功'}`,
              type: 'success'
            })
            that.getList()
          }
        }).catch(error => {

        })
      }).catch(() => {
        that.$message({
          message: '取消操作',
          type: 'warning'
        })
      })
    }
  }
}
</script>

<style scoped>
.policy-name{
  font-weight: bold;
  color: #46a6ff;
  cursor: pointer;
}
</style>
