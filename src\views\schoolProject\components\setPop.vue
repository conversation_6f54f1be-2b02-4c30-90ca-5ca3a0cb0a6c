<template>
  <el-dialog :visible.sync="setPop" :title="setTitle" :close-on-click-modal="!setPop" width="30%" @close="cancelClass">
    <el-form label-width="90px">
      <el-form-item label="播放源设置">
        <el-select v-model="playSource" placeholder="默认" filterable clearable class="filter-item">
          <el-option v-for="item in playSources" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <div class="assign-operas">
        <el-button type="infor" size="mini" @click="cancelClass()">取消</el-button>
        <el-button type="primary" size="mini" @click="custormClass">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import {
  playSources
} from '@/utils/field-conver'
import { setUpPlay } from '@/api/schoolCampus'
export default {
  name: 'ResetPop',
  data() {
    return {
      setPop: false,
      setTitle: '播放源设置',
      assignSatuts: [],
      playSource: 0,
      playSources: playSources,
      schoolId: ''
    }
  },
  mounted() {
    this.$nextTick(() => {

    })
  },
  methods: {
    cancelClass() {
      this.setPop = false
      this.schoolId = ''
    },
    custormClass() {

      if (this.schoolId && (this.playSource || this.playSource === 0)) {
        setUpPlay(this.playSource, this.schoolId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '设置成功'
            })
            this.setPop = false
            this.schoolId = ''
            this.$emit('updatePlayer')
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择播放源设置'
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
