<template>
  <div class="app-container story-add">
    <el-row>
      <el-form id="story-add" :model="listQuery" label-width="80px">
        <el-col :sm="{span:24}" :md="{span:24}" class="relation-list">
          <div>
            <div class="story-info">
              <div class="course-title">
                <div id="error">
                  <!-- Banner图PC端 -->
                  <h4 class="common-title pd10">
                    <span>Banner图(PC端)：</span>
                    <el-select v-model="valuePC" filterable clearable placeholder="请选择类型(PC端)" style="width:200px;dispaly:inline-block;" @change="typeSelect( 'pc' )">
                      <el-option v-for="item in optionsPC" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </h4>
                  <div class="story-pics" style="min-height:120px;">
                    <div v-if="bannerPcImg.length<5" class="upload-btn" @click="uploadImg( 'pc' )">
                      <i class="el-icon-plus" />
                    </div>
                    <div v-for="(item,index) in bannerPcImg" :key="index" class="drage-img">
                      <img :src="item.imagePath" class="title-img">
                      <p class="img-opera">
                        <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,item.imagePath)" />
                        <span class="el-icon-delete" @click.stop="delImg(index,item.id,'pc')" />
                      </p>
                    </div>
                    <p class="tips">(至多5张图片,图片尺寸1920*450)</p>
                  </div>
                </div>
                <div id="error">
                  <!-- Banner图PC端 -->
                  <h4 class="common-title pd10">
                    <span>Banner图(移动端)：</span>
                    <el-select v-model="valueMobile" filterable clearable placeholder="请选择类型(移动端)" style="width:200px;dispaly:inline-block;" @change="typeSelect( 'mobile' )">
                      <el-option v-for="item in optionsMobile" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </h4>
                  <div class="story-pics" style="min-height:120px;">
                    <div v-if="bannerPcImg.length<5" class="upload-btn" @click="uploadImg( 'mobile' )">
                      <i class="el-icon-plus" />
                    </div>
                    <div v-for="(item,index) in bannerMobileImg" :key="index" class="drage-img">
                      <img :src="item.imagePath" class="title-img">
                      <p class="img-opera">
                        <span class="el-icon-zoom-in" @mousedown.stop="amplification($event,item.src)" />
                        <span class="el-icon-delete" @click.stop="delImg(index,item.id,'mobile')" />
                      </p>
                    </div>
                    <p class="tips">(至多5张图片,图片尺寸750*360)</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <!-- <el-col class="ml15">
          <el-button type="default" size="mini" @click="cancelStory">取消</el-button>
          <el-button type="primary" size="mini" @click="confirmStory">保存</el-button>
        </el-col> -->
      </el-form>
    </el-row>
    <!-- 批量上传图片弹框 -->
    <batch-opera ref="batchStory" @getimgPc="getimgPc" @getimgMobile="getimgMobile" />
    <!-- 图片放大弹框 -->
    <div v-if="amplificationImg" class="amplification">
      <img :src="amplificationImg">
      <span class="el-icon-circle-close amplification-closed" @click="amplificationImg=''" />
    </div>
  </div>
</template>
<script>
import batchOpera from './component/batchOpera'
import { getShowBanner, removeBanner } from '@/api/website'
export default {
  name: 'Banner',
  inject: ['reload'],
  components: { batchOpera },
  data() {
    return {
      listQuery: {},
      amplificationImg: '',
      bannerPcImg: [],
      bannerMobileImg: [],
      optionsPC: [
        { value: '1', label: '首页' },
        { value: '2', label: '产品体系-普高' },
        { value: '3', label: '产品体系-烨晨' },
        { value: '4', label: '产品体系-单词' },
        { value: '5', label: '微课堂' },
        { value: '6', label: '走进三陶' },
        { value: '7', label: '企业文化' },
        { value: '8', label: '加入我们' }
      ],
      optionsMobile: [
        { value: '1', label: '首页' },
        // { value: '5', label: '微课堂' },
        { value: '6', label: '走进三陶' },
        { value: '7', label: '企业文化' },
        { value: '8', label: '加入我们' }
      ],
      valuePC: '',
      valueMobile: ''
    }
  },
  watch: {
  },
  mounted() {

  },
  methods: {
    typeSelect(device) {
      let terminal, type
      if (device === 'pc') {
        terminal = 1; type = this.valuePC; this.bannerPcImg = []
      } else { terminal = 2; type = this.valueMobile; this.bannerMobileImg = [] }
      // 查询按照类型获取可展示的banner
      getShowBanner(terminal, type).then(res => {
        if (res.code === '000000') {
          if (device === 'pc') {
            this.bannerPcImg = res.data
          } else { this.bannerMobileImg = res.data }
        }
      }).catch(() => {
        this.$message({
          type: 'error',
          message: '查询banner失败'
        })
      })
    },
    uploadImg(imgFlag) {
      if (imgFlag === 'pc') {
        if (!this.valuePC) {
          this.$message({ type: 'error', message: '先选择类型(PC端)' })
          return false
        }
      } else {
        if (!this.valueMobile) {
          this.$message({ type: 'error', message: '先选择类型(移动端)' })
          return false
        }
      }
      this.$refs.batchStory.storyFlag = true
      this.$refs.batchStory.imgFlag = imgFlag
      this.$refs.batchStory.imgList = []
      this.$refs.batchStory.selectType = imgFlag === 'pc' ? this.valuePC : this.valueMobile
      this.$refs.batchStory.bannerPcImg = imgFlag === 'pc' ? this.bannerPcImg : []
      this.$refs.batchStory.bannerMobileImg = imgFlag === 'mobile' ? this.bannerMobileImg : []
    },
    getimgPc(imgs) {
      this.bannerPcImg.push(...imgs)
    },
    getimgMobile(imgs) {
      this.bannerMobileImg.push(...imgs)
    },
    amplification(e, urls) {
      this.amplificationImg = urls
    },
    delImg(index, id, type) {
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        removeBanner(id).then(res => {
          if (res.code === '000000') {
            this.$message({ type: 'success', message: '删除图片成功' })
            if (type === 'pc') {
              this.bannerPcImg.splice(index, 1)
            } else {
              this.bannerMobileImg.splice(index, 1)
            }
          }
        }).catch(() => {
          this.$message({ type: 'error', message: '删除图片失败' })
        })
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.relation-list{
    margin-bottom: 20px;
    margin-left: 15px;
    .common-title{
        padding-bottom: 10px;
        span{
            color: #606266;
            font-size: 16px;
            font-weight: bold;
        }
        em{
            color: #A09F9F;
            font-size: 14px;
        }

    }
}
.story-pics{
  width: 100%;
  .upload-btn{
    float: left;
    width: 120px;
    height: 120px;
    line-height: 108px;
    border-radius: 4px;
    border:1px #108FFB dashed;
    text-align: center;
    cursor: pointer;
    i{
        color: #108FFB;
        font-size: 20px;
    }
  }
  .tips{
    padding-top: 8px;
    clear: both;
    font-size: 12px;
    color: #ed5a43;
  }
}
.drage-img{
  position: relative;
  display: inline-block;
  width:120px;
  height:120px;
  margin-right: 10px;
  margin-bottom: 15px;
}
.title-img{
  width: 120px;
  height: 120px;
  border-radius: 4px;
  border:1px #E9E9E9 solid;
}

  .img-opera{
    position:absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 30px;
    padding-top: 6px;
    background: rgba($color: #000000, $alpha: 0.6);
    display: flex;
    align-content: center;
    justify-content: center;
    span{
      font-size: 20px;
      color: #fff;
      cursor: pointer;
      &:first-child{
        padding-right: 15px;
      }
    }
  }
.amplification{
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: 0.6);
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  img{
    width: 60%;
    // height: 600px;
    align-items: center;
  }
  .amplification-closed{
    position: absolute;
    top: 12%;
    right: 20%;
    color: #fff;
    font-size: 50px;
    cursor: pointer;
  }
}

.pd10{
  padding-top: 10px;
}
#one{
  margin-bottom: 10px;
}
#couse-title{
  margin-bottom: 10px;
}
#correct{
  margin-bottom: 10px;
}
#error{
  margin-bottom: 10px;
}
.ml15{
  margin-left: 15px;
}

</style>

