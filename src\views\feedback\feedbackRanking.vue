<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.feedbackType" placeholder="反馈类型" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in feedbackList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.beginTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="开始时间"
      />
      <el-date-picker
        v-model="listQuery.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="getRank">
        分析
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="resetRank">
        重置
      </el-button>
    </div>
    <div id="main" ref="myEchart" :style="{ width: '100%', height: '350px', marginTop: '-1.0rem' }" />
  </div>
</template>

<script>
import { feedbackList } from '@/utils/field-conver'
import { feedbackRanking } from '@/api/feedback'
import echarts from 'echarts'
export default {
  name: 'FeedbackRanking',
  data() {
    return {
      listQuery: {},
      feedbackList: feedbackList,
      feedbackType: ['运营', '课程', '系统', '测试', '客服', '反应'],
      rankNum: [10, 20, 30, 45, 78, 98]
    }
  },
  watch: {
    feedbackType(val) {
      this.drawPie('main')
    },
    rankNum(val) {
      this.drawPie('main')
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.getRank()
      this.drawPie('main')
      this.init()
    })
  },
  methods: {
    drawPie(id) {
      const that = this
      var feedbackType = that.feedbackType
      var rankNum = that.rankNum
      that.charts = echarts.init(document.getElementById(id))
      that.charts.setOption({
        color: ['#2196f3', '#e45d52', '#66c68e', '#80428a', '#bf6c2e', '#58ad58'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: feedbackType,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#3c4751'
              }
            }
          }
        ],
        yAxis: [
          {
            min: 0,
            max: 100,
            type: 'value',
            axisLabel: {
              show: true,
              formatter: '{value}%',
              textStyle: {
                color: '#3c4751'
              }
            }
          }
        ],
        series: [
          {
            name: '满意度',
            type: 'bar',
            barWidth: '50%',
            data: rankNum,
            itemStyle: {
              normal: {
                color: function(params) {
                  var colorList = [
                    '#f6695e', '#2196f3', '#60c289', '#9950a5', '#b1ad37',
                    '#FE8463', '#9BCA63', '#FAD860', '#F3A43B', '#60C0DD',
                    '#D7504B', '#C6E579', '#F4E001', '#F0805A', '#26C0C0'
                  ]
                  return colorList[params.dataIndex]
                },
                label: {
                  show: true,
                  position: 'top',
                  formatter: '{b}\n{c}%'
                }
              }
            }
          }
        ]
      })
    },
    init() {
      const self = this // 因为箭头函数会改变this指向，指向windows。所以先把this保存
      setTimeout(() => {
        window.onresize = function() {
          self.chart = echarts.init(self.$refs.myEchart)
          self.chart.resize()
        }
      }, 20)
    },
    getRank() { // 1:运营,2:市场,3,课程,4:系统,5:智能终端,6:三陶AI,7:其他
      const data = Object.assign({}, this.listQuery)
      feedbackRanking(data).then(res => {
        if (res.code === '000000') {
          const arrs = []
          res.data.forEach(item => {
            const obj = {}
            switch (item.category) {
              case 1:obj['type'] = '运营'; break
              case 2:obj['type'] = '市场'; break
              case 3:obj['type'] = '课程'; break
              case 4:obj['type'] = '系统'; break
              case 5:obj['type'] = '智能终端'; break
              case 6:obj['type'] = '三陶AI'; break
              case 7:obj['type'] = '其他'; break
              case 8:obj['type'] = '烨晨课程'; break
              case 9:obj['type'] = '艺考课程'; break
              default:obj['type'] = '其他'
            }
            obj['rank'] = item.rank
            obj['satisfiedCount'] = item.satisfiedCount
            obj['satisfiedPercent'] = item.satisfiedPercent
            obj['totalCount'] = item.totalCount
            arrs.push(obj)
          })
          this.feedbackType = arrs.map(item => item.type)
          this.rankNum = arrs.map(item => item.satisfiedPercent)

        }
      })
    },
    resetRank() {
      this.listQuery = {}
      this.getRank()
    }
  }
}
</script>

<style scoped>

</style>
