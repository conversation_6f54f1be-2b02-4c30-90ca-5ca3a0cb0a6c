<template>
  <div class="app-container">
    <el-card class="box-card" shadow="hover">
      <el-row>
        <div class="box-header">
          <div class="box-search">
            <el-input
              clearable
              v-model="keyWord"
              placeholder="请输入跟进记录内容"
            />
            <el-button type="primary" @click="getTableData">搜索</el-button>
          </div>
          <el-button type="primary" @click="addFollowHandle">新增跟进</el-button>
        </div>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="hover">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="followTime" label="创建时间" width="200"></el-table-column>
        <el-table-column prop="remark" label="跟进内容">
          <template slot-scope="scope">
            <span class="st-whitespace-pre" :title="scope.row.remark">{{scope.row.remark}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="nextFollowTime" label="下次跟进时间" width="200"></el-table-column>
        <el-table-column prop="createBy" label="跟进人" width="200"></el-table-column>
        <el-table-column prop="pic" label="附件" width="300">
          <template slot-scope="scope">
            <div  class="st-flex">
              <div v-for="(item, index) in scope.row.pic" :key="index" class="first:st-ml-0 st-ml-[10px]">
                <el-image
                  class="st-w-[50px] st-h-[50px] st-cursor-pointer"
                  v-if="item.endsWith('.pdf')"
                  @click="previewPdfHandle(item)"
                  :src="pdfImage">
                </el-image>
                <el-image
                  class="st-w-[50px] st-h-[50px] st-cursor-pointer"
                  v-if="/\.(jpe?g|png)$/i.test(item)"
                  :src="item"
                  :preview-src-list="[item]">
                </el-image>
                <video
                  v-if="/\.(mp4|avi|mov|wmv|flv|mkv|webm|mpeg|3gp|ts)$/i.test(item)"
                  :src="item"
                  controls
                  class="st-w-[50px] st-h-[50px] st-cursor-pointer"
                ></video>
                <div class="audio-content-table" v-else-if="/\.(mp3|wav|ogg|flac|aac)$/i.test(item)">
                  <audio
                    :src="item"
                    controls
                    class="st-w-[40px] st-h-[15px] st-cursor-pointer"
                  ></audio>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <follow-list ref="followRef" @refresh="getTableData" />
  </div>
</template>
<script>
import {addFollow, qureyFollowList} from "@/api/customer";
import pdfImage from '@/assets/img/pdf.png'
import {uuid} from "@/utils";
import {followTemplateList} from "@/utils/field-conver";
import {getOSSClient} from "@/components/upload/getOSSTst";
import FollowList from './followList'
export default {
  name: "followTable",
   components: {
    FollowList
  },
  data() {
    return {
      keyWord:'',
      pdfImage,
      followStyleList: followTemplateList,
      host: 'https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/',
      aliData: {
        name: '',
        key: 'upload/sign/' + '${filename}',
        success_action_status: '200'
      },
      client:null,
      tableData: [],
      showCreate: false,
      school: {},
      fllowImgs: [],
      fllowUpdate: true,
      followModule: {
        followType: '2',
        remark: '',
        nextFollowTime: ''
      },
      followModuleRules: {
        remark: {
          required: true,
          message: '请输入跟进内容',
          trigger: 'blur'
        },
        followType: {
          required: true,
          message: '请选择跟进模板',
          trigger: 'blur'
        }
      },
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  async mounted() {
    this.getTableData()
    this.client = await getOSSClient('santtaojiaoyu')
    this.$set(this.followModule, 'followType', '2');
  },
  methods: {
    addFollowHandle () {
      this.$refs.followRef.getLists(this.$props.id)
    },
    /**
     * 预览pdf
     */
    previewPdfHandle(item) {
      window.open(item, '_blank');
    },
    getTableData() {
      // let params = {
      //   page: 1,
      //   pageSize: 1000,
      //   customerId: this.$props.id
      // }
      qureyFollowList({clueId: this.$props.id, remark: this.keyWord}).then(res => {
        const list = []
        for (let i = 0; i < res.data.length; i++) {
          const obj = {}
          obj['followTime'] = res.data[i].followTime
          obj['followType'] = res.data[i].followType
          obj['nextFollowTime'] = res.data[i].nextFollowTime
          obj['remark'] = res.data[i].remark
          obj['createBy'] = res.data[i].createBy
          obj['pic'] = res.data[i].pic !== null && res.data[i].pic !== '' ? res.data[i].pic.split(',') : []
          list.push(obj)
        }
        this.tableData = list
      })
    }
  }
}
</script>

<style scoped>
  /deep/ .el-upload-list--picture {
    display: none !important;
  }
  /deep/ .el-upload-list--picture .el-upload-list__item-name i::before{
   /* font-size: 44px; */
  }
  /deep/ .el-upload--picture .el-icon-plus::before{
    font-size: 20px;
   /* font-size: 44px; */
  }
  .preview-box {
    position: relative;
    margin-top: -16px;
  }
  .el-icon-error  {
    cursor: pointer;
    background-color: #fff;
    position: absolute;
    right: 0;
    top: 0;
  }
  .audio-content {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 8px 0px rgb(48 65 86 / 20%);
  }
  .audio-content-table {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 8px 0px rgb(48 65 86 / 20%);
  }
  .box-header {
    display: flex;
    justify-content: space-between;
  }
  .box-search {
    display: flex;
  }
  .box-search button {
    margin-left: 10px;

  }
</style>
