<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.institutionCode" placeholder="校区项目编号" class="filter-item" style="width: 140px;"
                @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.clueCode" placeholder="客户编号" class="filter-item" style="width: 140px;"
                @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.agencyId" placeholder="关联机构Id" type="number" class="filter-item" style="width: 140px;"
                @keyup.enter.native="handleFilter" @change="refreshData" />
      <el-input
        v-model="listQuery.searchField"
        placeholder="合伙人/手机号/校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.projectId" placeholder="项目类型" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.joinStatusList" placeholder="加盟状态" filterable multiple class="filter-item" style="width: 200px;" clearable>
        <el-option v-for="item in schoolJoinStatusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input class="filter-item franchisePeriodCls"   v-model="listQuery.franchisePeriod" clearable  :maxlength="2" style="width: 120px;" placeholder="加盟年限">
        <template #suffix>
          <span>月</span>
        </template>
      </el-input>
      <area-picker :area-list="areaList" :level="'3'" :area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-select v-model="listQuery.customerLevel" placeholder="客户级别" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in customerLevelList"
          :key="item.key"
          :label="item.value"
          :value="item.key"
        />
      </el-select>
      <el-cascader
        placeholder="生命周期"
        v-model="listQuery.lifeCycle"
        :options="lifeCycleList"
        :props="{emitPath: false}"
        clearable></el-cascader>
      <el-date-picker
        v-model="schoolDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="校区到期开始时间"
        end-placeholder="校区到期结束时间"
      />
      <el-date-picker
        v-model="contractDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="合同到期开始时间"
        end-placeholder="合同到期结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
      <el-button
        v-waves
        v-permission="['customer:schoolProject:create']"
        class="filter-item"
        type="primary"
        size="mini"
        @click="handleAdd"
      >
        新增
      </el-button>
    </div>
    <div class="nums-tips">
      <div v-if="institutionNums.currentMonthExpires" class="primary">当月到期数:
        <el-tag type="primary" size="mini" @click="jumpOpera(1,'当月到期列表')">{{ institutionNums.currentMonthExpires }}</el-tag>
      </div>
      <div v-if="institutionNums.nextMonthExpires" class="success">下月到期数:
        <el-tag type="success" size="mini" @click="jumpOpera(2,'下月到期列表')">{{ institutionNums.nextMonthExpires }}</el-tag>
      </div>
      <div v-if="institutionNums.expired" class="danger">已到期数:
        <el-tag type="danger" size="mini" @click="jumpOpera(0,'已到期列表')">{{ institutionNums.expired }}</el-tag>
      </div>
    </div>
    <el-table  v-loading="listLoading" :data="list" border fit stripe highlight-current-row>
      <af-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="校区项目编号" prop="institutionCode" />
      <af-table-column label="关联机构Id" prop="agencyId" show-overflow-tooltip />
      <af-table-column label="客户编号" prop="orderSn" width="80">
        <template slot-scope="{ row }">
          <router-link :to="{ path: '/customer/detail/'+row.clueId, query: {title:'客户-'+row.clueName}}"
                       class="link-type">
            <span>{{ row.clueCode }}</span>
          </router-link>
        </template>
      </af-table-column>

      <af-table-column label="校区编号">
        <template slot-scope="{ row }">
          <span class="link-type" @click="handleQuerySchool(row)">{{ row.schoolCode }}</span>
        </template>
      </af-table-column>
      <af-table-column label="客户名称" prop="clueName" show-overflow-tooltip />
      <af-table-column label="客户级别" prop="customerLevel">
        <template slot-scope="{row}">
          {{
            (customerLevelList.find(item => item.key === row.customerLevel) || {}).value || '--'
          }}
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="schoolName" />
      <af-table-column label="签约区域" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.provinceName }}{{ row.cityName }}{{ row.areaName }}{{ row.countyName }}
        </template>
      </af-table-column>
      <af-table-column label="区域类型" prop="areaJoinType">
        <template slot-scope="{row}">
          {{ areaJoinTypeList[row.areaJoinType] || '--'   }}
        </template>
      </af-table-column>
      <af-table-column label="加盟项目" prop="projectName" />
      <!--      <af-table-column label="加盟状态" prop="joinStatus" :formatter="getSchoolJoinStatus" />-->
      <el-table-column label="加盟状态" prop="joinStatus">
        <template slot-scope="{row}">
          <SchoolJoinStatusTag :status="row.joinStatus"></SchoolJoinStatusTag>
        </template>
      </el-table-column>
      <af-table-column label="校区到期时间">
        <template slot-scope="{row}">
          <div>{{ row.schoolEndDate || '--' }}</div>
        </template>
      </af-table-column>
      <af-table-column label="合同到期时间">
        <template slot-scope="{row}">
          <div>{{ row.endDate || '--' }}</div>
        </template>
      </af-table-column>
      <!--      <af-table-column label="关联状态" prop="relationStatus" :formatter="getRelation" />-->
      <!--      <el-table-column label="订单状态" prop="status" :formatter="getOrderStatus" width="150" />-->
      <af-table-column label="创建时间">
        <template slot-scope="{row}">
          <div>{{ row.createTime || '--' }}</div>
        </template>
      </af-table-column>
      <af-table-column label="生命周期" prop="lifeCycle" >
        <template slot-scope="{ row }">
          {{row.lifeCycle ? getLifeCycleTitle(row.lifeCycle) : ''}}
        </template>
      </af-table-column>
      <af-table-column label="综合耗时" prop="kehaoNumber">
        <template slot-scope="{ row }">
          {{formatMinutes(row.kehaoNumber)}}
        </template>
      </af-table-column>
<!--      <af-table-column label="启动期课耗时(分)" prop="startKehaoNumber" />-->
      <af-table-column label="加盟年限(月)" prop="franchisePeriod" />
      <af-table-column label="学员数量" prop="studentNum" />
      <af-table-column label="盒子数量" prop="terminalNum" />
      <af-table-column label="业绩贡献值(元)" prop="totalAmount" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="320" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['customer:schoolProject:update']" type="text" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button  type="text" size="mini" @click="customerAttributes(row)">
            客户属性
          </el-button>
          <el-button v-if="row.relationStatus===1" type="text" v-permission="['customer:schoolProject:campus']" size="mini"
                     @click="campusManagement(row)">
            校区管理
          </el-button>
          <el-dropdown
            v-permission="['customer:schoolProject:renew', 'customer:schoolProject:allowance', 'customer:schoolProject:joinUpgrade','customer:schoolProject:rescission','customer:schoolProject:delete']"
            trigger="click"
            @command="handleCommand"
          >
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-show="row.joinStatus == 2" v-permission="['customer:schoolProject:renew']"
                                :command="beforeHandleCommand('a',row)">续约
              </el-dropdown-item>
              <el-dropdown-item v-show="row.joinStatus == 2&&row.agencyId!==null&&row.showRemind<=90"
                                v-permission="['customer:schoolProject:remind']" :command="beforeHandleCommand('f',row)">续约提醒
              </el-dropdown-item>
              <el-dropdown-item
                v-show="(row.joinStatus == 2 || row.joinStatus == 3) "
                v-permission="['customer:schoolProject:joinUpgrade']"
                :command="beforeHandleCommand('c',row)"
              >加盟升级
              </el-dropdown-item>
              <el-dropdown-item
                v-show="row.joinStatus == 2"
                v-permission="['customer:schoolProject:rescission']"
                :command="beforeHandleCommand('d',row)"
              >解约
              </el-dropdown-item>
              <el-dropdown-item v-show="row.joinStatus == 2" v-permission="['customer:schoolProject:delay']"
                                :command="beforeHandleCommand('e',row)">延期
              </el-dropdown-item>
              <el-dropdown-item
                v-show="row.joinStatus!==2"
                v-permission="['customer:schoolProject:delete']"
                type="primary"
                size="mini"
                :command="beforeHandleCommand('b',row)"
              >删除
              </el-dropdown-item>
              <el-dropdown-item v-show="row.joinStatus == 2" v-permission="['customer:schoolProject:repeatBuy']"
                                :command="beforeHandleCommand('h',row)">复购
              </el-dropdown-item>
              <el-dropdown-item v-permission="['customer:schoolProject:allowance']"
                                      :command="beforeHandleCommand('i',row)">冲减(财务)
                    </el-dropdown-item>
              <!--变更合同 isSupplementContract 是否显示变更合同 0：不显示 1：显示-->
              <el-dropdown-item v-show="row.isSupplementContract == 1" :command="beforeHandleCommand('l',row)">变更合同
              </el-dropdown-item>
              <el-dropdown-item v-show="row.agencyFlag == 1&&(row.joinStatus==2||row.joinStatus==1)"
                                v-permission="['customer:schoolProject:openAccount']" :command="beforeHandleCommand('k',row)">开通账号
              </el-dropdown-item>
              <el-dropdown-item v-show="row.agencyFlag == 1&&(row.joinStatus==2||row.joinStatus==1)"
                                v-permission="['customer:schoolProject:openAccountForAdmin']"
                                :command="beforeHandleCommand('ka',row)">直接开通账号
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      layout="total, sizes, prev, pager, next"
      @pagination="getList"
    />
    <school-project ref="schoolProject" :title="createSchoolTitle" @refresh="getList" />
    <create-new-school ref="createSchoolDialog" :type="schoolOption" :customer-id="currentCustomerId" :title="createSchoolTitle" />
    <during-list ref="duringList" :project-list="projectList" @getExpireInstitutionNums="getExpireInstitutionNums" />
    <customer-attributes v-model:dialog-visible="dialogVisible" ref="attributesRef" @refresh="getList" />
  </div>
</template>
<script>
import {
  getShoolProjectList,
  deleteShoolProjectList,
  rescissionShoolProjectList,
  delay,
  remind,
  openAccount,
  getExpireInstitutionNums, openAdminAccount
} from '@/api/school-project'
import {
  getAllProject
} from '@/api/common'
import Pagination from '@/components/Pagination'
import {
  converseEnToCn,
  relationList,
  schoolJoinStatusList,
  customerLevelList,
  orderStatusList,
  institutionTypes,
  lifeCycleList
} from '@/utils/field-conver'
import schoolProject from './detail'
import CreateNewSchool from '../customer/componets/createSchool'
import AreaPicker from '@/components/area-picker'
import DuringList from './components/during.vue'
import SchoolJoinStatusTag from '@/components/StatusTag/SchoolJoinStatusTag.vue'
import CustomerDetailLink from '@/components/link/CustomerDetailLink.vue'
import Collapse from '@/components/collapse/Collapse.vue'
import CustomerAttributes from "@/views/schoolProject/components/CustomerAttributes.vue";
import {cloneDeep} from 'lodash'

export default {
  name: 'SchoolProject',
  components: {
    Collapse, CustomerDetailLink,
    SchoolJoinStatusTag,
    Pagination,
    schoolProject,
    AreaPicker,
    CreateNewSchool,
    DuringList,
    CustomerAttributes
  },
  data() {
    return {
      areaJoinTypeList: ['区县单点', '区县独家', '乡镇独家', '乡镇单点'], // 区域类型列表
      lifeCycleList,
      listLoading: false,
      list: [],
      schoolJoinStatusList: schoolJoinStatusList,
      customerLevelList: customerLevelList,
      projectList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      total: 0,
      schoolOption: '',
      createSchoolTitle: '',
      currentCustomerId: '', // 当前客户的id
      institutionTypes: institutionTypes,
      schoolDate: [],
      contractDate: [],
      institutionNums: {},
      dialogVisible:false,
    }
  },
  props: {
    agencyId: {
      type: String,
      default: '',
      required: false
    }
  },
  created() {
    const agencyId = this.$route.query.agencyId || this.agencyId
    const orderCode = this.$route.query.orderCode
    if (agencyId)
      this.listQuery.agencyId = agencyId
    if (!orderCode) {
      this.initData()
    }
  },
  mounted() {
    if (this.$route.query.orderCode) {
      this.$set(this.listQuery, 'institutionCode', this.$route.query.orderCode);
      this.handleFilter();
    }
  },
  computed: {
    $_isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  },
  methods: {
    formatMinutes(minutes) {
      if (minutes === null || minutes === undefined || isNaN(minutes)) {
        return '--';
      }
      const h = Math.floor(minutes / 60);
      const m = minutes % 60;
      return `${h}时${m}分`;
    },
    // 获取生命周期名字
    getLifeCycleTitle(lifeCycle) {
      const values = lifeCycle.split(',');
      // 收集所有 value-label 映射（包括 children）
      const map = {};
      this.lifeCycleList.forEach(item => {
        map[item.value] = item.label;
        if (item.children) {
          item.children.forEach(child => {
            map[child.value] = child.label;
          });
        }
      });
      const labels = values.map(val => map[val]).filter(Boolean);
      return labels.join('、');
    },
    initData() {
      this.getList()
      this.getProject()
      this.getCurrentDay()
      this.getExpireInstitutionNums()
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.schoolDate = []
      this.contractDate = []
      this.areaList = {}
      this.getList()
    },
    /**
     * 新增校区项目
     * */
    handleAdd() {
      this.createSchoolTitle = '新增校区项目'
      this.$refs.schoolProject.getDetail('create')
    },
    /**
     * 修改
     * */
    handleUpdate(row) {
      this.createSchoolTitle = '修改校区项目'
      this.$refs.schoolProject.schoolProjectDetail(row.id, 'update')
    },

    /**
     * 设置客户属性
     * @param row
     */
    customerAttributes(row){
      this.$refs.attributesRef.setForm({
        id:row.id,
        lifeCycle:row.lifeCycle,
        customerLevel:row.customerLevel
      });
      this.dialogVisible = true;
    },
    /**
     * 删除按钮
     * */
    handleDelete(row) {
      this.$confirm('此操作将删除该校区项目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteShoolProjectList(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /**
     * 更多按钮
     * */
    handleCommand(command) {
      switch (command.type) {
        case 'a':
          // 授权
          this.renewHandover(command.row)
          break
        case 'b':
          // 授权
          this.handleDelete(command.row)
          break
        case 'c':
          // 加盟升级
          this.upgradeHandover(command.row)
          break
        case 'd':
          // 解约校区项目
          this.rescissionSchool(command.row)
          break
        case 'e':
          // 延期
          this.delay(command.row)
          break
        case 'h':
          // 延期
          this.purchase(command.row)
          break
        // 续约提醒
        case 'f':
          this.remind(command.row)
          break
        case 'k':
          this.openAccount(command.row)
          break
        case 'ka':
          this.openAdminAccount(command.row)
          break
        case 'l':
          // 变更合同
          this.modification(command.row)
          break
        case 'i':
          // 冲减
          this.reduce(command.row)
          break
      }
    },
    beforeHandleCommand(type, row) {
      return {
        'type': type,
        'row': row
      }
    },
    getList() {
      const that = this
      const params = Object.assign(cloneDeep(that.listQuery), that.areaList, {
        schoolStartDate: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[0] : '',
        schoolEndDate: that.schoolDate && that.schoolDate.length > 0 ? that.schoolDate[1] : '',
        contractStartDate: that.contractDate && that.contractDate.length > 0 ? that.contractDate[0] : '',
        contractEndDate: that.contractDate && that.contractDate.length > 0 ? that.contractDate[1] : ''
      })
      that.listLoading = true
      if(params.hasOwnProperty('joinStatusList')){
        params.joinStatusList = params.joinStatusList.join(',')
      }
      getShoolProjectList(params).then(res => {
        that.listLoading = false
        const list = res.data.records && res.data.records.length > 0 ? res.data.records : []
        that.total = res.data.total
        const projectArr = []
        const currentDays = that.getCurrentDay()
        list.forEach(item => { // institutionCode
          const projects = {}
          projects['id'] = item.id
          projects['customerLevel'] = item.customerLevel
          projects['institutionCode'] = item.institutionCode
          projects['lifeCycle'] = item.lifeCycle
          projects['kehaoNumber'] = item.kehaoNumber
          projects['terminalNum'] = item.terminalNum
          projects['totalAmount'] = item.totalAmount
          projects['studentNum'] = item.studentNum
          projects['franchisePeriod'] = item.franchisePeriod
          projects['agencyFlag'] = item.agencyFlag
          projects['agencyId'] = item.agencyId
          projects['agencyMobile'] = item.agencyMobile
          projects['areaJoinType'] = item.areaJoinType
          projects['areaName'] = item.areaName
          projects['cityName'] = item.cityName
          projects['clueCode'] = item.clueCode
          projects['clueId'] = item.clueId
          projects['clueName'] = item.clueName
          projects['countyName'] = item.countyName
          projects['createTime'] = item.createTime
          projects['endDate'] = item.endDate ? item.endDate : null
          projects['institutionName'] = item.institutionName
          projects['institutionType'] = item.institutionType
          projects['joinStatus'] = item.joinStatus
          projects['projectId'] = item.projectId
          projects['projectName'] = item.projectName
          projects['provinceName'] = item.provinceName
          projects['relationStatus'] = item.relationStatus
          projects['schoolAccount'] = item.schoolAccount
          projects['schoolCode'] = item.schoolCode
          projects['schoolEndDate'] = item.schoolEndDate
          projects['schoolId'] = item.schoolId
          projects['schoolName'] = item.schoolName
          projects['orderId'] = item.orderId
          projects['showRemind'] = item.schoolEndDate !== null ? that.getDaysBetween(item.schoolEndDate, currentDays) : 0
          projects['isSupplementContract'] = item.isSupplementContract
          projectArr.push(projects)
        })
        that.list = projectArr
      }).catch(error => {
        that.listLoading = false
      })
    },
    /**
     * 跳转续约交接单
     * type:
     * create: 创建交接单
     * update: 修改交接单
     * renew: 续约
     * upgrade： 升级交接单
     * sp: 校区项目Id
     * */
    renewHandover(row) { // 续约
      this.$router.push({
        name: 'HandoverRenew',
        query: {
          type: 'renew',
          name: row.clueName,
          pjId: row.projectId, // 项目id
          spId: row.id, // 校区项目id
          slId: row.schoolId, // 校区id
          orderId: row.orderId
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    upgradeHandover(row) { // 加盟升级
      this.$router.push({
        name: 'HandoverUpgrade',
        query: {
          orderId: row.orderId,
          type: 'upgrade',
          name: row.clueName,
          pjId: row.projectId, // 项目id
          spId: row.id, // 校区项目id
          slId: row.schoolId // 校区id
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    /**
     *  解约校区项目
     * */
    rescissionSchool(row) {
      const id = row.id
      const that = this
      that.$confirm('是否确认解约', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rescissionShoolProjectList(id).then(res => {
          if (res.code === '000000') {
            that.$message({
              type: 'success',
              message: '校区项目解约成功!'
            })
            that.getList()
          }
        }).catch(res => {

        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '取消操作'
        })
      })
    },
    /**
     *  延期
     * */
    delay(row) {
      const id = row.id
      const that = this
      that.$confirm('是否确认延期合同', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delay(id).then(res => {
          if (res.code === '000000') {
            that.$message({
              type: 'success',
              message: '已创建延期合同,请到合同列表查看!'
            })
            that.getList()
          }
        }).catch(res => {

        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '取消操作'
        })
      })
    },
    /**
     * 查看校区
     * */
    handleQuerySchool(row) {
      this.schoolOption = 'query'
      this.createSchoolTitle = '校区详情'
      this.$refs.createSchoolDialog.getSchool(row.schoolId)
    },
    /**
     * 获取省市区的地址
     * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
     * 项目列表
     * */
    getProject() {
      const that = this
      getAllProject().then(res => {
        that.projectList = res.data
      })
    },
    /**
     * 转换
     * @param row
     * @returns {*}
     */
    getOrderStatus(row) {
      return converseEnToCn(orderStatusList, row.status)
    },
    getRelation(row) {
      return converseEnToCn(relationList, row.relationStatus)
    },
    getSchoolJoinStatus(row) {
      return converseEnToCn(schoolJoinStatusList, row.joinStatus)
    },
    campusManagement(row) { // 调到校区管理页面
      this.$router.push({
        name: 'Campusmanagement',
        query: {
          id: row.id,
          title: row.schoolName
        }
      })
    },
    purchase(row) {
      this.$router.push({
        name: 'CustomerPurchase',
        query: {
          pjId: row.projectId, // 项目id
          spId: row.id, // 校区项目id
          slId: row.schoolId, // 校区id
          type: 'add',
          name: row.clueName
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    reduce(row) {
      this.$router.push({
        name: 'CustomerPurchase',
        query: {
          pjId: row.projectId, // 项目id
          spId: row.id, // 校区项目id
          slId: row.schoolId, // 校区id
          type: 'add',
          name: row.clueName,
          isReduce: true
        },
        params: {
          clueId: row.clueId
        }
      })
    },
    remind(row) {
      this.$confirm('校区即将到期，是否给合伙人发送续约提醒', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        remind(row.agencyId).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '续约提醒已发送!'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },
    openAccount(row) { // 开通账号
      this.$confirm('是否需要开通机构账号', '提示', {
        confirmButtonText: '需要',
        cancelButtonText: '不需要',
        type: 'warning'
      }).then(() => {
        openAccount(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '开通成功!'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },
    openAdminAccount(row) { // 开通账号
      this.$confirm('是否需要直接开通机构账号', '提示', {
        confirmButtonText: '需要',
        cancelButtonText: '不需要',
        type: 'warning'
      }).then(() => {
        openAdminAccount(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '开通成功!'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作!'
        })
      })
    },

    /**
     *  变更合同
     * */
    modification(row) {
      const that = this
      that.$confirm('是否确认生成变更合同', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push({
          path: '/contract/modification',
          query: { id: row.id, isEdit: 'true', flags: 1 }
        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '取消操作'
        })
      })
    },

    getInstitutionType(row) {
      return converseEnToCn(institutionTypes, row.institutionType)
    },
    getCurrentDay() {
      const days = new Date()
      const year = days.getFullYear()
      const month = days.getMonth() + 1
      const date = days.getDate()
      return `${ year }-${ month < 10 ? '0' + month : month }-${ date < 10 ? '0' + date : date }`
    },
    getDaysBetween(sDate1, sDate2) {
      var dateSpan,
        iDays
      sDate1 = Date.parse(sDate1)
      sDate2 = Date.parse(sDate2)
      dateSpan = sDate2 - sDate1
      dateSpan = Math.abs(dateSpan)
      iDays = Math.floor(dateSpan / (24 * 3600 * 1000))
      return iDays
    },
    jumpOpera(type, title) {
      this.$refs.duringList.duringPop = true
      this.$refs.duringList.expireType = type
      this.$refs.duringList.duringTitle = title
      this.$refs.duringList.getList(type)
    },
    getExpireInstitutionNums() { // 查询到期数量
      getExpireInstitutionNums().then(res => {
        if (res.code === '000000') {
          this.institutionNums = res.data || {}
        }
      }).catch(error => {

      })
    },
    refreshData() {
      this.$forceUpdate()
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .el-select__tags{
  .el-select__input{
    background: transparent;
    border: none;
  }
}
/deep/ .franchisePeriodCls{
  .el-input__suffix{
    display: flex;
    align-items: center;
  }
}
.nums-tips {
  margin: 0 0 10px;
  display: flex;

  div {
    font-size: 14px;
    margin-right: 15px;

    span {
      margin-left: 5px;
      cursor: pointer;
    }

    &.primary {
      color: #1890ff;
    }

    &.success {
      color: #13ce66;
    }

    &.danger {
      color: #ff4949;
    }
  }
}
</style>
