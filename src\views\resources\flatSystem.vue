<template>
  <div class="app-container">
    <el-tabs type="border-card" @tab-click="handleClick">
      <el-tab-pane label="平板管理">
        <div class="filter-container">
          <el-input
            v-model="listQuery.serialNumber"
            placeholder="平板序列号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.queryInfo"
            placeholder="合伙人/手机号/校区名称"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.agencyId"
            placeholder="关联机构ID"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.userName"
            placeholder="学生姓名"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.userAccount"
            placeholder="学生账号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-select v-model="listQuery.isOpen" placeholder="跨区状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in regionalStatus" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="listQuery.status" placeholder="平板状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in statusList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-date-picker
            v-model="localtionDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="定位开始日期"
            end-placeholder="定位结束日期"
          />
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
            查询
          </el-button>
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
            重置
          </el-button>
        </div>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          stripe
          highlight-current-row
          style="width: 100%;"
        >
          <af-table-column label="平板编号" show-overflow-tooltip prop="id" />
          <af-table-column label="平板序列号" show-overflow-tooltip prop="serialNumber" />
          <el-table-column label="合伙人(机构账号)" prop="partnerAccount" show-overflow-tooltip width="150" />
          <af-table-column label="机构ID" prop="institution" show-overflow-tooltip />
          <af-table-column label="校区名称" prop="schoolName" show-overflow-tooltip />
          <af-table-column label="校区地址" prop="schoolAddress" />
          <af-table-column label="定位地址" prop="gpsAddress" show-overflow-tooltip />
          <af-table-column label="最近登录学生(学生账号)" prop="userAccount" />
          <af-table-column label="跨区状态" prop="isOpen">
            <template slot-scope="scope">
              <span v-show="scope.row.isOpen==0">不开放跨区</span>
              <span v-show="scope.row.isOpen==0">开放跨区</span>
            </template>
          </af-table-column>
          <af-table-column label="设备状态" prop="status" :formatter="equipmentStatus" />
          <af-table-column label="最近定位时间" prop="gpsTime" />
          <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="260" fixed="right">
            <template slot-scope="{row}">
              <el-button v-permission="['customer:list:update']" type="primary" size="mini" @click="interregionalSet(row)">查看位置</el-button>
              <el-button v-permission="['customer:list:deal']" class="ml-10" type="primary" size="mini" @click="regionalSettingPop=true,regionalSetting(row)">
                跨区设置
              </el-button>
              <el-button v-permission="['customer:list:update']" type="primary" size="mini" @click="operation(row)">{{ row.status===2?'启用':'禁用' }}</el-button>
              <!-- <el-button v-permission="['customer:list:update']" type="primary" size="mini" @click="operaRecords(row)">操作记录</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.pageIndex"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="激活管理">
        <div class="filter-container">
          <el-input
            v-model="activeQuery.id"
            placeholder="设备编号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-input
            v-model="activeQuery.serialNumber"
            placeholder="序列号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-input
            v-model="activeQuery.agencyId"
            placeholder="关联机构ID"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-input
            v-model="activeQuery.agencySearchStr"
            placeholder="合伙人/手机号/校区名称"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-input
            v-model="activeQuery.bindSearchStr"
            placeholder="绑定姓名/账号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-input
            v-model="activeQuery.activateSearchStr"
            placeholder="激活姓名/账号"
            class="filter-item"
            style="width: 200px;"
            @keyup.enter.native="handleActiveFilter"
          />
          <el-select v-model="activeQuery.bindStatus" placeholder="绑定状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in bindStatusList" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
          <el-select v-model="activeQuery.activateStatus" placeholder="激活状态" filterable clearable class="filter-item" style="width: 140px;">
            <el-option v-for="item in activeStatus" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
          <el-date-picker
            v-model="bindDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="绑定开始日期"
            end-placeholder="绑定结束日期"
          />
          <el-date-picker
            v-model="activeDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="激活开始日期"
            end-placeholder="激活结束日期"
          />
          <el-date-picker
            v-model="importDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="导入开始日期"
            end-placeholder="导入结束日期"
          />
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleActiveFilter">
            查询
          </el-button>
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleActiveReset">
            重置
          </el-button>
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="importExcel">
            导出Excel
          </el-button>
          <el-button v-waves class="filter-item" type="primary" size="mini" @click="importFile">
            导入新设备
          </el-button>
        </div>
        <el-table
          v-loading="activeLoading"
          :data="activationList"
          border
          fit
          stripe
          highlight-current-row
          style="width: 100%;"
        >
          <af-table-column label="平板编号" prop="id" width="80" />
          <af-table-column label="序列号" prop="serialNumber" />
          <el-table-column label="关联机构ID" prop="schoolId" show-overflow-tooltip width="150" />
          <af-table-column label="绑定校区" prop="schoolName" show-overflow-tooltip />
          <af-table-column label="绑定账号" prop="bindUserName" show-overflow-tooltip />
          <af-table-column label="绑定日期" prop="bindTime" />
          <af-table-column label="绑定状态" prop="bindStatus">
            <template slot-scope="scope">
              <span v-show="scope.row.bindStatus===0">未绑定</span>
              <span v-show="scope.row.bindStatus===1">已绑定</span>
            </template>
          </af-table-column>
          <af-table-column label="激活账号" prop="activateUserAccount" />
          <af-table-column label="激活日期" prop="activateTime" />
          <af-table-column label="激活状态" prop="activateStatus">
            <template slot-scope="scope">
              <span v-show="scope.row.activateStatus===0">未激活</span>
              <span v-show="scope.row.activateStatus===1">已激活</span>
            </template>
          </af-table-column>
          <af-table-column label="导入日期" prop="createTime" />
        </el-table>
        <pagination
          v-show="activeTotal>0"
          :total="activeTotal"
          :page.sync="activeQuery.pageIndex"
          :limit.sync="activeQuery.pageSize"
          @pagination="getActiveList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 导入新设备弹框 -->
    <file-upload ref="fileUpload" @refresh="getActiveList" />
    <!-- 查看位置弹框 -->
    <interregional-pop ref="interregional" />

    <!--跨区设置弹框-->
    <el-dialog title="跨区设置" :visible.sync="regionalSettingPop" :close-on-click-modal="!regionalSettingPop" width="70%" @close="regionalSettingCancel">
      <el-form :model="regionalForm" label-width="120px">
        <el-form-item label="跨区状态：">
          <el-radio-group v-model="regionalForm.isOpen">
            <el-radio :label="1">允许跨区</el-radio>
            <el-radio :label="0">禁止跨区</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="regionalForm.isOpen === 1">
          <el-form-item label="跨区有效期：">
            <el-checkbox v-model="openDateChecked">开启</el-checkbox>
            <el-date-picker
              v-if="openDateChecked"
              v-model="openDate"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              style="margin-left: 20px"
            />
          </el-form-item>
          <el-form-item label="地区范围限制：">
            <el-checkbox v-model="openLocationChecked">开启</el-checkbox>
            <el-button v-if="openLocationChecked" type="primary" size="mini" style="margin-left: 20px" @click="getLatitude">设置区域</el-button>
          </el-form-item>
          <el-form-item v-if="openLocationChecked && regionalForm.address" label="当前允许区域：">
            <span>{{ regionalForm.address }} 附近{{ regionalForm.locationRadius }}米</span>
          </el-form-item>
          <el-form-item label="允许观看班型：" required>
            <el-checkbox v-if="regionalForm.openClass.length>0" v-model="checkAll" @change="handleCheckAllChange(checkAll)">全选</el-checkbox>
            <el-checkbox-group v-model="checkedOpenClass" @change="handleCheckedClassChange(checkedOpenClass)">
              <el-checkbox v-for="openClass in regionalForm.openClass" :key="openClass.classTypeId" :label="openClass.classTypeId" style="width: 18%">{{ openClass.classTypeName }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div class="assign-operas">
          <el-button type="infor" size="mini" @click="regionalSettingCancel">取消</el-button>
          <el-button type="primary" size="mini" @click="regionalSettingConfirm">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <school-map ref="schoolMap" @refresh="getMapData" />
  </div>
</template>

<script>
import FileUpload from './components/fileUploadPop.vue'
import Pagination from '@/components/Pagination'
import InterregionalPop from './components/interregionalPop.vue'
import SchoolMap from '@/views/customer/componets/schoolMapNew'
import { flatStatus, converseEnToCn } from '@/utils/field-conver'
import { querySchoolPadPages, getOpenSetting, interregionalOpen, saveOpenSetting, isStatus, queryPadPages } from '@/api/charge'
export default {
  components: {
    Pagination,
    FileUpload,
    InterregionalPop,
    SchoolMap
  },
  data() {
    return {
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      listLoading: false,
      total: 0,
      list: [],
      regionalStatus: [
        {
          label: '不开放',
          value: 0
        }, {
          label: '开放',
          value: 1
        }
      ],
      statusList: flatStatus,
      followStatusList: [],
      localtionDate: [],
      bindStatus: [],
      schoolDate: [],
      regionalSettingPop: false,
      regionalForm: {
        isOpen: 0,
        locationRadius: 300, // 开启地区范围限制-定位半径(米)
        lockLocation: 0,
        openClass: []
      },
      openDateChecked: false,
      openLocationChecked: false,
      openDate: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      },
      openClassInfo: {
        checked: 0,
        classTypeId: -1,
        classTypeName: ''
      },
      checkAll: false,
      // 跨区时，该智能终端可以观看的班型id列表
      checkedOpenClass: [],
      activeStatus: [// 0:未激活,1:激活
        {
          id: 0,
          label: '未激活'
        },
        {
          id: 1,
          label: '激活'
        }
      ],
      bindStatusList: [// 0:未绑定,1:已绑定
        {
          id: 0,
          label: '未绑定'
        },
        {
          id: 1,
          label: '已绑定'
        }
      ],
      bindDate: [],
      activeDate: [],
      importDate: [],
      activeLoading: false,
      activationList: [],
      activeQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      activeTotal: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    getList() {
      const params = Object.assign({}, this.listQuery, {
        beginTime: this.localtionDate && this.localtionDate.length > 0 ? this.localtionDate[0] : '',
        endTime: this.localtionDate && this.localtionDate.length > 0 ? this.localtionDate[1] : ''
      })
      this.listLoading = true
      querySchoolPadPages(params).then(res => {
        if (res.code === '000000') {

          this.list = res.data.records
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch((err) => {

        this.listLoading = false
      })
    },
    handleClick(e) {

      if (e.label === '平板管理') {
        this.getList()
      } else {
        this.getActiveList()
      }
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.localtionDate = []
      this.getList()
    },
    equipmentStatus(row) {


      return converseEnToCn(this.statusList, row.status)
    },
    operation(row) {
      const title = row.status === 2 ? '确认进行恢复正常操作' : '确认禁用跨区'
      const operaTitle = row.status === 2 ? '智能终端恢复正常!' : '智能终端禁用跨区成功!'
      const params = {
        id: row.id,
        status: row.status
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        isStatus(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: `${operaTitle}`
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    operaRecords(row) {},
    importExcel() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      const url = `${exportUrl}stip/pad/exportTrade?pageIndex=${that.activeQuery.pageIndex}&pageSize=${that.activeQuery.pageSize}&id=${that.activeQuery.id ? that.activeQuery.id : ''}&serialNumber=${that.activeQuery.serialNumber ? that.activeQuery.serialNumber : ''}&agencyId=${that.activeQuery.agencyId ? that.activeQuery.agencyId : ''}
      &agencySearchStr=${that.activeQuery.agencySearchStr ? that.activeQuery.agencySearchStr : ''}&bindSearchStr=${that.activeQuery.bindSearchStr ? that.activeQuery.bindSearchStr : ''}&activateSearchStr=${that.activeQuery.activateSearchStr ? that.activeQuery.activateSearchStr : ''}&bindStatus=${that.activeQuery.bindStatus ? that.activeQuery.bindStatus : ''}
      &activateStatus=${that.activeQuery.activateStatus ? that.activeQuery.activateStatus : ''}
      &bindBeginTime=${that.bindDate.length > 0 ? that.bindDate[0] : ''}&bindEndTime=${that.bindDate.length > 0 ? that.bindDate[1] : ''}&activateBeginTime=${that.activeDate.length > 0 ? that.activeDate[0] : ''}&activateEndTime=${that.activeDate.length > 0 ? that.activeDate[1] : ''}
      &createBeginTime=${that.importDate.length > 0 ? that.importDate[0] : ''}&createEndTime=${that.importDate.length > 0 ? that.importDate[1] : ''}`
      that.$confirm('确定导出数据?', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        that.activeLoading = true
        const params = Object.assign({}, that.activeQuery, {
          bindBeginTime: that.bindDate && that.bindDate.length > 0 ? that.bindDate[0] : '',
          bindEndTime: that.bindDate && that.bindDate.length > 0 ? that.bindDate[1] : '',
          activateBeginTime: that.activeDate && that.activeDate.length > 0 ? that.activeDate[0] : '',
          activateEndTime: that.activeDate && that.activeDate.length > 0 ? that.activeDate[1] : '',
          createBeginTime: that.importDate && that.importDate.length > 0 ? that.importDate[0] : '',
          createEndTime: that.importDate && that.importDate.length > 0 ? that.importDate[1] : ''
        })
        queryPadPages(params).then(response => {
          that.activationList = response.data.records || []
          that.activeTotal = response.data.total || 0
          that.activeLoading = false
          if (url && that.activationList.length > 0) {
            a.href = url
            a.target = '_blank'
            document.body.appendChild(a)
            a.click()
          } else {
            setTimeout(() => {
              that.$message({
                type: 'warning',
                message: '没有可以导出的数据'
              })
            }, 500)
          }
        }).catch(() => {
          that.$message({
            type: 'warning',
            message: '没有可以导出的数据'
          })
        })
      }).catch(() => {
        that.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    importFile() {
      this.$refs.fileUpload.fileUploadFlag = true
    },
    interregionalSet(row) {
      this.$refs.interregional.addressDetail = row.schoolAddress
      this.$refs.interregional.getDeviceCheckRegion(row.id)
      this.$refs.interregional.interregionalPop = true
    },
    interregionalOpen(row) {
      const title = row.isOpen === 0 ? '是否开启跨区' : '是否关闭跨区'
      const params = {
        id: row.id
      }
      this.$confirm(`${title}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        interregionalOpen(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    /**
     * 打开地图获取经纬度
     */
    getLatitude() {

      this.$refs.schoolMap.getLonAndLat(this.areaList, this.regionalForm.address, this.regionalForm.locationRadius)
      this.$refs.schoolMap.getMapData('', this.regionalForm.address)
    },
    /**
     * 获取 地图选择的经纬度
     */
    getMapData(val) {

      this.$set(this.regionalForm, 'address', val.addressDetail)
      this.$set(this.regionalForm, 'longitude', val.lng)
      this.$set(this.regionalForm, 'latitude', val.lat)
      this.$set(this.regionalForm, 'locationRadius', val.defaulRadius)
      this.$set(this.areaList, 'provinceId', val.areaList.provinceId)
      this.$set(this.areaList, 'cityId', val.areaList.cityId)
      this.$set(this.areaList, 'areaId', val.areaList.areaId)
      this.$set(this.areaList, 'countyId', val.areaList.countyId)
    },
    // 全选操作
    handleCheckAllChange(val) {
      if (val) {
        this.regionalForm.openClass.forEach(item => {
          this.checkedOpenClass.push(item.classTypeId)
        })
      } else {
        this.checkedOpenClass = []
      }
      this.$forceUpdate()
    },
    // 单个班型选择监听
    handleCheckedClassChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.regionalForm.openClass.length
    },
    // 获取跨区域设置
    regionalSetting(row) {
      const that = this
      getOpenSetting(row.id).then(res => {
        if (res.code === '000000') {
          that.regionalForm = JSON.parse(JSON.stringify(res.data))
          // 跨区有效期
          that.openDate = that.regionalForm.openStartDate && that.regionalForm.openEndDate ? [that.regionalForm.openStartDate, that.regionalForm.openEndDate] : []
          that.openDateChecked = that.openDate.length !== 0
          that.openLocationChecked = that.regionalForm.lockLocation === 1
          // 跨区地区范围限制
          that.areaList = {
            provinceId: that.regionalForm.provinceId,
            cityId: that.regionalForm.cityId,
            areaId: that.regionalForm.areaId,
            countyId: that.regionalForm.countyId
          }
          // 允许观看班型
          that.regionalForm.openClass.forEach(item => {
            if (item.checked === 1) that.checkedOpenClass.push(item.classTypeId)
          })
          that.checkAll = that.checkedOpenClass.length === that.regionalForm.openClass.length
        }
      })
    },
    regionalSettingCancel() {
      this.regionalSettingPop = false
      this.openDateChecked = false
      this.openLocationChecked = false
      this.regionalForm = {}
      this.checkedOpenClass = []
      this.checkAll = false
    },
    // 保存终端跨区设置
    regionalSettingConfirm() {
      let data = {}
      if (this.regionalForm.isOpen === 1) {
        // 允许跨区
        if (this.openDateChecked) {
          if (!this.openDate || this.openDate.length === 0) {
            this.$message({
              type: 'warning',
              message: '跨区有效期不能为空！'
            })
            return
          }
          if (this.openDate[0] === this.openDate[1]) {
            this.$message({
              type: 'warning',
              message: '跨区有效期起始日期不能相同！'
            })
            return
          }
        }
        if (this.openLocationChecked && !this.regionalForm.address) {
          this.$message({
            type: 'warning',
            message: '地区范围限制不能为空！'
          })
          return
        }
        if (!this.checkedOpenClass || this.checkedOpenClass.length === 0) {
          this.$message({
            type: 'warning',
            message: '允许观看班型不能为空！'
          })
          return
        }
        data = Object.assign(this.regionalForm, this.areaList, {
          openStartDate: this.openDate[0],
          openEndDate: this.openDate[1],
          openClasses: this.checkedOpenClass,
          lockLocation: this.openLocationChecked ? 1 : 0
        })
      } else {
        // 禁止跨区
        data = {
          id: this.regionalForm.id,
          isOpen: 0
        }
      }

      saveOpenSetting(data).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.regionalSettingCancel()
          this.getList()
        }
      }).catch(() => {

      })
    },
    getActiveList() { // queryPadPages
      const params = Object.assign({}, this.activeQuery, {
        bindBeginTime: this.bindDate && this.bindDate.length > 0 ? this.bindDate[0] : '',
        bindEndTime: this.bindDate && this.bindDate.length > 0 ? this.bindDate[1] : '',
        activateBeginTime: this.activeDate && this.activeDate.length > 0 ? this.activeDate[0] : '',
        activateEndTime: this.activeDate && this.activeDate.length > 0 ? this.activeDate[1] : '',
        createBeginTime: this.importDate && this.importDate.length > 0 ? this.importDate[0] : '',
        createEndTime: this.importDate && this.importDate.length > 0 ? this.importDate[1] : ''
      })
      this.activeLoading = true
      queryPadPages(params).then(res => {
        if (res.code === '000000') {

          this.activeTotal = res.data.total
          this.activationList = res.data.records
          this.activeLoading = false
        }
      }).catch(err => {

        this.activeLoading = false
      })
    },
    handleActiveFilter() {
      this.activeQuery.pageIndex = 1
      this.getActiveList()
    },
    // 重置
    handleActiveReset() {
      this.activeQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.bindDate = []
      this.activeDate = []
      this.importDate = []
      this.getActiveList()
    }
  }

}
</script>

<style>

</style>
