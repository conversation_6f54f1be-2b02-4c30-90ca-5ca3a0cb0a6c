<template>
  <div class="app-container">
    <div class="app-container bgGrey">
      <el-row :gutter="10">
        <el-col :lg="{span:24}">
          <el-form
            ref="form"
            size="small"
            :model="customerInfo"
            label-width="100px"
          >
            <el-card class="box-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span>客户信息</span>
              </div>

              <div class="item">
                <div class="item-header">
                  基础信息
                </div>
                <el-row>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="客户编号：">
                      <div>{{ customerInfo.clueCode }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="客户名称：">
                      <div>{{ customerInfo.customer }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="手机号码：">
                      <div>{{ customerInfo.mobile }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="校区名称：">
                      <div>{{ customerInfo.institution }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="机构区域：">
                      <div>{{ customerInfo.provinceName }} | {{ customerInfo.cityName }} | {{ customerInfo.areaName }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="机构地址：">
                      <div>{{ customerInfo.address }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{span:24}" :sm="{span:12}">
                    <el-form-item label="信息来源：">
                      <div class="information-sources">
                        {{customerInfo.originName}}
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="校区信息" name="campus" >
        <div class="app-container bgGrey">
          <el-row>
            <el-col :span="24">
              <el-card class="box-card" shadow="hover">
                <div slot="header" class="clearfix">
                  <span>我的校区</span>
                  <el-button v-permission="['customer:list:detail:school']" size="mini" type="primary" @click="createSchools">添加校区</el-button>
                </div>
                <div class="text">
                  <el-table
                    v-loading="schoolListLoading"
                    :data="schoolList"
                    border
                    fit
                    stripe
                    highlight-current-row
                  >
                    <af-table-column type="index" label="#" align="center" />
                    <af-table-column label="校区编号" prop="schoolCode" />
                    <af-table-column label="校区名称" prop="schoolName" />
                    <af-table-column label="签约区域" width="280px" show-overflow-tooltip>
                      <template slot-scope="scope">
                        <span>{{ scope.row.provinceName }} | {{ scope.row.cityName }} | {{ scope.row.areaName }}{{ scope.row.countyName ? (' | ' + scope.row.countyName) : '' }}</span>
                      </template>
                    </af-table-column>
                    <af-table-column label="详细地址" prop="address" />
                    <af-table-column label="校区经度/纬度">
                      <template slot-scope="scope">
                        <span>{{ scope.row.longitude }} / {{ scope.row.latitude }}</span>
                      </template>
                    </af-table-column>
                    <el-table-column label="操作" width="230" fixed="right">
                      <template slot-scope="{row}">
                       <div class="st-flex">
                         <el-button v-permission="['customer:list:detail:school']" type="primary" size="mini" @click="handleQuerySchool(row)">
                           查看
                         </el-button>
                         <el-button v-permission="['customer:list:detail:school:update']" type="primary" size="mini" @click="handleEditSchool(row)">
                           修改
                         </el-button>
                         <el-button v-permission="['customer:list:detail:school:update']" type="primary" size="mini" @click="handleDeleteSchool(row)">
                           删除
                         </el-button>
                       </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-card class="box-card" shadow="hover">
                <div slot="header" class="clearfix">
                  <span>加盟项目</span>
                </div>
                <div class="text">
                  <el-table
                    v-loading="projectListLoading"
                    :data="projectList"
                    border
                    fit
                    stripe
                    highlight-current-row
                  >

                    <af-table-column type="index" label="#" align="center" />
                    <af-table-column label="校区项目编号" prop="institutionCode" />
                    <af-table-column label="校区编号" prop="schoolCode" />
                    <af-table-column label="加盟项目" prop="projectName" />
                    <af-table-column label="加盟状态" prop="status" :formatter="getJoinStatusCN" />
                    <af-table-column label="合同时间(开始时间/结束时间)">
                      <template slot-scope="scope">
                        <span>{{ scope.row.startDate }} / {{ scope.row.endDate }}</span>
                      </template>
                    </af-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <relate-customer ref="relativeCustomerDialog" :current-id="currentCustomerId" :title="relativeTitle" @success="getCustomerRelations" />
          <create-new-school ref="createSchoolDialog" :type="schoolOption" :customer-id="currentCustomerId" :title="createSchoolTitle" @success="getSchoolList" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="跟进" name="follow" >
        <followTable :id="currentCustomerId"></followTable>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getCustomerDetail, getCustomerRelative, getJoinProjects, getCustomerSchoolList, cancelRelative } from '@/api/customer'
import { deleteSchool } from '@/api/school'
import { originList, joinStatusList, intentionList, converseEnToCn, businessTypeList } from '@/utils/field-conver'
import RelateCustomer from './componets/relateCustomer'
import CreateNewSchool from './componets/createSchool'
import followTable from "@/views/customer/componets/followTable.vue";
export default {
  name: 'CustomerDetail',
  components: { RelateCustomer, CreateNewSchool,followTable },
  directives: {},
  data() {
    return {
      activeName: 'campus',
      schoolOption: '',
      relativeTitle: '',
      createSchoolTitle: '',
      currentCustomerId: '', // 当前客户的id
      customerInfo: {},
      relationList: [],
      schoolList: [],
      projectList: [],
      relationListLoading: false,
      schoolListLoading: false,
      projectListLoading: false
    }
  },
  computed: {
  },
  created() {
    const id = this.$route.params && this.$route.params.id
    this.currentCustomerId = id
    this.getCustomerInfo(id)
    this.getCustomerRelations(id)
    this.getSchoolList(id)
    this.getProjectList(id)
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    /**
     * 查看线索/客户详情
     * @param row
     */
    getCustomerInfo(id) {
      getCustomerDetail(id).then(res => {
        this.customerInfo = res.data
      })
    },
    /**
     * 查看关联的客户
     * @param row
     */
    getCustomerRelations(id) {
      const params = { clueId: id || this.currentCustomerId }
      this.relationListLoading = true
      getCustomerRelative(params).then(res => {
        this.relationList = res.data
        this.relationListLoading = false
      })
    },
    /**
     * 获取我的校区
     * @param id
     */
    getSchoolList(id) {
      const params = { clueId: id || this.currentCustomerId }
      this.schoolListLoading = true
      getCustomerSchoolList(params).then(res => {
        this.schoolList = res.data
        this.schoolListLoading = false
      })
    },
    /**
     * 创建校区弹窗
     * */
    createSchools() {
      this.schoolOption = 'create'
      this.createSchoolTitle = '添加校区'
      this.$refs.createSchoolDialog.openDialog()
    },
    /**
     * 查看校区
     * */
    handleQuerySchool(row) {
      this.schoolOption = 'query'
      this.createSchoolTitle = '校区详情'
      this.$refs.createSchoolDialog.getSchool(row.id)
    },
    /**
     * 修改校区
     * */
    handleEditSchool(row) {
      this.schoolOption = 'update'
      this.createSchoolTitle = '修改校区'
      this.$refs.createSchoolDialog.getSchool(row.id)
    },
    /**
     * 删除校区
     * */
    handleDeleteSchool(row) {
      const params = {
        schoolId: row.id
      }
      this.$confirm('确认删除该校区吗？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        deleteSchool(params).then(res => {
          const msg = res.code === '000000' ? '取消关联成功' : res.msg
          this.$message({
            message: msg,
            type: 'success'
          })
          this.getSchoolList()
        })
      })
    },
    /**
     * 获取已加盟的项目列表
     * */
    getProjectList(id) {
      const params = { clueId: id }
      this.projectListLoading = true
      getJoinProjects(params).then(res => {
        this.projectList = res.data
        this.projectListLoading = false
      })
    },
    /**
     * 打开关联客户的弹窗
     **/
    toRelativeCustomer() {
      this.relativeTitle = '关联客户'
      this.$refs.relativeCustomerDialog.getLists()
    },
    /**
     * 转化信息来源
     */
    getInfoOrigin(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(originList, isNaN(data) ? data.origin : data)
    },
    /**
     * 转换加盟状态
     */
    getJoinStatusCN(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(joinStatusList, isNaN(data) ? data.status : data)
    },
    /**
     * 转换意向度key与value
     */
    getIntention(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(intentionList, isNaN(data) ? data.clueType : data)
    },
    getBusinessTypeList(data) {
      if (!data) {
        return '--'
      }
      return converseEnToCn(businessTypeList, isNaN(data) ? data.businessType : data)
    },
    /**
     * 取消客户的关联
     */
    handleCancelRelate(row) {
      const params = {
        clueId: this.currentCustomerId,
        relatedClueId: row.id
      }
      const id = this.$route.params && this.$route.params.id
      this.$confirm('确认取消关联吗？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        cancelRelative(params).then(res => {
          this.$message({
            message: '取消关联成功',
            type: 'success'
          })
          this.getCustomerRelations(id)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .el-row {
    margin-bottom: 10px;
  }
  /deep/ .el-card .el-card__header {
    position: relative;
  }
  .el-card__header .el-button {
    position: absolute;
    right: 20px;
    top: 10px;
  }
  .information-sources{
    display: flex;
    span{
      padding-right: 5px;
    }
  }
</style>
