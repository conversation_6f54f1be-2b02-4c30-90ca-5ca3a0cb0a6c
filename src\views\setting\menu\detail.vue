<template>
  <el-dialog v-el-drag-dialog :title="title" :visible.sync="dialogCreateMenu" :close-on-click-modal="!dialogCreateMenu" width="30%">
    <el-form :model="createMenuForm" :rules="createMenuFormRules" label-width="120px" :disabled="!disabled">
      <el-form-item label="菜单类型" label-width="120px">
        <el-radio-group v-model="createMenuForm.category">
          <el-radio :label="1">菜单</el-radio>
          <el-radio :label="2">按钮</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="父级菜单" prop="parentId">
        <el-select-tree
          v-model="createMenuForm.parentId"
          placeholder="请选择父级菜单"
          :data="treeList"
          :props="defaultProps"
          :check-strictly="checkAnyLevel"
        />
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="createMenuForm.name" autocomplete="off" />
      </el-form-item>
      <el-form-item label="菜单注释" prop="alias">
        <el-input v-model="createMenuForm.alias" autocomplete="off" />
      </el-form-item>
      <el-form-item v-if="createMenuForm.category === 1" label="菜单路径" prop="url">
        <el-input v-model="createMenuForm.url" autocomplete="off" />
      </el-form-item>
      <el-form-item label="菜单编码" prop="code">
        <el-input v-model="createMenuForm.code" autocomplete="off" />
      </el-form-item>
      <el-form-item v-if="createMenuForm.category === 1" label="菜单排序" prop="sort">
        <el-input v-model="createMenuForm.sort" autocomplete="off" />
      </el-form-item>
      <el-form-item label="是否启用" prop="valid">
        <el-radio-group v-model="createMenuForm.valid">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer text-center">
      <el-button @click="dialogCreateMenu = false">取消</el-button>
      <el-button v-if="!isUpdate" type="primary" @click="confirmCreate">确定</el-button>
      <el-button v-if="isUpdate" type="primary" @click="confirmUpdate">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getMenuTree, createMenu, getMenuDetail, editMenus } from '@/api/system-setting'
import ElSelectTree from '@/components/el-select-tree'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  name: 'MenuDetail',
  components: { ElSelectTree },
  directives: { elDragDialog },
  props: {
    disabled: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isUpdate: false, // 是否为查看菜单详情
      dialogCreateMenu: false, // 增删查菜单
      isCloseSelect: false,
      currentMenuId: '',
      createMenuForm: {
        valid:1,
        category:2,
      },
      createMenuFormRules: {
        name: {
          required: true,
          message: '菜单/按钮名称必填'
        },
        alias: {
          required: true,
          message: '菜单/按钮别名必填'
        },
        code: {
          required: true,
          message: '编码必填'
        },
        sort: {
          required: true,
          message: '菜单/按钮排序必填'
        },
        url: {
          required: true,
          message: '菜单/按钮访问路径必填'
        }
      },
      treeList: [],
      defaultProps: {
        value: 'id', label: 'name', children: 'childes'
      },
      checkAnyLevel: true
    }
  },
  methods: {
    getMenuTreeList() {
      getMenuTree().then(res => {
        this.treeList = res.data
        this.dialogCreateMenu = true
      })
    },
    /**
       * 新增菜单
       **/
    confirmCreate() {
      createMenu(this.createMenuForm).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '新增菜单成功',
            type: 'success'
          })
          this.dialogCreateMenu = false
          this.createMenuForm = {}
          /**
             * 通知父组件更新
             */
          this.$emit('refreh')
        }
      }).catch(() => {

      })
    },
    /**
       * 修改
       * */
    confirmUpdate() {
      const params = Object.assign({ id: this.currentMenuId }, this.createMenuForm)
      editMenus(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.dialogCreateMenu = false
          this.createMenuForm = {}
          /**
             * 通知父组件更新
             */
          this.$emit('refreh')
        }
      }).catch(() => {

      })
    },
    /**
       * 获取菜单详情
       */
    getMenusDetail(id) {
      this.currentMenuId = id
      getMenuDetail(id).then(res => {
        this.dialogCreateMenu = true
        this.createMenuForm = res.data
      })
    }
  }
}
</script>

<style scoped>

</style>
