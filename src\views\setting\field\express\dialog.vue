<template>
  <el-dialog v-el-drag-dialog title="快递详情" :visible.sync="expressDialog" :close-on-click-modal="!expressDialog">
    <el-form
      ref="detailForm"
      :model="detail"
      label-width="120px"
      :rules="baseInfoRules"
    >
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="快递公司编号：" prop="expressCode"><el-input v-model="detail.expressCode" placeholder="快递公司编号" /></el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24">
          <el-form-item label="快递公司名称：" prop="expressName"><el-input v-model="detail.expressName" placeholder="快递公司名称" /></el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmExpressDetail">确 定</el-button>
      <el-button type="primary" @click="closeExpressDetail">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addExpress, editExpress } from '@/api/system-setting'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui

export default {
  name: 'ExpressDialog',
  directives: {
    elDragDialog
  },
  props: {
    'isEdit': {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      expressDialog: false,
      detail: {},
      baseInfoRules: {
        expressCode: { required: true, message: '快递公司编号必填', trigger: 'blur' },
        expressName: { required: true, message: '快递公司名称必填', trigger: 'blur' }
      }
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      const that = this
      that.detail = {}
      that.expressDialog = true
    },
    editDetail(data) {
      const that = this
      that.detail = data
      that.expressDialog = true
    },
    /**
     * 确认修改信息
     */
    confirmExpressDetail() {
      const that = this
      that.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!that.isEdit) {
            addExpress(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '新增成功！',
                  type: 'success'
                })
              }
              that.expressDialog = false
              that.$emit('refresh')
            })
          } else {
            editExpress(that.detail).then(res => {
              if (res.code === '000000') {
                that.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              }
              that.expressDialog = false
              that.$emit('refresh')
            })
          }
        }
      })
    },
    closeExpressDetail() {
      this.expressDialog = false
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
</style>
