import { getBtns, getMenuList } from '@/utils/auth'
import { getUserInfo } from '@/utils/auth'
export default (Vue) => {
  Vue.directive('permission', {
    inserted(el, binding, vnode) {
      const { value } = binding
      const btnList = getBtns()

      if (value && value instanceof Array && value.length > 0) {
        const bindBtn = value
        const hasPermission = btnList.some(btn => {
          return bindBtn.includes(btn.code)
        })

        if (!hasPermission && getUserInfo()['userName'] !== 'admin') {
          el.parentNode && el.parentNode.removeChild(el)
        }
      } else {
        throw new Error(`需要写入权限code!形如 v-permission="['clue:manage']"`)
      }
    }
  })

  Vue.directive('permissionMenu', {
    inserted(el, binding, vnode) {
      const { value } = binding
      const btnList = getMenuList()

      if (value && value instanceof Array && value.length > 0) {
        const bindBtn = value
        const hasPermission = btnList.some(btn => {
          return bindBtn.includes(btn.code)
        })

        if (!hasPermission && getUserInfo()['userName'] !== 'admin') {
          el.parentNode && el.parentNode.removeChild(el)
        }
      } else {
        throw new Error(`需要写入权限code!形如 v-permission="['clue:manage']"`)
      }
    }
  })
}
