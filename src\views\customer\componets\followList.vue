<template>
  <el-dialog
    v-if="dialogFollow"
    title="新增跟进"
    width="50%"
    :visible.sync="dialogFollow"
  >
      <el-form
        ref="followModule"
        :inline="false"
        :model="followModule"
        :rules="followModuleRules"
        class="demo-form-inline"
      >
            <el-form-item label="跟进模板" prop="followType">
              <el-select v-model="followModule.followType" placeholder="请选择" filterable @change="templateChangeHandle">
                <el-option v-for="(item,index) in followStyleList" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="跟进内容" prop="remark">
              <el-input v-model="followModule.remark" type="textarea" maxlength="1000" show-word-limit :autosize="{ minRows: 8, maxRows: 16 }" />
            </el-form-item>
            <el-row>
            <el-col :sm="24" :md="24">
              <el-form-item label="下次跟进时间" prop="nextFollowTime">
                <el-date-picker
                  v-model="followModule.nextFollowTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
            <el-form-item label="附件上传">
              <el-upload
                :action="host"
                :data="aliData"
                accept=".jpg,.jpeg,.png,.pdf,video/*,audio/*"
                :http-request="leaseContractUpload"
                :on-preview="handlePictureCardPreview"
                :on-exceed="exceed"
                :on-remove="leaseContractRemove"
                list-type="picture"
                :file-list="fllowImgs"
                :limit="5"
                :class="{hide: fllowUpdate}"
              >
                <i slot="default" class="el-icon-plus" />
                <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件/音视频文件</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="">
              <div class="st-flex st-mt-[10px]">
                <div v-for="(item,index) in fllowImgs" :key="index" class=" first:st-ml-0 st-ml-[10px] preview-box" >
                  <el-image
                    class="st-w-[80px] st-h-[80px] st-cursor-pointer"
                    @click="previewPdfHandle(item.imagePath)"
                    v-if="item.imagePath.endsWith('.pdf')"
                    :src="pdfImage">
                  </el-image>
                  <el-image
                    class="st-w-[80px] st-h-[80px] st-cursor-pointer"
                    :src="item.imagePath"
                    v-if="/\.(jpe?g|png)$/i.test(item.imagePath)"
                    :preview-src-list="[item.imagePath]">
                  </el-image>
                  <video
                    v-if="/\.(mp4|avi|mov|wmv|flv|mkv|webm|mpeg|3gp|ts)$/i.test(item.imagePath)"
                    :src="item.imagePath"
                    controls
                    class="st-w-[80px] st-h-[80px] st-cursor-pointer"
                  ></video>
                  <div class="audio-content" v-else-if="/\.(mp3|wav|ogg|flac|aac)$/i.test(item.imagePath)">
                    <audio
                       :src="item.imagePath"
                       controls
                       class="st-w-[60px] st-h-[20px] st-cursor-pointer"
                     ></audio>
                  </div>
                  <i class="el-icon-error" @click="deleteFile(index)"></i>
                </div>
              </div>
            </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button size="small" round @click="cancelAdd">取 消</el-button>
        <el-button type="primary" round size="small" @click="confirmAddFollow">确 定</el-button>
      </template>
    </el-dialog>
</template>

<script>
import {
  addFollow,
} from '@/api/customer'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import {
  followTemplateList,
} from '@/utils/field-conver'
import {
  parseTime
} from '@/utils'
import { uuid } from '@/utils/index'
import { getOSSClient } from '@/components/upload/getOSSTst'
import pdfImage from '@/assets/img/pdf.png'
import _ from 'lodash'
export default {
  name: 'FollowList',
  directives: {
    elDragDialog
  },
  components: {},
  props: {},
  data() {
    return {
      pdfImage,
      currentCustomerId: '',
      dialogFollow: false,
      followListLoading: false,
      followList: [],
      total: 0,
      showCreate: true,
      followModule: {
        followType: '2',
        remark: '',
        nextFollowTime: ''
      },
      fllowImgs: [],
      client:null,
      followStyleList: followTemplateList,
      followModuleRules: {
        remark: {
          required: true,
          message: '请输入跟进内容',
          trigger: 'blur'
        },
        followType: {
          required: true,
          message: '请选择跟进模板',
          trigger: 'blur'
        }
      },
      host: 'https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/',
      aliData: {
        name: '',
        key: 'upload/sign/' + '${filename}',
        success_action_status: '200'
      },
      dialogImageUrl: '',
      dialogVisible: false,
      fllowUpdate: true,
      school: {}
    }
  },
  async mounted() {
    this.client = await getOSSClient('santtaojiaoyu')
    this.createFollow();
    this.$set(this.followModule, 'followType', '2');
    this.templateChangeHandle(this.followModule.followType);
  },
  methods: {

    /**
     * 模板切换
     * @param value
     */
    templateChangeHandle(value){
      const findObj = this.followStyleList.find(item => {
        return item.value === value;
      })
      this.$set(this.followModule, 'remark', findObj.desc)
    },

    /**
       * 预览pdf
       */
    previewPdfHandle(item) {
      window.open(item, '_blank');
    },

    /**
       * 获取跟进记录
       * */
    getLists(id) {
      this.showCreate = true;
      this.dialogFollow = true;
      this.currentCustomerId = id
    },

    /**
       * 新增跟进
       */
    confirmAddFollow() {
      let pics = []
      if (this.fllowImgs && this.fllowImgs.length > 0) {
        pics = this.fllowImgs.map(item => {
          return item.imagePath || ''
        })
      }
      const params = Object.assign({
        clueId: this.currentCustomerId,
        pic: pics.join(',')
      }, this.followModule)
      this.$refs.followModule.validate((valid) => {
        if (valid) {
          addFollow(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '跟进记录添加成功',
                type: 'success'
              })
              // this.getLists(this.currentCustomerId)
              this.followModule = {}
              this.dialogFollow = false
              this.showCreate = false
              this.fllowImgs = []
              /**
                 * 通知父组件更新
                 */
              this.$emit('refresh')
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    createFollow() {
      this.followModule = {}
      this.showCreate = true
    },
    closedFollow() {
      this.cancelAdd()
      this.followModule = {}
      this.dialogFollow = false
    },
    cancelAdd() {
      this.followModule = {}
      this.dialogFollow = false
      this.showCreate = false
      this.fllowImgs = []
      /**
         * 通知父组件更新
         */
      this.$emit('refresh')
    },
    getFollowTime(row) {
      return parseTime(row.nextFollowTime, '{y}-{m}-{d}')
    },
    exceed() {
      this.$message({
        type: 'warning',
        message: '超出文件上传个数'
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      window.open(this.dialogImageUrl, '_blank');
    },

    leaseContractUpload(params) {
      var file = params.file
      const tempName = file.name.split('.')
      const fileName = 'upload/sign/' + uuid() + '.' + tempName[tempName.length - 1]
      const obj = {}
      this.client.put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          obj['imagePath'] = url

          this.fllowImgs.push(obj)
          if (this.school.leaseContract) this.school.leaseContract.push(obj)
        }

      }).catch((err) => {

      })
    },
    // 删除附件
    deleteFile(index) {
      this.fllowImgs.splice(index, 1);
    },
    leaseContractRemove(file) {

      this.fllowImgs.forEach((item, index, arr) => {
        if (item.imagePath === file.imagePath) {

          this.client.delete(item.imagePath).then(res => {

            arr.splice(index, 1)
          }).catch(() => {

          })
        }
      })
    }
  }
}
</script>

<style scoped>

  /deep/ .el-upload-list--picture {
    display: none !important;
  }
  /deep/ .el-upload-list--picture .el-upload-list__item-name i::before{
   /* font-size: 44px; */
  }
  /deep/ .el-upload--picture .el-icon-plus::before{
    font-size: 20px;
   /* font-size: 44px; */
  }
  .preview-box {
    position: relative;
    margin-top: -16px;
  }
  .el-icon-error  {
    cursor: pointer;
    background-color: #fff;
    position: absolute;
    right: 0;
    top: 0;
  }
  .audio-content {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 8px 0px rgb(48 65 86 / 20%);
  }
  .audio-content-table {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 8px 0px rgb(48 65 86 / 20%);
  }

</style>
