<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.dictCode" placeholder="海报分类" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in posterCate" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="产品线" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.posterStatus" placeholder="海报状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in StatusList" :key="item.id" :label="item.val" :value="item.id" />
      </el-select>
      <el-date-picker
              v-model="followDate"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="上架日期开始"
              end-placeholder="上架日期结束"
      />
      <el-date-picker
              v-model="shelvesDate"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="下架日期开始"
              end-placeholder="下架日期结束"
      />
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['posters:index:upload']" class="filter-item" size="mini" type="primary"
                 @click="handleCreate">
        添加海报
      </el-button>
    </div>
    <div v-if="posters&&posters.length>0">
      <el-row class="poster-list" >
        <el-col v-for="(item,i) in posters" :key="i" class="poster-item">
          <img :src="item.resourcesUrl+'?x-oss-process=image/resize,w_600/quality,Q_50'">
          <div class="poster-opera">
            <el-button-group>
              <el-button v-permission="['posters:index:opera']" size="mini" plain :type="item.posterStatus===1?'primary':'danger'"
                         @click="shelvesPoster(item)">{{ item.posterStatus === 1 ? '下架' : '上架' }}
              </el-button>
              <el-button v-permission="['posters:index:edit']" size="mini" plain type="primary"
                         @click="showPoster=true,editPoster(item)">编辑
              </el-button>
              <el-button v-permission="['posters:index:del']" size="mini" plain type="danger" @click="deletePoster(item)">删除
              </el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
      <pagination
              v-show="total>0"
              :total="total"
              :page.sync="listQuery.current"
              :limit.sync="listQuery.pageSize"
              @pagination="getList"
      />
    </div>
    <p v-else class="no-data">暂无数据</p>
    <upload-poster ref="uploadPoster" :client-code="clientCode" :poster-cate="posterCate" @refresh="getList" />
    <!--编辑海报-->
    <el-dialog :visible.sync="showPoster" title="编辑海报" :close-on-click-modal="!showPoster" width="30%">
      <el-form label-width="80px" :model="posterObj">
        <el-form-item label="海报分类">
          <el-select v-model="posterObj.dictCode" placeholder="海报分类" filterable clearable class="filter-item">
            <el-option v-for="item in posterCate" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品线">
          <el-select v-model="posterObj.clientCode" placeholder="海报分类" filterable clearable class="filter-item">
            <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" @click="confirmPoster">确定</el-button>
          <el-button size="mini" type="default" @click="showPoster=false,cancelPoster">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--编辑海报-->
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import UploadPoster from './uploadPoster/uploadPoster'
import { clientCode } from '@/api/classType'
import { posterCate, posterList, deletPoster, setPoster, updatePoster } from '@/api/poster'

export default {
  name: 'Posters',
  components: {
    Pagination,
    UploadPoster
  },
  data() {
    return {
      signTime: [],
      listQuery: {
        current: 1,
        pageSize: 50
      },
      total: 0,
      listLoading: false,
      clientCode: [],
      posterCate: [],
      StatusList: [
        {
          id: 1,
          val: '上架'
        },
        {
          id: 2,
          val: '下架'
        }
      ],
      posters: [],
      posterObj: {},
      showPoster: false,
      followDate: [],
      shelvesDate: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
      this.getPoster('poster_type')
    })
  },
  methods: {
    async getList() {
      this.listLoading = true
      const params = Object.assign({}, this.listQuery, {
        startPutawayTime: this.followDate[0] ? this.followDate[0] : '',
        endPutawayTime: this.followDate[1] ? this.followDate[1] : '',
        startOffShelvesTime: this.shelvesDate[0] ? this.shelvesDate[0] : '',
        endOffShelvesTime: this.shelvesDate[1] ? this.shelvesDate[1] : ''
      })
      posterList(params).then(res => {
        if (res.code === '000000') {
          this.posters = res.data.records || []
          this.total = res.data.total
          this.listLoading = false
        }
      }).catch(res => {

      })
    },
    handleFilter() {
      this.listQuery.current = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        current: 1,
        pageSize: 10
      }
      this.followDate = []
      this.shelvesDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.uploadPoster.posterFlag = true
      this.$refs.uploadPoster.posterTitle = '上传海报'
    },
    getCode() {
      clientCode().then(res => {
        const clientCodes = res.data || []
        this.clientCode = clientCodes.filter(item => item.level === 1)
      })
    },
    getPoster(type) {
      posterCate(type).then(res => {
        if (res.code === '000000') {
          this.posterCate = res.data || []
        }
      })
    },
    shelvesPoster(item) {
      const tips = item.posterStatus === 1 ? '确定要下架该海报?' : '确定要上架该海报?'
      this.$confirm(`${ tips }`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setPoster(item.id, item.posterStatus === 1 ? 2 : 1).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    },
    editPoster(item) {
      this.posterObj = item
      this.posterObj.clientCode = Number(item.clientCode)
    },
    confirmPoster() {
      const params = Object.assign({}, {
        id: this.posterObj.id,
        clientCode: this.posterObj.clientCode,
        dictCode: this.posterObj.dictCode
      })
      updatePoster(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '编辑成功'
          })
          this.showPoster = false
          this.getList()
        }
      }).catch(() => {

      })
    },
    cancelPoster() {
    },
    deletePoster(item) {
      this.$confirm('确定要删除该数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletPoster(item.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        }).catch(res => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消操作'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
//海报管理
.poster-list {

  column-count: 8;
  column-width: 180px;
  column-gap: 5px;

  .poster-item {
    position: relative;
    width: 100%;
    margin-right: 0;
    margin-bottom: 5px;
    border: 1px solid #ccc;
    break-inside: avoid;

    img {
      width: 100%;
      vertical-align: middle;
    }

    .poster-opera {
      padding: 5px 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
      border: 1px solid #ccc;
    }
  }
}
</style>
