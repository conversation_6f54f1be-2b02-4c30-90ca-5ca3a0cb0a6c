<template>
  <div class="s-canvas">
    <img :src="'data:image/png;base64,'+imgUrl" alt=""  @click="initCode"/>
  </div>
</template>

<script>
import { getImgVerificationCode } from '@/api/login'

export default {
  name: 'ImgVerificationCode',
  data(){
    return {
      imgUrl:'',
      captchaId:''
    }
  },
  props: {
  },
  created() {
    this.initCode()
  },
  methods: {
    initCode(){
      getImgVerificationCode().then(res=>{
          this.imgUrl =res.data.captchaBase64;
          this.captchaId=res.data.captchaToken;
          this.$emit('setCaptchaId',this.captchaId)
      })
    }

  }
}
</script>

<style scoped>

</style>
