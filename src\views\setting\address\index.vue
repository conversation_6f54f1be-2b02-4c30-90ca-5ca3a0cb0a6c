<template>
  <div class="address">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="区域id" class="filter-item" style="width: 200px;" clearable />
      <el-input v-model="listQuery.name" placeholder="区域名称" class="filter-item" style="width: 200px;" clearable />
      <el-input v-model="listQuery.parentId" placeholder="父区域id" class="filter-item" style="width: 200px;" clearable />
      <el-select v-model="listQuery.level" placeholder="区域等级" filterable class="filter-item" style="width: 120px;" clearable>
        <el-option
          v-for="item in grades"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" size="mini" type="primary" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-permission="['setting:address:addArea']" v-waves class="filter-item" type="primary" size="mini" @click="addAddress">
        新增区域
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="区域id" show-overflow-tooltip prop="id" />
      <af-table-column label="名称" show-overflow-tooltip prop="name" />
      <af-table-column label="级别" show-overflow-tooltip prop="level" :formatter="getLevel" />
      <af-table-column label="全名称" prop="fullName" show-overflow-tooltip />
      <af-table-column label="区域行政码" show-overflow-tooltip prop="areaCode" />
      <el-table-column label="父区域id" prop="parentId" show-overflow-tooltip width="120px" />
      <af-table-column label="父级区域名称" prop="parentName" show-overflow-tooltip />
      <af-table-column label="父级区域行政码" prop="parentAreaCode" show-overflow-tooltip width="150px" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:address:detailArea']" type="primary" size="mini" @click="detailAddress(row)">详情</el-button>
          <el-button v-permission="['setting:address:editArea']" type="primary" size="mini" @click="editAddress(row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!-- 添加区域弹框 -->
    <area-pop ref="addArea" @updateArea="getList" />
  </div>
</template>

<script>
import { grades, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import { getAddressList } from '@/api/addressList.js'
import AreaPop from './components/areaPop.vue'
export default {
  name: 'Address',
  components: {
    Pagination,
    AreaPop
  },
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        orderCode: ''
      },
      total: 0,
      grades: grades
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)
      await getAddressList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      })
    },
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 重置
     * */
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    addAddress() { // 省市区新增区域
      this.$refs.addArea.setTile('新增区域', 1, false)
      this.$refs.addArea.areaShow = true
      this.$refs.addArea.areaForm = {}
      this.$refs.addArea.isEdit = false
    },
    editAddress(row) { // 省市区修改区域
      this.$refs.addArea.setTile('修改区域', 2, false)
      this.$refs.addArea.setArea(row)
      this.$refs.addArea.areaShow = true
      this.$refs.addArea.isEdit = false
    },
    detailAddress(row) { // 省市区详情
      this.$refs.addArea.setTile('区域详情', 3, false)
      this.$refs.addArea.setArea(row)
      this.$refs.addArea.areaShow = true
      this.$refs.addArea.isEdit = true
    },
    getLevel(row) {
      return converseEnToCn(this.grades, row.level)
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
  .address{
    padding: 20px;
  }
</style>
