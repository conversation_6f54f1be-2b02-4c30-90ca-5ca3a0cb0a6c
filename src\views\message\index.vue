<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="消息编号"
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.messageType" placeholder="消息类型" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in messageTypes" :key="item.id" :label="item.name" :value="item.code" />
      </el-select>
      <el-select v-model="listQuery.clientCode" placeholder="客户端" filterable clearable class="filter-item" style="width: 150px;">
        <el-option v-for="item in clientCode" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建的开始时间"
        end-placeholder="创建的结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['message:add']" class="filter-item" type="primary" size="mini" @click="addMessage">
        新增
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="消息编号" show-overflow-tooltip prop="id" width="80px">
        <template slot-scope="scope">
          <!--<a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>-->
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <!-- <af-table-column label="图片" prop="imageResource">
        <template slot-scope="scope">
          <img v-if="scope.row.imageResourceUrl" :src="scope.row.imageResourceUrl" class="cover-img">
          <span v-else>/</span>
        </template>
      </af-table-column> -->
      <af-table-column label="消息类型" show-overflow-tooltip prop="messageTypeName" />
      <af-table-column label="消息标题" prop="title" show-overflow-tooltip min-width="150px" />
      <!-- <af-table-column label="消息简介" prop="message" width="250px" /> -->
      <!-- <af-table-column label="消息详情" prop="details" width="250px">
        <template slot-scope="scope">
          <span v-if="scope.row.detailsType===1">{{ scope.row.details }}</span>
        </template>
      </af-table-column> -->
      <af-table-column label="消息外链" prop="detailsPath" width="100px">
        <template slot-scope="scope">
          <el-link type="primary" v-if="scope.row.detailsType===2" :href="scope.row.detailsPath" target="_blank">查看</el-link>
        </template>
      </af-table-column>
      <el-table-column label="接收范围" prop="messageScope" min-width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.messageScope===1">全部合伙人</span>
          <span v-if="scope.row.messageScope===2">指定合伙人</span>
          <span v-if="scope.row.messageScope===3">全部校区</span>
          <span v-if="scope.row.messageScope===4">仅总校</span>
          <span v-if="scope.row.messageScope===5">仅分校</span>
        </template>
      </el-table-column>
      <af-table-column label="客户端" prop="clientName" show-overflow-tooltip />
      <af-table-column label="有效时间">
        <template slot-scope="scope">
          <span v-if="scope.row.beginTime&&scope.row.endTime">{{ scope.row.beginTime }}-{{ scope.row.endTime }}</span>
          <span v-else>/</span>
        </template>
      </af-table-column>
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="280" fixed="right">
        <template slot-scope="{row}">
          <el-button v-permission="['message:recevive']" type="primary" size="mini" @click="receviveMessage(row)">接收人员</el-button>
          <el-button v-show="row.messageTypeName!=='续约提醒'" v-permission="['message:edit']" type="primary" size="mini" @click="editMessage(row)">修改</el-button>
          <el-button v-permission="['message:del']" type="primary" size="mini" @click="delMessage(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <message-pop ref="message" @addMessageList="getList" />
    <!-- 接收人员弹框 -->
    <receive-mess ref="receiveMess" />
    <!-- 接收人员弹框 -->
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { clientCode } from '@/api/classType'
import { getMessageList, delMess, messType } from '@/api/message'
import { isShows, converseEnToCn } from '@/utils/field-conver'
import MessagePop from './components/messagePop'
import ReceiveMess from './components/receiveMess'
import { getClientList } from './common'
export default {
  name: 'MessageList',
  components: {
    Pagination,
    MessagePop,
    ReceiveMess
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        menuType: 0
      },
      clientCode: [],
      isShows: isShows,
      messageTypes: [],
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getCode()
      this.getMessageType()
    })
  },
  methods: {
    getCode() {
      getClientList().then(res => {
        this.clientCode = res
      })
    },
    getMessageType() { // 消息类型
      messType().then(res => {
        this.messageTypes = res.data || []
      })
    },
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { createBeginTime: this.followDate[0] ? this.followDate[0] : '', createEndTime: this.followDate[1] ? this.followDate[1] : '' })

      await getMessageList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = this.$options.data().listQuery
      this.followDate = []
      this.getList()
    },
    getIshow(row) {
      return converseEnToCn(this.isShows, row.isShow)
    },
    getDetail(row) { // 获取列表详情
    },
    addMessage() {
      this.$refs.message.messagePop = true
      this.$refs.message.messageTitle = '新增消息'
      this.$refs.message.isEdit = false
      this.$refs.message.flags = 1
    },
    editMessage(row) {
      this.$refs.message.messagePop = true
      this.$refs.message.messageTitle = '修改消息'
      this.$refs.message.messageDetail(row.id)
      this.$refs.message.isEdit = false
      this.$refs.message.flags = 0
      this.$refs.message.messId = row.id
    },
    delMessage(row) {
      const params = {
        id: row.id
      }
      this.$confirm('确定要删除该条数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delMess(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: '取消删除'
        })
      })
    },
    receviveMessage(row) { // 接收人员
      this.$refs.receiveMess.receiveTitle = '信息接收人员'
      this.$refs.receiveMess.receivePop = true
      this.$refs.receiveMess.getList(row.id)
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
