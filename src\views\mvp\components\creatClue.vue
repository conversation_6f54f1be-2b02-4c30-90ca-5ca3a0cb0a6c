<template>
  <el-dialog title="添加家长线索" :visible.sync="cluePop" :close-on-click-modal="!cluePop" width="40%">
    <el-form ref="clueForm" :model="clueParent" :rules="rulesClue" label-width="85px" class="creat-clue">
      <el-form-item label="姓名" prop="customerName">
        <el-input v-model="clueParent.customerName" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="手机号" prop="customerPhone">
        <el-input v-model="clueParent.customerPhone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="所在区域">
        <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="subClue">提交</el-button>
        <el-button type="infor" size="mini" @click="cancelClue">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
import AreaPicker from '@/components/area-picker'
import { validPhone, customer } from '@/utils/validate.js'
import { customerClues } from '@/api/mvp'
export default {//
  components: {
    AreaPicker
  },
  data() {
    return {
      cluePop: false,
      clueParent: {},
      areaList: {
        province: '',
        city: '',
        area: ''
      },
      rulesClue: {
        customerName: { required: true, validator: customer, trigger: 'blur' },
        customerPhone: { required: true, validator: validPhone, trigger: 'blur' }
      }
    }
  },
  methods: {
    /**
         * 获取省市区的地址
         * */
    getAreaList(data) {
      this.areaList.province = data.provinceId
      this.areaList.city = data.cityId
      this.areaList.area = data.areaId
    },
    subClue() {
      const params = Object.assign({}, this.clueParent, { province: this.areaList.province, city: this.areaList.city, area: this.areaList.area })
      this.$refs.clueForm.validate(valid => {
        if (valid) {
          customerClues(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '添加成功!'
              })
              this.cluePop = false
              this.$emit('updateClue')
              this.clueParent = {}
              this.areaList = {}
            }
          }).catch(() => {

          })
        } else {

          return false
        }
      })
    },
    cancelClue() {
      this.cluePop = false
      if (this.$refs.clueForm) {
        this.$refs.clueForm.resetFields()
      }
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
/deep/.creat-clue .el-input{
    width: 99%;
  }
</style>
