<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.feedbackId"
        placeholder="反馈编号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.queryInfo"
        placeholder="合伙人/手机号/校区名称"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-date-picker
        v-model="listQuery.startTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="反馈开始时间"
      />
      <el-date-picker
        v-model="listQuery.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="反馈结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="机构反馈编号" show-overflow-tooltip prop="feedbackId" width="120px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.feedbackId }}</a>
        </template>
      </el-table-column>
      <af-table-column label="合伙人(机构账号)" show-overflow-tooltip width="200px">
        <template slot-scope="scope">
          <span>{{ scope.row.partnerName }}(</span>
          <span>{{ scope.row.partnerAccount }})</span>
        </template>
      </af-table-column>
      <af-table-column label="校区名称" prop="schoolName" show-overflow-tooltip />
      <af-table-column label="校区地址" prop="schoolAddress" show-overflow-tooltip />
      <af-table-column label="反馈内容" prop="content" />
      <af-table-column label="反馈时间" prop="feedbackTime" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { getnstitutionsList } from '@/api/feedback'
export default {
  name: 'InstitutionsFeedback',
  components: {
    Pagination
  },
  data() {
    return {
      list: [
      ],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      }
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery)

      await getnstitutionsList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    getDetail(row) { // 获取列表详情

    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
