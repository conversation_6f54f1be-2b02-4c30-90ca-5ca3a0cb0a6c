import request from '@/utils/request'
/**
 * 全部打款记录列表
 * @param data
 */
export function getPaymentRecordList(data) {
  return request({
    url: 'orderPayRecord/list',
    method: 'GET',
    params: data
  })
}
/**
 * 打款记录详情
 * @param data
 */
export function getOrderPaymentRecord(data) {
  return request({
    url: 'orderPayRecord/getRecord',
    method: 'GET',
    params: data
  })
}
/**
 * 确认打款记录
 * @param data
 */
export function confirmPaymentRecord(data) {
  return request({
    url: 'orderPayRecord/auditRecord',
    method: 'PUT',
    data: data
  })
}

/**
 * 强制删除打款记录
 * @param id
 * @returns {*}
 */
export function forceDelPaymentRecord(id) {
  return request({
    url: 'orderPayRecord/deleteRecord',
    method: 'DELETE',
    params: { id: id }
  })
}

/**
 * 获取订单打款记录
 * @param data
 */
export function getOrderPaymentRecordList(data) {
  return request({
    url: 'orderPayRecord/listRecord',
    method: 'get',
    params: data
  })
}
/**
 * 新增打款记录详情
 * @param data
 */
export function createPaymentDetail(data) {
  return request({
    url: 'orderPayRecord/createRecord',
    method: 'POST',
    data: data
  })
}
/**
 * 订单打款记录详情
 * @param data
 */
export function getOrderPaymentDetail(data) {
  return request({
    url: 'orderPayRecord/listRecord',
    method: 'GET',
    params: data
  })
}

/**
 * 修改打款记录详情
 * @param data
 */
export function modifyPaymentDetail(data) {
  return request({
    url: 'orderPayRecord/modifyRecord',
    method: 'PUT',
    data: data
  })
}
/**
 * 删除打款记录详情
 * @param data
 */
export function deletePaymentRecord(data) {
  return request({
    url: 'orderPayRecord/removeRecord',
    method: 'DELETE',
    params: data
  })
}
/**
 * 提交打款记录
 * @param data
 */
export function submitPaymentRecord(data) {
  return request({
    url: 'orderPayRecord/submitRecord',
    method: 'PUT',
    params: data
  })
}
/**
 * 审核打款记录
 * @param data
 */
export function auditPaymentRecord(data) {
  return request({
    url: 'orderPayRecord/auditRecord',
    method: 'PUT',
    data: data
  })
}
/**
 * 打款记录详情
 * @param data
 */
export function payDetail(ids) {
  return request({
    url: `orderPayRecord/getRecord?id=${ids}`,
    method: 'GET'
  })
}
