<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.dictName" placeholder="字典名称" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.dictCode" placeholder="字典编码" class="filter-item" style="width: 200px;" clearable @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['setting:dictionary:create']" class="filter-item" type="primary" size="mini" @click="handleAdd">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
    >
      <af-table-column label="字典名称" prop="dictName" />
      <af-table-column label="字典编码" prop="dictCode" />
      <af-table-column label="描述" prop="description" />
      <af-table-column label="是否可用" prop="valid" :formatter="getMenuValidList" />
      <af-table-column label="操作" class-name="small-padding fixed-width action-warp" fixed="right" width="300">
        <template slot-scope="{row}">
          <el-button v-permission="['setting:dictionary:update']" type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button v-permission="['setting:dictionary:set']" type="primary" size="mini" @click="handleConfig(row)">
            字典配置
          </el-button>
          <!-- <el-button :type="buttonColor(row.valid)" size="mini" @click="handleStatus(row)">
            {{ getButtonText(row.valid) }}
          </el-button> -->
          <el-button v-if="row.valid==1" type="danger" size="mini" @click="handleStatus(row)">停用</el-button>
          <el-button v-else v-permission="['setting:dictionary:enable']" type="primary" size="mini" @click="handleStatus(row)">启用</el-button>
        </template>
      </af-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <dictionary-dialog ref="dictionaryDialog" :is-edit="isEdit" @refresh="getList" />
    <el-drawer
      title="字典列表"
      :visible.sync="drawer"
      :destroy-on-close="true"
      :wrapper-closable="false"
    >
      <dict-drawer :dict-id="dictId" />
    </el-drawer>
  </div>
</template>

<script>
import { editDictStatus, getDictionaryList } from '@/api/system-setting'
import { menuValidList, converseEnToCn } from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import dictionaryDialog from './dialog'
import dictDrawer from './drawer'

export default {
  name: 'Express',
  components: { Pagination, dictionaryDialog, dictDrawer },
  directives: {},
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      rolePermission: {
        roleIds: [],
        roles: []
      },
      isEdit: false,
      menuValidList: menuValidList,
      buttonText: '',
      drawer: false,
      dictId: 0
    }
  },
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    /**
     * 查询列表
     * */
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
     * 新增字典
     * */
    handleAdd() {
      this.$refs.dictionaryDialog.getDetail()
      this.isEdit = false
    },

    getList() {
      const that = this
      getDictionaryList(that.listQuery).then(res => {
        that.list = res.data.records
        that.total = res.data.total
      })
    },
    /**
     * 修改字典
     * */
    handleUpdate(row) {
      this.isEdit = true
      const form = JSON.parse(JSON.stringify(row))
      this.$refs.dictionaryDialog.editDetail(form)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleConfig(row) {
      this.dictId = row.id
      this.drawer = true
    },
    /**
     * 更改状态
     * */
    handleStatus(row) {
      if (row.valid === 1) {
        this.$confirm('', '确定停用 ' + row.dictName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editDictStatus(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '停用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      } else {
        this.$confirm('', '确定启用 ' + row.dictName + '？', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          editDictStatus(row.id).then(res => {
            if (res.code === '000000') {
              this.$message({
                type: 'success',
                message: '启用成功!'
              })
              this.getList()
            }
          })
        }).catch(action => {

        })
      }
    },
    getMenuValidList(row) {
      return converseEnToCn(menuValidList, row.valid)
    },
    getButtonText(valid) {
      return valid === 1 ? '停用' : '启用'
    },
    buttonColor(valid) {
      return valid === 1 ? 'danger' : 'primary'
    }
  }
}
</script>

<style scoped>

</style>
