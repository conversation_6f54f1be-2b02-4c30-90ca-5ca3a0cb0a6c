<template>
  <el-dialog :visible.sync="storyFlag" :title="storyTitle" :close-on-click-modal="!storyFlag" width="60%" @close="changeInit">
    <h3 v-if="savePoster" class="upload-img-title">请点击保存按钮上传图片</h3>
    <div class="hello">
      <div class="upload">
        <div v-if="showDel" class="upload_warp">
          <div class="upload_warp_left" @click="fileClick">
            <img src="../../../assets/img/upload.png">
          </div>
          <div v-if="imgFlag!==2" class="upload_warp_right" @drop="drop($event)" @dragenter="dragenter($event)" @dragover="dragover($event)">
            或者将文件拖到此处
          </div>
        </div>
        <div class="upload_warp_text">
          <i v-if="imgList.length>0">选中{{ imgList.length }}张文件，</i><i v-if="size">共{{ bytesToSize(size) }}</i>
        </div>
        <input id="upload_file" type="file" multiple style="display: none" @change="fileChange($event)">
        <div v-show="imgList.length!=0" class="upload_warp_img">
          <div v-for="(item,index) of imgList" :key="index" class="upload_warp_img_div">
            <div class="upload_warp_img_div_top">
              <div class="upload_warp_img_div_text">
                {{ item.file.name }}
              </div>
              <img src="../../../assets/img/del.png" class="upload_warp_img_div_del dels" @click.stop.prevent="fileDel($event,index,item.file.src)">
            </div>
            <img :src="item.file.src">
          </div>
        </div>
      </div>
      <div class="upload-opera">
        <el-button v-if="showUpload" size="mini" type="primary" :disabled="imgList.length===0" @click="imgUpload">提交图片</el-button>
        <el-button v-if="savePoster" size="mini" type="primary" @click="confirmUpload">保存</el-button>
        <el-button size="mini" type="primary" @click="storyFlag=false,changeInit()">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { uuid } from '@/utils/index'
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { uploadBanner } from '@/api/common'
export default {
  name: 'BatchOpera',
  data() {
    return {
      storyFlag: false,
      obsImgs: [],
      storyTitle: '批量上传图片',
      imgList: [],
      size: 0,
      listQuery: {},
      uploadImg: [],
      savePoster: false,
      showUpload: true,
      imgFlag: null,
      bannerMobileImg: null,
      errorImgs: null,
      bannerPcImg: null,
      len: 0,
      showDel: true,
      selectType: 0
    }
  },
  watch: {
    'imgList': {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.showUpload = true
          this.showDel = true
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    changeInit() {
      this.listQuery = {}
      this.imgList = []
      this.obsImgs = []
      this.storyFlag = false
      this.savePoster = false
      this.size = 0
    },
    fileClick() {
      document.getElementById('upload_file').click()
    },
    fileChange(el) {
      if (!el.target.files[0].size) return
      this.fileList(el.target)
      el.target.value = ''
    },
    fileList(fileList) {
      const files = fileList.files
      for (let i = 0; i < files.length; i++) {
        // 判断是否为文件夹
        if (files[i].type !== '') {
          this.fileAdd(files[i])
        } else {
          // 文件夹处理
          this.folders(fileList.items[i])
        }
      }
    },
    // 文件夹处理
    folders(files) {
      const _this = this
      // 判断是否为原生file
      if (files.kind) {
        files = files.webkitGetAsEntry()
      }
      files.createReader().readEntries(function(file) {
        for (let i = 0; i < file.length; i++) {
          if (file[i].isFile) {
            _this.foldersAdd(file[i])
          } else {
            _this.folders(file[i])
          }
        }
      })
    },
    foldersAdd(entry) {
      const _this = this
      entry.file(function(file) {
        _this.fileAdd(file)
      })
    },
    fileAdd(file) {
      // 总大小
      this.size = this.size + file.size
      // 判断是否为图片文件
      if (file.type.indexOf('image') === -1) {
        file.src = 'wenjian.png'
        this.imgList.push({
          file
        })

      } else {
        const reader = new FileReader()
        reader.vue = this
        reader.readAsDataURL(file)
        reader.onload = function() {
          file.src = this.result
          this.vue.imgList.push({
            file
          })
        }
      }
    },
    fileDel(e, index, url) {
      this.size = this.size - this.imgList[index].file.size// 总大小
      e.stopPropagation() // 表示阻止向父元素冒泡
      e.preventDefault()
      e.target.click = null
      this.$confirm('确定要删除此图片?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.imgList.splice(index, 1)
        this.savePoster = false
      }).catch(() => {
        this.$message({
          message: '取消删除',
          type: 'warning'
        })
      })
    },
    bytesToSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1000 // or 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    },
    dragenter(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    dragover(el) {
      el.stopPropagation()
      el.preventDefault()
    },
    drop(el) {
      el.stopPropagation()
      el.preventDefault()
      this.fileList(el.dataTransfer)
    },
    imgUpload() {
      const validateImg = this.imgList.every(item => {
        return (item.file.size / 1024 / 1024).toFixed(3) < 5
      })
      if (!validateImg) {
        this.$message({
          type: 'warning',
          message: '请上传5M以内的图片'
        })
        return false
      }
      const imgPcLength = this.imgFlag === 'pc' && this.bannerPcImg && this.bannerPcImg.length > 0 ? this.bannerPcImg.length + this.imgList.length : this.imgList.length
      const imgMobileLength = this.imgFlag === 'mobile' && this.bannerMobileImg && this.bannerMobileImg.length > 0 ? this.bannerMobileImg.length + this.imgList.length : this.imgList.length

      let tips
      if (this.imgFlag === 'pc') {
        tips = 'banner图片(pc端)只能显示5张，请先删去已经存在的图片再上传'
      } else if (this.imgFlag === 'mobile') {
        tips = 'banner图片(移动端)只能显示5张，请先删去已经存在的图片再上传'
      }
      if (imgPcLength > 5 && this.imgFlag === 'pc') {
        this.$message({ type: 'error', message: `${tips}` })
        return false
      }
      if (imgMobileLength > 5 && this.imgFlag === 'mobile') {
        this.$message({ type: 'error', message: `${tips}` })
        return false
      }
      this.posterUpload(this.imgList, 0)
    },
    confirmUpload() { // listQuery.clientCode

      this.uploadImgs(this.obsImgs, 0)
    },
    uploadImgs(obsImgs, j) {
      const that = this
      const loading = that.$loading({
        lock: true,
        text: '快速上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      uploadBanner(obsImgs[j]).then(res => {
        if (res.code === '000000') {

          that.imgList[j].id = res.data
          that.imgList[j].imagePath = obsImgs[j].imagePath
          if (++j < obsImgs.length) {
            that.uploadImgs(obsImgs, j)
          } else {
            setTimeout(() => {
              loading.close()
              that.savePoster = true
              that.showUpload = false
              that.showDel = false
              if (that.imgList.length > 0) {
                if (that.imgFlag === 'pc') {
                  that.$emit('getimgPc', that.imgList)
                } else if (that.imgFlag === 'mobile') {
                  that.$emit('getimgMobile', that.imgList)
                }
                that.storyFlag = false
              } else {
                that.$message({
                  type: 'warning',
                  message: '请先上传图片'
                })
              }
            }, 2000)
          }
        }
      }).catch(() => {
        loading.close()
        that.showUpload = true
        that.showDel = true
      })
    },
    posterUpload(files, i) {
      const that = this
      const loading = that.$loading({
        lock: true,
        text: '正在拼命加载中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const tempName = files[i].file.name.split('.')
      const ids = uuid()
      const fileName = `santao_stip/crm/banner/${ids}.${tempName[tempName.length - 1]}`
      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: files[i].file// 文件路径
      }, function(err, result) {
        if (err) {
          that.$message({ type: 'error', message: err.msg })
        } else {
          const paramsUpload = Object.assign({}, {
            'imagePath': `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/banner/${ids}.${tempName[tempName.length - 1]}`,
            'mobileShowFlag': that.imgFlag === 'mobile' ? 1 : 0,
            'pcShowFlag': that.imgFlag === 'pc' ? 1 : 0,
            'sort': 0,
            'type': that.selectType
          })
          that.obsImgs.push(paramsUpload)
          if (++i < files.length) {
            that.posterUpload(files, i)
          } else {
            setTimeout(() => {
              loading.close()
              that.savePoster = true
              that.showUpload = false
              that.showDel = false
            }, 2000)
          }
        }
      })
    }
  }
}
</script>

<style scoped>
  .upload_warp_img_div_del {
    position: absolute;
    top: 6px;
    width: 16px;
    right: 4px;
  }

  .upload_warp_img_div_top {
    position: absolute;
    top: 0;
    width: 100%;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.4);
    line-height: 30px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    text-indent: 4px;
  }

  .upload_warp_img_div_text {
    white-space: nowrap;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload_warp_img_div img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
  }

  .upload_warp_img_div {
    position: relative;
    height: 100px;
    width: 120px;
    border: 1px solid #ccc;
    margin: 0px 30px 10px 0px;
    float: left;
    line-height: 100px;
    display: table-cell;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
  }

  .upload_warp_img {
    border-top: 1px solid #D2D2D2;
    padding: 14px 0 0 14px;
    overflow: hidden
  }

  .upload_warp_text {
    text-align: left;
    margin-bottom: 10px;
    padding-top: 10px;
    text-indent: 14px;
    border-top: 1px solid #ccc;
    font-size: 14px;
  }

  .upload_warp_right {
    float: left;
    width: 57%;
    margin-left: 2%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    line-height: 130px;
    color: #999;
  }

  .upload_warp_left img {
    margin-top: 32px;
  }

  .upload_warp_left {
    float: left;
    width: 40%;
    height: 100%;
    border: 1px dashed #999;
    border-radius: 4px;
    cursor: pointer;
  }

  .upload_warp {
    margin: 14px;
    height: 130px;
  }

  .upload {
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 0px 1px 0px #ccc;
    border-radius: 4px;
  }

  .hello {
    text-align: center;
  }
  .upload-opera{
    padding: 20px 0;
  }
  .upload-img-title{
    color: red;
    padding-bottom: 10px;
  }
</style>
<style>
  .el-dialog__body{
    padding-top: 10px !important;
  }
</style>
