import request from '@/utils/request'
/**
 * 获取班型记录列表
 * @param data
 */
export function getPayList(data) {
  return request({
    url: 'stip/classTypes/page',
    method: 'POST',
    data: data
  })
}
/**
 * 获取客户端列表
 * @param data
 */
export function clientCode(data) {
  return request({
    url: 'stip/sysClients',
    method: 'get'
  })
}
/**
 * 删除班型
 * @param data
 */
export function classTypeDelete(data) {
  return request({
    url: `stip/classTypes/stip/${data}`,
    method: 'DELETE'
  })
}
/**
 * 删除班型屏蔽区域
 * @param data
 */
export function classTypeDeleteArea(id) {
  return request({
    url: `stip/classTypes/shieldRegion/${id}`,
    method: 'DELETE'
  })
}
/**
 * 添加屏蔽区域
 * @param data
 */
export function addRegion(data) {
  return request({
    url: 'stip/classTypes/shieldRegion',
    method: 'POST',
    data: data
  })
}
/**
 * 查询班型屏蔽区域
 * @param data
 */
export function getShieldRegion(data) {
  return request({
    url: `stip/classTypes/shieldRegion/page`,
    method: 'GET',
    params: data
  })
}
/**
 * 停用/启用 班型屏蔽区域
 * @param data
 */
export function enableArea(data) {
  return request({
    url: `stip/classTypes/shieldRegion/${data}`,
    method: 'PUT'
  })
}
/**
 * 添加/修改班型
 * @param data
 */
export function addClassType(data) {
  return request({
    url: 'stip/classTypes',
    method: 'POST',
    data: data
  })
}
/**
 * 获取列表班型的详情
 * @param data
 */
export function getClassDetail(id) {
  return request({
    url: `stip/classTypes/${id}`,
    method: 'GET'
  })
}
/**
 * 修改列表班型
 * @param data
 */
export function editClassType(data) {
  return request({
    url: 'stip/classTypes',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取科目列表
 * @param data
 */
export function getSubjects(data) {
  return request({
    url: 'stip/subjects/page',
    method: 'POST',
    data: data
  })
}
/**
 * 添加科目
 * @param data
 */
export function addSubjects(data) {
  return request({
    url: 'stip/subjects',
    method: 'POST',
    data: data
  })
}
/**
 * 获取科目详情
 * @param data
 */
export function getSubjectsDetail(id) {
  return request({
    url: `stip/subjects/${id}`,
    method: 'GET'
  })
}
/**
 * 修改科目
 * @param data
 */
export function editSubjects(data) {
  return request({
    url: 'stip/subjects',
    method: 'PUT',
    data: data
  })
}
/**
 * 所有有效科目
 * @param data
 */
export function getAllSubjects(data) {
  return request({
    url: 'stip/subjects',
    method: 'GET'
  })
}

export function getSubjectsByClassType(classTypeId) {
  return request({
    url: `stip/subjects/classType/${classTypeId}`,
    method: 'GET'
  })
}
/**
 * 获取教师列表
 * @param data
 */
export function getAllTeacher(data) {
  return request({
    url: 'stip/teachers/page',
    method: 'POST',
    data: data
  })
}

export function getStyleModelPage(data) {
  return request({
    url: 'stip/subjectStyle/page',
    method: 'POST',
    data: data
  })
}

export function getStyleModelByClassAndSubject(data) {
  return request({
    url: 'stip/subjectStyle/subjectStyleByClassId',
    method: 'GET',
    params: data
  })
}

/**
 * 知识模块分页
 * @param data
 */
export function getSubjectCatalogPage(data) {
  return request({
    url: 'stip/subjectCatalog/page',
    method: 'POST',
    data: data
  })
}
/**
 * 新增教师
 * @param data
 */
export function addTeacher(data) {
  return request({
    url: 'stip/teachers',
    method: 'POST',
    data: data
  })
}

/**
 * 新增科目样式
 * @param data
 * @returns {*}
 */
export function addStyleModel(data) {
  return request({
    url: 'stip/subjectStyle',
    method: 'POST',
    data: data
  })
}

/**
 * 新增知识模块
 * @param data
 * @returns {*}
 */
export function addSubjectCatalog(data) {
  return request({
    url: 'stip/subjectCatalog',
    method: 'POST',
    data: data
  })
}
/**
 * 获取教师详情
 * @param data
 */
export function getTeacherDetail(id) {
  return request({
    url: `stip/teachers/${id}`,
    method: 'GET'
  })
}
export function getStyleModelDetail(id) {
  return request({
    url: `stip/subjectStyle/${id}`,
    method: 'GET'
  })
}

export function getSubjectCatalogDetail(id) {
  return request({
    url: `stip/subjectCatalog/${id}`,
    method: 'GET'
  })
}

export function getMaterialListByClassTypeId(id) {
  return request({
    url: `stip/new/materials/classType/${id}`,
    method: 'GET'
  })
}
export function getMaterialListByClientCode(clientCode) {
  return request({
    url: `stip/new/materials/clientCode/${clientCode}`,
    method: 'GET'
  })
}

export function getCourseMaterialByParam(params) {
  return request({
    url: `stip/new/materials/getCourseMaterialByParam`,
    method: 'GET',
    params
  })
}

export function getSubMaterialListByMainId(id) {
  return request({
    url: `stip/materialVersion/list/${id}`,
    method: 'GET'
  })
}
/**
 * 修改教师详情
 * @param data
 */
export function editTeacher(data) {
  return request({
    url: 'stip/teachers',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取分页教材列表
 * @param data
 */
export function materialList(data) {
  return request({
    url: 'stip/materials/page',
    method: 'POST',
    data: data
  })
}

export function materialListNew(data) {
  return request({
    url: 'stip/new/materials/page',
    method: 'POST',
    data: data
  })
}

/**
 * 获取子教材列表
 * @param materialId
 * @returns {*}
 */
export function getSubMaterialListById(materialId) {
  return request({
    url: `stip/materialVersion/list/${materialId}`,
    method: 'GET'
  })
}

/**
 * 教材子版本的启用/禁用
 * @param id
 */
export function enableSubMaterial(id) {
  return request({
    url: `stip/materialVersion/enable/${id}`,
    method: 'PUT'
  })
}

/**
 * 新增教材列表
 * @param data
 */
export function addMaterialList(data) {
  return request({
    url: 'stip/materials',
    method: 'POST',
    data: data
  })
}

export function addMaterialListNew(data) {
  return request({
    url: 'stip/new/materials',
    method: 'POST',
    data: data
  })
}

export function addSubMaterial(data) {
  return request({
    url: 'stip/materialVersion',
    method: 'POST',
    data: data
  })
}
/**
 * 获取教材详情
 * @param data
 */
export function getMaterialList(id) {
  return request({
    url: `stip/materials/${id}`,
    method: 'GET'
  })
}

export function getMaterialDetailsNew(id) {
  return request({
    url: `stip/new/materials/${id}`,
    method: 'GET'
  })
}

/**
 * 获取子教材详情
 * @param id
 */
export function getSubMaterial(id) {
  return request({
    url: `stip/materialVersion/${id}`,
    method: 'GET'
  })
}
/**
 * 获取教材列表
 * @param data
 */
export function materialsList() {
  return request({
    url: 'stip/materials',
    method: 'GET'
  })
}
/**
 * 获取分页年级列表
 * @param data
 */
export function gradeList(data) {
  return request({
    url: 'stip/grades/page',
    method: 'POST',
    data: data
  })
}
/**
 * 获取年级列表
 * @param data
 */
export function grades(data) {
  return request({
    url: 'stip/grades',
    method: 'GET'
  })
}
/**
 * 添加年级
 * @param data
 */
export function addGrade(data) {
  return request({
    url: 'stip/grades',
    method: 'POST',
    data: data
  })
}
/**
 * 年级详情
 * @param data
 */
export function gradeDetail(id) {
  return request({
    url: `stip/grades/${id}`,
    method: 'GET'
  })
}
/**
 * 修改年级
 * @param data
 */
export function editGrades(data) {
  return request({
    url: 'stip/grades',
    method: 'PUT',
    data: data
  })
}
/**
 * 教材的启用/禁用
 * @param data
 */
export function enable(id) {
  return request({
    url: `stip/materials/enable/${id}`,
    method: 'PUT'
  })
}
export function enableNew(id) {
  return request({
    url: `stip/new/materials/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 教师的启用/禁用
 * @param data
 */
export function enableTeacher(id) {
  return request({
    url: `stip/teachers/enable/${id}`,
    method: 'PUT'
  })
}

export function enableStyleModel(id) {
  return request({
    url: `stip/subjectStyle/enable/${id}`,
    method: 'PUT'
  })
}

/**
 * 知识模块启用禁用
 * @param id
 */
export function enableSubjectCatalog(id) {
  return request({
    url: `stip/subjectCatalog/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 大纲列表
 * @param data
 */
export function outlineList(data) {
  return request({
    url: 'stip/outlines/page',
    method: 'POST',
    data: data
  })
}
/**
 * 大纲启用/禁用
 * @param data
 */
export function outlineEnable(id) {
  return request({
    url: `stip/outlines/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 大纲详情
 * @param data
 */
export function getOutLine(id) {
  return request({
    url: `stip/outlines/${id}`,
    method: 'GET'
  })
}
/**
 * 查询所有可用班型
 * @param data
 */
export function getAllClassType() {
  return request({
    url: `stip/classTypes/list`,
    method: 'GET'
  })
}

/**
 * 根据客户端获取所有班型
 * @param clientCode
 * @returns {*}
 */
export function getClassType(clientCode) {
  return request({
    url: `stip/classTypes/all/client/${clientCode}`,
    method: 'GET'
  })
}
/**
 * 根据班型获取教材
 * @param data
 */
export function getMaterials(classId) {
  return request({
    url: `stip/materials/classType/${classId}`,
    method: 'GET'
  })
}
/**
 * 新增/修改大纲
 * @param data
 */
export function addOutLine(data) {
  return request({
    url: 'stip/outlines',
    method: 'POST',
    data: data
  })
}
/**
 * 获取考点管理列表
 * @param data
 */
export function examinationList(data) {
  return request({
    url: 'stip/keynotes/page',
    method: 'POST',
    data: data
  })
}
/**
 * 新增/修改考点管理列表
 * @param data
 */
export function addExamination(data) {
  return request({
    url: 'stip/keynotes',
    method: 'POST',
    data: data
  })
}
/**
 * 启用/禁用考点管理
 * @param data
 */
export function examinationEnable(id) {
  return request({
    url: `stip/keynotes/enable/${id}`,
    method: 'PUT'
  })
}
/**
 * 考点详情
 * @param data
 */
export function examinationDetail(id) {
  return request({
    url: `stip/keynotes/${id}`,
    method: 'GET'
  })
}
/**
 * 获取分页的课程列表
 * @param data
 */
export function courseList(data) {
  return request({
    url: 'stip/courses/page',
    method: 'POST',
    data: data
  })
}
/**
 * 获取所有可用老师
 * @param data
 */
export function getTeachers(data) {
  return request({
    url: 'stip/teachers',
    method: 'GET'
  })
}

/**
 * 获取年级
 */
export function getGrades() {
  return request({
    url: '/stip/grades',
    method: 'GET'
  })
}
/**
 * 根据班型和科目查询老师信息
 * @param data
 */
export function classAndSubject(data) {
  return request({
    url: 'stip/teachers/classAndSubject',
    method: 'GET',
    params: data
  })
}

export function getTeacherByClassAndSubject(data) {
  return request({
    url: 'stip/teachers/classAndSubject/list',
    method: 'GET',
    params: data
  })
}

export function getSubjectCatalogByClassAndSubject(data) {
  return request({
    url: 'stip/subjectCatalog/classAndSubject/list',
    method: 'GET',
    params: data
  })
}

export function getSubjectCatalogByClassAndSubjectForAddCourse(data) {
  return request({
    url: 'stip/subjectCatalog/list',
    method: 'GET',
    params: data
  })
}
/**
 * 根据班型、科目、教材获取大纲
 * @param data
 */
export function outLines(data) {
  return request({
    url: 'stip/outlines/list',
    method: 'GET',
    params: data
  })
}
/**
 * 根据大纲获取有效考点
 * @param data
 */
export function getKeynote(outlineId) {
  return request({
    url: `stip/keynotes/outline/${outlineId}`,
    method: 'GET'
  })
}
/**
 * 新增课程
 * @param data
 */
export function addCourse(data) {
  return request({
    url: 'stip/courses',
    method: 'POST',
    data: data
  })
}
/**
 * 课程详情
 * @param data
 */
export function courseDetail(id) {
  return request({
    url: `stip/courses/${id}`,
    method: 'GET'
  })
}
/**
 * 修改课程
 * @param data
 */
export function editCourse(data) {
  return request({
    url: 'stip/courses',
    method: 'PUT',
    data: data
  })
}
/**
 * 加入推荐/取消推荐
 * @param data
 */
export function joinRecommend(id) {
  return request({
    url: `stip/courses/recommend/${id}`,
    method: 'PUT'
  })
}
/**
 * 加入推荐/取消推荐
 * @param data
 */
export function synchronous(id) {
  return request({
    url: `stip/courses/sync/${id}`,
    method: 'PUT'
  })
}
/**
 * 课程操作日志
 * @param data
 */
export function logs(courseId) {
  return request({
    url: `stip/courses/log/${courseId}`,
    method: 'GET'
  })
}
/**
 * 根据clientCode查询所有可用班型
 * @param data
 */
export function classAvailable(clientCode) {
  return request({
    url: `stip/classTypes/list/special/client/${clientCode}`,
    method: 'GET'
  })
}
/**
 * 查询可用大纲列表信息
 * @param data
 */
export function outlineLists() {
  return request({
    url: `stip/outlines`,
    method: 'GET'
  })
}
/**
 * 查询所有可用考点
 * @param data
 */
export function keynotesList() {
  return request({
    url: `stip/keynotes`,
    method: 'GET'
  })
}
/**
 * 删除课程
 * @param data
 */
export function delCourse(id) {
  return request({
    url: `stip/courses/${id}`,
    method: 'DELETE'
  })
}
/**
 * 获取产品线
 * @param data
 */
export function getSysClients(data) {
  return request({
    url: `stip/sysClients`,
    method: 'GET',
    params: data
  })
}
/**
 * 根据产品线获取体系
 * @param data
 */
export function classSystem(clientCode) {
  return request({
    url: `stip/classTypes/getClassSeriesByClientCode?clientCode=${clientCode}`,
    method: 'GET'
  })
}
/**
 * 获取系列分页
 * @param data
 */
export function pageCourseClassSeries(data) {
  return request({
    url: `stip/classSeries/pageCourseClassSeries`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取可用系列
 * @param data
 */
export function classSeries() {
  return request({
    url: `/stip/classSeries/listEnableClassSeries`,
    method: 'GET'
  })
}

/**
 * 删除系列
 * @param data
 */
export function deleteClassSeries(id) {
  return request({
    url: `stip/classSeries/deleteCourseClassSeries/${id}`,
    method: 'GET'
  })
}
/**
 * 获取系列详情
 * @param data
 */
export function getClassSeries(id) {
  return request({
    url: `stip/classSeries/getCourseClassSeries/${id}`,
    method: 'GET'
  })
}
/**
 * 编辑系列详情
 * @param data
 */
export function updateClassSeries(data) {
  return request({
    url: `stip/classSeries/updateCourseClassSeries`,
    method: 'POST',
    data: data
  })
}
/**
 * 编辑系列详情
 * @param data
 */
export function addClassSeries(data) {
  return request({
    url: `stip/classSeries/addCourseClassSeries`,
    method: 'POST',
    data: data
  })
}
/**
 * 获取好课系列
 * @param data
 */
export function getSeriesSan(clientCode) {
  return request({
    url: `stip/classSeries/listClassSeriesByClientCode/${clientCode}`,
    method: 'GET'
  })
}

/**
 * 课程管理-设置推流
 * @param data
 */
export function generateLive(data) {
  return request({
    url: `stip/courses/generateLiveBroadcast`,
    method: 'POST',
    data: data
  })
}

/**
 * 课程管理-关闭直播
 * @param data
 */
export function editLiveStatus(data) {
  return request({
    url: `stip/courses/editLiveStatus`,
    method: 'POST',
    data: data
  })
}

// 获取教材下拉列表
export function getv3BookList(params) {
  return request({
    url: 'stip/new/materials/v3/bookList',
    method: 'GET',
    params
  })
}

// 获取子版本下拉列表-v3
export function getv3SubVersionList(params) {
  return request({
    url: 'stip/new/materials/v3/bookSubList',
    method: 'GET',
    params
  })
}

// 根据id查询教材信息
export function getCoursesMaterials(id) {
  return request({
    url: `stip/new/materials/${id}`,
    method: 'GET'
  })
}

// 获取菁优网学科
export function getBankSubjectList(data) {
  return request({
    url: 'knowledge/flash/quiz/getBankSubjectList',
    method: 'GET',
    params: data
  })
}
// 获取菁优网教材
export function getBankBookList(data) {
  return request({
    url: 'knowledge/flash/quiz/getBankBookList',
    method: 'GET',
    params: data
  })
}

// 获取三陶学科
export function getSubjectList(data) {
  return request({
    url: 'knowledge/flash/quiz/getSubjectList',
    method: 'GET',
    params: data
  })
}

// 获取三陶教材及版本
export function getMaterialAndVersion(data) {
  return request({
    url: 'knowledge/flash/quiz/getMaterialAndVersion',
    method: 'GET',
    params: data
  })
}
// 绑定知识点课程
export function addPointCourse(data) {
  return request({
    url: 'knowledge/flash/quiz/addPointCourse',
    method: 'POST',
    data
  })
}
// 删除知识点课程
export function deletePointCourse(id) {
  return request({
    url: `knowledge/flash/quiz/deletePointCourse/${id}`,
    method: 'DELETE'
  })
}

// 获取知识点课程
export function getPointCourse(data) {
  return request({
    url: 'knowledge/flash/quiz/getPointCourse',
    method: 'GET',
    params: data
  })
}

// 教材同步
export function syncMaterial(data) {
  return request({
    url: 'knowledge/flash/quiz/syncMaterial',
    method: 'POST',
    data
  })
}

// 获取章节知识点列表
export function categoryList(data) {
  return request({
    url: 'knowledge/flash/quiz/categoryPointList',
    method: 'GET',
    params: data
  })
}
// 删除章节知识点
export function deleteCategoryPoint(data) {
  return request({
    url: 'knowledge/flash/quiz/deleteCategoryPoint',
    method: 'DELETE',
    data
  })
}
