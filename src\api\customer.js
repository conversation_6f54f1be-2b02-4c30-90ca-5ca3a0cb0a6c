import request from '@/utils/request'

/**
 * 获取客户列表
 * @param data
 */
export function getCustomerList(data) {
  return request({
    url: 'clue/pageClue',
    method: 'get',
    params: data
  })
}

/**
 * 获取校区学习列表
 * @param data
 */
export function getCustomerStudyList(data) {
  return request({
    url: '/resource/data/record/getListPage',
    method: 'get',
    params: data
  })
}
/**
 * 新增客户
 * @param data
 */
export function createCustomer(data) {
  return request({
    url: '/clue/add',
    method: 'POST',
    data: data
  })
}
/**
 * 获取客户详情
 * @param data
 */
export function getCustomerDetail(data) {
  return request({
    url: 'clue/find/' + data,
    method: 'get'
  })
}
/**
 * 查询跟进记录
 */
export function qureyFollowList(data) {
  return request({
    url: 'clue/findFus',
    method: 'get',
    params: data
  })
}
/**
 * 客户、线索转让 支持多条
 * @param data
 */
export function putCustomerTransfer(data) {
  return request({
    url: 'clue/batchTransfer',
    method: 'put',
    data: data
  })
}
/**
 * 获取员工列表
 * @param data
 */
export function getEmployeeList(data) {
  return request({
    url: 'user/findUser',
    method: 'get',
    params: data
  })
}
/**
 * 获取关联客户
 * @param data
 */
export function getCustomerRelative(data) {
  return request({
    url: 'clue/getRelations',
    method: 'get',
    params: data
  })
}
/**
 * 获取客户列表
 * @param data
 */
export function getCustomerSchoolList(data) {
  return request({
    url: 'clueSchool/listClueSchool',
    method: 'get',
    params: data
  })
}
/**
 * 修改客户详情
 * @param data
 */
export function updateCustomer(data) {
  return request({
    url: 'clue/edit',
    method: 'PUT',
    data: data
  })
}
/**
 * 获取加盟的项目列表
 * @param data
 */
export function getJoinProjects(data) {
  return request({
    url: 'clue/getJoinInfos',
    method: 'get',
    params: data
  })
}
/**
 * 获取尘锋数据
 * @param data
 */
export function getCfCrmData(data) {
  return request({
    url: 'clue/getCfCrmData',
    method: 'get',
    params: data
  })
}

/**
 * 关联客户
 */

export function relateCustomer(data) {
  return request({
    url: 'clue/relateClue',
    method: 'POST',
    data: data
  })
}
/**
 * 取消关联客户
 */

export function cancelRelative(data) {
  return request({
    url: 'clue/cancelRelate',
    method: 'PUT',
    params: data
  })
}
/**
 * 新增跟进记录
 */
export function addFollow(data) {
  return request({
    url: 'clue/addFu',
    method: 'post',
    data: data
  })
}
/**
 * 默认成员转为合伙人
 */
export function customerToParterner(data) {
  return request({
    url: 'clue/changeType/' + data,
    method: 'PUT'
  })
}

/**
 * 查询经销商申请列表
 */
export function distributorList(data) {
  return request({
    url: 'distributor/apply/pages',
    method: 'GET',
    params: data
  })
}

/**
 * 查询经销商申请详情
 */
export function distributorDetail(id) {
  return request({
    url: `distributor/apply/detail/${id}`,
    method: 'GET'
  })
}

/**
 * 驳回
 */
export function distributorReject(data) {
  return request({
    url: `distributor/apply/audit/deny`,
    method: 'POST',
    data: data
  })
}

/**
 * 通过
 */
export function distributorResolve(applyId) {
  return request({
    url: `distributor/apply/audit/pass?applyId=${applyId}`,
    method: 'POST'
  })
}

export function emtpyList(data) {
  return request({
    url: `institutions/empty`,
    method: 'GET',
    params: data
  })
}

/**
 * 获取跟进记录统计数据
 * @param data
 * @return {Promise|*}
 */
export function getSelectFollowUpLog(data) {
  return request({
    url: '/clue/selectFollowUpLog',
    method: 'post',
    data: data
  })
}

/**
 * 导出跟进记录总表
 * @param data
 * @return {AxiosPromise}
 */
export function exportFollowUpLog(data) {
  return request({
    url: '/clue/exportFollowUpLog',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

/**
 * 导出跟进记录详情
 * @param data
 * @return {AxiosPromise}
 */
export function exportFollowUpLogDetail(data) {
  return request({
    url: '/clue/exportFollowUpLogDetail',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

/**
 * 获取跟进记录详情
 * @param data
 * @return {AxiosPromise}
 */
export function selectFollowUpLogDetail(data) {
  return request({
    url: '/clue/selectFollowUpLogDetail',
    method: 'post',
    data: data
  })
}
