<template>
  <el-dialog
    v-el-drag-dialog
    width="60%"
    title="退款记录"
    :visible.sync="dialogFollow"
    :close-on-click-modal="!dialogFollow"
  >
    <el-row :gutter="10">
      <el-button v-if="!showCreate" v-permission="['order:list:payment:create']" type="primary" size="mini" class="fr" @click="createFollow">新增</el-button>
      <el-col v-if="!showCreate" :span="24" class="mt10">
        <el-table
          v-loading="followListLoading"
          :data="followList"
          border
          fit
          stripe
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="#" type="index" width="50" />
          <el-table-column label="财务审核状态" prop="auditStatus" width="120" :formatter="getAuditStatus" />
          <el-table-column label="退款金额" prop="payAmount" />
          <el-table-column label="退款方式" prop="payTypeName" />
          <el-table-column label="退款时间" prop="payTypeName" />
          <af-table-column label="收款人姓名" prop="payTypeName" />
          <el-table-column label="银行卡号" prop="transactionNo" width="120" show-overflow-tooltip />
          <el-table-column label="交易流水号" prop="transactionNo" width="120" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" width="100" show-overflow-tooltip />
          <el-table-column label="操作" min-width="230">
            <template v-if="row.auditStatus == 10" slot-scope="{row}">
              <el-button type="primary" size="mini" @click="handleUpdate(row)">
                修改
              </el-button>
              <el-button type="primary" size="mini" @click="handleDelete(row)">
                删除
              </el-button>
              <el-button type="primary" size="mini" @click="handleSubmit(row)">
                提交
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-form v-if="showCreate" ref="followForm" :inline="true" :model="followModule" :rules="followModuleRules" class="demo-form-inline" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="退款金额:" prop="payAmount">
              <el-input v-model="followModule.payAmount" maxlength="10" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款方式:" prop="payType">
              <el-select v-model="followModule.payType" placeholder="打款方式" filterable class="width-100">
                <el-option v-for="item in payTypes" :key="item.id" :label="item.itemName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="退款时间：" prop="payTime">
              <el-date-picker
                v-model="followModule.payTime"
                type="date"
                placeholder="打款时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易流水号:" prop="transactionNo">
              <el-input v-model="followModule.transactionNo" maxlength="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="收款人姓名:" prop="transactionNo">
              <el-input v-model="followModule.transactionNo" type="text" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行卡号:" prop="transactionNo">
              <el-input v-model="followModule.transactionNo" type="text" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input v-model="followModule.remark" type="textarea" style="min-width:520px;" maxlength="255" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
    <div slot="footer" class="dialog-footer text-center">
      <el-button v-if="showCreate" type="primary" @click="confirmAddFollow">新增</el-button>
      <el-button v-if="showCreate" @click="cancelAdd">取消</el-button>
      <el-button v-show="!showCreate" @click="dialogFollow = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createPaymentDetail, getOrderPaymentRecordList, modifyPaymentDetail, submitPaymentRecord, deletePaymentRecord } from '@/api/payment'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import { followStyleList, converseEnToCn, payMethod, auditStatus } from '@/utils/field-conver'
import { parseTime } from '@/utils'
import { payAmount } from '@/utils/validate.js'
import {
  getPayType
} from '@/api/common'
export default {
  name: 'RefundRecord',
  directives: { elDragDialog },
  components: {},
  props: {
  },
  data() {
    return {
      currentCustomerId: '',
      dialogFollow: false,
      followListLoading: false,
      followList: [],
      total: 0,
      showCreate: false,
      followModule: {},
      followStyleList: followStyleList,
      followModuleRules: {
        payAmount: { required: true, trigger: 'blur', validator: payAmount },
        payType: { required: true, message: '请选择退款方式 ', trigger: 'blur' },
        payTime: { required: true, message: '请选择退款时间', trigger: 'blur' },
        transactionNo: { required: true, message: '请输入交易流水号', trigger: 'blur' }
      },
      payTimeFormat: '',
      payMethod: payMethod,
      isEdit: false,
      payTypes: []
    }
  },
  created() {
  },
  mounted() {
    this.getAccountType3('pay_type')
  },
  methods: {
    /**
       * 获取打款记录
       * */
    getLists(id) {
      this.dialogFollow = true
      this.currentCustomerId = id
      getOrderPaymentRecordList({ orderId: id }).then(res => {
        this.followList = res.data
      })
    },
    /**
       * 新增打款记录详情
       */
    confirmAddFollow() {
      this.$refs['followForm'].validate((valid) => {
        if (valid) {
          if (!this.isEdit) {
            const params = Object.assign({ orderId: this.currentCustomerId, payMethod: this.followModule.payType }, this.followModule)
            createPaymentDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '新增打款记录成功',
                  type: 'success'
                })
                this.getLists(this.currentCustomerId)
                this.showCreate = false
              }
            })
          } else {
            const params = Object.assign({ payMethod: this.followModule.payType }, this.followModule)
            this.followModule.payTimeFormat = parseTime(this.followModule.payTime, '{y}-{m}-{d}')
            modifyPaymentDetail(params).then(res => {
              if (res.code === '000000') {
                this.$message({
                  message: '修改打款记录成功',
                  type: 'success'
                })
                this.getLists(this.currentCustomerId)
                this.showCreate = false
              }
            })
          }
        } else {

          return false
        }
      })
    },
    getFollowStyle(row) {
      return converseEnToCn(followStyleList, row.followType)
    },
    createFollow() {
      this.followModule = {}
      this.showCreate = true
      this.isEdit = false
    },
    handleUpdate(row) {
      this.followModule = row
      this.showCreate = true
      this.isEdit = true
    },
    /**
       * 删除打款记录
       * */
    handleDelete(row) {
      const params = {
        id: row.id
      }
      deletePaymentRecord(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '打款记录删除成功',
            type: 'success'
          })
          this.getLists(this.currentCustomerId)
        }
      })
    },
    /**
       * 提交打款记录
       */
    handleSubmit(row) {
      const params = {
        id: row.id
      }
      submitPaymentRecord(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            message: '打款记录提交审核成功',
            type: 'success'
          })
          this.getLists(this.currentCustomerId)
          this.$emit('submit')
          this.dialogFollow = false
        }
      })
    },
    cancelAdd() {
      this.showCreate = false
    },
    getFollowTime(row) {
      return parseTime(row.nextFollowTime, '{y}-{m}-{d}')
    },
    getAuditStatus(data) {
      return converseEnToCn(auditStatus, data.auditStatus)
    },
    getPayMethod(row) {
      return converseEnToCn(payMethod, row.payMethod)
    },
    getAccountType3(str) {
      const that = this
      getPayType(str).then(res => {
        that.payTypes = res.data
      })
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog__body {
    padding: 12px 20px 20px;
  }
</style>
<style>
  .el-date-editor.el-input, .el-date-editor.el-input__inner{
    width: 186px;
  }
</style>
