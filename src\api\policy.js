import request from '@/utils/request'

/**
 * 创建套餐
 * @param data
 */
export function createPolicy(data) {
  return request({
    url: 'policy/createPolicy',
    method: 'post',
    data: data
  })
}
/**
 * 获取套餐详情
 * @param data
 */
export function getPolicyDetail(data) {
  return request({
    url: 'policy/getPolicy/' + data,
    method: 'get'
  })
}
/**
 * 修改套餐
 * @param data
 */
export function updatePolicy(data) {
  return request({
    url: 'policy/modifyPolicy',
    method: 'post',
    data: data
  })
}
/**
 * 删除套餐
 * @param data
 */
export function deletePolicy(data) {
  return request({
    url: 'policy/removePolicy/' + data,
    method: 'get'
  })
}
/**
 * 获取套餐列表
 * @param data
 */
export function getPolicyList(data) {
  return request({
    url: 'policy/listPage',
    method: 'get',
    params: data
  })
}
/**
 * 获取播客价格列表
 * @param data
 */
export function getProductPriceList(data) {
  return request({
    url: 'product/productPrices/' + data,
    method: 'get'
  })
}
/**
 * 套餐的启用/禁用
 * @param data
 */
export function enablePolicy(policyId) {
  return request({
    url: `policy/enable/${policyId}`,
    method: 'PUT'
  })
}
