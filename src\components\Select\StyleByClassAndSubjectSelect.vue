<template>
  <span>
      <el-select v-model="tmpId" clearable placeholder="样式类型" style="width: 200px;"
                 :disabled="disabled">
        <el-option
          v-for="item in optionList"
          :key="item.id"
          filterable
          :label="item.label"
          :value="Number(item.id)">
             <div style="min-width: 200px;">
              <span class="fl">{{ item.label }}</span>
              <span class="subTitle">{{ item.id }}</span>
            </div>
        </el-option>
      </el-select>
  </span>
</template>
<script>
import { styleTags } from '@/utils/field-conver'
import { getStyleModelByClassAndSubject } from '@/api/classType'
import { SUCCESS } from '@/utils/http-status-code'

/**
 * 班型科目样式选择框
 */
export default {
  name: 'StyleByClassAndSubjectSelect',
  data: function () {
    return {
      optionList: []
    }
  },
  model: {
    prop: 'targetId',
    event: 'change',
  },
  props: {
    targetId: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // showType: {
    //   type: String,
    //   default: 'select',
    //   validator(val) {
    //     return ['select', 'radio'].indexOf(val) !== -1
    //   }
    // },
    classTypeId: {
      type: [String, Number],
      required: false
    },
    subjectId: {
      type: [String, Number],
      required: false
    }
  },
  watch: {
    classTypeId(val) {
      if (val) {
        this.getList()
      }
    },
    subjectId(val) {

      if (val) {
        this.getList()
      }
    }
  },
  computed: {
    tmpId: {
      get() {
        return this.targetId ? Number(this.targetId) : null
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.optionList.find(option => option.id === value)
      return this.$emit('change', value, selectedOption)
    },
    getList() {

      if (!this.classTypeId || !this.subjectId) return
      this.dataList = []
      const data = {
        classTypeId: this.classTypeId,
        subjectId: this.subjectId
      }
      this.loading = true
      getStyleModelByClassAndSubject(data).then(res => {
        this.loading = false
        if (res.code === SUCCESS) {
          this.optionList = res.data
          if (this.optionList) {
            styleTags.forEach(item => {
              this.optionList.forEach(option => {
                if (item.value === option.value) {
                  option.label = item.label
                }
              })
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.subTitle {
  float: right;
  color: #8492a6;
  font-size: 13px
}
</style>
