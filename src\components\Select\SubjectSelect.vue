<template>
  <el-select v-model="tmpId" style="width: 140px;" filterable clearable placeholder="科目">
    <el-option
      v-for="item in dataList"
      :key="item.id"
      :label="item.title"
      :value="`${item.id}`">
    </el-option>
  </el-select>
</template>
<script>
import { SUCCESS } from '@/utils/http-status-code'
import { getAllSubjects } from '@/api/classType'

/**
 * 科目选择框
 */
export default {
  name: 'SubjectSelect',
  data: function () {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'id',
    event: 'change',
  },
  props: {
    id: {
      type: [String, Number],
      required: false
    },
  },
  computed: {
    tmpId: {
      get() {
        return !!this.id ? this.id + '' : ''
      },
      set(val) {
        this.handleChange(val)
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(value) {
      const selectedOption = this.dataList.find(option => option.id == value)
      const selectedName = selectedOption ? selectedOption.title : ''
      return this.$emit('change', value, selectedName)
    },
    getList() {
      this.loading = true
      getAllSubjects().then(res => {
        this.loading = false
        if (res.code === SUCCESS) {
          this.dataList = res.data
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
</style>
