<template>
  <div>
    <el-dialog v-if="dialogCreateSchool" v-el-drag-dialog width="700px" top="0" :title="title" :visible.sync="dialogCreateSchool" :close-on-click-modal="!dialogCreateSchool">
      <!-- 外层添加校区弹框 -->
      <el-form
        ref="detailForm"
        :model="createSchool"
        :rules="createSchoolRules"
        :disabled="type === 'query'"
        class="demo-form-inline"
        label-width="100px"
      >
        <el-row>
          <el-col :sm="24" :md="24" :lg="24">
            <el-form-item label="校区名称" prop="schoolName">
              <el-input v-model="createSchool.schoolName" maxlength="40" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :sm="24" :md="24" :lg="24">
            <el-form-item label="签约区域" required>
              <area-picker
                :area-list="areaList"
                :level="'4'"
                area-style="width:100%"
                class="filter-item"
                @getAreaList="getAreaList"
                @initCreateSchool="initCreateSchool"
                @getAreaName="getAreaName"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :sm="24" :md="24" :lg="24">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="createSchool.address" maxlength="50" @input="updateView">
                <!-- <el-button slot="append" type="primary" @click="getLatitude">获取经纬度</el-button> -->
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="createSchool.longitude" @input="handleLongitudeInput"/>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="12" :lg="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="createSchool.latitude" @input="handleLatitudeInput"/>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="24" :lg="24">
            <el-form-item label="" size="mini">
              <el-input v-model="coordinate" placeholder="请复制高德地图经纬度到此处，点击转换即可自动代填" maxlength="100" >
                <el-button slot="append"  @click="setGPS">转换</el-button>
              </el-input>
              <el-link type="primary" target="_blank" href="https://lbs.amap.com/tools/picker">点击打开高德地图获取经纬度<i class="el-icon-map-location el-icon--right"></i></el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="dialogCreateSchool = false">取 消</el-button>
        <el-button v-if="type === 'create'" type="primary" @click="confirmCreateSchool">确 定</el-button>
        <el-button v-if="type === 'update'" type="primary" @click="confirmUpdateSchool">确 定</el-button>
      </div>
      <!-- 外层添加校区弹框 -->
    </el-dialog>
    <school-map ref="schoolMap" @refresh="getMapData" />
  </div>
</template>
<script>
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import AreaPicker from '@/components/area-picker'
import SchoolMap from './schoolMap'
import { createNewSchool, updateSchool, getSchoolDetail } from '@/api/school'
export default {
  name: 'CreateNewSchool',
  components: { AreaPicker, SchoolMap },
  directives: { elDragDialog },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    customerId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogCreateSchool: false,
      createSchool: {},
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      },
      coordinate:'',
      areaListName: '',
      createSchoolRules: {
        schoolName: {
          required: true,
          message: '请输入校区名称',
          trigger: 'blur'
        },
        address: {
          required: true,
          message: '请输入详细地址',
          trigger: 'blur'
        },
        latitude: {
          required: true,
          message: '请输入纬度，纬度在3°N~54°N之间',
          trigger: 'blur',
          validator: (rule, value, callback) => {
            //必须是中国的经纬度
            if (!value) {
              callback(new Error('请输入纬度'))
            }else if(value<3 || value>54){
              callback(new Error('纬度超出范围，纬度在3°N~54°N之间'))
            } else {
              callback()
            }
          }
        },
        longitude: {
          required: true,
          message: '请输入经度，经度在73°E~136°E之间',
          trigger: 'blur',
          validator: (rule, value, callback) => {
            //必须是中国的经纬度
            if (!value) {
              callback(new Error('请输入经度'))
            }else if(value<73 || value>136){
              callback(new Error('经度超出范围，经度在73°E~136°E之间'))
            } else {
              callback()
            }
          }
        }
      },
      dataFlags: -1,
      schoolInit: {},
      CreateSchoolFlags: false,
      UpdateSchoolFlags: false,
      areaName: ''
    }
  },
  methods: {
    // 处理经度输入，只允许数字和一个小数点
    handleLongitudeInput(value) {
      // 移除非数字和非小数点的字符
      let cleanValue = value.replace(/[^0-9.]/g, '')

      // 如果有多个小数点，只保留第一个
      const dotIndex = cleanValue.indexOf('.')
      if (dotIndex !== -1) {
        cleanValue = cleanValue.substring(0, dotIndex + 1) + cleanValue.substring(dotIndex + 1).replace(/\./g, '')
      }

      this.createSchool.longitude = cleanValue
    },

    // 处理纬度输入，只允许数字和一个小数点
    handleLatitudeInput(value) {
      // 移除非数字和非小数点的字符
      let cleanValue = value.replace(/[^0-9.]/g, '')

      // 如果有多个小数点，只保留第一个
      const dotIndex = cleanValue.indexOf('.')
      if (dotIndex !== -1) {
        cleanValue = cleanValue.substring(0, dotIndex + 1) + cleanValue.substring(dotIndex + 1).replace(/\./g, '')
      }

      this.createSchool.latitude = cleanValue
    },

    setGPS(){
      if(this.coordinate){
        const arr = this.coordinate.split(',')
        this.createSchool.longitude = arr[0]
        this.createSchool.latitude = arr[1]
      }
    },
    /**
     * 公共刷新方法
     * */
    updateView(e) {
      this.$forceUpdate()
    },
    confirmCreateSchool() {
      const params = Object.assign({
        clueId: this.customerId
      }, this.createSchool, this.areaList)
      this.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!this.areaList.provinceId || !this.areaList.cityId || !this.areaList.areaId) {
            this.$message({
              message: '请选择签约区域',
              type: 'warning'
            })
            return
          }
          createNewSchool(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '新增校区成功',
                type: 'success'
              })
              this.$emit('success')
              this.coordinate=''
              this.dialogCreateSchool = false
            }
          })
        }
      })
    },
    confirmUpdateSchool() {
      this.UpdateSchoolFlags = true
      const params = Object.assign({
        clueId: this.customerId
      }, this.createSchool, this.areaList)
      this.$refs['detailForm'].validate(valid => {
        if (valid) {
          if (!this.areaList.provinceId || !this.areaList.cityId || !this.areaList.areaId) {
            this.$message({
              message: '请选择签约区域',
              type: 'warning'
            })
            return
          }
          updateSchool(params).then(res => {
            if (res.code === '000000') {
              this.$message({
                message: '修改校区成功',
                type: 'success'
              })
              this.dialogCreateSchool = false
              this.$emit('success')
              this.coordinate=''
              this.UpdateSchoolFlags = false
            }
          })
        }
      })
    },
    /**
     * 获取校区详情
     * */
    getSchool(schoolId) {
      const params = {
        schoolId: schoolId
      }
      getSchoolDetail(params).then(res => {
        this.dialogCreateSchool = true
        this.createSchool = res.data
        this.areaList = {
          provinceId: res.data.provinceId,
          cityId: res.data.cityId,
          areaId: res.data.areaId,
          countyId: res.data.countyId
        }
      })
    },
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
      this.areaList.countyId = data.countyId
    },
    /**
     * 打开地图获取经纬度
     */
    getLatitude() {


      if (!this.createSchool.address) {
        this.$message({
          type: 'warning',
          message: '请输入详细地址'
        })
        return
      }
      this.$refs.schoolMap.getLonAndLat(this.areaList, this.createSchool.address)
      this.$refs.schoolMap.getMapData(this.areaName, this.createSchool.address)
    },
    getAreaName(name) {
      this.areaName = ''
      this.areaName = (name.cityName || '') + (name.areaName || '') + (name.countyName || '') + (name.zhenName ||
        '')
    },
    /**
     * 获取经纬度
     */
    getMapData(val) { // 接受获取的经纬度

      this.$set(this.createSchool, 'address', val.addressDetail)
      this.$set(this.createSchool, 'longitude', val.lng)
      this.$set(this.createSchool, 'latitude', val.lat)
    },
    openDialog() {
      this.createSchool = {}
      this.areaList = {
        provinceId: '',
        cityId: '',
        areaId: '',
        countyId: ''
      }
      this.dialogCreateSchool = true
    },
    initCreateSchool() {

      this.$set(this.createSchool, 'address', '')
      this.$set(this.createSchool, 'longitude', '')
      this.$set(this.createSchool, 'latitude', '')
    }
  }
}
</script>

<style scoped lang="scss">
</style>
