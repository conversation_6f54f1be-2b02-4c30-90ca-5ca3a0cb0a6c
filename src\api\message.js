import request from '@/utils/request'
/**
 * 获取消息列表
 * @param data
 */
export function getMessageList(data) {
  return request({
    url: 'messageNotice/list',
    method: 'POST',
    data: data
  })
}
/**
 *删除消息
 * @param data
 */
export function delMess(id) {
  return request({
    url: 'messageNotice/delete',
    method: 'GET',
    params: id
  })
}
/**
 *消息类型
 * @param data
 */
export function messType() {
  return request({
    url: 'dict/dictTypeList',
    method: 'GET'
  })
}
/**
 *消息详情
 * @param data
 */
export function messageDetail(id) {
  return request({
    url: `messageNotice/queryDetail?id=${id}`,
    method: 'POST'
  })
}
/**
 *新增消息
 * @param data
 */
export function addMessage(data) {
  return request({
    url: 'messageNotice/save',
    method: 'POST',
    data: data
  })
}
/**
 *修改消息
 * @param data
 */
export function updateMessage(data) {
  return request({
    url: 'messageNotice/update',
    method: 'POST',
    data: data
  })
}
/**
 *消息接收人
 * @param data
 */
export function receiveMessage(messageId, data) {
  return request({
    url: `/messageNotice/pageMessageNoticeScopeByMessageId/${messageId}`,
    method: 'GET',
    params: data
  })
}
