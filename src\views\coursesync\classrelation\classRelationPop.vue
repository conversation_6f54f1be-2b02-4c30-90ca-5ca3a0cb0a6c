<template>
  <el-dialog title="关联班型" :visible.sync="relationPop" :close-on-click-modal="!relationPop">
    <el-row :gutter="10">
      <el-col :sm="24" :lg="12" :md="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>草稿信息</span>
          </div>
          <div class="item">
            <el-form label-width="100" :model="classLeft">
              <el-form-item label="产品线：" prop="clientCodeName" class="relation-list">
                <el-input v-model="classLeft.clientCodeName" disabled />
              </el-form-item>
              <el-form-item label="班型系列：" prop="seriesName" class="relation-list">
                <el-input v-model="classLeft.seriesName" disabled />
              </el-form-item>
              <el-form-item label="关联班型：" prop="mgrClassTypeName" class="relation-list">
                <el-input v-model="classLeft.mgrClassTypeName" disabled />
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="12" :md="12" :sm="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>关联班型信息</span>
          </div>
          <div class="item">
            <el-form ref="relationForms" :model="relationForm">
              <el-form-item label="" prop="clientCode" class="relation-right">
                <el-select v-model="relationForm.clientCode" filterable placeholder="请选择所属产品线" class="filter-item" @change="listSeriesByClientCode">
                  <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="classSeriesId" class="relation-right">
                <el-select v-model="relationForm.classSeriesId" placeholder="请选择班型系列" class="filter-item" @change="listClassTypes" filterable>
                  <el-option v-for="item in seriesList" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="courseClassTypeId" class="relation-right">
                <el-select v-model="relationForm.courseClassTypeId" placeholder="请选择关联班型" class="filter-item" @change="refreshData" filterable>
                  <el-option v-for="item in calssTypeList" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div class="dialog-footer">
      <el-button type="infor" size="mini" @click="cancel">取消</el-button>
      <el-button type="primary" size="mini" @click="save">确认关联</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { saveClassTypeRelation, listClassTypes } from '@/api/courseSyncApi'
import { getSysClients, getSeriesSan } from '@/api/classType'

export default {
  name: 'RelationDialog',

  data() {
    return {
      relationPop: false,
      relationForm: {},
      courseMgrClassTypeId: null,
      classLeft: {},
      productCodeList: [],
      seriesList: [],
      calssTypeList: []
    }
  },
  watch: {
    'relationForm.clientCode': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.relationForm.classSeriesId = ''
          this.relationForm.courseClassTypeId = ''
        }
      }
    },
    'relationForm.classSeriesId': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.relationForm.courseClassTypeId = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.listSysClients()
    })
  },
  methods: {
    listSysClients() {
      const params = {
        level: 1
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.productCodeList = res.data || []
        }
      }).catch((error) => {

      })
    },
    // 获取所有可用系列
    listSeriesByClientCode(clientCode) {
      getSeriesSan(clientCode).then(res => {
        if (res.code === '000000') {
          this.seriesList = res.data
        }
      }).catch(error => {

      })
    },
    // 系列变更时处理班型下拉列表
    listClassTypes(val) {
      this.listClassTypesByClientCodeAndSeries(this.relationForm.clientCode, val)
    },
    // 查询系列下班型
    listClassTypesByClientCodeAndSeries(clientCode, classSeriesId) {
      listClassTypes(clientCode, classSeriesId).then(res => {
        if (res.code === '000000') {
          this.calssTypeList = res.data
        }
      }).catch(error => {

      })
    },

    cancel() {
      if (this.$refs.relationForms) {
        this.$refs.relationForms.resetFields()
      }
      this.relationPop = false
    },
    refreshData() {
      this.$forceUpdate()
    },
    save() {
      if (!this.relationForm.clientCode) {
        this.$message({
          message: '请选择所属产品线',
          type: 'error'
        })
        return false
      }

      if (!this.relationForm.classSeriesId) {
        this.$message({
          message: '请选择班型系列',
          type: 'error'
        })
        return false
      }

      if (!this.relationForm.courseClassTypeId) {
        this.$message({
          message: '请选择关联班型',
          type: 'error'
        })
        return false
      }

      this.relationForm.courseMgrClassTypeId = this.courseMgrClassTypeId
      const params = Object.assign({}, this.relationForm)
      saveClassTypeRelation(params).then(res => {
        if (res.code === '000000') {
          this.$message({
            type: 'success',
            message: '添加成功'
          })
          // 刷新上级列表
          this.$emit('addRelationList')
          this.relationPop = false
        }
      }).catch(() => {

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.relation-list{
  display: flex;
  margin-bottom: 15px;
}
.relation-list>>> .el-form-item__label{
  width: 100px;
  text-align: right;
}
.relation-list>>> .el-form-item__content{
  width: 92%;
}
.relation-right{
  margin-bottom: 19px;
}
.dialog-footer{
  padding:25px;
}
</style>
