
import request from '@/utils/request'

/**
 * 获取审课班型列表
 * @param data
 * @returns {AxiosPromise}
 */
export function pageClassType(data) {
  return request({
    url: '/course/sync/classTypes/page',
    method: 'POST',
    data: data
  })
}

/**
 * 获取班型关联列表
 * @param data
 * @returns {AxiosPromise}
 */
export function pageClassTypeRelation(data) {
  return request({
    url: `/course/sync/relation/page?mgrClassTypeId=${data.mgrClassTypeId}&pageIndex=${data.pageIndex}&pageSize=${data.pageSize}`,
    method: 'GET'
  })
}

/**
 * 删除班型关系
 * @param data
 * @returns {AxiosPromise}
 */
export function deleteClassTypeRelation(data) {
  return request({
    url: `/course/sync/relation/${data}`,
    method: 'DELETE'
  })
}

/**
 * 保存班型关联关系
 * @param data
 * @returns {AxiosPromise}
 */
export function saveClassTypeRelation(data) {
  return request({
    url: '/course/sync/relation',
    method: 'POST',
    data: data
  })
}

/**
 * 根据系列和产品线查询所有班型
 * @param clientCode
 * @param seriesId
 * @returns {AxiosPromise}
 */
export function listClassTypes(clientCode, seriesId) {
  return request({
    url: `/stip/classTypes/listClassTypes?clientCode=${clientCode}&seriesId=${seriesId}`,
    method: 'GET'
  })
}
