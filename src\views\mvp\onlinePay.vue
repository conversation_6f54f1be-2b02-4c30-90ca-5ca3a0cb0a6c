<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.orderNo"
        placeholder="订单号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.customer"
        placeholder="客户姓名/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.agency"
        placeholder="指派校区名称/手机号"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.settlement" placeholder="结算状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in settlements" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="times"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      />
      <area-picker :area-list="areaList" :level="'3'" area-style="'width:350px'" class="filter-item" @getAreaList="getAreaList" />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['mvp:onlinePay:payment']" class="filter-item" type="primary" size="mini" @click="batchPaymentShow">
        批量结算
      </el-button>
      <el-button v-waves v-permission="['mvp:onlinePay:export']" class="filter-item" type="primary" size="mini" @click="handleExport">
        导出明细报表
      </el-button>
      <!--      <span>-->
      <!--        <em>总标记金额</em>-->
      <!--        <el-tag>{{ actualSettlementAmount }}</el-tag>-->
      <!--      </span>-->
      <span>
        <em>待结算金额</em>
        <el-tag>{{ settlementAmount }}</el-tag>
      </span>
      <!--      <span>-->
      <!--        <em>总成本</em>-->
      <!--        <el-tag>{{ costAmount }}</el-tag>-->
      <!--      </span>-->
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column label="#" type="index" width="50" align="center" />
      <af-table-column label="订单号" show-overflow-tooltip prop="orderNo" />
      <af-table-column label="买家姓名/买家手机号" show-overflow-tooltip>
        <template slot-scope="scope">
          <p v-if="scope.row.userName">{{ scope.row.userName }}</p>
          <p v-if="scope.row.userPhone">{{ scope.row.userPhone }}</p>
        </template>
      </af-table-column>
      <af-table-column label="收款校区" prop="agencyName" show-overflow-tooltip />
      <af-table-column label="收款校区省" prop="agencyProvince" />
      <af-table-column label="收款校区城市" prop="agencyCity" />
      <af-table-column label="收款校区地区" prop="agencyArea" />
      <af-table-column label="银行卡信息" how-overflow-tooltip>
        <template slot-scope="scope">
          <p>{{ scope.row.bankName }}</p>
          <p>{{ scope.row.bankCardNo }}</p>
          <p>{{ scope.row.bankBranchName }}</p>
        </template>
      </af-table-column>
      <af-table-column label="交易渠道订单号" prop="wxTransactionId">
        <template slot-scope="scope">
          <p>微信</p>
          <p>{{ scope.row.wxTransactionId }}</p>
        </template>
      </af-table-column>
      <af-table-column label="交易金额" prop="paymentAmount" />
      <af-table-column label="应结算金额" prop="settlementAmount" />
      <af-table-column label="成本费" prop="costAmount" />
      <af-table-column label="交易时间" prop="paymentTime" />
      <af-table-column label="结算人" prop="settlementName" show-overflow-tooltip />
      <af-table-column label="结算状态" prop="settlement" :formatter="settlementStatue" />
      <af-table-column label="结算时间" prop="settlementTime" />
      <af-table-column label="货物信息" prop="orderDescription" />
      <af-table-column label="备注" prop="remark" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.settlement===0" v-permission="['mvp:onlinePay:Singlepayment']" type="primary" size="mini" @click="handleUpdate(row)">结算</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 结算弹框 -->
    <payment-pop ref="payment" @updetePaymentList="getList" />
    <!-- 结算弹框 -->
    <!-- 批量结算弹框 -->
    <el-dialog title="批量结算" :visible.sync="batchPaymentPop" width="25%">
      <div class="batch-info">
        <h3>根据当前查询条件,共查询到</h3>
        <p>共计:<em>{{ total }}</em>笔交易</p>
        <p>合计应付金额:<em>{{ settlementAmount }}</em></p>
      </div>
      <div class="batch-btns">
        <el-button type="infor" size="mini" @click="batchPaymentPop=false">取消</el-button>
        <el-button type="primary" size="mini" @click="getTransferDialog">确定</el-button>
      </div>
    </el-dialog>
    <!-- 批量结算弹框 -->
  </div>
</template>

<script>
import {
  getransactionList,
  batchPayment
} from '@/api/mvp'
import {
  parseTime
} from '@/utils'
import {
  customerSource,
  assignSatuts,
  settlements,
  postType,
  converseEnToCn
} from '@/utils/field-conver'
import Pagination from '@/components/Pagination'
import AreaPicker from '@/components/area-picker'
import PaymentPop from './components/paymentPop.vue'
export default {
  name: 'OnlinePayList',
  components: {
    Pagination,
    AreaPicker,
    PaymentPop
  },
  directives: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      assignSatuts: assignSatuts,
      settlements: settlements,
      postType: postType,
      editCustomer: true,
      times: [],
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        customer: '',
        settlement: '',
        agency: '',
        postType: '',
        orderNo: ''
      },
      dialogTransfer: false, // 批量转让的弹窗
      multipleSelection: [], // 多选框被选中的row
      multipleSelectCustomerId: [], // 多选框选中的客户id
      beTransferCustomer: '',
      exportSetting: { // 导出按钮
        downloadLoading: false
      },
      testDialog: false,
      costAmount: '',
      settlementAmount: '',
      actualSettlementAmount: '',
      waitPostNum: '',
      customerSource: customerSource,
      clueIds: [], // 指派时需要传递的客户资源id
      payment: {}, // 结算需要传递的id
      batchPaymentPop: false
    }
  },
  computed: {},
  created() {
    this.listLoading = false
    this.getList()
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, that.areaList, {
        startTime: this.times !== null ? this.times[0] : '',
        endTime: this.times !== null ? this.times[1] : ''
      })

      await getransactionList(params).then(response => {
        that.list = response.data.page.records
        that.total = response.data.page.total
        that.listLoading = false
        that.settlementAmount = response.data.settlementAmount || 0
        that.costAmount = response.data.costAmount || 0
        that.waitPostNum = response.data.waitPostNum || 0
        that.actualSettlementAmount = response.data.actualSettlementAmount || 0
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    /**
       * 获取省市区的地址
       * */
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    /**
       * 格式化时间
       */
    formatterTime(row) {
      return parseTime(row.createTime)
    },
    // 重置
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = {}
      this.times = []
      this.getList()
    },
    sortChange(data) {},
    handleUpdate(row) {
      const costAmounts = row.costAmount || 0
      const settlementAmounts = row.settlementAmount || 0
      const paymentObj = Object.assign(row, { costAmount: costAmounts, settlementAmount: settlementAmounts })
      this.$refs.payment.paymentPop = true
      this.$refs.payment.getInitVal(paymentObj)
    },
    handleFollow(row) {
      this.$refs.followRef.getLists(row.id)
    },
    batchPaymentShow() {
      if (this.times.length !== 2) {
        this.$message({
          type: 'warning',
          message: '请先选择开始时间和结束时间'
        })
      } else if (this.listQuery.settlement === '' || this.listQuery.settlement !== 0) { // 请选择结算状态
        this.$message({
          type: 'warning',
          message: '请先选择待结算的数据'
        })
      } else {
        this.getList()
        setTimeout(() => {
          this.batchPaymentPop = true
        }, 500)
      }
    },
    /**
       * 批量结算的弹窗
       * */
    getTransferDialog(row) {
      if (this.total === 0) {
        this.$message({
          type: 'warning',
          message: '没有需要结算的交易!'
        })
        this.batchPaymentPop = false
        this.times = []
        this.listQuery.settlement = ''
        this.getList()
      } else
      if (this.times.length === 2 && this.listQuery.settlement === 0) {
        const params = Object.assign({}, this.listQuery, { startTime: this.times[0], endTime: this.times[1] })

        batchPayment(params).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '结算成功!'
            })
            this.batchPaymentPop = false
            this.times = []
            this.listQuery.settlement = ''
            this.getList()
          }
        }).catch(() => {
          this.batchPaymentPop = false
          this.times = []
          this.listQuery.settlement = ''
          this.getList()

        })
      }
    },
    /**
       * 获取搜索筛选之后的用户id
       **/
    getEmployeeId(id) {
      this.beTransferCustomer = id
    },
    /**
       * 多选框的返回值
       */
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.multipleSelectCustomerId = this.multipleSelection.map(item => {
        return item.id
      })
    },
    /* 导出数据 */
    handleExport() {
      const that = this
      const a = document.createElement('a')
      const ulrs = process.env.VUE_APP_BASE_API
      const exportUrl = ulrs.charAt(ulrs.length - 1) === '/' ? ulrs : ulrs + '/'
      let url = exportUrl + 'userOrders/export?pageIndex=' + that.listQuery.pageIndex + '&pageSize=' + that.listQuery.pageSize
      if (that.times.length !== 2) {
        that.$message({
          type: 'warning',
          message: '请先选择导出开始时间和结束时间'
        })
      } else {
        url = url + '&provinceId=' + that.areaList.provinceId + '&cityId=' + that.areaList.cityId + '&areaId=' + that.areaList.areaId + '&settlement=' + that.listQuery.settlement + '&agency=' + that.listQuery.agency + '&customer=' + that.listQuery.customer + '&orderNo=' + that.listQuery.orderNo + '&startTime=' + that.times[0] + '&endTime=' + that.times[1] + '&postType=' + that.listQuery.postType
        that.$confirm('确定导出数据?', {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.listLoading = true
          const params = Object.assign(that.listQuery, that.areaList, {
            startTime: that.times[0] || '',
            endTime: that.times[1] || ''
          })

          getransactionList(params).then(response => {
            that.list = response.data.page.records
            that.total = response.data.page.total
            that.listLoading = false
            if (url && that.list.length > 0) {
              a.href = url
              a.target = '_blank'
              document.body.appendChild(a)
              a.click()
            } else {
              setTimeout(() => {
                that.$message({
                  type: 'warning',
                  message: '没有可以导出的数据'
                })
              }, 500)
            }
          }).catch(() => {

          })
        }).catch(() => {
          that.$message({
            type: 'infor',
            message: '取消操作'
          })
        })
      }
    },
    cancelTransfer() {
      this.dialogTransfer = false
      this.$refs.transfer.transferForm.employeeSearchField = ''
    },
    // 转化信息来源
    customerSources(row) {
      return converseEnToCn(this.customerSource, row.customerSource)
    },
    assignSatutsList(row) {
      return converseEnToCn(this.assignSatuts, row.assignSatuts)
    },
    // 结算状态
    settlementStatue(row) {
      return converseEnToCn(this.settlements, row.settlement)
    },
    // 邮寄状态
    postTypeStatue(row) {
      return converseEnToCn(this.postType, row.postType)
    }
  }
}
</script>
<style scoped="scoped" lang="scss">
  em {
    font-style: normal;
  }
  .filter-container .filter-item{
    margin-top: 6px !important;
  }
  .batch-info{
    h3{
      padding-top: 0;
      margin-top: 0;
    }
    p{
      em{
        font-weight: bold;
        font-size: 30px;
      }
      &:last-child{
        padding-bottom: 10px;
      }
    }
  }
  .batch-btns{
    padding:10px 0;
    border-top: 1px #eaeaea solid;
  }
</style>
<style>
  .el-dialog__body{
    padding: 10px 20px;
  }
</style>
