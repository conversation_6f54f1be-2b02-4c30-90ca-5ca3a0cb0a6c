<template>
  <el-dialog :visible.sync="classPop" :title="classTitle" :close-on-click-modal="!classPop" width="60%" @close="changeInit">
    <div class="assing-info">
      <el-form ref="classForms" :model="classForm" :rules="rules" label-width="100px">
        <el-form-item label="班型名称" prop="title">
          <el-input v-model="classForm.title" placeholder="请输入班型名称" maxlength="20" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="班型状态" prop="status">
          <el-radio-group v-model="classForm.status" :disabled="isEdit">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">已上架</el-radio>
            <el-radio :label="2">已下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属产品线" prop="productCode">
          <el-select v-model="classForm.productCode" filterable placeholder="请选择所属产品线" clearable class="filter-item" :disabled="isEdit" @change="getClientCodes">
            <el-option v-for="item in productCodeList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="classForm.productCode" label="显示范围">
          <el-checkbox-group v-model="clientCodes" :disabled="isEdit">
            <el-checkbox v-for="item in clientCode" :key="item.code" :label="item.code">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="跨区校验" prop="checkAddress">
          <el-radio-group v-model="classForm.checkAddress" :disabled="isEdit">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属系列" prop="classSeriesId">
          <el-select v-model="classForm.classSeriesId" filterable placeholder="请选择所属系列" clearable class="filter-item" :disabled="isEdit">
            <el-option v-for="item in seriesList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程副标题" prop="subTitle">
          <el-input v-model="classForm.subTitle" placeholder="请输入课程副标题" maxlength="50" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="班型介绍">
          <el-input v-model="classForm.introduction" placeholder="请输入班型介绍" type="textarea" maxlength="500" show-word-limit :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model.number="classForm.sort" placeholder="请输入排序" maxlength="7" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="封面" :style="{paddingBottom:classForm.productCode===100?'0px':'40px'}">
          <div class="upload-imgs">
            <div v-if="!uploadImg&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="upload($event)">
              <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
              <div class="upload-tips">课程主封面(423*487)</div>
            </div>
            <div class="img">
              <img v-if="uploadImg" :src="uploadImg">
              <a v-if="uploadImg&&!isEdit" class="close" @click="delImgA">
                <i class="el-icon-delete" />
              </a>
              <div v-if="uploadImg" class="upload-tips">课程主封面(423*487)</div>
            </div>
          </div>

          <div class="upload-imgs-small">
            <div v-if="!uploadImgClass&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadClass($event)">
              <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
              <div class="upload-tips">课程列表封面(689*367)</div>
            </div>
            <div class="img">
              <img v-if="uploadImgClass" :src="uploadImgClass">
              <a v-if="uploadImgClass&&!isEdit" class="close" @click="delImgB">
                <i class="el-icon-delete" />
              </a>
              <div v-if="uploadImgClass" class="upload-tips">课程列表封面(689*367)</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="classForm.productCode===100" label="新版封面" :style="{paddingBottom:classForm.productCode===100?'40px':'0px'}">
          <div class="upload-imgs">
            <div v-if="!newLongPath&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadNew($event)">
              <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
              <div class="upload-tips">课程主封面(1086*500)</div>
            </div>
            <div class="img">
              <img v-if="newLongPath" :src="newLongPath">
              <a v-if="newLongPath&&!isEdit" class="close" @click="delImgC">
                <i class="el-icon-delete" />
              </a>
              <div v-if="newLongPath" class="upload-tips">课程主封面(1086*500)</div>
            </div>
          </div>

          <div class="upload-imgs-small">
            <div v-if="!newImagePath&&!isEdit">
              <input ref="inputerA" type="file" class="upload" multiple accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadClassNew($event)">
              <a class="add"><i class="iconfont icon-plus" /><p class="el-icon-picture" /></a>
              <div class="upload-tips">课程列表封面(298*360)</div>
            </div>
            <div class="img">
              <img v-if="newImagePath" :src="newImagePath">
              <a v-if="newImagePath&&!isEdit" class="close" @click="delImgD">
                <i class="el-icon-delete" />
              </a>
              <div v-if="newImagePath" class="upload-tips">课程列表封面(298*360)</div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button type="infor" size="mini" @click="classPop=false,cancelClass()">取消</el-button>
      <!--      新增班型-->
      <el-button v-if="flags===1" type="primary" size="mini" @click="custormClass">确定</el-button>
      <!--      修改班型-->
      <el-button v-if="flags===0" type="primary" size="mini" @click="custormEditClass">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { addClassType, getClassDetail, editClassType, classSystem, getSysClients } from '@/api/classType'
import { uploadSuccess } from '@/api/common'
export default {
  name: 'AddClassPop',
  data() {
    return {
      classTitle: '',
      classPop: false,
      classForm: {
      },
      clientCodes: [],
      clientCode: [],
      customerUpdate: true,
      rules: {
        title: { required: true, trigger: 'blur', message: '请输入班型名称' },
        status: { required: true, trigger: 'blur', message: '请选择班型状态' },
        checkAddress: { required: true, trigger: 'blur', message: '请选择跨区校验' },
        sort: { required: true, trigger: 'blur', message: '请输入排序' },
        productCode: { required: true, trigger: 'blur', message: '请选择所属产品线' },
        subTitle: { required: true, trigger: 'blur', message: '请输入课程副标题' },
        classSeriesId: { required: true, trigger: 'blur', message: '请选择所属系列' }
      },
      isEdit: false,
      flags: -1,
      host: 'https://obs-d812.obs.cn-north-1.myhuaweicloud.com',
      uploadImg: '',
      imageResource: '', // 首页封面
      uploadImgClass: '',
      longImageResource: '', // 班型封面
      uuid: '',
      subjectsAll: [],
      productCodeList: [],
      seriesList: [],
      newImageResource: '',
      newLongImageResource: '',
      newImagePath: '',
      newLongPath: ''
    }
  },
  watch: {
    // 'productCodeList': {
    //   immediate: true,
    //   deep: true,
    //   handler(newVal) {
    //     if (newVal.length === 0) {
    //       this.classForm.productCode = ''
    //       this.clientCodes = []
    //       this.clientCode = []
    //       this.classForm.productCode = ''
    //       this.seriesList = []
    //     }
    //   }
    // },
    'classForm.productCode': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (oldVal && oldVal !== newVal) {
          this.clientCodes = []
          this.classForm.classSeriesId = ''
          this.newImageResource = ''
          this.newLongImageResource = ''
          this.newImagePath = ''
          this.newLongPath = ''
        } else if (newVal !== 100) {
          this.newImageResource = ''
          this.newLongImageResource = ''
          this.newImagePath = ''
          this.newLongPath = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
    })
  },
  methods: {
    getClassDetail(id) {
      getClassDetail(id).then(res => {
        if (res.code === '000000') {
          this.classForm = res.data
          this.uploadImg = res.data.imageUrl || ''
          this.uploadImgClass = res.data.longImageUrl || ''
          this.imageResource = res.data.imageResource || ''
          this.longImageResource = res.data.longImageResource || ''
          this.clientCodes = res.data.clientCodes || []
          this.newLongImageResource = res.data.newLongImageResource
          this.newLongPath = res.data.newLongPath
          this.newImageResource = res.data.newImageResource
          this.newImagePath = res.data.newImagePath
          if (this.classForm.productCode) this.getClassSystem(this.classForm.productCode)
          if (this.classForm.productCode) this.getClientCodes(this.classForm.productCode)
          this.getSysClients()
        }
      })
    },
    cancelClass() {
      if (this.$refs.classForms) {
        this.$refs.classForms.clearValidate()
      }
      this.clientCodes = []
      this.classForm = {}
    },
    custormClass() { // 新增班型
      this.$refs.classForms.validate((valid) => {
        if (valid) {
          if (this.clientCodes && this.clientCodes.length > 0) {
            const param = Object.assign({}, this.classForm, { clientCodes: this.clientCodes, imageResource: this.imageResource || '', longImageResource: this.longImageResource || '', newImageResource: this.newImageResource || '', newLongImageResource: this.newLongImageResource || '' })
            addClassType(param).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.$emit('addClassList')
                this.classPop = false
                this.clientCodes = []
                this.classForm = {}
                this.uploadImg = ''
                this.imageResource = ''
                this.longImageResource = ''
                this.uploadImgClass = ''
                this.newLongImageResource = ''
                this.newLongPath = ''
                this.newImageResource = ''
                this.newImagePath = ''
                this.$refs.classForms.clearValidate()
              }
            }).catch(() => {

            })
          } else {
            this.$message({
              type: 'warning',
              message: '请选择适用客户端'
            })
          }
        } else {
          return false
        }
      })
    },
    custormEditClass() { // 修改班型
      this.$refs.classForms.validate((valid) => {
        if (valid) {
          if (this.clientCodes && this.classForm.id && this.clientCodes.length > 0) {
            const param = Object.assign({}, this.classForm, { clientCodes: this.clientCodes, id: this.classForm.id, imageResource: this.imageResource || '', longImageResource: this.longImageResource || '', newImageResource: this.newImageResource || '', newLongImageResource: this.newLongImageResource || '' })
            editClassType(param).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.$emit('addClassList')
                this.classPop = false
                this.clientCodes = []
                this.classForm = {}
                this.uploadImg = ''
                this.imageResource = ''
                this.longImageResource = ''
                this.uploadImgClass = ''
                this.newLongImageResource = ''
                this.newLongPath = ''
                this.newImageResource = ''
                this.newImagePath = ''
                this.$refs.classForms.clearValidate()
              }
            }).catch(() => {

            })
          } else {
            this.$message({
              type: 'warning',
              message: '请选择适用客户端'
            })
          }
        } else {
          return false
        }
      })
    },
    upload(e) { // 首页封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '请上传1M以内的图片'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.imageResource = res.data.id
                that.uploadImg = res.data.url
              }
            })
          }
        })
      }
    },
    uploadClass(e) { // 班型封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的班型封面不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.longImageResource = res.data.id
                that.uploadImgClass = res.data.url
              }
            })
          }
        })
      }
    },

    uploadNew(e) { // 首页封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的新版课程主封面不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.newLongImageResource = res.data.id
                that.newLongPath = res.data.url
              }
            })
          }
        })
      }
    },
    uploadClassNew(e) { // 班型封面
      const that = this
      const file = e.target.files[0]
      const size = (file.size / 1024 / 1024).toFixed(3)
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '上传的新版课程列表封面不能大于1M'
        })
      } else {
        obsClient.putObject({
          Bucket: 'obs-d812',
          Key: `${fileName}`, // 文件名
          SourceFile: file// 文件路径
        }, function(err, result) {
          if (err) {
            console.error('Error-->' + err)
          } else {
            const paramsUpload = Object.assign({}, {
              imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/course/${that.uuid}.${tempName[tempName.length - 1]}`,
              resourceType: 'image'
            })
            uploadSuccess(paramsUpload).then(res => {
              if (res.code === '000000') {
                that.newImageResource = res.data.id
                that.newImagePath = res.data.url
              }
            })
          }
        })
      }
    },
    changeInit() {
      this.clientCodes = []
      this.classForm = {}
      this.uploadImg = ''
      this.imageResource = ''
      this.longImageResource = ''
      this.uploadImgClass = ''
      this.newLongImageResource = ''
      this.newLongPath = ''
      this.newImageResource = ''
      this.newImagePath = ''
      if (this.$refs.classForms) {
        this.$refs.classForms.clearValidate()
      }
    },
    delImgA() {
      this.imageResource = ''
      this.uploadImg = ''
    },
    delImgB() {
      this.longImageResource = ''
      this.uploadImgClass = ''
    },
    delImgC() {
      this.newLongImageResource = ''
      this.newLongPath = ''
    },
    delImgD() {
      this.newImageResource = ''
      this.newImagePath = ''
    },
    getSysClients() {
      const params = {
        level: 1
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.productCodeList = res.data || []
        }
      }).catch((error) => {

      })
    },
    getClientCodes(val) {
      const params = {
        parentCode: val
      }
      getSysClients(params).then(res => {
        if (res.code === '000000') {
          this.clientCode = res.data || []
        }
      }).catch((error) => {

      })
      this.getClassSystem(val)
    },
    getClassSystem(code) {
      classSystem(code).then(res => {
        if (res.code === '000000') {
          this.seriesList = res.data || []
        }
      }).catch((error) => {

      })
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    }
  }
}
</script>

<style scoped>
  .assign-operas{
    position: absolute;
    z-index: 999999999;
    width: 100%;
    bottom: 20px;
    left: 0;
    display: flex;
    justify-content: center;
    align-content: center;
  }

  .upload-imgs{
    position: relative;
    width: 185px;
    height: 165px;
    font-size: 14px;
    display: inline-block;
    margin-right: 25px;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0 3px 3px #eaeaea;
    border-radius: 3px;
  }
  .upload-imgs .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 130px;
    line-height: 130px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }
  .upload-imgs .add .el-icon-picture{
    font-size: 35px;
  }
  .upload-imgs .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 185px;
    height: 165px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs .img{
    width: 185px;
    height: 130px;
  }
  .upload-imgs .img img{
    vertical-align: middle;
    width: 185px;
    height: 130px;
  }
  .upload-imgs .img .close{
    display: none;
  }
  .upload-imgs:hover .img .close{
    display: block;
    position: absolute;
    top:0px;
    left: 0px;
    width:185px;
    height:165px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 150px;
    font-size: 24px;
    color: #fff;
  }
  .img-upload{
    padding-right: 8px;
  }
  .upload-tips{
    position: absolute;
    bottom: 0;
    left: 0;
    right:0;
    width:100%;
    text-align: center;
    font-size: 12px;
    color: #666;
    z-index: 9999;
  }
  .upload-imgs-small{
    position: relative;
    width: 140px;
    height: 165px;
    font-size: 14px;
    display: inline-block;
    margin-right: 25px;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0 3px 3px #eaeaea;
    border-radius: 3px;
  }
  .upload-imgs-small .add{
    display: block;
    background-color: #ccc;
    color: #ffffff;
    height: 130px;
    line-height: 130px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }
  .upload-imgs-small .add .el-icon-picture{
    font-size: 35px;
  }
  .upload-imgs-small .upload{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 140px;
    height: 165px;
    opacity: 0;
    cursor: pointer;
  }
  .upload-imgs-small .img{
    width: 140px;
    height: 130px;
  }
  .upload-imgs-small .img img{
    vertical-align: middle;
    width: 140px;
    height: 130px;
  }
  .upload-imgs-small .img .close{
    display: none;
  }
  .upload-imgs-small:hover .img .close{
    display: block;
    position: absolute;
    top:0px;
    left: 0px;
    width:140px;
    height:165px;
    background: rgba(0,0,0,.5);
    text-align: center;
    line-height: 160px;
    font-size: 24px;
    color: #fff;
  }
</style>
