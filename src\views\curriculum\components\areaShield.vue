<template>
  <el-dialog :visible.sync="areaPop" :title="areaTitle" :close-on-click-modal="!areaPop" width="70%">
    <span slot="title">
      <em v-html="areaTitle" />
      <i style="font-size: 14px;">-屏蔽区域</i>
    </span>
    <div class="assing-info">
      <el-form :model="listQuery">
        <div style="display:flex">
          <area-picker :area-list="areaList" :level="'3'" area-style="'width:450px'" class="filter-item" @getAreaList="getAreaList" />
          <div>
            <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
              查询
            </el-button>
            <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
              重置
            </el-button>
            <el-button v-waves class="filter-item" type="primary" size="mini" :disabled="enableFlagsCreate" @click="addRegion">
              添加
            </el-button>
          </div>
        </div>
      </el-form>
      <el-table
        ref="areaTab"
        v-loading="listLoading"
        :data="list"
        border
        fit
        stripe
        highlight-current-row
        style="width: 100%;margin-top: 20px"
      >
        <af-table-column label="区域ID" show-overflow-tooltip prop="regionId" />
        <af-table-column label="屏蔽区域" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-show="scope.row.provinceName">{{ scope.row.provinceName }}</span>
            <span v-show="scope.row.cityName">-{{ scope.row.cityName }}</span>
            <span v-show="scope.row.areaName">-{{ scope.row.areaName }}</span>
          </template>
        </af-table-column>
        <af-table-column label="当前状态" prop="status" show-overflow-tooltip>
          <template>
            <span>已屏蔽</span>
          </template>
        </af-table-column>
        <af-table-column label="创建时间" prop="createTime" show-overflow-tooltip />
        <el-table-column label="操作" class-name="small-padding fixed-width action-warp auto-fixed" min-width="230" fixed="right">
          <template slot-scope="{row}">
            <!-- <el-button v-if="row.status===1" type="primary" size="mini" @click="handleUpdate(row)">禁用</el-button> -->
            <el-button v-permission="['curriculum:index:removeArea']" type="primary" size="mini" @click="handleUpdate(row)">解除屏蔽</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.pageIndex"
        :limit.sync="listQuery.pageSize"
        @pagination="getList(orderId)"
      />
    </div>
  </el-dialog>
</template>

<script>
import AreaPicker from '@/components/area-picker'
import Pagination from '@/components/Pagination'
import { addRegion, getShieldRegion, classTypeDeleteArea } from '@/api/classType'
export default {
  name: 'AreaShield',
  components: {
    AreaPicker,
    Pagination
  },
  props: {
    classTypeId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      areaPop: false,
      areaTitle: '',
      areaList: {
        provinceId: '',
        cityId: '',
        areaId: ''
      },
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      listLoading: true,
      enableFlagsCreate: false,
      orderId: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
    })
  },
  methods: {
    getAreaList(data) {
      this.areaList.provinceId = data.provinceId
      this.areaList.cityId = data.cityId
      this.areaList.areaId = data.areaId
    },
    async getList(ids) {
      const that = this
      that.listLoading = true
      const params = Object.assign({
        classTypeId: ids,
        status: 1
      }, that.listQuery, that.areaList)
      await getShieldRegion(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList(this.orderId)
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.areaList = []
      this.getList(this.orderId)
    },
    handleUpdate(row) {
      // eslint-disable-next-line no-undef
      // const title = row.status === 1 ? '禁用' : '启用'
      // const tips = row.status === 1 ? '禁用成功' : '启用成功'
      this.$confirm(`是否确认解除屏蔽?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        classTypeDeleteArea(row.id).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '解除成功'
            })
            this.getList(this.classTypeId)
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'warning',
          message: `取消解除`
        })
      })
    },
    addRegion() {
      this.enableFlagsCreate = true
      if (!this.areaList.provinceId) {
        this.$message({
          message: '请选择屏蔽省份',
          type: 'error'
        })
        this.enableFlagsCreate = false
        return
      }
      var regionId
      if (this.areaList.provinceId && !this.areaList.cityId) {
        regionId = this.areaList.provinceId
      } else if (this.areaList.provinceId && this.areaList.cityId && !this.areaList.areaId) {
        regionId = this.areaList.cityId
      } else {
        regionId = this.areaList.areaId
      }
      if (this.classTypeId && regionId) {
        const data = Object.assign({}, { classTypeId: this.classTypeId, regionId: regionId }, this.areaList)
        addRegion(data).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '添加成功'
            })
            this.$set(this.areaList, 'areaId', '')
            this.$set(this.areaList, 'countyId', '')
            this.getList(this.classTypeId)
            this.enableFlagsCreate = false
          }
        }).catch(() => {
          this.enableFlagsCreate = false
          this.areaList = {}

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择屏蔽的区域'
        })
        this.enableFlagsCreate = false
      }
    }
  }
}
</script>

<style scoped>

</style>
