<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="年级"
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="年级状态" filterable clearable class="filter-item" style="width: 140px;">
        <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="followDate"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleFilter">
        查询
      </el-button>
      <el-button v-waves class="filter-item" type="primary" size="mini" @click="handleReset">
        重置
      </el-button>
      <el-button v-waves v-permission="['curriculum:grade:add']" class="filter-item" size="mini" type="primary" @click="handleCreate">新增</el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <af-table-column label="#" type="index" width="50" />
      <af-table-column label="年级编号" show-overflow-tooltip prop="createTime" width="100px">
        <template slot-scope="scope">
          <a class="codes" @click="getDetail(scope.row)">{{ scope.row.id }}</a>
        </template>
      </af-table-column>
      <af-table-column label="年级" show-overflow-tooltip prop="name" />
      <af-table-column label="年级状态" prop="status" show-overflow-tooltip :formatter="getJoinStatusCN" />
      <af-table-column label="年级排序" prop="sort" show-overflow-tooltip />
      <af-table-column label="更新时间" prop="updateTime" />
      <af-table-column label="创建时间" prop="createTime" />
      <el-table-column label="操作" class-name="small-padding fixed-width action-warp" width="230">
        <template slot-scope="{row}">
          <el-button v-permission="['curriculum:grade:edit']" type="primary" size="mini" @click="handleUpdate(row)">修改</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!--    新增/修改班型弹框-->
    <grade-pop ref="grade" @addGradeList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import GradePop from './components/gradePop'
import { gradeList } from '@/api/classType'
import {
  enableList,
  converseEnToCn
} from '@/utils/field-conver'
export default {
  name: 'Grade',
  components: {
    Pagination,
    GradePop
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      enableList: enableList,
      paymentStatus: [],
      followDate: []
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    async getList() {
      const that = this
      that.listLoading = true
      const params = Object.assign(that.listQuery, { startTime: this.followDate[0] ? this.followDate[0] : '', endTime: this.followDate[1] ? this.followDate[1] : '' })
      await gradeList(params).then(response => {
        that.list = response.data.records
        that.total = response.data.total
        that.listLoading = false
      }).catch(() => {

      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.followDate = []
      this.getList()
    },
    handleCreate() {
      this.$refs.grade.gradePop = true
      this.$refs.grade.gradeTitle = '添加年级'
      this.$refs.grade.isEdit = false
      this.$refs.grade.flags = 1
      this.$refs.grade.listQuery.status = 1
    },
    handleUpdate(row) {
      this.$refs.grade.gradePop = true
      this.$refs.grade.gradeTitle = '修改年级'
      this.$refs.grade.isEdit = false
      this.$refs.grade.flags = 0
      this.$refs.grade.gradeDetail(row.id)
    },
    getJoinStatusCN(row) {
      return converseEnToCn(this.enableList, row.status)
    },
    getDetail(row) {
      this.$refs.grade.gradePop = true
      this.$refs.grade.gradeTitle = '修改年级'
      this.$refs.grade.isEdit = true
      this.$refs.grade.flags = -1
      this.$refs.grade.gradeDetail(row.id)
    }
  }
}
</script>

<style scoped>
  .codes{
    font-weight: bold;
    color: #0a76a4;
  }
</style>
