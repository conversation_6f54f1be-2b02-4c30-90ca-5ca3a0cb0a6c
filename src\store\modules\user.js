import { login, logout } from '@/api/login'
import { getToken, setToken, removeToken, getMenuList, setMenuList, setBtns, setUserinfo, getUserInfo } from '@/utils/auth'
import { resetRouter } from '@/router'
import { Message } from 'element-ui'
import { getWxToken } from '@/api/wxLogin'
const state = {
  token: getToken(),
  userInfo: '',
  avatar: '',
  introduction: '',
  menus: '',
  uploadType: ''
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_MENUS: (state, menus) => {
    state.menus = menus
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  DISTRIBUTORID: (state, id) => {
    state.distributorId = id
  },
  UPLOADTYPE: (state, type) => {
    state.uploadType = type
  }
}
function getAuthCode(list) {
  let res = []
  list.forEach(item => {
    res.push({ code: item.code })
  })
  if (getUserInfo()['userName'] === 'admin') {
    res = [{ code: 'all' }]
  }
  return res
}
const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login({ ...userInfo }).then(response => {
        const { data } = response
        //
        if (data.menuList && data.menuList.length < 1 && data.userName !== 'admin') {
          Message({
            message: '该账号没有配置页面权限，请联系管理员',
            type: 'error'
          })
          reject('')
          return
        }
        commit('SET_TOKEN', data.token)
        setUserinfo({
          userName: data.userName,
          realName: data.realName,
          mobile: data.mobile,
          userId: data.id
        })
        setToken(data.token)
        setBtns(data.buttonList)
        setMenuList(getAuthCode(data.menuList))
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // wx user login
  wxLogin({ commit }, { code }) {
    return new Promise((resolve, reject) => {
      getWxToken(code).then(response => {
        const { data } = response
        if (data.menuList && data.menuList.length < 1 && data.userName !== 'admin') {
          Message({
            message: '该账号没有配置页面权限，请联系管理员',
            type: 'error'
          })
          reject('')
          return
        }
        commit('SET_TOKEN', data.token)
        setUserinfo({
          userName: data.userName,
          realName: data.realName,
          mobile: data.mobile,
          userId: data.id
        })
        setToken(data.token)
        setBtns(data.buttonList)
        setMenuList(getAuthCode(data.menuList))
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('SET_TOKEN', '')
        removeToken()
        setMenuList('[]')
        commit('SET_MENUS', [])
        resetRouter()
        dispatch('tagsView/delAllViews', null, { root: true })
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },
  getMenus({ commit }) {
    return new Promise(resolve => {
      commit('SET_MENUS', getMenuList())
      resolve()
    })
  },
  getUserInfo({ commit }) {
    return new Promise(resolve => {
      commit('SET_USERINFO', getUserInfo())
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
