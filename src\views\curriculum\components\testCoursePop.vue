<template>
  <el-dialog
    :visible.sync="coursePop"
    :title="courseTitle"
    top="0"
    :close-on-click-modal="!coursePop"
    width="60%"
    @close="changeInit"
  >
    <div class="assing-info">
      <el-form
        ref="courseForms"
        :model="listQuery"
        size="mini"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="知识点名称" prop="title">
              <el-input
                v-model="listQuery.title"
                placeholder="请输入知识点名称"
                maxlength="30"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="所属产品线" prop="clientCode">
              <ProductLineSelect
                :isTest="true"
                :id="listQuery.clientCode"
                v-model="listQuery.clientCode"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :style="">
            <el-form-item label="所属科目" prop="subjectId">
              <el-select
                v-model="listQuery.subjectId"
                filterable
                placeholder="请选择所属科目"
                clearable
                class="filter-item"
                :disabled="isEdit"
                @change="validateField('subjectId')"
              >
                <el-option
                  v-for="item in subjectsAll"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              v-if="listQuery.courseSource === 1"
              label="阿里链接"
              prop="vid"
              required
            >
              <el-input
                v-model="listQuery.vid"
                placeholder="请输入阿里链接"
                maxlength="32"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item v-if="listQuery.courseSource === 1" label="华为链接">
              <el-input
                v-model="listQuery.assetId"
                placeholder="请输入华为链接"
                maxlength="32"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="课程时长" prop="videoMinute" required>
              <div class="course-duration">
                <el-input
                  v-model="listQuery.videoMinute"
                  placeholder="请输入分钟"
                  maxlength="20"
                  :disabled="isEdit"
                  @blur="validateCourseDuration"
                  @input="onCourseDurationInput"
                  @change="validateCourseDuration"
                />
                <em>分</em>
                <el-input
                  v-model="listQuery.videoSecond"
                  placeholder="请输入秒钟"
                  maxlength="2"
                  :disabled="isEdit"
                  @blur="validateCourseDuration"
                  @input="onCourseDurationInput"
                  @change="validateCourseDuration"
                />
                <em>秒</em>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-form-item label="显示状态" prop="isShow">
            <el-radio-group
              v-model="listQuery.isShow"
              :disabled="isEdit"
              @change="validateField('isShow')"
            >
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="课程状态" prop="status">
            <el-radio-group
              v-model="listQuery.status"
              :disabled="isEdit"
              @change="validateField('status')"
            >
              <el-radio :label="0">未上架</el-radio>
              <el-radio :label="1">已上架</el-radio>
              <el-radio :label="2">已下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上线时间" prop="onlineDate">
            <el-date-picker
              v-model="listQuery.onlineDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="filter-item"
              placeholder="上线时间"
              @change="validateField('onlineDate')"
            />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div v-if="!isEdit" class="assign-operas">
      <el-button
        type="infor"
        size="mini"
        @click="(coursePop = false), changeInit()"
        >取消</el-button
      >
      <!-- 新增课程-->
      <el-button
        v-if="flags === 1"
        :disabled="disableSubmit"
        type="primary"
        size="mini"
        @click="addCourse"
        >确定</el-button
      >
      <!-- 修改课程-->
      <el-button
        v-if="flags === 0"
        :disabled="disableSubmit"
        type="primary"
        size="mini"
        @click="editCourse"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
// eslint-disable-next-line no-undef

var obsClient = new ObsClient({
  access_key_id: "CSMHAP6XJZ3Q9NTLYX7W",
  secret_access_key: "o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC",
  server: "obs.cn-north-1.myhuaweicloud.com",
  timeout: 60 * 5
});
import {
  addCourse,
  editCourse,
  courseDetail,
  getSubjectList
} from "@/api/classType";
import { uploadSuccess } from "@/api/common";
import ProductLineSelect from "@/components/Select/ProductLineSelect.vue";

const initListQuery = {
  subjectId: null,
  classTypeId: null,
  courseSource: 1,
  materialNewId: null,
  title: "",
  clientCode: "",
  vid: "",
  assetId: "",
  videoMinute: "",
  videoSecond: "",
  isShow: null,
  status: null,
  onlineDate: ""
};
export default {
  name: "AddClassPop",
  components: {
    ProductLineSelect
  },
  data() {
    return {
      courseTitle: "",
      coursePop: false,
      disableSubmit: false,
      listQuery: {
        subjectId: null,
        classTypeId: null,
        courseSource: 1,
        materialNewId: null,
        title: "",
        clientCode: "",
        vid: "",
        assetId: "",
        videoMinute: "",
        videoSecond: "",
        isShow: null,
        status: null,
        onlineDate: ""
      },

      host: "https://santtaojiaoyu.oss-cn-beijing.aliyuncs.com/",
      subjectsAll: [],
      isEdit: false,
      flags: -1,
      validateTimer: null
    };
  },
  watch: {
    // 监听弹窗打开状态
    coursePop(newVal) {
      if (newVal && this.listQuery.clientCode) {
        // 弹窗打开且有产品线时，加载科目列表
        this.getSubjectList();
      }
    },
    // 三陶产品线改变
    "listQuery.clientCode": {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal) {
          // 只有在用户主动切换产品线时才清空科目选择，避免在查看详情时清空
          if (newVal !== oldVal && oldVal !== undefined) {
            this.listQuery.subjectId = "";
          }
          this.getSubjectList();
        }
      }
    }
  },

  computed: {
    rules() {
      return {
        title: { required: true, trigger: "blur", message: "请输入知识点名称" },
        subjectId: {
          required: true,
          trigger: "change",
          message: "请选择所属科目"
        },
        courseSource: {
          required: true,
          trigger: "blur",
          message: "请选择课程渠道"
        },
        isShow: {
          required: true,
          trigger: "change",
          message: "请选择显示状态"
        },
        status: {
          required: true,
          trigger: "change",
          message: "请选择课程状态"
        },
        vid: [
          {
            validator: (rule, value, callback) => {
              // 如果课程渠道是阿里云(1)且没有填写阿里链接，则验证失败
              if (this.listQuery.courseSource === 1 && !value) {
                callback(new Error("请输入阿里链接"));
                return;
              }
              callback();
            },
            trigger: "blur"
          }
        ],
        videoMinute: [
          {
            validator: (rule, value, callback) => {
              // 课程时长验证：分钟和秒钟必须同时填写
              const minute = value || ""; // 确保不是 undefined
              const second = this.listQuery.videoSecond || ""; // 确保不是 undefined

              // 如果两个都为空，则验证失败
              if (!minute && !second) {
                callback(new Error("请输入课程时长"));
                return;
              }

              // 如果只有一个有值，另一个为空，则验证失败
              if (!minute && second) {
                callback(new Error("请同时输入分钟和秒钟"));
                return;
              }

              if (minute && !second) {
                callback(new Error("请同时输入分钟和秒钟"));
                return;
              }

              // 验证分钟数格式
              if (
                minute &&
                !/^([0-9]|[1-9][0-9]|[1-9][0-9][0-9])$/.test(minute)
              ) {
                callback(new Error("分钟数必须为0-999的整数"));
                return;
              }

              // 验证秒钟数格式
              if (second && !/^([0-9]|[1-5][0-9])$/.test(second)) {
                callback(new Error("秒钟数必须为0-59的整数"));
                return;
              }

              callback();
            },
            trigger: "blur"
          }
        ],
        onlineDate: {
          required: true,
          trigger: "change",
          message: "请选择课程上线时间"
        }
      };
    }
  },
  mounted() {},
  methods: {
    // 验证课程时长
    validateCourseDuration() {
      // 直接触发 videoMinute 字段的验证，它会自动处理所有逻辑
      this.$refs.courseForms.validateField("videoMinute");
    },
    // 验证指定字段
    validateField(fieldName) {
      this.$refs.courseForms.validateField(fieldName);
    },
    // 课程时长输入时的处理
    onCourseDurationInput() {
      // 延迟触发验证，避免输入过程中频繁验证
      clearTimeout(this.validateTimer);
      this.validateTimer = setTimeout(() => {
        this.validateCourseDuration();
      }, 300);
    },
    // 获取三陶科目
    getSubjectList() {
      // 1 高中   2 初中
      return getSubjectList({ clientCode: this.listQuery.clientCode })
        .then(res => {
          // 获取科目
          this.subjectsAll = res.data;
          return res.data;
        })
        .catch(err => {
          // 假数据
          this.subjectsAll = [];
          return [];
        });
    },
    addCourse() {
      this.$refs.courseForms.validate(valid => {
        if (valid) {
          const params = Object.assign({}, this.listQuery);
          params.teacherId = -1;
          params.classTypeId =
            this.listQuery.clientCode === "100" ? 10000465 : 10000466;
          addCourse(params)
            .then(res => {
              if (res.code === "000000") {
                this.$message({
                  type: "success",
                  message: "添加成功"
                });
                this.$emit("addCourseList");
                this.coursePop = false;
                this.listQuery = { ...initListQuery };
                this.$refs.courseForms.clearValidate();
              }
            })
            .catch(() => {});
        } else if (
          this.listQuery.courseSource === 2 &&
          !this.listQuery.visitUrl
        ) {
          this.$message({
            type: "warning",
            message: "请填写课程URL"
          });
        } else {
        }
      });
    },
    courseDetail(ids) {
      // 获取课程详情
      courseDetail(ids).then(res => {
        if (res.code === "000000") {
          const detailData = res.data;
          // 先设置产品线，触发科目列表加载
          this.listQuery.clientCode = detailData.clientCode;

          // 等待科目列表加载完成后再设置完整数据
          this.$nextTick(() => {
            // 如果有产品线，先加载科目列表
            if (detailData.clientCode) {
              this.getSubjectList().then(() => {
                // 科目列表加载完成后设置完整数据
                this.listQuery = detailData;
              });
            } else {
              // 没有产品线直接设置数据
              this.listQuery = detailData;
            }
          });
        }
      });
    },
    editCourse() {
      // 修改课程
      this.$refs.courseForms.validate(valid => {
        if (valid) {
          if (this.listQuery.id) {
            const params = Object.assign({}, this.listQuery, {
              id: this.listQuery.id
            });
            params.teacherId = -1;
            editCourse(params)
              .then(res => {
                if (res.code === "000000") {
                  this.$message({
                    type: "success",
                    message: "修改成功"
                  });
                  this.$emit("addCourseList");
                  this.coursePop = false;
                  this.listQuery = { ...initListQuery };
                  this.$refs.courseForms.clearValidate();
                }
              })
              .catch(() => {});
          }
        } else if (!this.listQuery.videoMinute && !this.listQuery.videoSecond) {
          this.$message({
            type: "warning",
            message: "请输入课程时长"
          });
        } else if (
          this.listQuery.courseSource === 2 &&
          !this.listQuery.visitUrl
        ) {
          this.$message({
            type: "warning",
            message: "请填写课程URL"
          });
        } else if (this.listQuery.courseSource === 1 && !this.listQuery.vid) {
          this.$message({
            type: "warning",
            message: "请输入阿里链接"
          });
        } else {
        }
      });
    },
    changeInit() {
      // 重置表单数据
      this.listQuery = { ...initListQuery };
      this.subjectsAll = []; // 清空科目列表

      // 重置其他状态
      this.isEdit = false;
      this.flags = -1;

      // 使用 $nextTick 确保 DOM 更新后再清除验证
      this.$nextTick(() => {
        if (this.$refs.courseForms) {
          // 清除所有验证状态
          this.$refs.courseForms.clearValidate();

          // 重置表单字段到初始状态（可选）
          // this.$refs.courseForms.resetFields();
        }
      });
    }
  }
};
</script>
<style scoped lang="scss">
em {
  font-style: normal;
}

.assign-operas {
  display: flex;
  justify-content: center;
  align-content: center;
}

.course-duration {
  display: flex;
  justify-content: space-around;

  em {
    padding-left: 8px;
    padding-right: 8px;
  }
}

.upload-imgs {
  position: relative;
  width: 118px;
  height: 118px;
  font-size: 14px;
  display: inline-block;
  padding: 10px;
  margin-right: 25px;
  border: 2px dashed #ccc;
  text-align: center;
  vertical-align: middle;
}

.upload-imgs .add {
  display: block;
  background-color: #ccc;
  color: #ffffff;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .add .iconfont {
  padding: 10px 0;
  font-size: 40px;
}

.upload-imgs .upload {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 118px;
  height: 118px;
  opacity: 0;
  cursor: pointer;
}

.upload-imgs .img {
  position: relative;
  width: 94px;
  height: 94px;
  line-height: 94px;
}

.upload-imgs .img img {
  vertical-align: middle;
  width: 94px;
  height: 94px;
}

.upload-imgs .img .close {
  display: none;
}

.upload-imgs:hover .img .close {
  display: block;
  position: absolute;
  top: -10px;
  left: -10px;
  width: 118px;
  height: 118px;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 118px;
  font-size: 24px;
  color: #fff;
}

.img-upload {
  padding-right: 8px;
}

::v-deep .el-divider--horizontal {
  margin: 10px 0;
}

::v-deep .el-form-item--mini.el-form-item,
::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 10px;
}
</style>
