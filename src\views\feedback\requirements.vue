<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.userName"
        placeholder="姓名"
        class="filter-item"
        style="width: 200px;"
      />
      <el-select
        v-model="listQuery.deptId"
        placeholder="部门"
        filterable
        class="filter-item"
        style="width: 200px;"
        clearable
      >
        <el-option
          v-for="item in parentDepartmentList"
          :key="item.id"
          :label="item.departmentName"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="处理状态"
        filterable
        clearable
        class="filter-item"
        style="width: 140px;"
      >
        <el-option
          v-for="item in requirementsStatusList"
          :key="item.key"
          :label="item.value"
          :value="item.key"
        />
      </el-select>
      <el-date-picker
        v-model="addTime"
        type="daterange"
        range-separator="至"
        class="filter-item"
        style="width: 300px;"
        value-format="yyyy-MM-dd"
        start-placeholder="添加开始日期"
        end-placeholder="添加结束日期"
        unlink-panels
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="mini"
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="mini"
        @click="handleReset"
      >
        重置
      </el-button>
    </div>
    <el-table
      ref="assignTab"
      v-loading="listLoading"
      :data="list"
      border
      fit
      stripe
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column
        label="姓名"
        show-overflow-tooltip
        prop="realName"
        width="140px"
      />
      <el-table-column
        label="部门"
        prop="deptName"
        show-overflow-tooltip
        width="160px"
      />
      <el-table-column label="需求描述" prop="content" show-overflow-tooltip />
      <el-table-column label="需求类型" prop="type" width="140px">
        <template slot-scope="scope">
          {{ getName(scope.row.type, "type") }}
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="140px" >
        <template slot-scope="scope">
          {{ getName(scope.row.status, "status") }}
        </template>
      </el-table-column>
      <el-table-column
        label="添加时间"
        prop="time"
        show-overflow-tooltip
        width="200px"
      />
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width action-warp"
        width="230"
      >
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            v-if="row.status === '1'"
            size="mini"
            @click="openDialog('3', row.id)"
          >
            完成处理
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handleDetails(row)"
          >
            详情
          </el-button>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageIndex"
      :limit.sync="listQuery.pageSize"
      @pagination="handlePagination"
    />
    <RequirementsDialog @refresh="refresh" ref="requirementsDialog"> </RequirementsDialog>
  </div>
</template>
<script>
import { getDepartmentList } from "@/api/system-setting";
import RequirementsDialog from "./components/requirementsDialog.vue";
import Pagination from "@/components/Pagination";
import { requirementPage } from "@/api/feedback";
import { requirementsStatusList } from "@/utils/field-conver";
export default {
  name: "Requirements",
  components: {
    Pagination,
    RequirementsDialog
  },
  data() {
    return {
      addTime: [],
      // 需求类型
      typeList: [
        {
          id: 0,
          name: "产品需求"
        },
        {
          id: 1,
          name: "教研需求"
        }
      ],
      // 需求状态 0 待处理 1 处理中 2 已上线 3 不处理
      requirementsStatusList,
      list: [],
      parentDepartmentList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        status: "",
        userName: "",
        type: "0", // 先写死 默认产品需求
        deptId: "",
        startTime: "",
        endTime: ""
      }
    };
  },
  created() {},
  mounted() {
    this.getDepartmentList();
    this.$nextTick(() => {
      this.getList();
    });
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.params.refresh) {
        vm.listQuery.pageIndex = 1;
        vm.getList();
      }
    });
  },

  methods: {
    openDialog(status, id) {
      this.$refs.requirementsDialog.init(status, id);
    },
    /**
     * 部门列表
     * */
    getDepartmentList() {
      getDepartmentList({ pageSize: 9999 }).then(res => {
        this.parentDepartmentList = res.data.records;
      });
    },
    handleDetails (row) {
      // 跳转到详情
      this.$router.push({
        name: "RequirementsInfo",
        query: {
          id: row.id,
          refresh: true
        },
      });
    },
    getName(id, type) {
      // 需求类型
      if (type === "type") {
        return this.typeList.find(item => Number(item.id) === Number(id)).name;
      } else {
        // 需求状态
        return this.requirementsStatusList.find(item => Number(item.key) === Number(id)).value;
      }
    },
    refresh () {
      this.listQuery.pageIndex = 1;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const params = { ...this.listQuery };
      params.startTime = Array.isArray(this.addTime) && this.addTime.length > 0 ? this.addTime[0] : "";
      params.endTime = Array.isArray(this.addTime) && this.addTime.length > 1 ? this.addTime[1] : "";
      await requirementPage(params)
        .then(response => {
          this.list = response.data.records;
          this.total = response.data.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    handleFilter() {
      this.listQuery.pageIndex = 1;
      this.getList();
    },
    handleReset() {
      this.addTime = [];
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        status: "",
        userName: "",
        type: "0",
        deptId: "",
        startTime: "",
        endTime: ""
      };
      this.getList();
    }
  }
};
</script>

<style scoped>
.codes {
  font-weight: bold;
  color: #0a76a4;
}
</style>
