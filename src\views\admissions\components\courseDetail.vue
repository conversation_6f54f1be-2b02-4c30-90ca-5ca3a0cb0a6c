<template>
  <div class="course-detail">
    <el-row>
      <el-col :sm="{span:24}" :md="{span:12}">
        <el-form ref="questionInfoForm" :model="basis" :rules="rules">
          <div class="basis-info">
            <h2>基础信息</h2>
            <el-row>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="科目" prop="subjectId">
                  <el-select filterable v-model="basis.subjectId" placeholder="选择科目" clearable class="filter-item" @change="getQuestionOutline">
                    <el-option v-for="item in subjectsList" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="题目类型" prop="type">
                  <el-input v-model="basis.type" disabled placeholder="选择题" />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="大纲" prop="outlineId">
                  <el-select filterable v-model="basis.outlineId" placeholder="大纲" clearable class="filter-item" @change="getQuestionKeynote">
                    <el-option v-for="item in outline" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:12}">
                <el-form-item label="考点" prop="keynoteId">
                  <el-select filterable v-model="basis.keynoteId" placeholder="考点" clearable multiple class="filter-item">
                    <el-option v-for="item in keynote" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="questions-info">
            <h2>试题问题</h2>
            <el-row>
              <el-col :sm="{span:24}" :md="{span:24}">
                <el-form-item label="题目内容">
                  <el-input v-model="basis.question" type="textarea" maxlength="1000" placeholder="请输入题目内容" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:24}">
                <el-form-item label="题目图片">
                  <a v-if="!basis.questionImage&&!isEdit||basis.questionImage=='false'" href="javascript:;" class="uploadFile">
                    <span class="el-icon-plus" />
                    <input ref="SubjectUpload" class="uFile" type="file" multiple="multiple" @change="uploadSubject($event)">
                  </a>
                  <!--新增和修改的图片-->
                  <img v-if="basis.questionImage&&basis.questionImage!=='false'&&!isEdit" :src="basis.questionImage" class="uploadFile" @click="basis.questionImage=''">
                  <!--详情的图片-->
                  <img v-if="basis.questionImage&&basis.questionImage!=='false'&&isEdit" :src="basis.questionImage" class="uploadFile">
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="basis-info">
            <h2>试题选项</h2>
            <el-row v-for="(item,i) in basis.answerList" :key="i" class="questions-lists">
              <el-col :sm="{span:22}" :md="{span:21}">
                <el-form-item>
                  <el-radio-group v-model="radio" size="mini">
                    <el-radio v-if="i===0" :label="0">正确A</el-radio>
                    <el-radio v-if="i===1" :label="1">正确B</el-radio>
                    <el-radio v-if="i===2" :label="2">正确C</el-radio>
                    <el-radio v-if="i===3" :label="3">正确D</el-radio>
                  </el-radio-group>
                  <el-input v-model="item.answer" type="textarea" maxlength="200" placeholder="请输入题目内容" :autosize="{minRows: 4}" show-word-limit style="width: 85%" />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:2}" :md="{span:3}">
                <a v-if="!item.answerImage&&!isEdit||item.answerImage==='false'" href="javascript:;" class="uploadFile">
                  <span class="el-icon-plus" />
                  <input ref="uploadBtn" class="uFile" type="file" multiple="multiple" accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadSubjectA($event,i)">
                </a>
                <p v-if="!item.answerImage&&!isEdit||item.answerImage==='false'" class="tips">图片最大尺寸为614px*138px</p>
                <!--新增和修改的图片-->
                <img v-if="item.answerImage&&item.answerImage!=='false'&&!isEdit" :src="item.answerImage" class="uploadFile" @click="item.answerImage=''">
                <!--详情的图片-->
                <img v-if="item.answerImage&&item.answerImage!=='false'&&isEdit" :src="item.answerImage" class="uploadFile">
              </el-col>
            </el-row>
          </div>
          <div class="questions-info">
            <h2>答案解析</h2>
            <el-row>
              <el-col :sm="{span:24}" :md="{span:21}">
                <el-form-item>
                  <el-input v-model="basis.analysis" type="textarea" maxlength="1000" placeholder="请输入文字解析" :autosize="{minRows: 3}" show-word-limit style="width: 90%" />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:3}">
                <a v-if="!basis.analysisImage&&!isEdit||basis.analysisImage==='false'" href="javascript:;" class="uploadFile">
                  <span class="el-icon-plus" />
                  <input class="uFile" type="file" multiple="multiple" accept="image/png,image/jpeg,image/gif,image/jpg" @change="uploadSubjectaAnswer($event)">
                </a>
                <img v-if="basis.analysisImage&&basis.analysisImage!=='false'&&!isEdit" :src="basis.analysisImage" class="uploadFile" @click="basis.analysisImage=''">
                <img v-if="basis.analysisImage&&basis.analysisImage!=='false'&&isEdit" :src="basis.analysisImage" class="uploadFile">
              </el-col>
            </el-row>
            <h2>解析视频</h2>
            <el-row>
              <el-col :sm="{span:24}" :md="{span:24}">
                <el-form-item>
                  <el-input v-model="basis.aliyunVid" placeholder="请输入阿里链接" style="width: 80%" />
                </el-form-item>
              </el-col>
              <el-col :sm="{span:24}" :md="{span:24}">
                <el-form-item>
                  <el-input v-model="basis.huaweiAssetId" placeholder="请输入华为链接" style="width: 80%" />
                  <!--<el-button type="primary" size="mini">同步到华为云</el-button>-->
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="basis-info">
            <el-form-item>
              <el-button v-if="showFlag==='upload'" size="mini" type="primary" :disabled="subFlag" @click="addQuestionInfo">确定</el-button>
              <el-button v-if="showFlag==='edit'" size="mini" type="primary" :disabled="subFlag" @click="addQuestionInfo">确定</el-button>
              <el-button size="mini" type="infor" @click="cancelSubmit">取消</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
      <el-col v-if="basis!==null" :sm="{span:24}" :md="{span:12}">
        <div class="preview-in">
          <h3 class="preview-info-title">
            <span>
              预览内容
            </span>
            <span>
              <i v-if="radio!==null">正确答案:</i>
              <i v-if="radio===0">A</i>
              <i v-if="radio===1">B</i>
              <i v-if="radio===2">C</i>
              <i v-if="radio===3">D</i>
            </span>
          </h3>
          <div class="preview-infos">
            <h3>
              <em v-if="basis.question">{{ basis.question }}</em>
              <img v-if="basis.questionImage" :src="basis.questionImage" class="title-big">
            </h3>
            <div v-if="basis.answerList&&basis.answerList.length>0" class="answer-list">
              <el-row>
                <el-col v-for="(item,i) in basis.answerList" :key="i" :sm="{span:24}" :md="{span:12}" class="answer-in">
                  <em v-if="i===0&&(item.answer||item.answerImage)">A:</em>
                  <em v-if="i===1&&(item.answer||item.answerImage)">B:</em>
                  <em v-if="i===2&&(item.answer||item.answerImage)">C:</em>
                  <em v-if="i===3&&(item.answer||item.answerImage)">D:</em>
                  <p :class="[{actived:radio===i},{line:item.answer||item.answerImage}]">
                    <em v-if="item.answer">{{ item.answer }}</em>
                    <img v-if="item.answerImage&&item.answerImage!=='false'" :src="item.answerImage" class="title-img">
                    <i v-show="radio===i" class="el-icon-success checked" />
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-if="basis.analysis||basis.analysisImage" class="preview-infos parse">
              <h3>答案解析</h3>
              <p v-if="basis.analysis">{{ basis.analysis }}</p>
              <img v-if="basis.analysisImage&&basis.analysisImage!=='false'" :src="basis.analysisImage" class="title-big">
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// eslint-disable-next-line no-undef
var obsClient = new ObsClient({
  access_key_id: 'CSMHAP6XJZ3Q9NTLYX7W',
  secret_access_key: 'o647hvflICHOtB89veGqCTd742vE9Cy9OtAvh1rC',
  server: 'obs.cn-north-1.myhuaweicloud.com',
  timeout: 60 * 5
})
import { uploadSuccess } from '@/api/common'
import { getQuestionOutline, getQuestionKeynote, getSubjects, addQuestionInfo, getExamQuestion, editExamQuestion } from '@/api/admissions'
export default {
  name: 'CourseDetail',
  inject: ['reload'],
  data() {
    return {
      enableList: [],
      radio: null,
      basis: {
        subjectId: '',
        type: '选择题',
        outlineId: '',
        keynoteId: [],
        question: '',
        questionImage: '',
        answerList: [
          {
            answer: '',
            answerImage: '',
            correctFlag: 0
          },
          {
            answer: '',
            answerImage: '',
            correctFlag: 0
          },
          {
            answer: '',
            answerImage: '',
            correctFlag: 0
          },
          {
            answer: '',
            answerImage: '',
            correctFlag: 0
          }
        ],
        analysis: '',
        analysisImage: '',
        aliyunVid: '',
        huaweiAssetId: ''
      },
      rules: {
        subjectId: { required: true, trigger: 'change', message: '请选择科目' },
        outlineId: { required: true, trigger: 'change', message: '请选择大纲' }
      },
      isEdit: false,
      subjectsList: [],
      outline: [],
      keynote: [],
      imgIndex: null,
      questionId: null,
      showFlag: '',
      subFlag: false,
      uuid: ''
    }
  },
  watch: {
    outline: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.basis.outlineId = ''
          this.basis.keynoteId = ''
        }
      },
      deep: true
    },
    keynote: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          this.basis.keynoteId = ''
        }
      },
      deep: true
    },
    'basis.subjectId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.basis.outlineId = ''
          this.keynote = []
        }
      }
    },
    'basis.outlineId': { // listQuery.clientCode
      deep: true,
      immediate: false,
      handler(newVal, oldVal) {


        if (newVal !== oldVal && oldVal) { // 选择的产品线绑定的值发生变化，班型要重新渲染
          this.basis.keynoteId = ''
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {

      this.isEdit = this.$route.params.isEdit
      this.questionId = this.$route.params.id
      this.showFlag = localStorage.getItem('showFlag')
      const name = localStorage.getItem('questionName')
      this.setTagsViewTitle(name)
      this.getSubjects()
      this.getExamQuestion(this.questionId)
    })
  },
  methods: {
    setTagsViewTitle(name) {
      const cRoute = Object.assign({}, this.$route)
      const title = '试题'
      const route = Object.assign({}, cRoute, { title: `${name}-${title}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    getSubjects() { // 查询科目
      getSubjects().then(res => {
        if (res.code === '000000') {
          this.subjectsList = res.data
        }
      }).catch(() => {

      })
    },
    getQuestionOutline(val) { // 大纲
      if (val) {
        getQuestionOutline(val).then(res => {
          if (res.code === '000000') {
            this.outline = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择科目'
        })
      }
    },
    getQuestionKeynote(val) { // 考点
      if (val) {
        getQuestionKeynote(val).then(res => {
          if (res.code === '000000') {
            this.keynote = res.data || []
          }
        }).catch(() => {

        })
      } else {
        this.$message({
          type: 'warning',
          message: '请先选择大纲'
        })
      }
    },
    getExamQuestion(ids) { // 获取试题的详情
      const that = this
      if (ids) {
        getExamQuestion(ids).then(res => {
          if (res.code === '000000') {
            that.basis.subjectId = res.data.subjectId || null
            that.basis.type = '选择题'
            that.basis.outlineId = res.data.outlineId || null
            that.basis.question = res.data.question || ''
            that.basis.questionImage = res.data.questionImage || ''
            that.basis.questionCode = res.data.questionCode || ''
            that.basis.id = res.data.id
            that.basis.huaweiAssetId = res.data.huaweiAssetId || null
            that.basis.analysisImage = res.data.analysisImage || null
            that.basis.analysis = res.data.analysis || ''
            that.basis.aliyunVid = res.data.aliyunVid || ''
            that.basis.answerList = res.data.questionAnswerList || []
            const arrK = res.data.keynoteId ? res.data.keynoteId.split(',') : []
            that.basis.keynoteId = arrK && arrK.length > 0 ? arrK.map(item => Number(item)) : []
            that.getQuestionKeynote(res.data.outlineId)
            that.getQuestionOutline(res.data.subjectId)
            that.basis.answerList.find((item, index) => {
              if (item.correctFlag === 1) {
                that.radio = index
              }
            })

          }
        }).catch(() => {

        })
      }
    },
    uploadSubjectA(e, index) {

      const file = e.target.files[0]

      const size = (file.size / 1024 / 1024).toFixed(3)
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '请上传1M以内的图片'
        })
      } else {
        this.uploadA(file, index)
      }
    },
    uploadA(file, i) {
      const that = this
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/question/${that.uuid}.${tempName[tempName.length - 1]}`

      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {

          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/question/${that.uuid}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              that.basis.answerList[i].answerImage = res.data.url
            }
          })
        }
      })
    },
    uploadSubject(e) {

      const file = e.target.files[0]

      const size = (file.size / 1024 / 1024).toFixed(3)
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '请上传1M以内的图片'
        })
      } else {
        this.upload(file, 'uploadSubject')
      }
    },
    uploadSubjectaAnswer(e) {

      const file = e.target.files[0]

      const size = (file.size / 1024 / 1024).toFixed(3)
      if (size > 1) {
        this.$message({
          type: 'warning',
          message: '请上传1M以内的图片'
        })
      } else {
        this.upload(file, 'uploadSubjectaAnswer')
      }
    },
    upload(file, flag) {
      const that = this
      that.uuid = that.get_uuid()
      const tempName = file.name.split('.')
      const fileName = `santao_stip/crm/question/${that.uuid}.${tempName[tempName.length - 1]}`

      obsClient.putObject({
        Bucket: 'obs-d812',
        Key: `${fileName}`, // 文件名
        SourceFile: file// 文件路径
      }, function(err, result) {
        if (err) {
          console.error('Error-->' + err)
        } else {

          const paramsUpload = Object.assign({}, {
            imageUrl: `https://obs-d812.obs.cn-north-1.myhuaweicloud.com/santao_stip/crm/question/${that.uuid}.${tempName[tempName.length - 1]}`,
            resourceType: 'image'
          })
          uploadSuccess(paramsUpload).then(res => {
            if (res.code === '000000') {
              if (flag === 'uploadSubject') {
                that.basis.questionImage = res.data.url
              } else {
                that.basis.analysisImage = res.data.url
              }
            }
          })
        }
      })
    },
    addQuestionInfo() { // 新增试题
      this.$refs.questionInfoForm.validate((valid) => {
        if (valid) {
          if (!this.basis.question && !this.basis.questionImage) {
            this.$message({
              type: 'warning',
              message: '请填写试题问题'
            })
            this.subFlag = false
            return
          }
          if (this.basis.question && this.basis.questionImage) {
            this.$message({
              type: 'warning',
              message: '试题问题只能是题目文字或者题目图片二者之一'
            })
            this.subFlag = false
            return
          }

          let showFlag = false
          let showFlagBoth = false

          this.basis.answerList.forEach((item, index, arr) => {
            if (index === this.radio) {
              item.correctFlag = 1
            } else {
              item.correctFlag = 0
            }
            if (!item.answer && !item.answerImage) {
              showFlag = true
            }
            if (item.answer && (item.answerImage && item.answerImage !== 'false')) {
              showFlagBoth = true
            }
          })
          if (showFlag) {
            this.$message({
              type: 'warning',
              message: '请填写试题选项'
            })
            this.subFlag = false
            return
          }
          if (showFlagBoth) {
            this.$message({
              type: 'warning',
              message: '试题选项只能是文字或者图片二者之一'
            })
            this.subFlag = false
            return
          }
          if (this.radio === null) {
            this.$message({
              type: 'warning',
              message: '请选择正确答案'
            })
            this.subFlag = false
            return
          }
          if (!this.basis.analysis && !this.basis.analysisImage) {
            this.$message({
              type: 'warning',
              message: '请填写答案解析'
            })
            this.subFlag = false
            return
          }
          if (this.basis.analysis && this.basis.analysisImage) {
            this.$message({
              type: 'warning',
              message: '答案解析只能是文字解析或者图片解析二者之一'
            })
            this.subFlag = false
            return
          }
          if (!this.basis.aliyunVid && !this.basis.huaweiAssetId) {
            this.$message({
              type: 'warning',
              message: '请填写视频解析'
            })
            this.subFlag = false
            return
          }
          this.subFlag = true
          const keynoteIds = this.basis.keynoteId && this.basis.keynoteId.length > 0 ? this.basis.keynoteId.join(',') : ''
          const data = Object.assign({}, this.basis, { keynoteId: keynoteIds, id: this.basis.id ? this.basis.id : null })
          if (this.showFlag === 'upload') {
            addQuestionInfo(data).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '试题新增成功'
                })
                this.subFlag = true
                this.basis = {
                  subjectId: '',
                  type: '选择题',
                  outlineId: '',
                  keynoteId: '',
                  question: '',
                  questionImage: '',
                  answerList: [
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    }
                  ],
                  analysis: '',
                  analysisImage: '',
                  aliyunVid: '',
                  huaweiAssetId: ''
                }
                this.closeIt()
              }
            }).catch(() => {
              this.subFlag = false

            })
          } else {
            editExamQuestion(data).then(res => {
              if (res.code === '000000') {
                this.$message({
                  type: 'success',
                  message: '试题编辑成功'
                })
                this.subFlag = true
                this.basis = {
                  subjectId: '',
                  type: '选择题',
                  outlineId: '',
                  keynoteId: '',
                  question: '',
                  questionImage: '',
                  answerList: [
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    },
                    {
                      answer: '',
                      answerImage: '',
                      correctFlag: 0
                    }
                  ],
                  analysis: '',
                  analysisImage: '',
                  aliyunVid: '',
                  huaweiAssetId: ''
                }
                this.closeIt()
              }
            }).catch(() => {
              this.subFlag = false

            })
          }
        } else {
          this.subFlag = false

          return false
        }
      })
    },
    closeIt() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
        setTimeout(() => {
          this.reload()
        }, 100)
      })
    },
    cancelSubmit() {
      this.$store.dispatch('tagsView/delView', this.$route).then(({ visitedViews }) => {
        this.$router.go(-1)
      })
    },
    get_uuid() { // 获取uuid
      var s = []
      var hexDigits = '0123456789abcdef'
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      var uuid = s.join('')
      return uuid
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style>
  .el-form-item__label{
    font-weight: normal;
    color: #67727c;
  }
</style>
