<!--suppress ALL -->
<template>
  <el-dialog :visible.sync="delayPop" :title="delayTitle" :close-on-click-modal="!delayPop" width="60%" :append-to-body="showAppend" @close="delayClose">
    <div class="delay-form">
      <el-form :model="delayForm" label-width="80px" class="add-delay">
        <el-form-item label="延期日期" required>
          <el-date-picker
            v-model="delayForm.delayExpirationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="创建日期"
          />
        </el-form-item>
        <el-form-item label="延期原因" style="width:70%" required>
          <el-input v-model="delayForm.delayRemark" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item>
          <el-button v-permission="['customer:schoolProject:delayList']" size="mini" type="primary" @click="confirmDeley">延期</el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="assignTab"
        v-loading="listLoading"
        :data="list"
        border
        fit
        stripe
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="原到期时间" show-overflow-tooltip width="200px">
          <template slot-scope="scope">
            <span v-if="scope.row.delayBeforeExpirationDate">
              {{ scope.row.delayBeforeExpirationDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="延期后时间" show-overflow-tooltip width="200px">
          <template slot-scope="scope">
            <span v-if="scope.row.delayAfterExpirationDate">
             {{ scope.row.delayAfterExpirationDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="延期原因" show-overflow-tooltip prop="delayRemark" width="300px" />
        <el-table-column label="操作人" prop="createName"  />
        <el-table-column label="操作时间" show-overflow-tooltip prop="createTime" />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { getDetail, addDelayLog } from '@/api/schoolCampus'
export default {
  name: 'DelayPop',
  data() {
    return {
      list: [],
      listLoading: true,
      delayPop: false,
      delayTitle: '',
      delayForm: {},
      orderDelay: '',
      schoolId: 0,
      expirationDate: '',
      delayFlag: false,
      institutionId: 0,
      showAppend: false
      // pickerOptions: {
      //   disabledDate(time) {
      //     return time.getTime() < Date.now()
      //   }
      // }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.listLoading = false
    })
  },
  methods: {
    getDetail(ids) {
      getDetail(ids).then(res => {
        if (res.code === '000000') {
          this.list = res.data.userSchoolDelayLogs !== null ? res.data.userSchoolDelayLogs : []
          this.listLoading = false
        }
      })
    },
    confirmDeley() {
      this.delayFlag = true
      const expirationDate = this.expirationDate !== null ? Date.parse(this.expirationDate) : ''

      const today = new Date()
      today.setTime(today.getTime())
      // const todays = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate()
      // const subTime = Date.parse(this.delayForm.delayExpirationDate)
      // const todayP = Date.parse(todays)
      if (!this.delayForm.delayRemark) {
        this.$message({
          type: 'warning',
          message: '请填写延期原因'
        })
        return
      }
      if (this.delayForm.delayExpirationDate && this.schoolId) {
        const data = Object.assign({}, this.delayForm, { schoolId: this.schoolId, institutionId: this.institutionId })
        addDelayLog(data).then(res => {
          if (res.code === '000000') {
            this.$message({
              type: 'success',
              message: '添加成功'
            })
            this.delayForm = {}
            this.getDetail(this.orderDelay)
          }
        }).catch(() => {

          this.delayForm = {}
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择延期日期'
        })
      }
    },
    delayClose() {
      this.delayPop = false
      this.delayForm = {}
    }
  }
}
</script>

<style scoped>
  .add-delay{
    display: flex;
  }
  .el-textarea{
    width: 95%;
  }
</style>
