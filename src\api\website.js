
import request from '@/utils/request'
/**
 * 按照类型获取可展示的banner
 * @param data
 */
export function getShowBanner(terminal, type) {
  return request({
    url: `websiteBanner/getShowBanner?terminal=${terminal}&type=${type}`,
    method: 'GET'
  })
}

/**
 * 删除官网banner
 * @param data
 */
export function removeBanner(id) {
  return request({
    url: `websiteBanner/remove/${id}`,
    method: 'DELETE'
  })
}

/**
 * 按照类型获取可展示的banner
 * @param data
 */
export function listPage(hotShowFlag, newShowFlag, pageIndex, pageSize, selfShowFlag, title, type) {
  return request({
    url: `websiteInformation/listPage?hotShowFlag=${hotShowFlag}&newShowFlag=${newShowFlag}&pageIndex=${pageIndex}&pageSize=${pageSize}&selfShowFlag=${selfShowFlag}&title=${title}&type=${type}`,
    method: 'GET'
  })
}

/**
 * 删除官网三陶资讯
 * @param data
 */
export function removeNews(id) {
  return request({
    url: `websiteInformation/remove/${id}`,
    method: 'DELETE'
  })
}

/**
 * 新增官网三陶资讯
 * @param data
 */
export function addInformation(data) {
  return request({
    url: 'websiteInformation/add',
    method: 'POST',
    data: data
  })
}

/**
 * 修改官网三陶资讯
 * @param data
 */
export function editInformation(data) {
  return request({
    url: 'websiteInformation/edit',
    method: 'PUT',
    data: data
  })
}

/**
 * 获取官网三陶资讯详情
 * @param data
 */
export function getInformation(id) {
  return request({
    url: `websiteInformation/getInformation/${id}`,
    method: 'GET'
  })
}

/**
 * 学生试听信息列表
 * @param data
 */
export function listListen(data) {
  return request({
    url: `website/auditionListPage`,
    method: 'GET',
    params: data
  })
}
/**
 * 分配学生试听
 * @param data
 */
export function allottStudent(auditionId, partnerId) {
  return request({
    url: `website/audition/allott?auditionId=${auditionId}&partnerId=${partnerId}`,
    method: 'PUT'
  })
}
/**
 * 学生试听信息列表
 * @param data
 */
export function listAgent(data) {
  return request({
    url: `website/agentInfoListPage?`,
    method: 'GET',
    params: data
  })
}
/**
 * 获取合作列表
 * @param data
 */
export function cooperationList(data) {
  return request({
    url: `website/cooperations/page`,
    method: 'GET',
    params: data
  })
}
/**
 * 获取获取跟进人员
 * @param data
 */
export function followList(search) {
  return request({
    url: `user/findUser?search=${search || ''}`,
    method: 'GET'
  })
}

/**
 * 分配人员
 * @param data
 */
export function allott(cooperationId, userId) {
  return request({
    url: `website/cooperations/allott?cooperationId=${cooperationId}&userId=${userId}`,
    method: 'GET'
  })
}
/**
 * 获取已加盟的合伙人列表
 * @param data
 */
export function partnerLists(searchField) {
  return request({
    url: `clue/listJoinedClues?searchField=${searchField || ''}`,
    method: 'PUT'
  })
}
/**
 * 分配合伙人
 * @param data
 */
export function allottPartner(auditionId, partnerId) {
  return request({
    url: `website/audition/allott?auditionId=${auditionId}&partnerId=${partnerId}`,
    method: 'PUT'
  })
}
/**
 * 申请代理分配
 * @param data
 */
export function allottWebsite(agentId, userId) {
  return request({
    url: `website/agents/allott?agentId=${agentId}&userId=${userId}`,
    method: 'PUT'
  })
}
